<mat-card class="mat-elevation-z0">
    <mat-card-title>
        <button mat-mini-fab  class="md-mini-fab-right" 
            [matMenuTriggerFor]="menu"
            color="primary"
            *ngIf="hasAccessCreate">
            <mat-icon>add</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
            <ng-container *ngFor="let tipo of tiposAvaliacoes">
                <button mat-menu-item  (click)="add(tipo.id)"> 
                    {{tipo.nome}}
                </button>
            </ng-container>
        </mat-menu>
    </mat-card-title>
    <mat-card-content>
        <table mat-table [dataSource]="avaliacoesFiltered"> 
            <!-- Tipo Column -->
            <ng-container matColumnDef="tipo">
                <th mat-header-cell *matHeaderCellDef>
                    Tipo de Avaliação
                    <button mat-icon-button
                        [matMenuTriggerFor]="menuFilterTipo"
                        color="gray"
                        matTooltip="Filtrar avaliações por Tipo de Avaliação.">
                        <mat-icon style="font-size: small;">filter_list</mat-icon>
                    </button>
                    <mat-menu #menuFilterTipo="matMenu">
                        <button mat-menu-item  (click)="filterAvaliacoes(undefined)"> 
                            <small>Todos os tipos...</small>
                        </button>
                        <ng-container *ngFor="let tipo of tiposAvaliacoes">
                            <button mat-menu-item  (click)="filterAvaliacoes(tipo.id)" *ngIf="possuiAvaliacoes(tipo.id)"> 
                                <small>{{tipo.nome}}</small>
                            </button>
                        </ng-container>
                    </mat-menu>
                </th>
    
                <td mat-cell *matCellDef="let row" (click)="navigateToRead(row.id)">
                    <a (click)="navigateToRead(row.id)" class="edit tdlinked" 
                        *ngIf="hasAccessRead">
                        {{ getNomeTipoAvaliacao(row.idTipoAvaliacao) }}
                    </a>
                </td>
            </ng-container>
        
            <!-- data Column -->
            <ng-container matColumnDef="data">
                <th mat-header-cell *matHeaderCellDef >Data</th>
                <td mat-cell *matCellDef="let row">
                    <a (click)="navigateToRead(row.id)" class="edit tdlinked" 
                        *ngIf="hasAccessRead">
                        {{row.data | date: 'dd/MM/yyyy'}}
                    </a>
                </td>
            </ng-container>

            <!-- profissional Column -->
            <ng-container matColumnDef="profissional">
                <th mat-header-cell *matHeaderCellDef >Responsável</th>
                <td mat-cell *matCellDef="let row">
                    <a (click)="navigateToRead(row.id)" class="edit tdlinked" 
                        *ngIf="hasAccessRead">
                        {{row?.profissional?.nome}}
                    </a>
                </td>
            </ng-container>
      
          <!-- Action Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Ações</th>
            <td mat-cell *matCellDef="let row">
                <a (click)="generatePDF(row.id)" class="edit"
                    *ngIf="hasAccessRead">
                    <i class="material-icons">
                        print
                    </i>
                </a>
                <a (click)="navigateToRead(row.id)" class="edit"
                    *ngIf="hasAccessRead">
                    <i class="material-icons">
                        remove_red_eye
                    </i>
                </a>
                <a (click)="navigateToEdit(row.id)" class="edit"
                    *ngIf="hasAccessUpdate">
                    <i class="material-icons">
                        edit
                    </i>
                </a>
                <a (click)="deleteAvaliacao(row.id)" class="delete"
                    *ngIf="hasAccessDelete">
                    <i class="material-icons">
                        delete
                    </i>
                </a>
            </td>
          </ng-container>
      
          <app-avaliacoespdf *ngIf="viewAvaliacaoPDF" [idAvaliacao] = "idAvaliacao" (pdfGenerated)="onPDFGenerated()"></app-avaliacoespdf>
          <app-avaliacaovbmapppdf *ngIf="viewVbmappPDF" [idAvaliacao] = "idAvaliacao" (pdfGenerated)="onPDFGenerated()"></app-avaliacaovbmapppdf>
          <app-esdmchecklistpdf *ngIf="viewESDMPDF" [idAvaliacao] = "idAvaliacao" [pacienteSearch]="paciente" (pdfGenerated)="onPDFGenerated()"></app-esdmchecklistpdf>

          <tr mat-header-row *matHeaderRowDef="displayedColumns" class="first-header-row"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
    </mat-card-content>
</mat-card>
<div *ngIf="avaliacoesFiltered?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Nenhuma avaliação cadastrada!</h1>
</div>