import { AuthService } from './auth.service';
import { HeaderService } from './../header/header.service';
import { Injectable } from "@angular/core";
//import 'rxjs/add/operator/toPromise';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireAuth } from '@angular/fire/auth';
import * as firebase from 'firebase/app';
//import { auth } from 'firebase';

@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(
    public db: AngularFirestore,
    public afAuth: AngularFireAuth,
    public authService: AuthService,
    public headerService: HeaderService
  ){
  }
 
 
   getCurrentUser(){
     let t = this;
    //  console.log("111")
     return new Promise<any>((resolve, reject) => {
       var user = firebase.auth().onAuthStateChanged(function(user){
         if (user) {
          //  console.log(user)
           //t.headerService.setUser(user);
           t.authService.setUser(user);
           //console.log(t.authService.getUser())
           //console.log(t.headerService.getUser())
           resolve(user);
           //resolve(t.authService.getUser());
         } else {
           reject('No user logged in');
         }
       })
     })
   }

   updateCurrentUser(value){
     return new Promise<any>((resolve, reject) => {
       var user = firebase.auth().currentUser;
       //console.log(value.nome);
       user.updateProfile({
         displayName: value.nome,
         photoURL: user.photoURL
       }).then(res => {
         resolve(res);
       }, err => reject(err))
     })
   }
}
