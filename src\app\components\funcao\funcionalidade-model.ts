import { Permissao } from './permissao-model';
export class Funcionalidade{
    nome: string;
    permission: Map<string, string>;

    constructor(){
        this.permission.set("create", "X");
        this.permission.set("read", "X");
        this.permission.set("update", "X");
        this.permission.set("delete", "X");
    }

    /*constructor() {
        this.permission = new Permissao();
    }*/
}