table {
    width: 100%
}

.edit {
    margin-right: 10px;
}
.edit > i {
    color: #d9cd26;
    cursor: pointer;
}

.delete > i {
    color: #e35e6b;
    cursor: pointer;
}

.mat-column-nome {
    word-wrap: break-word !important;
    white-space: unset !important;
    flex: 0 0 28% !important;
    
    overflow-wrap: break-word;
    word-wrap: break-word;
  
    word-break: break-word;
  
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto;
  }