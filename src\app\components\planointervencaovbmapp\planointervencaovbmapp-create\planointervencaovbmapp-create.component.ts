import { HabilidadeAvaliacaoService } from './../../avaliacao/habilidade-avaliacao.service';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { MarcoVBMAPP } from './../../marcovbmapp/marcovbmapp-model';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { MatSelectChange } from '@angular/material/select';
import { PlanointervencaovbmappService } from './../planointervencaovbmapp.service';
import { VBMAPPMsAssmtService } from './../../vbmappmsassmt/vbmappmsassmt.service';
import { ObjetivoVBMAPPService } from './../../objetivovbmapp/objetivovbmapp.service';
import { PacienteService } from './../../paciente/paciente.service';
import { AuthService } from './../../template/auth/auth.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { VBMAPPMilestonesAssessment } from './../../vbmappmsassmt/vbmappmsassmt-model';
import { VBMAPPMilestonesAssessmentItem } from './../../vbmappmsassmt/vbmapp-milestones-item-model';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { Profissional } from './../../profissional/profissional-model';
import { Paciente } from './../../paciente/paciente-model';
import { MatTableDataSource } from '@angular/material/table';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { PlanoIntervencaoVBMAPP } from './../planointervencaovbmapp-model';
import { AppDateAdapter, APP_DATE_FORMATS } from '../../../shared/format-datepicker';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { DateAdapter } from 'angular-calendar';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Component, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { Avaliacao } from '../../avaliacao/avaliacao-model';
import { AvaliacaoService } from '../../avaliacao/avaliacao.service';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { HabilidadeAvaliacao } from '../../avaliacao/habilidade-avaliacao-model';
import { RespostaHabilidadeAvaliacao } from '../../avaliacao/resposta-habilidade-avaliacao-model';
import { DominioAvaliacao } from '../../avaliacao/dominio-avaliacao-model';
import { NivelAvalicao } from '../../avaliacao/nivel-avaliacao-model';
import { NivelAvaliacaoService } from '../../avaliacao/nivel-avaliacao.service';
import { DominioAvaliacaoService } from '../../avaliacao/dominio-avaliacao.service';
import { DominioRespostaHabilidadeAvaliacao } from '../../avaliacao/dominio-resposta-habilidade-avaliacao-model';
import { ESDMChecklist } from '../../esdmchecklist/esdmchecklist-model';
import { EsdmchecklistService } from '../../esdmchecklist/esdmchecklist.service';
import { forkJoin } from 'rxjs';
import { NivelService } from '../../nivel/nivel.service';
import { ObjetivoService } from '../../objetivo/objetivo.service';
import { ESDMChkLstCompetencia } from '../../esdmchecklist/esdmchklst-competencia-model';
import { Objetivo } from '../../objetivo/objetivo-model';
import { ObjetivoCreateComponent } from '../../objetivo/objetivo-create/objetivo-create.component';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { ObjetivovbmappCreateComponent } from '../../objetivovbmapp/objetivovbmapp-create/objetivovbmapp-create.component';
import { ConfirmDialogCustomComponent, ConfirmDialogCustomData } from '../../template/confirm-dialog-custom/confirm-dialog-custom.component';

@Component({
  selector: 'app-planointervencaovbmapp-create',
  templateUrl: './planointervencaovbmapp-create.component.html',
  styleUrls: ['./planointervencaovbmapp-create.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class PlanointervencaovbmappCreateComponent implements OnInit {

  public planointervencao: any = new PlanoIntervencaoVBMAPP();
  public nivel: any = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();

  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  public profissionaisDoPaciente: Profissional[];
  public niveis: any[] = [];
  public dominios: any[] = [];
  public objetivosMap: Map<string,any> = new Map<string,any>();
  public objetivosESDMMap: Map<string,any> = new Map<string,any>();
  public objetivos: any[];
  
  public vbmappMsAssmtItemView: VBMAPPMilestonesAssessmentItem[] = [];
  public chklstcompView: ESDMChkLstCompetencia[] = [];
  public vbmappmsassmt: VBMAPPMilestonesAssessment;
  public esdmchecklist: ESDMChecklist;
  
  public avaliacoesMarco: VBMAPPMilestonesAssessment[] = [];
  public avaliacoesESDM: ESDMChecklist[] = [];
  public avaliacoesPlano: Avaliacao[] = []; 
  public avaliacoes: Avaliacao[] = []; 
  public avaliacoesFiltered: Avaliacao[] = []; 
  public avaliacao: Avaliacao; 
  public tiposAvaliacoes: TipoAvaliacao[];
  public tipoAvaliacao: TipoAvaliacao;
  public respostasHabilidadesView: RespostaHabilidadeAvaliacao[] = [];
  public habilidadesView: any[] = [];
  public habilidades: any[] = [];
  public domoniosResposta: DominioRespostaHabilidadeAvaliacao[] = [];

  @ViewChild('habilidadesInconsistentes') private habIncChkBox: MatCheckbox;

  public idPaciente: string;
  public sugested:boolean = false;
  public nivelDisabled: boolean = false;
  public dominioDisabled: boolean = false;
  public tipoAvaliacaoDisabled: boolean = false;
  public avaliacaoDisabled: boolean = false;

  public nome = '';
  public tiposAvaliacao: TipoAvaliacao[] = [];
  public habilidade: HabilidadeAvaliacao | null = null;
  public siglaHabilidade = '';
  public tiposSuporte = '';
  public isVbmappOrESDM = '';

  saveDisabled: boolean = false;

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessRead: boolean;
  public hasAccessCreateObjetivo: boolean;

  aba: string;
  selected = new FormControl(0);

  //Form Controls
  data = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);
  ativo = new FormControl(false, [Validators.required]);

  @ViewChild(NgForm) form;

  datasourceObjetivos = new MatTableDataSource();
  datasourceMarcos = new MatTableDataSource();

  public displayedColumns: string[] = ['id', 'nome']

  displayedColumnsMsAssmt = ['id', 'nome', 'status']
  displayedColumnsObjsSelList = ['nomeObjSelList', 'habilidadeObjSelList', 'estimuloObjSelList', 'tipoSuporteObjSelList']
  displayedColumnsObjs = ['nomeObj', 'habilidades', 'estimulos', 'descricao', 'tipoSuporte', 'tipoColeta', 'action']
  displayedColumnsMarcos = ['index', 'expand', 'idNome', 'action']
  todasAvaliacoes: Avaliacao[];
  objetivosESDM: Objetivo[];
  listaDeObjetivosDoPlano: any[] = [];
  tipoAvaliacoesFiltered: TipoAvaliacao[];
  objetivosOrdenados: any;
  listaDeObjetivosDoPlanoOriginal: any;
  planos: any;
  
  constructor(private planointervencaoService: PlanointervencaovbmappService,
    private objetivoService: ObjetivoVBMAPPService,
    private objetivoESDMService: ObjetivoService,
    // private marcoService: MarcovbmappService,
    private vbmappMsAssmtService: VBMAPPMsAssmtService,
    // private nivelService: NivelService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    private nivelService: NivelService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    private habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    private avaliacaoService: AvaliacaoService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private esdmchecklistService: EsdmchecklistService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute) {
    
  }

  public isTableExpanded = false;

  toggleTableRow(row: any){
    //console.log(row)
    //this.datasourceObjetivos.data.find(o => o.id ==row.id)
    row.isExpanded = !row.isExpanded;
  }

  toggleTableRows() {
    this.isTableExpanded = !this.isTableExpanded;

    this.datasourceObjetivos.data.forEach((row: any) => {
      row.isExpanded = this.isTableExpanded;
    })
  }

  async ngOnInit(): Promise<void> {
    try {
      this.saveDisabled = true;
      this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
      this.planointervencao.idPaciente = this.idPaciente;

      let idPlanoIntervencao = this.route.snapshot.paramMap.get('idVbmappPlan');

      this.aba = this.route.snapshot.paramMap.get('tab');
      this.setAba();

      //Carrego os profissionais vinculados ao paciente
      this.pacienteService.findById(this.idPaciente).subscribe(paciente => {

        this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create')
        this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','update')
        this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','delete')
        this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','read')
        this.hasAccessCreateObjetivo = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Objetivo.Cadastro de objetivos (VBMAPP)','create')

        this.profissionaisDoPaciente = paciente.equipe;
      });

      //Carregando Profissionais
      this.profissionalService.find().subscribe(async profissionais => {
        this.profissionais = profissionais;
          //this.profissionalService.findByUserId((this.authService.getUser() as FirebaseUserModel).uid).subscribe(p => {
        if(idPlanoIntervencao == undefined) { //Create
          //Caso seja um profisisonal, seto como o criador
          if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
            if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
              this.planointervencao.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
              this.planointervencao.idProfissional = this.planointervencao.profissional.id;
            }
          }
        }

        // Carregar tipos de avaliações
        const tipos = await this.tipoAvaliacaoService.find().toPromise();
        this.tiposAvaliacoes = tipos
          .filter(ta => ta.ativo == true)
          .sort((a, b) => {
            const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
            const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
            return nomeA.localeCompare(nomeB);
          });

        // Carregar avaliações e avaliações do Marco simultaneamente
        const avaliacoesObservable = this.avaliacaoService.findByPaciente(this.paciente.id);
        const avaliacoesMarcoObservable = this.vbmappMsAssmtService.findByPaciente(this.paciente.id);

        const [avs, avsM] = await forkJoin([avaliacoesObservable, avaliacoesMarcoObservable]).toPromise();

        this.avaliacoesMarco = avsM;
        for (const [, av] of this.avaliacoesMarco.entries()) {
          (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id;
        }

        this.todasAvaliacoes = (avs.concat(this.avaliacoesMarco as unknown as Avaliacao[]));
        this.avaliacoes = (avs.filter(a => a.ativo == true).concat(this.avaliacoesMarco.filter(a => a.status != false) as unknown as Avaliacao[]));
        this.avaliacoesFiltered = this.avaliacoes;

        // Carregar avaliações ESDM
        const esdm = await this.esdmchecklistService.findByPaciente(this.paciente.id).toPromise();

        this.avaliacoesESDM = esdm;
        for (const [, av] of this.avaliacoesESDM.entries()) {
          (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[ESDM] Early Start Denver Model").id;
        }

        this.todasAvaliacoes = (this.todasAvaliacoes.concat(this.avaliacoesESDM as unknown as Avaliacao[]));
        this.avaliacoes = (this.avaliacoesFiltered.concat(this.avaliacoesESDM.filter(a => a.status != false) as unknown as Avaliacao[]));
        this.avaliacoesFiltered = this.avaliacoes;

        // Ordenando as avaliações por data
        this.ordenaAvaliacoes();

        //console.log(idPlanoIntervencao)
        if(idPlanoIntervencao != undefined) { //Update
          //Carrego o plano de intervenção a ser editado
          this.planointervencaoService.findById(idPlanoIntervencao).subscribe(async plano => {
            this.planointervencao = plano;
            // if (this.planointervencao.habilidades){
            //   this.planointervencao.habilidades = this.planointervencao.habilidades.filter(h => h != undefined)
            // } 

            this.listaDeObjetivosDoPlanoOriginal = this.planointervencao.objetivos.map(obj => ({ ...obj }));

            await this.ordenarObjetivos();
            
            this.datasourceObjetivos.data = this.objetivosOrdenados;
            this.listaDeObjetivosDoPlano = [...this.planointervencao.objetivos];
            this.datasourceMarcos.data = this.planointervencao.marcos;

            //Converto os marcos do plano para habilidades
            if(this.planointervencao.marcos != undefined) {
              for(const[,m] of this.planointervencao.marcos.entries()){
                //Inicializo o vetor de habilidades caso esteja vazio
                if(this.planointervencao.habilidades == undefined){
                  this.planointervencao.habilidades = [];
                }
                this.planointervencao.habilidades.push({
                  id: m.id,
                  nome: m.nome,
                  ordem: m.ordem,
                  idNivelAvaliacao: m.id_nivel,
                  nivel: null,
                  idDominioAvaliacao: m.id_dominio,
                  dominio: null,
                  sigla: m.id,
                  idTipoAvaliacao: tipos.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id,
                  dominioResposta:  [
                                      {
                                        id: "1",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Adquirido totalmente",
                                        sigla: "A",
                                        valor: 1
                                      },
                                      {
                                        id: "2",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Não adquirido",
                                        sigla: "N",
                                        valor: 0
                                      },
                                      {
                                        id: "3",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Adquirido parcialmente",
                                        sigla: "P",
                                        valor: 0.5
                                      },
                                      {
                                        id: "4",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Não observado",
                                        sigla: "X",
                                        valor: undefined
                                      }
                                    ]
                })
              }

            }
            // console.log(this.planointervencao.habilidades)
            
            //Carregando as avaliações ligadas ao plano
            //Carrego a avaliação de marco, caso hajam marcos associados ao plano
            if(this.planointervencao.marcos != undefined && this.planointervencao.marcos.length >0){
              this.vbmappMsAssmtService.findLastByPacienteData(this.idPaciente, moment(this.planointervencao.data).format('YYYY-MM-DD')).subscribe(msassmts => {  
                if(msassmts.length > 0){
                  (msassmts.filter(msassmt => msassmt.status != false)[0] as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id;
                  this.avaliacoesPlano.push(msassmts.filter(msassmt => msassmt.status != false)[0] as unknown as Avaliacao);
                  if(this.planointervencao.idsAvaliacoes == undefined){
                    this.planointervencao.idsAvaliacoes = [];
                  }
                  this.planointervencao.idsAvaliacoes.push(msassmts.filter(msassmt => msassmt.status != false)[0].id);
                }
                this.planointervencao.marcos = [];
              })
            }
            
            if (this.planointervencao.idsAvaliacoes != undefined && this.planointervencao.idsAvaliacoes.length > 0) {
              for (const idAv of this.planointervencao.idsAvaliacoes) {
                const avaliacaoEncontrada = this.todasAvaliacoes.find(av => av.id === idAv);
                
                if (avaliacaoEncontrada) {
                  // Adiciona ao plano de avaliações
                  this.avaliacoesPlano.push(avaliacaoEncontrada as Avaliacao);
                } else {
                  console.warn(`Avaliação não encontrada na lista de avaliações do paciente.`);
                }
              }
            }

            await this.ordenarHabilidades();
            //console.log(this.planointervencao.habilidades)

            //Ordenando as avaliacoes por data
            this.avaliacoesPlano.sort(function(a, b) {
              if( a.data > b.data) {
                return 1;
              } else {
                return -1;
              }
            });
            // Se o campo ativo não existir, inicialize como true (ou false, conforme sua regra)
            if (this.planointervencao.ativo === undefined) {
              this.planointervencao.ativo = false;
            }
          })
        } else {
          //Aplicando a data de hoje ao Plano de Intervenção
          this.planointervencao.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
          this.planointervencao.ativo = false;
          this.datasourceObjetivos.data = [];
          this.listaDeObjetivosDoPlano = [];
          this.datasourceMarcos.data = [];
        }

        this.tipoAvaliacoesFiltered = this.tiposAvaliacoes.filter(ta=> ta.id == this.avaliacoes.find(a => a.idTipoAvaliacao == ta.id)?.idTipoAvaliacao)

        //Carregando os Objetivos
        this.objetivoService.find().subscribe(objetivos => {
          this.objetivoESDMService.find().subscribe(objetivosESDM => {
            this.objetivos = objetivos;
            
            for(let objetivo of objetivos){
              if(objetivo.habilidades == undefined){
                objetivo.habilidades = [];
              }
              
              //Converto o marco do objetivo para habilidade, caso ele ainda não esteja registrado como habilidade
              if(objetivo.marco != undefined && objetivo.habilidades.find(h => h.sigla == objetivo.id_marco) == undefined){
                objetivo.habilidades.push({
                  id: objetivo.marco.id,
                  nome: objetivo.marco.nome,
                  ordem: objetivo.marco.ordem,
                  idNivelAvaliacao: objetivo.marco.id_nivel,
                  nivel: null,
                  idDominioAvaliacao: objetivo.marco.id_dominio,
                  dominio: null,
                  sigla: objetivo.marco.id,
                  idTipoAvaliacao: this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id,
                  dominioResposta:  [
                                      {
                                        id: "1",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Adquirido totalmente",
                                        sigla: "A",
                                        valor: 1
                                      },
                                      {
                                        id: "2",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Não adquirido",
                                        sigla: "N",
                                        valor: 0
                                      },
                                      {
                                        id: "3",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Adquirido parcialmente",
                                        sigla: "P",
                                        valor: 0.5
                                      },
                                      {
                                        id: "4",
                                        idHabilidadeAvaliacao: null,
                                        nome: "Não observado",
                                        sigla: "X",
                                        valor: undefined
                                      }
                                    ]
                })
              }

              this.objetivosMap.set(objetivo.id, objetivo);
            }

            for(let objetivoESDM of objetivosESDM){
              objetivoESDM.sigla = objetivoESDM.id;
              objetivoESDM.idTipoAvaliacao = "5";
              this.objetivos.push(objetivoESDM)
              this.objetivosMap.set(objetivoESDM.id, objetivoESDM);
            }
            this.objetivos = this.objetivos.filter(obj => this.planointervencao.objetivos?.find(o => o.id == obj.id) == undefined);
          })
        })
      })
      
      // Seto os objetivos em conexão com o filter
      this.objetivoService.objetivos.subscribe((data) => {
        this.objetivos = data;
        this.objetivoESDMService.find().subscribe(objetivos => {
          for(let objetivo of objetivos){
            objetivo.sigla = objetivo.id;
            objetivo.idTipoAvaliacao = "5";
            this.objetivos.push(objetivo)
            this.objetivosMap.set(objetivo.id, objetivo);
          }
        })
        this.objetivos = this.objetivos.filter(obj => this.listaDeObjetivosDoPlano?.find(o => o.id == obj.id) == undefined);
      })

      this.objetivoESDMService.find().subscribe((data) => {
        this.objetivosESDM = data;
      })

      //Carregando Pacientes
      this.pacienteService.findById(this.idPaciente).subscribe(paciente => {
        this.paciente = paciente;
        this.planointervencao.paciente = paciente;
        this.planointervencao.idPaciente = this.idPaciente;
      })
      this.saveDisabled = false;
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  ordenaAvaliacoes(){
    //Ordenando as avaliacoes por data
    this.avaliacoes.sort(function(a, b) {
      if( a.data < b.data) {
        return 1;
      } else {
        return -1;
      }
    });

  }
  
  async ordenarHabilidades() {
    if (!this.planointervencao?.habilidades?.length) return;
  
    // Preenche o nomeTipoAvaliacao se não estiver preenchido
    this.planointervencao.habilidades.forEach(hab => {
      if (!hab.nomeTipoAvaliacao && hab.idTipoAvaliacao) {
        hab.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == hab.idTipoAvaliacao)?.nome;
      }
    });

    const parseNivel = (nivelId: string) => parseInt(nivelId?.replace(/\D/g, ''), 10) || Infinity;
    const parseSigla = (sigla: string) => parseInt(sigla?.substr(4), 10) || Infinity;
  
    this.planointervencao.habilidades.sort((a, b) => {
      const getNomeTipoAvaliacao = (hab) =>
        hab.nomeTipoAvaliacao?.replace(/\[.*?\]\s*/, '') || '';
  
      let nomeA = getNomeTipoAvaliacao(a);
      let nomeB = getNomeTipoAvaliacao(b);
  
      // Corrige nome do ESDM, se necessário
      if (a.idTipoAvaliacao == "5") nomeA = "Early Start Denver Model";
      if (b.idTipoAvaliacao == "5") nomeB = "Early Start Denver Model";
  
      const compareTipoAvaliacao = nomeA.localeCompare(nomeB);
      if (compareTipoAvaliacao != 0) return compareTipoAvaliacao;
  
      // Se forem do tipo ESDM, segue regras específicas
      if (a.idTipoAvaliacao == "5" || b.idTipoAvaliacao == "5") {
        const dominioOrdemA = a.dominio?.ordem ?? Infinity;
        const dominioOrdemB = b.dominio?.ordem ?? Infinity;
        if (dominioOrdemA != dominioOrdemB) return dominioOrdemA - dominioOrdemB;
  
        const nivelA = parseNivel(a.nivel?.id);
        const nivelB = parseNivel(b.nivel?.id);
        if (nivelA !== nivelB) return nivelA - nivelB;

        const ordemSiglaA = parseSigla(a.sigla);
        const ordemSiglaB = parseSigla(b.sigla);
        return ordemSiglaA - ordemSiglaB;
      }
  
      // Para outros tipos de avaliação: domínio > nível > id
      const idDominioA = a.idDominioAvaliacao ?? Infinity;
      const idDominioB = b.idDominioAvaliacao ?? Infinity;
  
      if (idDominioA != idDominioB) return idDominioA - idDominioB;
  
      const idNivelA = a.idNivelAvaliacao ?? Infinity;
      const idNivelB = b.idNivelAvaliacao ?? Infinity;
  
      if (idNivelA != idNivelB) return idNivelA - idNivelB;
  
      const idA = parseInt(a.id, 10) || Infinity;
      const idB = parseInt(b.id, 10) || Infinity;
  
      return idA - idB;
    });
  
    // Atualiza referência para Angular detectar
    this.planointervencao.habilidades = [...this.planointervencao.habilidades];
  }  

  async ordenarObjetivos() {
    this.objetivosOrdenados = this.planointervencao.objetivos;
    
    this.objetivosOrdenados?.forEach(obj => {
      if (!obj?.habilidades?.length) return;
      obj.habilidades[0].nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == obj.habilidades[0].idTipoAvaliacao)?.nome;
    });
    
    this.objetivosOrdenados.sort((a, b) => {
      const getNomeTipoAvaliacao = (obj) => obj.habilidades?.length ? obj.habilidades[0].nomeTipoAvaliacao.replace(/\[.*?\]\s*/, '') || "" : "";
      const getTipoId = (obj) => obj?.habilidades ? obj?.habilidades[0]?.idTipoAvaliacao == "5" : false;
    
      let nomeTipoAvaliacaoA = getNomeTipoAvaliacao(a);
      let nomeTipoAvaliacaoB = getNomeTipoAvaliacao(b);

      const tipoIdAESDM = getTipoId(a);
      const tipoIdBESDM = getTipoId(b);
    
      // Ajusta nome do ESDM para garantir ordenação correta sem empurrá-lo para o final
      if (a.idTipoAvaliacao == "5") {
        nomeTipoAvaliacaoA = "Early Start Denver Model";
      }
      if (b.idTipoAvaliacao == "5") {
        nomeTipoAvaliacaoB = "Early Start Denver Model";
      }
    
      // Ordena pelo nome do tipo de avaliação (alfabética)
      const compareTipoAvaliacao = nomeTipoAvaliacaoA.localeCompare(nomeTipoAvaliacaoB);
      if (compareTipoAvaliacao !== 0) return compareTipoAvaliacao;

      // Ordenação para objetivos ESDM
      if (a.idTipoAvaliacao == "5" || b.idTipoAvaliacao == "5" || tipoIdAESDM || tipoIdBESDM) {
        const dominioA = a.dominio?.ordem || a.habilidades[0]?.dominio?.ordem || Infinity;
        const dominioB = b.dominio?.ordem || b.habilidades[0]?.dominio?.ordem || Infinity;
        
        if (dominioA !== dominioB) {
          return dominioA - dominioB; // Ordena por domínio
        }
  
        const nivelA = parseInt(a.nivel?.id.replace(/\D/g, ''), 10) || parseInt(a.habilidades[0]?.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        const nivelB = parseInt(b.nivel?.id.replace(/\D/g, ''), 10) || parseInt(b.habilidades[0]?.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        
        if (nivelA !== nivelB) {
          return nivelA - nivelB; // Ordena por nível
        }
        
        let idA: number;
        if (!tipoIdAESDM) {
          idA = parseInt(a.id.replace(/\D/g, ''), 10) || parseInt(a.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        } else {
          idA = parseInt(a.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        }

        let idB: number;
        if (!tipoIdBESDM) {
          idB = parseInt(b.id.replace(/\D/g, ''), 10) || parseInt(b.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        } else {
          idB = parseInt(b.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        }
  
        return idA - idB; // Ordena pelo número final do ID
      }
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID do dominio da primeira habilidade (numérico)
      const idDominioA = a.habilidades?.length ? a.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      const idDominioB = b.habilidades?.length ? b.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      
      if (idDominioA !== idDominioB) return idDominioA - idDominioB;
      
      // Se os tipos de avaliação forem iguais, e o dominio for igual, ordena pelo ID do nivel da primeira habilidade (numérico)
      const idNivelA = a.habilidades?.length ? a.habilidades[0].idNivelAvaliacao || Infinity : Infinity;
      const idNivelB = b.habilidades?.length ? b.habilidades[0].idNivelAvaliacao || Infinity : Infinity;

      if (idNivelA !== idNivelB) return idNivelA - idNivelB;
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID da primeira habilidade (numérico)
      const idHabilidadeA = a.habilidades?.length ? parseInt(a.habilidades[0].id, 10) || Infinity : Infinity;
      const idHabilidadeB = b.habilidades?.length ? parseInt(b.habilidades[0].id, 10) || Infinity : Infinity;
    
      return idHabilidadeA - idHabilidadeB;
    });
  }

  setAba(){
   
    if(this.aba=="marcos"){
      this.selected.setValue(0);
    }

    if(this.aba=="objetivos"){
      this.selected.setValue(1);
    }   
    
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

   // console.log(funcoes)


    return funcoes;
  }

  msIsSelected(marco: MarcoVBMAPP): boolean{
    if(this.planointervencao.habilidades == undefined || this.planointervencao.habilidades.find(m => m.sigla == marco.id) == undefined){
      return false;
    } else {
      return true;
    }
  }

  habilidadeIsSelected(habilidade: HabilidadeAvaliacao): boolean{
    if(this.planointervencao.habilidades == undefined || this.planointervencao.habilidades.find(m => m.sigla == habilidade.sigla) == undefined){
      return false;
    } else {
      return true;
    }
  }

  async sugestedMilestones(event: MatCheckboxChange){
    
    // console.log(event)
    if(event.checked){ //Apenas observados sem consistência
      if(this.isAvaliacaoMarcoOrESDM()){
        if (this.isVbmappOrESDM == "VBMAPP") {
          this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(msassmt => 
            msassmt.valor == "N" || msassmt.valor == "P")
          
          //Ordenando o array de marcos visível
          this.vbmappMsAssmtItemView.sort(function (a, b) {
            if( ( (a.marco.dominio.ordem.toString().length == 2 ? "0" + a.marco.dominio.ordem : a.marco.dominio.ordem.toString()) + 
                      (a.marco.ordem.toString().length == 1 ? "0" + a.marco.ordem : a.marco.ordem.toString()) ) 
                  > ( (b.marco.dominio.ordem.toString().length == 2 ? "0" + b.marco.dominio.ordem : b.marco.dominio.ordem.toString()) + 
                      (b.marco.ordem.toString().length == 1 ? "0" + b.marco.ordem : b.marco.ordem.toString()) ) ) {
                    return 1;
            }
            if( ( (a.marco.dominio.ordem.toString().length == 2 ? "0" + a.marco.dominio.ordem : a.marco.dominio.ordem.toString()) + 
                      (a.marco.ordem.toString().length == 1 ? "0" + a.marco.ordem : a.marco.ordem.toString()) ) 
                  < ( (b.marco.dominio.ordem.toString().length == 2 ? "0" + b.marco.dominio.ordem : b.marco.dominio.ordem.toString()) + 
                      (b.marco.ordem.toString().length == 1 ? "0" + b.marco.ordem : b.marco.ordem.toString()) ) ) {
                    return -1;
            }
            return 0;
          })
        } else {
          this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
            chklst.valor == "N" || chklst.valor == "P")

          //Ordenando o array de objetivos
          this.chklstcompView.sort(function (a, b) {
            // console.log( ( (a.competencia.dominio.ordem.toString().length == 2 ? "0" + a.competencia.dominio.ordem : a.competencia.dominio.ordem) + a.competencia.id_dominio + a.competencia.id_nivel + a.competencia.id.substr(4,2) ) )
            if( ( (a.competencia.dominio.ordem.toString().length == 2 ? "0" + a.competencia.dominio.ordem : a.competencia.dominio.ordem) + a.competencia.id_dominio + a.competencia.id_nivel + a.competencia.id.substr(4,2) ) 
                  > ( (b.competencia.dominio.ordem.toString().length == 2 ? "0" + b.competencia.dominio.ordem : b.competencia.dominio.ordem) + b.competencia.id_dominio + b.competencia.id_nivel + b.competencia.id.substr(4,2) ) ) {
                    return 1;
            }
            if( ( (a.competencia.dominio.ordem.toString().length == 2 ? "0" + a.competencia.dominio.ordem : a.competencia.dominio.ordem) + a.competencia.id_dominio + a.competencia.id_nivel + a.competencia.id.substr(4,2) ) 
                  < ( (b.competencia.dominio.ordem.toString().length == 2 ? "0" + b.competencia.dominio.ordem : b.competencia.dominio.ordem) + b.competencia.id_dominio + b.competencia.id_nivel + b.competencia.id.substr(4,2) ) ) {
                    return -1;
            }
            return 0;
          })
        }
      } else {
        this.habilidadesView = this.habilidades.filter(habilidade => 
          this.getRespostaHabilidade(habilidade.id).valor != undefined
          && parseInt(this.getRespostaHabilidade(habilidade.id).valor) < 1
        )
      }
      

      this.nivelDisabled=true;
      this.dominioDisabled=true; 
    } else { //Todos
      await this.filterHabilidades();
      this.nivelDisabled=false;
      this.dominioDisabled=false;
    }
  }

  async addAvaliacao(){
    try {
      //Verifico se a avaliação já está no plano
      if(this.planointervencao.idsAvaliacoes?.find(id => id == this.avaliacao.id) == undefined) {
        try {
          //Se não estiver, a adiciono no plano
          if (this.planointervencao.idsAvaliacoes == undefined){
            this.planointervencao.idsAvaliacoes = []
          }
          this.planointervencao.idsAvaliacoes.push(this.avaliacao.id);
          this.avaliacaoService.findById(this.avaliacao.id).subscribe(av => {
            if(av == undefined){
              //Se não encontrei nas avaliações, busco nas avaliações de marco
              this.vbmappMsAssmtService.findById(this.avaliacao.id).subscribe(avMarco => {
                (avMarco as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id;
                  this.avaliacoesPlano.push(avMarco as unknown as Avaliacao);
                  this.avaliacoesPlano.sort(function(a, b) {
                    if( a.data > b.data) {
                      return 1;
                    } else {
                      return -1;
                    }
                  });
              });
            } else {
              this.avaliacoesPlano.push(av);
              this.avaliacoesPlano.sort(function(a, b) {
                if( a.data > b.data) {
                  return 1;
                } else {
                  return -1;
                }
              });
            }
          })
        } catch (error) {
          console.error("Erro:", error);
        } finally {
          // console.log("addAvaliacao 1")
          await this.save();
          // console.log("addAvaliacao 2")
        }
      }
      // console.log("addAvaliacao 3")
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  async addAvaliacaoESDM(){
    try {
      //Verifico se a avaliação já está no plano
      if(this.planointervencao.idsAvaliacoes?.find(id => id == this.avaliacao.id) == undefined) {
        //Se não estiver, a adiciono no plano
        if (this.planointervencao.idsAvaliacoes == undefined){
          this.planointervencao.idsAvaliacoes = []
        }
        this.planointervencao.idsAvaliacoes.push(this.avaliacao.id);
        this.esdmchecklistService.findById(this.avaliacao.id).subscribe(avESDM => {
          (avESDM as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[ESDM] Early Start Denver Model").id;
          this.avaliacoesPlano.push(avESDM as unknown as Avaliacao);
        })
      }
    } catch (error) {
      console.error("Erro:", error);
    } 
  }

  findHabilidade(idHabilidade: any){
    return this.habilidades.find(h => h.sigla == idHabilidade);
  }

  getNomeTipoAvaliacao(h: any){
    // console.log(idTipoAvaliacao)
    if(h == undefined){
      return '';
    } else {
      return this.tiposAvaliacoes?.find(ta => ta.id == h.idTipoAvaliacao)?.nome
    }
  }

  getNomeTipoDominio(d: any, n: any, id: any){
    if(d == undefined){
      return '';
    } else {
      return "[ESDM]" + " - " + n.id + " - " + d.nome + " - " + id.substr(4)
    }
  }
  
  getNameHabilidade(hab: any) {
    if (!hab.idTipoAvaliacao || hab.idTipoAvaliacao == "5"){  
      return '[ESDM] - ' + hab.id_nivel + ' - ' + hab.dominio.nome + ' - ' + hab.id.substr(4)
    } else { 
      return '' + this.tiposAvaliacoes?.find(ta => ta.id == hab.idTipoAvaliacao)?.nome + ' - ' + hab.sigla
    }
  }

  async deleteAvaliacao(avaliacao: Avaliacao){
    if(this.validForm()){
      var id = this.planointervencao.idsAvaliacoes.indexOf(avaliacao.id);
      this.planointervencao.idsAvaliacoes.splice(id, 1);
      this.planointervencao.idsAvaliacoes = [...this.planointervencao.idsAvaliacoes];

      let i = this.avaliacoesPlano.indexOf(avaliacao);
      this.avaliacoesPlano.splice(i, 1);
      this.avaliacoesPlano = [...this.avaliacoesPlano];

      await this.save();

    } else {
      this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  async addHabilidade(siglaHabilidade: any){
    try {
      let habilidadeHaveObj = false;
      let objetivosHabilidade = [];
      if(this.validForm()){
        
        //Verifico se o array de habilidades está vazio. Caso esteja, inicializo.
        if(this.planointervencao.habilidades == undefined){
          this.planointervencao.habilidades = [];
        }
  
        let habilidade = this.findHabilidade(siglaHabilidade)
  
        //Verifico se a habilidade já foi adicionado anteriormente
        if( this.planointervencao.habilidades.find(h => h.sigla == siglaHabilidade) == undefined) {
          if(this.findHabilidade(siglaHabilidade) != undefined && this.findHabilidade(siglaHabilidade).sigla.trim() != ''){
            habilidade.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == habilidade.idTipoAvaliacao)?.nome
            this.planointervencao.habilidades.push(habilidade)
          } else {
            return
          }
  
          //Ordenando o array de habilidades
          await this.ordenarHabilidades();
          // console.log("AddHabilidade 1")
          await this.save();
          // console.log("AddHabilidade 2")
          try {
            // console.log("AddHabilidade 3")
            await this.addAvaliacao();
            // console.log("AddHabilidade 4")
            //Verifica e adiciona os objetivos ligados ao marco selecionado
            this.objetivos.filter(async objetivo => {
              if(objetivo.habilidades?.find(h => h.sigla == siglaHabilidade)){
                habilidadeHaveObj = true;
                objetivosHabilidade.push(objetivo);
              }
            });

            if (!habilidadeHaveObj){
              const dialogRef = this.dialog.open(ConfirmDialogComponent, {
                width: 'auto',
                height: 'auto',
                data: {
                  valida: true,
                  msg: 'Não existe nenhum objetivo associado a essa habilidade! Gostaria de criar um objetivo para essa habilidade?'
                } 
              });
          
              dialogRef.afterClosed().subscribe(result => {
                // console.log("addHabilidade 1")
                if(result){
                  // console.log("addHabilidade 2")
                  localStorage.removeItem("vbmapp_objetivo");
                  this.dialog.open(ObjetivovbmappCreateComponent, {
                    data:{
                      idPaciente: this.idPaciente,
                      idVbmappPlan: this.planointervencao.id,
                      idHabilidade: this.findHabilidade(siglaHabilidade).id
                    }
                  }).afterClosed().subscribe(async () => {
                    if(localStorage.getItem("vbmapp_objetivo")){
                      if(!this.planointervencao.objetivos){
                        this.planointervencao.objetivos = [];
                      }
                      let obj = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
                      this.planointervencao.objetivos.push(obj);
                      this.datasourceObjetivos.data = this.planointervencao.objetivos;
                      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
                      await this.ordenarObjetivos();
                      await this.save();
                    }
                  });    
                }
                // console.log("addHabilidade 3")
              });
            }
          } catch (error) {
            console.error("Erro:", error);
          }
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  findIndexHabilidade(idHabilidade: number){
    return this.avaliacao.respostasHabilidades.findIndex( resp => parseInt(resp.idHabilidadeAvaliacao) == idHabilidade);
  }

  getDominioResposta(valor: string){
    let dr = this.domoniosResposta.find(d => "" + d.valor == valor);

    if (dr == undefined){
      dr = this.domoniosResposta.find(d => d.valor == undefined);
    }
    return dr;
  }

  getRespostaHabilidade(idHabilidade: any){
    const index = this.findIndexHabilidade(idHabilidade);
    if(this.avaliacao.respostasHabilidades[index] == undefined){
      this.avaliacao.respostasHabilidades[index] = {
        idHabilidadeAvaliacao: idHabilidade,
        valor: ""
      }
    }
    if (this.avaliacao.respostasHabilidades[index]?.valor == undefined){
      // //console.log("Resposta indefinida")
      this.avaliacao.respostasHabilidades[index].valor = "";
      return this.avaliacao.respostasHabilidades[index]
    } else {
      return this.avaliacao.respostasHabilidades[index]
    }
  }

  getClassRespostaHabilidade(idHabilidade: any){
    const habilidadeIndex = this.habilidadesView?.findIndex(habilidade => habilidade.id == idHabilidade);
    const habilidade = (this.habilidadesView[habilidadeIndex] as any);
    const maxValor = habilidade.dominioResposta
      .map(dom => parseFloat(dom.valor))
      .filter(v => !isNaN(v))
      .sort((a, b) => b - a)[0];

    const valorResposta = this.getRespostaHabilidade(idHabilidade)?.valor;
    
    // console.log(this.getRespostaHabilidade(idHabilidade))
    if (this.tipoAvaliacao.id == '6' ) {
      // Avalição Socially Savvy
      switch (valorResposta){
        case '0':
          return "label label-N";
        case '1':
          return "label label-P";
        case '2':
          return "label label-P3";
        case '3':
          return "label label-A";
        default:
          return "label label-X";
      }
    } else if (this.tipoAvaliacao.id == "2" || "7") {
      // Avaliação ABLLS-R e AFLS
      if (maxValor == 2) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-P";
          case '2':
            return "label label-A";
          default:
            return "label label-X";
        }
      } else if (maxValor == 3) {
        switch (valorResposta){
          case '0':
            return "label label-ABLLS-N";
          case '1':
            return "label label-ABLLS-P";
          case '2':
            return "label label-ABLLS-P3";
          case '3':
            return "label label-ABLLS-A";
          default:
            return "label label-ABLLS-X";
        }
      } else if (maxValor == 4) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-P1";
          case '2':
            return "label label-P";
          case '3':
            return "label label-P3";
          case '4':
            return "label label-A";
          default:
            return "label label-X";
        }
      } else if (maxValor == 1) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-A";
          default:
            return "label label-X";
        }
      }
    } else {
      switch (valorResposta){
        case '0':
          return "label label-N";
        case '0.5':
          return "label label-P";
        case '1':
          return "label label-A";
        default:
          return "label label-X";
      }
    }
  }

  async addMarco(idHabilidade: any){
    try {
      let habilidadeHaveObj = false;
      if(this.validForm()){
        
        //Verifico se o array de habilidades está vazio. Caso esteja, inicializo.
        if(this.planointervencao.habilidades == undefined){
          this.planointervencao.habilidades = [];
        }
  
        //Verifico se a habilidade já foi adicionado anteriormente
        if( this.planointervencao.habilidades.find(marco => marco.sigla == idHabilidade) == undefined) {
          if(this.findHabilidade(idHabilidade) != undefined 
            && this.findHabilidade(idHabilidade).sigla.trim() != ''){
            this.planointervencao.habilidades.push(this.findHabilidade(idHabilidade))
          } else {
            return
          }
  
          //Ordenando o array de objetivos
          await this.ordenarHabilidades();
          // console.log("AddMarco 1")
          await this.save();
          // console.log("AddMarco 2")
          // .then(async () => {
          await this.addAvaliacao();
          // console.log("AddMarco 3")
          //Verifica e adiciona os objetivos ligados ao marco selecionado
          await this.objetivos.filter(async objetivo => {
            if(objetivo.habilidades?.find(h => h.sigla == idHabilidade) || objetivo.id_marco == idHabilidade){
              habilidadeHaveObj = true;
              await this.addObjetivo(objetivo.id, idHabilidade)
            }
          })
          // console.log("AddMarco 4")
          if (!habilidadeHaveObj){
            const dialogRef = this.dialog.open(ConfirmDialogComponent, {
              width: 'auto',
              height: 'auto',
              data: {
                valida: true,
                msg: 'Não existe nenhum objetivo associado a essa habilidade! Gostaria de criar um objetivo para essa habilidade?'
              }
            });
        
            // dialogRef.afterClosed().subscribe(result => {
            //   if(result){
            //     this.router.navigate(['/vbmapp_objetivo/create', {
            //       idPaciente: this.idPaciente,
            //       idVbmappPlan: this.planointervencao.id,
            //       idHabilidade: this.findHabilidade(idHabilidade).id
            //     }]);
            //   }
            // });

            dialogRef.afterClosed().subscribe(result => {
                // console.log("addMarco 1")
                if(result){
                  // console.log("addMarco 2")
                  localStorage.removeItem("vbmapp_objetivo");
                  this.dialog.open(ObjetivovbmappCreateComponent, {
                    data:{
                      idPaciente: this.idPaciente,
                      idVbmappPlan: this.planointervencao.id,
                      idHabilidade: this.findHabilidade(idHabilidade).id
                    }
                  }).afterClosed().subscribe(async () => {
                    if(localStorage.getItem("vbmapp_objetivo")){
                      if(!this.planointervencao.objetivos){
                        this.planointervencao.objetivos = [];
                      }
                      let obj = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
                      this.planointervencao.objetivos.push(obj);
                      this.datasourceObjetivos.data = this.planointervencao.objetivos;
                      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
                      await this.ordenarObjetivos();
                      await this.save();
                    }
                  });    
                }
                // console.log("addMarco 3")
              });
            // }








          }
        } else {
          this.planointervencaoService.showMessage('Essa habilidade ja foi adicionada!',true);
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  async addCompetencia(idHabilidade: any){
    try {
      let habilidadeHaveObj = false;
      if(this.validForm()){
        //Verifico se o array de habilidades está vazio. Caso esteja, inicializo.
        if(this.planointervencao.habilidades == undefined){
          this.planointervencao.habilidades = [];
        }
  
        //Verifico se a habilidade já foi adicionado anteriormente
        if( this.planointervencao.habilidades.find(comp => comp.sigla == idHabilidade) == undefined) {
          if(this.findHabilidade(idHabilidade) != undefined 
            && this.findHabilidade(idHabilidade).sigla.trim() != ''){
            this.planointervencao.habilidades.push(this.findHabilidade(idHabilidade))
          } else {
            return
          }
  
          //Ordenando o array de objetivos
          await this.ordenarHabilidades();
          
          //console.log("addCompetencia 1")
          await this.save();
          //console.log("addCompetencia 2")

          try {
            await this.addAvaliacaoESDM();
            //console.log("addCompetencia 3")
            //Verifica e adiciona os objetivos ligados ao marco selecionado
            this.objetivosESDM.filter(async objetivo => {
              if(objetivo.id == idHabilidade){
                habilidadeHaveObj = true;
                await this.addObjetivoESDM(objetivo.id, idHabilidade)  
              }
            })
  
            this.objetivos.filter(async objetivo => {
              if(objetivo.habilidades?.find(h => h.id == idHabilidade)){
                habilidadeHaveObj = true;
                await this.addObjetivo(objetivo.id, idHabilidade)
              }
            });

            if (!habilidadeHaveObj){
              const dialogRef = this.dialog.open(ConfirmDialogComponent, {
                width: 'auto',
                height: 'auto',
                data: {
                  valida: true,
                  msg: 'Não existe nenhum objetivo associado a essa habilidade! Gostaria de criar um objetivo para essa habilidade?'
                } 
              });
          
              dialogRef.afterClosed().subscribe(result => {
                // console.log("addHabilidade 1")
                if(result){
                  // console.log("addHabilidade 2")
                  localStorage.removeItem("vbmapp_objetivo");
                  this.dialog.open(ObjetivovbmappCreateComponent, {
                    data:{
                      idPaciente: this.idPaciente,
                      idVbmappPlan: this.planointervencao.id,
                      idHabilidade: this.findHabilidade(idHabilidade).id,
                      tipo: 'ESDM'
                    }
                  }).afterClosed().subscribe(async () => {
                    if(localStorage.getItem("vbmapp_objetivo")){
                      if(!this.planointervencao.objetivos){
                        this.planointervencao.objetivos = [];
                      }
                      let obj = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
                      this.planointervencao.objetivos.push(obj);
                      this.datasourceObjetivos.data = this.planointervencao.objetivos;
                      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
                      await this.ordenarObjetivos();
                      await this.save();
                    }
                  });    
                }
                // console.log("addHabilidade 3")
              });
            }
          } catch (error) {
            console.error("Erro:", error);
          }
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  async deleteHabilidade(habilidade: HabilidadeAvaliacao){
    let objs;

    if(this.validForm()){
      var id = this.planointervencao.habilidades.indexOf(habilidade);
      this.planointervencao.habilidades.splice(id, 1);
      this.planointervencao.habilidades = [...this.planointervencao.habilidades];
      // this.datasourceMarcos.data = this.planointervencao.marcos;
      // this.datasourceMarcos.data = [...this.datasourceMarcos.data];

      //console.log("deleteHabilidade 1")
      await this.save();
      //console.log("deleteHabilidade 2")
      //Verifica e remove os objetivos associados
      for(let i=0; i < this.planointervencao.objetivos?.length; i++){
        //console.log("deleteHabilidade 3." + i)
        let findhabilidade = this.planointervencao.objetivos[i].habilidades?.find(h => h.sigla == habilidade.sigla) != undefined
        let findHabilidadeESDM = this.planointervencao.objetivos[i].habilidades?.find(h => h.id == habilidade.sigla) != undefined
        if(findhabilidade || findHabilidadeESDM ){
          //Se o objetivo tiver mais alguma outra habilidade do plano que não tenha sido excluída, mantenho-o
          if(this.planointervencao.objetivos[i].habilidades.filter(ho => this.planointervencao.habilidades.filter(hp => hp.sigla == ho.sigla).length > 0).length == 0){
            this.deleteObjetivo(this.planointervencao.objetivos[i]);
            i--;
          }
        } else if (this.planointervencao.objetivos[i].sigla == habilidade.sigla){
          this.deleteObjetivo(this.planointervencao.objetivos[i]);
          i--;
        }
      }
    } else {
      this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  objIsSelected(objetivo: ObjetivoVBMAPP): boolean{
    if(this.planointervencao.objetivos == undefined){
      return false;
    }
    if(this.planointervencao.objetivos.find(o => o.id == objetivo.id) == undefined){
      return false;
    } else {
      return true;
    }
  }
  
  async addObjetivo(idObjetivo: any, siglaHabilidade?: any){
    try {
      let obj: ObjetivoVBMAPP;
  
      if (this.validForm()) {
        // Verifico se o array de objetivos está vazio. Caso esteja, inicializo.
        if (this.planointervencao.objetivos == undefined) {
          this.planointervencao.objetivos = [];
        }
  
        //Verifico se o objetivo já foi adicionado anteriormente
        if (this.planointervencao.objetivos.find(obj => obj.id == idObjetivo) == undefined) {
          obj = this.objetivosMap.get(idObjetivo);
  
          // Se o tipo de coleta for Naturalista, incluo os Estímulos dentro de cada Tipo de Suporte
          // para conseguir gerenciar as etapas de cada objetivo.
          // Etapa Naturalista: 
          // Objetivo
          //  Tipo de Suporte
          //    Estímulo
          if (obj.tipoColeta == "Naturalista") {
            for (const [, tipo] of obj.tiposSuporte.entries()) {
              tipo.estimulos = [];
              for (const [, estimulo] of obj.estimulos.entries()) {
                tipo.estimulos.push(estimulo);
              }
            }
          }
          this.planointervencao.objetivos.push(this.objetivosMap.get(idObjetivo));

          //Ordenando o array de objetivos
          await this.ordenarObjetivos();
    
          this.planointervencao.objetivos = [...this.planointervencao.objetivos];
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
  
          // Certifique-se de que a lista atualizada será enviada ao filtro
          this.listaDeObjetivosDoPlano.push(obj);
          this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano]; // Atualiza a referência
          
          try {
            this.objetivos = [...this.objetivos.filter(obj => this.listaDeObjetivosDoPlano?.find(o => o.id == obj.id) == undefined)]
            //console.log("addObjetivo 1")
            
            await this.save();
            //console.log("addObjetivo 2")
          } catch (e) {
            console.error("Erro ao salvar:", e);
          }
        }
        //console.log("addObjetivo 3")
         //console.log("Hide addObjetivo");
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!', true);
      }
    } catch (e) {
      console.error(e);
      
    }
  }

  async addObjetivoESDM(idObjetivo: any, siglaHabilidade?: any){
    try {
      let obj: Objetivo;
      // console.log(this.objetivosMap)
      if (this.validForm()) {
        // Verifico se o array de objetivos está vazio. Caso esteja, inicializo.
        if (this.planointervencao.objetivos == undefined) {
          this.planointervencao.objetivos = [];
        }
  
        //Verifico se o objetivo já foi adicionado anteriormente
        if (this.planointervencao.objetivos.find(obj => obj.id == idObjetivo) == undefined) {
          obj = this.objetivosMap.get(idObjetivo);
  
          this.planointervencao.objetivos.push(this.objetivosMap.get(idObjetivo));
  
          //Ordenando o array de objetivos
          await this.ordenarObjetivos();
    
          this.planointervencao.objetivos = [...this.planointervencao.objetivos];
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
  
          // Certifique-se de que a lista atualizada será enviada ao filtro
          this.listaDeObjetivosDoPlano.push(obj);
          this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano]; // Atualiza a referência
    
          try {
            await this.save();
            
          } catch (e) {
            console.error("Erro ao salvar:", e);
          }
        } else {
          this.planointervencaoService.showMessage('Objetivo já adicionado!', true);
          
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!', true);
      }
    } catch (e) {
      console.error(e);
    } 
  }

  async deleteObjetivo(objetivo: ObjetivoVBMAPP){
    if(this.validForm()){
      var id = this.planointervencao.objetivos.indexOf(objetivo);
      this.planointervencao.objetivos.splice(id, 1);
      this.planointervencao.objetivos = [...this.planointervencao.objetivos];
      this.datasourceObjetivos.data = this.planointervencao.objetivos;
      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
      
      var index = this.listaDeObjetivosDoPlano.indexOf(objetivo);
      this.listaDeObjetivosDoPlano.splice(index, 1);
      this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano];

      //Verifica e remove as habilidades associadas ao objetivo
      if(objetivo.habilidades != undefined){
        for(const[,h] of objetivo.habilidades?.entries()){ //Para cada habilidade do objetivo
          if(this.planointervencao.objetivos.filter(obj => 
            obj.habilidades?.filter(hab => hab.sigla == h.sigla).length > 0 //Verifico se ela está associada a mais de um objetivo
  
            // if (obj.habilidades?.filter(hab => hab.sigla == h.sigla).length > 0){ 
              // return true;
            // }
          ).length == 0 ){ //Se a habilidade estiver associada apenas ao objetivo excluído, excluo a habilidade
            if(this.planointervencao.habilidades == undefined){
              id = -1;
            } else {
              id = this.planointervencao.habilidades.findIndex(hb => hb.sigla == h.sigla);
            }
            if(id != -1){
              this.planointervencao.habilidades.splice(id, 1);
              this.planointervencao.habilidades = [...this.planointervencao.habilidades];
            }
          }
        }
      }

      try {
        this.objetivos.push(objetivo);
        this.objetivos = [...this.objetivos];
        await this.save();
      } catch (e) {
        console.error("Erro ao salvar:", e);
      }

    } else {
      this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  async deleteObjetivoESDM(objetivo: any){
    if(this.validForm()){
      var id = this.planointervencao.objetivos.indexOf(objetivo);
      this.planointervencao.objetivos.splice(id, 1);
      this.planointervencao.objetivos = [...this.planointervencao.objetivos];
      this.datasourceObjetivos.data = this.planointervencao.objetivos;
      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];

      var index = this.listaDeObjetivosDoPlano.indexOf(objetivo);
      this.listaDeObjetivosDoPlano.splice(index, 1);
      this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano];

      //Verifica e remove as habilidades associadas ao objetivo
      if(this.planointervencao.objetivos.filter(obj => 
        obj.habilidades?.filter(hab => hab.sigla == objetivo.sigla).length > 0 //Verifico se ela está associada a mais de um objetivo
      ).length == 0 ){ //Se a habilidade estiver associada apenas ao objetivo excluído, excluo a habilidade
        if(this.planointervencao.habilidades == undefined){
          id = -1;
        } else {
          id = this.planointervencao.habilidades.findIndex(hb => hb.sigla == objetivo.sigla);
        }
        if(id != -1){
          this.planointervencao.habilidades.splice(id, 1);
          this.planointervencao.habilidades = [...this.planointervencao.habilidades];
        }
      }

      await this.save();
      
    } else {
      this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  editObjetivo(objetivo: ObjetivoVBMAPP){
    // console.log(objetivo);
    localStorage.removeItem("vbmapp_objetivo");
    if (!objetivo.id) {
      this.openDialogVBMAPP(this.idPaciente, this.planointervencao.id, this.listaDeObjetivosDoPlanoOriginal.findIndex(obj => obj.nome == objetivo.nome), objetivo);
    } else {
      this.openDialogVBMAPP(this.idPaciente, this.planointervencao.id, objetivo.id, objetivo);
    }
  }

  openDialogVBMAPP(idPaciente: string, idVbmappPlan: string, idObjetivo: string, objetivo?: ObjetivoVBMAPP) {
    this.dialog.open(ObjetivovbmappCreateComponent, {
    data:{
      idPaciente,
      idVbmappPlan,
      idObjetivo
    }
    }).afterClosed().subscribe(async () => {
      if(localStorage.getItem("vbmapp_objetivo")){
        let idx = this.planointervencao.objetivos.findIndex(obj => obj.id == idObjetivo);
        if (!idx) {
          idx = this.planointervencao.objetivos.findIndex(obj => obj.nome == objetivo?.nome);
        }
        this.planointervencao.objetivos[idx] = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
        this.datasourceObjetivos.data = this.planointervencao.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        await this.ordenarObjetivos();
        await this.save();
      }
    });      
  }

  async setMsAssmtPorData(){
    if(this.form.valid){
      if (await this.hasPEISameDate(this.planointervencao.data)) {
        const data: ConfirmDialogCustomData = {
          message: 'Já existe um PEI com a mesma data, como deseja prosseguir?',
          options: [
            'Criar novo PEI mesmo assim',
            'Adicionar objetivos do outro PEI neste',
            'Cancelar'
          ]
        };
  
        const dialogRef = this.dialog.open(ConfirmDialogCustomComponent, {
          width: 'auto',
          data
        });
  
        dialogRef.afterClosed().subscribe(async result => {
          switch (result) {
            case 'Criar novo PEI mesmo assim':
              await this.save();
              break;
  
            case 'Adicionar objetivos do outro PEI neste':
              // Busca o outro plano com a mesma data
              const formattedDate = moment(this.planointervencao.data).format('YYYY-MM-DD');
              const planosPEI = this.planos.filter(plano => plano.data && moment(plano.data).format('YYYY-MM-DD') === formattedDate);
              const outroPlano = await this.planointervencaoService.findById(planosPEI[0].id).toPromise();
  
              this.planointervencao?.objetivos ? this.planointervencao.objetivos : this.planointervencao.objetivos = [];
              this.planointervencao?.habilidades ? this.planointervencao.habilidades : this.planointervencao.habilidades = [];
              this.planointervencao?.idsAvaliacoes == undefined ? this.planointervencao.idsAvaliacoes = [] : this.planointervencao.idsAvaliacoes;
              
              // Adiciona os objetivos do outro plano ao atual (evita duplicidade)
              outroPlano?.objetivos.forEach(obj => {
                if (!this.planointervencao?.objetivos.find(o => o.id === obj.id)) {
                  this.planointervencao?.objetivos.push(obj);
                }
              });
              // Adiciona as habilidades do outro plano ao atual (evita duplicidade)
              outroPlano?.habilidades.forEach(hab => {
                if (!this.planointervencao?.habilidades.find(h => h.sigla === hab.sigla)) {
                  this.planointervencao?.habilidades.push(hab);
                }
              });
              // Adiciona as avaliações do outro plano ao atual (evita duplicidade)
              outroPlano?.idsAvaliacoes.forEach(async av => {
                if (!this.planointervencao?.idsAvaliacoes.find(a => a === av)) {
                  this.planointervencao?.idsAvaliacoes.push(av);
                  const avaliacaoEncontrada = this.todasAvaliacoes.find(avt => avt.id === av);
                
                  if (avaliacaoEncontrada) {
                    // Adiciona ao plano de avaliações
                    this.avaliacoesPlano.push(avaliacaoEncontrada as Avaliacao);
                  }
                }
              });
  
              // Desativa o outro plano
              outroPlano.status = false;
              await this.planointervencaoService.update(outroPlano).toPromise();
              
              // Salva o plano atual como ativo
              await this.save();

              // Atualiza a lista de objetivos e marcos
              this.listaDeObjetivosDoPlanoOriginal = this.planointervencao.objetivos.map(obj => ({ ...obj }));

              await this.ordenarObjetivos();
              
              this.datasourceObjetivos.data = this.objetivosOrdenados;
              this.listaDeObjetivosDoPlano = [...this.planointervencao.objetivos];
              this.datasourceMarcos.data = this.planointervencao.marcos;
              break;
  
            case 'Cancelar':
              this.saveDisabled = false;
              break;
          }
        });
      } else {
        await this.save();
      }
    }
  }

  async setDominios(){
    try {
      if(this.isAvaliacaoMarcoOrESDM()){
        if (this.isVbmappOrESDM == "VBMAPP"){
          this.vbmappmsassmt = this.avaliacao as unknown as VBMAPPMilestonesAssessment;
        } else {
          this.esdmchecklist = this.avaliacao as unknown as ESDMChecklist;
        }
      }
      
      if (this.isVbmappOrESDM == "ESDM"){
        //Carregando Domínios
        this.objetivoESDMService.findDominiosByNivel(this.nivel.id).subscribe(async dominios => {
          this.dominios = dominios;
          this.dominio = this.dominios[0];
  
          await this.filterHabilidades();
          await this.sugestedMilestones(this.habIncChkBox as unknown as MatCheckboxChange)
          
        })
      } else {
        this.habilidadeAvaliacaoService.findDominiosByNivel(this.nivel.id).subscribe(async dominios => {
          this.dominios = dominios;
          this.dominio = this.dominios[0];
  
          await this.filterHabilidades();
          await this.sugestedMilestones(this.habIncChkBox as unknown as MatCheckboxChange)
        })
      }
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  async filterHabilidades(){  
    try {
      if(this.isAvaliacaoMarcoOrESDM()){
        if (this.isVbmappOrESDM == "VBMAPP"){
          // console.log(this.nivel.id)
          // console.log(this.dominio.id)
          this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(msassmt => 
            (msassmt.marco.nivel.nome == this.nivel.nome && msassmt.marco.dominio.nome == this.dominio.nome))
            
        } else {
          this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
            (chklst.competencia.nivel.nome == this.nivel.nome && chklst.competencia.dominio.nome == this.dominio.nome))
        }
      } else {
        this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel.id, this.dominio.id).subscribe(habilidades => {
    
          this.respostasHabilidadesView = this.avaliacao.respostasHabilidades.filter(resposta => {
            return habilidades.filter(h => "" + h.idDominioAvaliacao === "" + this.dominio.id
              && "" + h.idNivelAvaliacao === "" + this.nivel.id
              && "" + resposta.idHabilidadeAvaliacao === "" + h.id).length > 0
          })
    
          this.habilidadesView = this.habilidades.filter(habilidade => {
            return "" + habilidade.idDominioAvaliacao === "" + this.dominio.id
            && "" + habilidade.idNivelAvaliacao === "" + this.nivel.id
          })
    
          //Carrego os domínios máximos
          for(const [, h] of habilidades.entries()) {
            for(const [, d] of h.dominioResposta.entries()) {
              if(this.domoniosResposta.filter( resp =>  resp.sigla === d.sigla).length == 0){
                this.displayedColumns.push("resp-" + d.sigla)
                this.domoniosResposta.push(d);
              }
            }
          }
    
          this.domoniosResposta.sort(function(a, b) {
            if( a.valor < b.valor) {
              return 1;
            } else {
              return -1;
            }
          })
    
          this.habilidadesView.sort(function(a, b) {
            if( a.ordem > b.ordem) {
              return 1;
            } else {
              return -1;
            }
          })
        })
      }
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  filterMsAssmt(){
    /*this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(msassmt => 
      (msassmt.marco.id_nivel==this.nivel.id 
        && msassmt.marco.id_dominio == this.dominio.id))
    */
  }

  async setProfissional(event:MatSelectChange){
    //console.log(event)
    this.planointervencao.profissional = this.profissionais.find(p => p.id == event.value);
    //this.planointervencao.idProfissional = event.value.id;
    if(this.planointervencao.objetivos != undefined){
      //console.log('salva') //console.log("Show");
      await this.save();
    } 
  }

  setMsAssmt(){
    //Carrega novo chechklist no default N1-Mando
    //this.nivelField.value=   //nativeElement.children[0].selected=true;
    //this.dominioField.nativeElement.children[0].selected=true;
    //this.nivel.id = 'N1';
    //this.dominio.id = 'Mando'
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'Mando');
    this.filterMsAssmt()
  }

  validForm(){
    if(this.form.valid){
      return true;
    } else {
      this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      return false;
    }
  }

  async createPlano(plano: PlanoIntervencaoVBMAPP): Promise<string>{
    const response = await this.planointervencaoService.create(this.planointervencao).toPromise();
    return response;
  }

  async updatePlano(plano: PlanoIntervencaoVBMAPP): Promise<PlanoIntervencaoVBMAPP>{
    const response = await this.planointervencaoService.update(this.planointervencao).toPromise();
    return response;
  }

  async save(): Promise<void> {
    let ok: boolean;
    if (this.saveDisabled == false) {
      if (this.form.valid) {
        this.saveDisabled = true;
        if (this.planointervencao.id == undefined) {
          const id = await this.createPlano(this.planointervencao);
          this.saveDisabled = false;
          this.planointervencao.id = id;
        } else {
          await this.updatePlano(this.planointervencao);
          this.saveDisabled = false;
        }
      } else {
        this.saveDisabled = false;
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!', true);
      }
    }
  }

  async hasPEISameDate(date: Date = new Date()): Promise<boolean> {
    const formattedDate = moment(date).format('YYYY-MM-DD');

    // Caso a lista de planos ainda não tenha sido carregada, carrego-a
    if (!this.planos) {
      this.planos = await this.planointervencaoService.findResumoByPaciente(this.idPaciente).toPromise();
    }
    // Filtra os planos que possuem a mesma data formatada
    const planosPEI = this.planos.filter(plano => plano.data && moment(plano.data).format('YYYY-MM-DD') === formattedDate && plano?.id != this.planointervencao?.id);
    return planosPEI.length > 0;
  }

  exitEdit(){
    if (this.planointervencao.id != undefined) {
      if(this.planointervencao.ativo == false){
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
          width: 'auto',
          height: 'auto',
          data: {
            valida: true,
            msg: 'Esse PEI está inativo para coleta! Caso mantenha assim o pei mais recente será disponibilizado para coleta. Deseja realmente sair?'
          } 
        });
    
        dialogRef.afterClosed().subscribe(result => {
          if(result){
            this.router.navigate(['/paciente/' + this.idPaciente, {
              tab:"vbmapp_plano",
              idVbmappPlan: this.planointervencao.id
            }])
          } else {
            return;
          }
        });
      } else {
        this.router.navigate(['/paciente/' + this.idPaciente, {
          tab:"vbmapp_plano",
          idVbmappPlan: this.planointervencao.id
        }])
      }
    } else {
      this.router.navigate(['/paciente/' + this.idPaciente, {
        tab:"vbmapp_plano"
      }])
    }
  }

  async newObjetivo(){
    localStorage.removeItem("vbmapp_objetivo")
    localStorage.setItem("vbmapp_objetivo", JSON.stringify( this.objetivos ))
    let ok: boolean
    if(this.planointervencao.id == undefined){
      await this.save();
      ok = true;
      // console.log(ok);
      if(ok) {
        if(ok){
          localStorage.removeItem("vbmapp_objetivo")
          this.dialog.open(ObjetivovbmappCreateComponent, {
            data:{
              idPaciente: this.idPaciente,
              idVbmappPlan: this.planointervencao.id
            }
          }).afterClosed().subscribe(async () => {
            if(localStorage.getItem("vbmapp_objetivo")){
              if (!this.planointervencao.objetivos) {
                this.planointervencao.objetivos = [];
              }
              let obj = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
              this.planointervencao.objetivos.push(obj);
              this.datasourceObjetivos.data = this.planointervencao.objetivos;
              this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
              await this.ordenarObjetivos();
              await this.save();
            }
          });   
        }
      };
    } else {
      localStorage.removeItem("vbmapp_objetivo")
      this.dialog.open(ObjetivovbmappCreateComponent, {
        data:{
          idPaciente: this.idPaciente,
          idVbmappPlan: this.planointervencao.id
        }
      }).afterClosed().subscribe(async () => {
        if(localStorage.getItem("vbmapp_objetivo")){
          let obj = (JSON.parse(localStorage.getItem("vbmapp_objetivo")) as ObjetivoVBMAPP);
          this.planointervencao.objetivos.push(obj);
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
          await this.ordenarObjetivos();
          await this.save();
        }
      });   
    }
  }

  async setAvaliacoes(){
    try {
      this.niveis = [];
      this.dominios = [];
      this.habilidades = [];
      this.habilidadesView = [];
      this.domoniosResposta = [];
      this.displayedColumns = [];
      this.respostasHabilidadesView = [];
      this.habilidades = [];
      this.avaliacoesFiltered = [];
      this.avaliacao = undefined;
      this.vbmappmsassmt = undefined; 
      this.esdmchecklist = undefined; 
      this.isVbmappOrESDM = "";
      // console.log(this.avaliacaoService.avaliacoes.value)
  
      //Verifico se estão sendo exibidos apenas as habilidades incosistentes
      if(this.habIncChkBox.checked){
        //Se sim, desmarco a checkbox e  refaçø a lsita de habilidades exibidas
        this.habIncChkBox.checked = false;
        // this.sugestedMilestones(this.habIncChkBox as unknown as MatCheckboxChange)
      } 
  
      //Recupero as habilidades do Tipo de Avaliação selecionado
      if (this.tipoAvaliacao.id == "5" ) {
        //Carregando os Objetivos
        this.objetivoESDMService.find().subscribe(objetivos => {
          for(let objetivo of objetivos){
            objetivo.sigla = objetivo.id;
            objetivo.idTipoAvaliacao = "5";
            this.habilidades.push(objetivo)
          }
        })
      } else {
        (await this.habilidadeAvaliacaoService.findByTipoAvaliacao(this.tipoAvaliacao.id)).subscribe(habs => {
          this.habilidades = habs;
        })
      }
  
      //Filtro as avaliações do Tipo de Avaliação selecionado
      if(this.tipoAvaliacao?.id != undefined) {
        this.avaliacoesFiltered = await this.avaliacoes.filter(avs => avs.idTipoAvaliacao == this.tipoAvaliacao?.id);
        // console.log(this.avaliacoesFiltered)
      }
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  async setNiveis(){
    try {
      this.dominios = [];
  
      if(this.isAvaliacaoMarcoOrESDM()){
        if (this.isVbmappOrESDM == "VBMAPP"){
          this.vbmappmsassmt = this.avaliacao as unknown as VBMAPPMilestonesAssessment;
        } else {
          this.esdmchecklist = this.avaliacao as unknown as ESDMChecklist;
        }
      }
  
      if (this.isVbmappOrESDM == "ESDM"){
        //Carregando Níveis
        this.nivelService.find().subscribe(async niveis => {
          this.niveis = niveis;
          this.nivel = niveis[0];
          await this.setDominios();
        })
      } else {
        //Carrego os níveis do tipo de avaliação
        this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao).subscribe(async n => {
          this.niveis = n;
          this.nivel = n[0];
          await this.setDominios();
        })
      }
    } catch (error) {
      console.error("Erro:", error);
    }
  }

  do(){}

  isAvaliacaoMarcoOrESDM(): boolean {
    if (!this.tipoAvaliacao) {
      return false;
    }
  
    const tipoNome = this.tiposAvaliacoes.find(ta => ta.id === this.tipoAvaliacao.id)?.nome;
  
    switch (tipoNome) {
      case "[VB-MAPP] Avaliação de Marcos":
        this.isVbmappOrESDM = "VBMAPP";
        return true;
      case "[ESDM] Early Start Denver Model":
        this.isVbmappOrESDM = "ESDM";
        return true;
      default:
        this.isVbmappOrESDM = "";
        return false;
    }
  }  

  editarObjetivoESDM(id: number){
    localStorage.removeItem('objetivoPersonalizado');

    var objetivo:Objetivo = this.datasourceObjetivos.data[id] as Objetivo;
    this.openDialog(objetivo, id);
     
    // this.router.navigate(['/objetivo/update/' + this.planointervencao.objetivos[id].id])
  }

  openDialog(objetivo: any, id: number) {
    this.dialog.open(ObjetivoCreateComponent, {
    data:{
      obj: objetivo
    }
    }).afterClosed().subscribe(async () => {
      if(localStorage.getItem("objetivoPersonalizado")){
        this.planointervencao.objetivos[id] = (JSON.parse(localStorage.getItem("objetivoPersonalizado")) as Objetivo);
        this.datasourceObjetivos.data = this.planointervencao.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        await this.save();
      }
    });      
  }

  changeStatus(e: any){
    if(e.value){
      this.planointervencao.ativo = true;
      this.save();
    }else{
      this.planointervencao.ativo = false;
      this.save();
    }
  }

}
