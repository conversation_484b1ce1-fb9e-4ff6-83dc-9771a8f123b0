import { Parentesco } from './parentesco-model';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ParentescoService {

  parentescoUrl = `${environment.API_URL}/parentesco`;
  
  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(parentesco: Parentesco): Observable<Parentesco>{
    //console.log(parentesco);
    return this.http.post<Parentesco>(this.parentescoUrl, parentesco).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(parentesco: Parentesco): Observable<Parentesco>{
    //console.log(parentesco); 
    return this.http.put<Parentesco>(this.parentescoUrl  + '/' + parentesco.id, parentesco).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Parentesco>{
    return this.http.get<Parentesco>(this.parentescoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Parentesco[]>{ 
    return this.http.get<Parentesco[]>(this.parentescoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Parentesco>{
    return this.http.delete<Parentesco>(this.parentescoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
