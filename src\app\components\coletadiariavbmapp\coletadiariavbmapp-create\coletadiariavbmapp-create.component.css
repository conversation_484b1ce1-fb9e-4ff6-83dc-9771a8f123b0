@media (max-width: 700px) {

  .paciente-field{
    width: 90%; 
  }

  .profissional-field{
    width: 90%; 
  }

  .data-field{
    width: 35%;  
  }

  .plano-intervencao-field{
    width: 35%;  
    padding-left: 10px;
    padding-right: 30px;
  }

  .back-arrow{
    margin: 15px;
  }

  .new-session{
    text-align: center;
  }

  .hora-inicio-field{
    width: 40%;  
    margin-left: 0px;
  }

  .hora-fim-field{
    width: 40%;  
    margin-left: 50px;
  }

  .begin-session{
    margin-left: 0px;
  }
}

@media (min-width: 701px) {
  .paciente-field{
    width: 15%; 
    padding: 20px;
  }

  .profissional-field{
    width: 20%; 
    padding: 20px;
  }

  .data-field{
    width: 15%;  
    padding-left: 20px;
  }

  .plano-intervencao-field{
    width: 15%;  
    padding-left: 20px;
  }

  .back-arraow{
    margin: 15px;
  }

  .new-session{
    margin-left: 40px; 
    align-self: flex-end;
  }

  .hora-inicio-field{
    width: 15%;  
    margin-left: 20px;
  }

  .hora-fim-field{
    width: 15%;  
    margin-left: 40px;
  }

  .begin-session{
    margin-left: 40px;
  }

}

table.dominio {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

table.etapa {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

th.tipoSuporte{
  width: 40%;
}

/* th.periodo{
width: 10%;
} */

td.dominio {
  width: 100%;
  background-color: silver;
  font-family: Helvetica;
  font-size: 1.4em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
}

td.objetivo {
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
  width: 100%;
}

td.objetivoAdquirido {
  background-color: aquamarine;
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
  width: 100%;
}

td.etapa {
  width: 30%;
  font-size: 1em;
  font-weight: normal;
  padding: 10px 10px 5px 5px;
}



tr.dominio {
  background-color: lightgray;
  width: 100%;
}

p.dominio {
  font-family: Helvetica;
  font-size: 1.2em;
  font-weight: bold;
  line-height: normal;
}

p.objetivo {
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  line-height: normal;
}

p.etapa {
  font-family: Helvetica;
  font-size: 0.7em;
  font-weight: normal;
  line-height: normal;
  page-break-inside: avoid;
}

div.subtitulo{
  display: flex; 
  flex-direction: row;
  width: 90%; 
  padding: 20px 0px 20px 0px;
}

.container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start; /* Ajusta o espaçamento entre os itens */
  
}

.item {
  flex: 1 1 33%;
  max-width: 33%;
  min-width: 200px; /* Define um valor mínimo para a largura */

}

@media screen and (max-width: 768px) {
  .item {
    flex: 1 1 100%;
    max-width: 100%;
  }
}

.disabled {
  pointer-events: none; /* Desabilita a interação do usuário */
  opacity: 0.5;         /* Torna o componente visualmente desabilitado */
}

.estimulo-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.contador {
  width: 24px;
  text-align: center;
  font-weight: bold;
  margin: 0 8px;
}

.btn-row {
  margin-bottom: 4px;
}

.resps-row {
  display: flex;
  align-items: flex-start;
  gap: 32px;   /* Antes era 16px — aumente para o valor que quiser */
}

/* Cada grupo: botão em cima, input embaixo */
.resp-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24px;
}

/* Espaço entre botão e input */
.contador-input {
  width: 50px;
  margin-top: 4px;
  text-align: center;
}

/* Botão de reset alinhado ao topo também */
.reset-btn {
  align-self: center;
  height: 32px;
  margin-top: 4px;
}

.limite-box {
  font-size: 0.9em;
  color: #888;
  margin-top: 10px;
  text-align: center;
}
