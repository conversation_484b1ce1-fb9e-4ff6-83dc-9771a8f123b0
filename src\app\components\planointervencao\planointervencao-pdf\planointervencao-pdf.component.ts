import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { DominioService } from './../../dominio/dominio.service';
import { Objetivo } from './../../objetivo/objetivo-model';
import { Dominio } from './../../dominio/dominio-model';
import { Router, ActivatedRoute } from '@angular/router';
import { PlanoIntervencao } from './../planointervencao-model';
import { PlanoIntervencaoService } from './../planointervencao.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-planointervencao-pdf',
  templateUrl: './planointervencao-pdf.component.html',
  styleUrls: ['./planointervencao-pdf.component.css']
})
export class PlanointervencaoPdfComponent implements OnInit {

  public planointervencao: PlanoIntervencao = new PlanoIntervencao();
  public dominioMap: Map<string, Objetivo[]> = new Map<string, Objetivo[]>();
  public dominios: Dominio[];
  public pdfGerado: boolean = false;

  @ViewChild("pdf") htmlData: ElementRef;

  

  constructor(private planointervencaoService: PlanoIntervencaoService,
    public authService: AuthService,
    private dominioService: DominioService,
    private router: Router,
    private route: ActivatedRoute) { }

  ngOnInit(): void {
    
    let idPlano = this.route.snapshot.paramMap.get("id");

      this.planointervencaoService.findById(idPlano).subscribe(plano => {
        this.planointervencao = plano;
        //console.log(this.planointervencao)
        this.setObjetivosPorDominio();
      })

      this.dominioService.find().subscribe(doms => {
        this.dominios = doms;
      })
      
  }

  get user(): FirebaseUserModel{
    //console.log(this.headerService.user.name);
    //console.log(this.headerService.getUser())
    
    return this.authService.getUser();
    //return this.headerService.getUser();


    /*console.log(this.userService.getCurrentUser())
    this.userService.getCurrentUser()
      .then( (user) => {
        return user;
      }, (error) => {
        console.log('Usuário não está logado.')
        return null;
      });
      return null;*/
  }
  
  ngAfterViewInit(){
    
    let DATA = this.htmlData.nativeElement; 

    //console.log("Id:" + this.planointervencao.id)

    if(this.planointervencao.id != undefined){
      //console.log("PDF Gerado")
      //console.log(DATA.innerText)
      this.pdfGerado = true;
      //const doc = new JsPDF('p', 'mm', 'a4')
    
      let handleElement = {
        '#editor':function(element,renderer){
          return true;
        }
      };
      

      //doc.text(DATA.innerText,10,10)
  
    
    
      //doc.text('Hello world!', 10, 10);
      //doc.output('dataurlnewwindow');
      //doc.save('a4.pdf');
    }
  }

  do(){
    /*
    let DATA = this.htmlData.nativeElement; 
    //const doc = new jsPDF('p', 'pt', 'a4')
    const doc = new jsPDF('portrait', 'pt', 'a4',true)
    //doc.setFont("helvetica");
    //doc.setFontType("bold");
    //doc.setFontSize(9);
    
    let handleElement = {
      '#editor':function(element,renderer){
        return true;
      }
    };

    //console.log(DATA.innerHTML)
    
    //doc.html(DATA.innerHTML, {callback: function(doc) {
    doc.html(DATA, {callback: function(doc) {
      //doc.setLineWidth(90)
      //doc.setTextColor('blue')
      //console.log(doc)
      doc.save();
      },
      //margin: [1500, 2500, 1500, 2500],
      x:30, 
      y:30, 
    })

*/

    //doc.output('dataurlnewwindow');

    //doc.text(DATA.innerText,10,10)

  
  
    //doc.text('Hello world!', 10, 10);
    //doc.output('dataurlnewwindow');
    /*
    const options = {
      filename: "teste.pdf",
      image: { type: "jpg" },
      html2canvas: {},
      jsPDF: { orientation: 'landscape' }
    }; 

    html2pdf()
      .from(DATA)
      .set(options)
      .save()
    */
/*
    html2canvas(DATA).then(canvas => {
      // Few necessary setting options
      var imgWidth = 208;
      var pageHeight = 295;
      var imgHeight = canvas.height * imgWidth / canvas.width;
      var heightLeft = imgHeight;
      
      const contentDataURL = canvas.toDataURL('image/png')
      let pdf = new jsPDF('p', 'mm', 'a4', true); // A4 size page of PDF
      var position = 0;
    
      pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight)
      pdf.save('new-file.pdf'); // Generated PDF
    });
*/




   /* const _doc = new JsPDF('p', 'pt', 'a4', true)
    doc.html(DATA.innerHTML, {callback: function(doc) {
      doc.save();
      },
      x:10, y:10
    })
    doc.output('dataurlnewwindow');*/
  }

  setObjetivosPorDominio(){
    
    let objetivos: Objetivo[] = [];
    this.planointervencao.objetivos.forEach(objetivo => {
      //objetivos = [];
      if(this.dominioMap.get(objetivo.dominio.id) != undefined){
        objetivos = this.dominioMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        this.dominioMap.set(objetivo.id_dominio, objetivos)
      } else {
        this.dominioMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    
  }

  getObjetivosPorDominio(id: string){
    
    //console.log(this.dominioMap.get(id))
    return this.dominioMap.get(id);
    
  }
}
