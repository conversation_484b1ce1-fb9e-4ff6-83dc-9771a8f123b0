import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Avaliacao } from './avaliacao-model';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AvaliacaoService {
  avaliacaoUrl = `${environment.API_URL}/avaliacao`;

  // public avaliacoes: BehaviorSubject<Avaliacao[]> = 
  //   new BehaviorSubject<Avaliacao[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(avaliacao: Avaliacao): Observable<string>{
    return this.http.post<string>(this.avaliacaoUrl, avaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(avaliacao: Avaliacao): Observable<Avaliacao>{
    return this.http.put<Avaliacao>(this.avaliacaoUrl + "/" + avaliacao.id, avaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Avaliacao>{
    return this.http.get<Avaliacao>(this.avaliacaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByPacienteTipoAvaliacao(idPaciente: string, idTipoAvaliacao: string): Observable<Avaliacao[]>{
    return this.http.get<Avaliacao[]>(this.avaliacaoUrl + '/paciente/' + idPaciente + '/tipoavaliacao/' + idTipoAvaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  
  }
  findLastByPacienteData(idPaciente: string, idTipoAvaliacao: string, data: string): Observable<Avaliacao[]>{
    return this.http.get<Avaliacao[]>(this.avaliacaoUrl + '/paciente/' + idPaciente + '/tipoavaliacao/' + idTipoAvaliacao + '/data/' + data).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByPaciente(idPaciente: string, ): Observable<Avaliacao[]>{
    return this.http.get<Avaliacao[]>(this.avaliacaoUrl + '/paciente/' + idPaciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findResumoByPaciente(idPaciente: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.avaliacaoUrl}/paciente/${idPaciente}/resumo`).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }

  delete(id: string): Observable<Avaliacao>{
    return this.http.delete<Avaliacao>(this.avaliacaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
