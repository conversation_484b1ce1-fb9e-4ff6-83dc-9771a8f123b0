import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { TipoprocedimentoService } from './../tipoprocedimento.service';
import { TipoProcedimento } from './../tipoprocedimento-model';
import { Component, OnInit, DEFAULT_CURRENCY_CODE } from '@angular/core';

@Component({
  selector: 'app-tipoprocedimento-list',
  templateUrl: './tipoprocedimento-list.component.html',
  styleUrls: ['./tipoprocedimento-list.component.css'],
  providers: [{
    provide:  DEFAULT_CURRENCY_CODE,
    useValue: 'BRL'
  }]
})
export class TipoprocedimentoListComponent implements OnInit {

  public tiposprocedimento: TipoProcedimento[];

  displayedColumns = ['nome', 'minutos', 'valor','permiteAlterarValor', 'action']

  constructor(private tipoprocedimentoService: TipoprocedimentoService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {
    this.tipoprocedimentoService.find().subscribe(tiposprocedimento => {
      this.tiposprocedimento = tiposprocedimento;
    })
  }

  edit(tipoprocedimento: TipoProcedimento){
    this.router.navigate(['/tipoprocedimento/' + tipoprocedimento.id]);
  }

  delete(tipoprocedimento: TipoProcedimento): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        tipoprocedimento.ativo = false;
        this.tipoprocedimentoService.update(tipoprocedimento).subscribe((p) => {
          this.tipoprocedimentoService.showMessage('Tipo de procedimento inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/tipoprocedimento']);
        });
      }
    });
  }

}
