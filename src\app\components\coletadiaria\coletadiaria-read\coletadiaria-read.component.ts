import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { ColetadiariaService } from './../coletadiaria.service';
import { ColetaDiaria } from './../coleta-diaria-model';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { PlanoIntervencaoService } from './../../planointervencao/planointervencao.service';
import { PlanoIntervencao } from './../../planointervencao/planointervencao-model';
import { Etapa } from './../../etapa/etapa-model';
import { Objetivo } from './../../objetivo/objetivo-model';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Paciente } from './../../paciente/paciente-model';
import { Observable } from 'rxjs';
import { Component, OnInit, Input } from '@angular/core';
import { LoadingService } from 'src/app/shared/service/loading.service';
  
@Component({
  selector: 'app-coletadiaria-read',
  templateUrl: './coletadiaria-read.component.html',
  styleUrls: ['./coletadiaria-read.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ColetadiariaReadComponent implements OnInit {

  @Input()
  $pacienteSearch: Observable<Paciente>;

  datasourceObjetivos = new MatTableDataSource();

  displayedColumnsObjs = ['index', 'expand', 'etapas', 'idNome']

  paciente: Paciente = new Paciente();

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;
  public hasAccessCreatePlano: boolean;

  public planosIntervencao: PlanoIntervencao[];
  
  public planoIntervencao: PlanoIntervencao;

  public coletasDiarias: ColetaDiaria[];

  //                  etapa.id      data   Etapa[]
  public etapaMap: Map<string, Map<string, Etapa[]>> = new Map<string, Map<string, Etapa[]>>();

  public isTableExpanded = false;

  constructor(private planoIntervencaoService: PlanoIntervencaoService,
    private coletaDiariaService: ColetadiariaService,
    public authService: AuthService,
    private router: Router,
    private loadingService: LoadingService) { }

  ngOnInit(): void { 
    this.loadingService.show();
    //Atribuindo o paciente vindo por parâmetro
    this.$pacienteSearch.subscribe(paciente => { 
      this.paciente = paciente;
      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','create')
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','update')
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','read')
      this.hasAccessCreatePlano = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Plano de Intervenção','create')

      //Recupero os planos de intervenção do paciente
      this.planoIntervencaoService.findByPaciente(this.paciente.id).subscribe(planos => {
        this.planosIntervencao = planos.filter(p => p.status !== false);
        
        //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
        if(this.planosIntervencao.length > 0) {
          this.planoIntervencao = this.planosIntervencao[0];

          //Seto os objetivos do plano atual para serem visualizados
          // console.log(this.planoIntervencao.objetivos.filter(obj => obj != null));
          this.datasourceObjetivos.data = this.planoIntervencao.objetivos?.filter(obj => obj != null);

          //Recupero as coletas do plano atual
          this.coletaDiariaService.findByPlanoIntervencao(this.planoIntervencao.id).subscribe(coletas => {
            this.coletasDiarias = coletas;
            this.coletasDiarias = this.coletasDiarias.sort(function (a, b) {
              //let valorA = new Date(a.data).getDate() + "/" + (new Date(a.data).getMonth() + 1) + "/" + new Date(a.data).getFullYear();
              //let valorB = new Date(b.data).getDate() + "/" + (new Date(b.data).getMonth() + 1) + "/" + new Date(b.data).getFullYear();
              let valorA = new Date(a.data).getFullYear() + "/" + (new Date(a.data).getMonth() + 1) + "/" + ("0" + new Date(a.data).getDate()).slice(-2);
              let valorB = new Date(b.data).getFullYear() + "/" + (new Date(b.data).getMonth() + 1) + "/" + ("0" + new Date(b.data).getDate()).slice(-2);

              valorA = valorA + a.sessao;
              valorB = valorB + b.sessao;
              //console.log(a.data)
              //console.log(b.data)

              if (valorA < valorB) {
                return 1;
              }
              if (valorA > valorB) {
                return -1;
              }
              // a must be equal to b
              return 0;
            });
            this.setMapEtapasporData(coletas);
            this.loadingService.hide();
          })
        } else {
          this.loadingService.hide();
        }
      })
    })
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
   // console.log(funcoes)
    return funcoes;
  }

  getEtapaMapKeys(etapa: Etapa){
    return [etapa.id];
  }

  hasColetasEtapa(idEtapa: string): boolean{
    let m: Map<string, Etapa[]>;
    let has = false;
    let keys: string[];
    m = this.etapaMap.get(idEtapa);
    if(m == undefined){
      return false;
    } else {
      keys = Array.from(m.keys());
      keys.forEach(key => {
        m.get(key).forEach(etapa => {
          if(etapa.status != "" && etapa.status != undefined){
            has = true;
          }
        })
      })
      return has;
    }
  }

  setMapEtapasporData(coletas: ColetaDiaria[]){
    //console.log(coletas);
    let m: Map<string, Etapa[]>;
    let data: string;
    let etapas: Etapa[];
    this.etapaMap = new Map<string, Map<string, Etapa[]>>();
    for(const [,coleta] of coletas.entries()) { //coletas.forEach(coleta => {
      for(const [, objetivo] of coleta.objetivos.entries()) {//coleta.objetivos.forEach(objetivo => {
        for(const [,etapa] of objetivo.etapa.entries()) {//objetivo.etapa.forEach(etapa => {
          //console.log(coleta.data)
          data = new Date(coleta.data).getDate() + "/" + (new Date(coleta.data).getMonth() + 1) + "/" + new Date(coleta.data).getFullYear();
          //console.log(data)
          //Verifico se já tenho a etapa cadastrada
          if(this.etapaMap.get(etapa.id) == undefined){
            //Caso não tenha a etapa cadastrada, faço o cadastro já com a data corrente
            m = new Map<string, Etapa[]>();
            m.set(data, [etapa])
            this.etapaMap.set(etapa.id, m);
          } else {
            //Caso já tenha a etapa no Map, recupero o map da etapa e vejo se a data está cadastrada
            m = this.etapaMap.get(etapa.id);
            if(m.get(data) == undefined) {
              //Se a data não está cadastrada, faço o cadastro da mesma
              m.set(data, [etapa])
              this.etapaMap.set(etapa.id, m)
            } else {
              //Se a data está cadastrada, pego o array de datas e faço um push para o novo status
              etapas = m.get(data)
              etapas.push(etapa)
              this.etapaMap.set(etapa.id, m);
            }
          }
        }//})
      }//})
    }//})
    //console.log(this.etapaMap)
  }

  getColetasObjetivoEtapa(idObjetivo: string, idEtapa: string): ColetaDiaria[]{
    let coletas: ColetaDiaria[] = [];
    for(const [,coleta] of this.coletasDiarias.entries()) { //this.coletasDiarias.forEach(coleta => {
      for(const [, objetivo] of coleta.objetivos.entries()) { //coleta.objetivos.forEach(objetivo => {
        if(objetivo.id == idObjetivo){
          for(const [, etapa] of objetivo.etapa.entries()) { //objetivo.etapa.forEach(etapa => {
            if(etapa.id == idEtapa){
              coletas.push(coleta)
            }
          }
        }
      }
    }
    return coletas;
  }
  
  setPlanoIntervencao(){
    //Seto os objetivos do plano atual para serem visualizados
    this.datasourceObjetivos.data = [...this.planoIntervencao.objetivos.filter(obj => obj != null)];

    //Recupero as coletas do plano atual
    this.coletaDiariaService.findByPlanoIntervencao(this.planoIntervencao.id).subscribe(coletas => {
      this.coletasDiarias = coletas;
      this.setMapEtapasporData(coletas);
    })
  }

  toggleTableRow(row: any){
    row.isExpanded = !row.isExpanded;
  }

  percentualEtapas(row: Objetivo){
    if(row != null){
      return this.countEtapasAdquiridas(row) / this.countEtapas(row)
    } else {
      return 0;
    }
    // return this.countEtapasAdquiridas(row) / this.countEtapas(row)
  }

  countEtapas(row: Objetivo){
    //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
    if(row){
      return row.etapa.length
    } else {
      return 0;
    }
  }

  countEtapasAdquiridas(row: Objetivo){
    if(row){
      return row.etapa.filter(e => (e.status=='Adquirida')).length
    } else {
      return 0;
    }
  }

  changeEtapaStatus(etapa: Etapa){
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreatePlano){
      let objAdq: boolean;
      if(this.hasAccessUpdate){
        if(etapa.status == undefined || etapa.status == 'Não adquirida'){
          etapa.status = "Adquirida"
        } else {
          etapa.status = "Não adquirida"
        }
        //Verifico se todas as etapas do objetivo foram adquiridas e coloco o objetivo como "Adquirido"
        objAdq = true;
        for(const [,eta] of this.planoIntervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).etapa.entries()) { 
          // console.log(eta.id + " - " + eta.status)
          if(eta.status != "Adquirida"){
            // console.log("False")
            objAdq = false;
          }
        }
        
        if(objAdq == true){
          this.planoIntervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Adquirido"
        } else {
          this.planoIntervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Não adquirido";
        }
        this.save();
      }

    }
  }

  save(): void{
    this.loadingService.show();

    if(this.planoIntervencao.id == undefined){
      this.planoIntervencaoService.create(this.planoIntervencao).subscribe((id) => {
        this.planoIntervencao.id = id;
        this.loadingService.hide();

        // console.log(id)
        //this.planoIntervencaoService.showMessage('Plano de intervenção criado com sucesso!');
        //this.router.navigate(['/paciente/' + id]);
      });
    } else {
      this.planoIntervencaoService.update(this.planoIntervencao).subscribe((paciente) => {
        this.loadingService.hide();

        //this.planoIntervencaoService.showMessage('Plano de intervenção alterado com sucesso!');
        //this.router.navigate(['/paciente/' + paciente.id]);
      });
    } 
  }
  
  saveToPDF(){
    this.router.navigate([]).then(result => {
      window.open('planoIntervencao/pdf/' + this.planoIntervencao.id + "?hasFullView=true",'_blank');
    })
  }

  add(){
    //this.router.navigate(['/planoIntervencao/create', {   
    this.router.navigate(['/coletadiaria/' + this.planoIntervencao.id])
  }
}
