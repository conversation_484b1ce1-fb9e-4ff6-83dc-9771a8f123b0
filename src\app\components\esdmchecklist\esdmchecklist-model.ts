import { Profissional } from './../profissional/profissional-model';
import { ESDMChkLstCompetencia } from './esdmchklst-competencia-model';
import { Paciente } from './../paciente/paciente-model';
import { Competencia } from '../competencia/competencia-model';
export class ESDMChecklist {
    id?: string;
    idPaciente: string;
    paciente: Paciente;
    idProfissional: string;
    profissional: Profissional;
    data: Date;
    checklist: ESDMChkLstCompetencia[]; 
    status: boolean;

    constructor(){
        this.paciente = new Paciente();
        this.profissional = new Profissional();
        this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
        this.checklist = [];
    }
}