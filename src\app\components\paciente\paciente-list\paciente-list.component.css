table {
    width: 100%
}

.tdlinked{
    display: block;
    text-decoration: none;
}

@media (max-width: 700px) {
    .kpis{
        width: 90%;
    }
    .mat-column-nome {
        flex: 0 0 35% !important;
        width: 35% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .mat-column-kpis {
        flex: 0 0 35% !important;
        width: 35% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .mat-column-dataNascimento {
        flex: 0 0 20% !important;
        width: 20% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }
}

@media (min-width: 701px) {
    .kpis{
        width: 50%;
    }
    .mat-column-nome {
        flex: 0 0 35% !important;
        width: 35% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .mat-column-kpis {
        flex: 0 0 35% !important;
        width: 35% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .mat-column-dataNascimento {
        flex: 0 0 20% !important;
        width: 20% !important;
        text-align: left;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin: .5em;
}

.label-aviso {
    background-color: #ff902b;
}

.label-ok {
    background-color: #27c24c;
}

.label-atrasado {
    background-color: #f05050;
}
