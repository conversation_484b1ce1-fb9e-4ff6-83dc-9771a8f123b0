import { DuracaoObjetivo } from "./../objetivopic/duracoes-model";

export class SessaoColetaDiariaPIC {
    id?: string;
    data: Date;
    idProfissional:string;
    idPaciente:string;
    idPlanoIntervencao: string;
    nomeProfissional: string;
    horaInicio: string;
    horaTermino: string;
    sessao: string;

    objetivosColeta: ColetaDiariaPIC[];
    nota: string;
}

export class ColetaDiariaPIC {
    idObjetivo: string;
    
    //Registro de Eventos
    qtdObservada?: number;
    
    //Cronometragem ou Amostragem de Tempo
    duracoes?: DuracaoObjetivo[]; //sempre em segundos
}