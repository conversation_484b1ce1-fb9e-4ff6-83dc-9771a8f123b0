import { TipoAvaliacao } from './tipo-avaliacao-model';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { EMPTY, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})

export class TipoAvaliacaoService {

  tipoUrl = `${environment.API_URL}/tipo_avaliacao`;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(nivel: TipoAvaliacao): Observable<TipoAvaliacao>{
    return this.http.post<TipoAvaliacao>(this.tipoUrl, nivel).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(nivel: TipoAvaliacao): Observable<TipoAvaliacao>{
    return this.http.put<TipoAvaliacao>(this.tipoUrl + "/" + nivel.id, nivel).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<TipoAvaliacao>{
    return this.http.get<TipoAvaliacao>(this.tipoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<TipoAvaliacao[]>{
    return this.http.get<TipoAvaliacao[]>(this.tipoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  
  }

  delete(id: string): Observable<TipoAvaliacao>{
    return this.http.delete<TipoAvaliacao>(this.tipoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

}
