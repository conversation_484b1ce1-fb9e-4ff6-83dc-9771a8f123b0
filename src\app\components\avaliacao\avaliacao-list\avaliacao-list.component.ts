import { LoadingService } from '../../../shared/service/loading.service';
import { VBMAPPMsAssmtService } from './../../vbmappmsassmt/vbmappmsassmt.service';
import { Avaliacao } from './../avaliacao-model';
import { TipoAvaliacaoService } from './../tipo-avaliacao.service';
import { Component, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { Paciente } from '../../paciente/paciente-model';
import { AuthService } from '../../template/auth/auth.service';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TipoAvaliacao } from '../tipo-avaliacao-model';
import { AvaliacaoService } from '../avaliacao.service';
import { VBMAPPMilestonesAssessment } from '../../vbmappmsassmt/vbmappmsassmt-model';
import { Profissional } from '../../profissional/profissional-model';
import { DeleteConfirmDialogComponent } from '../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { C } from '@angular/cdk/keycodes';
import { ESDMChecklist } from '../../esdmchecklist/esdmchecklist-model';
import { EsdmchecklistService } from '../../esdmchecklist/esdmchecklist.service';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-avaliacao-list',
  templateUrl: './avaliacao-list.component.html',
  styleUrls: ['./avaliacao-list.component.css']

})
export class AvaliacaoListComponent implements OnInit {
  public avaliacoes: Avaliacao[] = [];
  public avaliacoesFiltered: Avaliacao[] = [];
  public avaliacoesMarco: VBMAPPMilestonesAssessment[] = [];
  public avaliacoesESDM: ESDMChecklist[] = [];
  public tiposAvaliacoes: TipoAvaliacao[];
  public paciente: Paciente = new Paciente();

  displayedColumns = ['tipo', 'data', 'profissional', 'action']

  @Input()
  $pacienteSearch: Observable<Paciente>;

  public viewAvaliacaoPDF: boolean;
  public viewVbmappPDF: boolean;
  public viewESDMPDF: boolean;
  public idAvaliacao: string;
  public avaliacao: Avaliacao = new Avaliacao();
  public hasAccessCreate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;

  constructor(
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private avaliacaoService: AvaliacaoService,
    private avaliacaoMarcoService: VBMAPPMsAssmtService,
    private esdmchecklistService: EsdmchecklistService,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    let avsMarco: Avaliacao[] = [];

    // Aguarda a busca do paciente
    this.paciente = await this.$pacienteSearch.toPromise();

    // Carregar tipos de avaliações
    this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','create')
    this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','delete')
    this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update')
    this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')

    const tipos = await this.tipoAvaliacaoService.find().toPromise();
    this.tiposAvaliacoes = tipos
      .filter(ta => ta.ativo == true)
      .sort((a, b) => {
        const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
        const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
        return nomeA.localeCompare(nomeB);
      });

    // Carregar avaliações e avaliações do Marco simultaneamente
    const avaliacoesObservable = this.avaliacaoService.findResumoByPaciente(this.paciente.id);
    const avaliacoesMarcoObservable = this.avaliacaoMarcoService.findResumoByPaciente(this.paciente.id);

    const [avs, avsM] = await forkJoin([avaliacoesObservable, avaliacoesMarcoObservable]).toPromise();

    this.avaliacoesMarco = avsM;
    for (const [, av] of this.avaliacoesMarco.entries()) {
      (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id;
    }

    this.avaliacoes = (avs.filter(a => a.ativo == true).concat(this.avaliacoesMarco.filter(a => a.status != false) as unknown as Avaliacao[]));
    this.avaliacoesFiltered = this.avaliacoes;

    // Carregar avaliações ESDM
    const esdm = await this.esdmchecklistService.findResumoByPaciente(this.paciente.id).toPromise();

    this.avaliacoesESDM = esdm;
    for (const [, av] of this.avaliacoesESDM.entries()) {
      (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[ESDM] Early Start Denver Model").id;
    }

    this.avaliacoes = (this.avaliacoesFiltered.concat(this.avaliacoesESDM.filter(a => a.status != false) as unknown as Avaliacao[]));
    this.avaliacoesFiltered = this.avaliacoes;

    // Ordenando as avaliações por data
    this.ordenaAvaliacoes();

    // Finalmente, esconder o loading
    this.loadingService.hide();
  }  

  ordenaAvaliacoes(){
    //Ordenando as avaliacoes por data
    this.avaliacoes.sort(function(a, b) {
      if( a.data < b.data) {
        return 1;
      } else {
        return -1;
      }
    });
  }

  getNomeTipoAvaliacao(idTipoAvaliacao: string){
    // console.log(idTipoAvaliacao)
    return this.tiposAvaliacoes?.find(ta => ta.id == idTipoAvaliacao).nome
  }

  add(idTipoAvaliacao: string){
    if(idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id){
      this.router.navigate(['/vbmappmsassmt/create', {  idPaciente: this.paciente.id}])
    } else if (idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id){
      this.router.navigate(['/esdmchecklist/create', {  idPaciente: this.paciente.id, paciente: this.paciente }])
    } else {
      this.router.navigate(['/avaliacao/create/' + idTipoAvaliacao + '/paciente/' + this.paciente.id]); 
    }
  }

  filterAvaliacoes(idTipoAvaliacao: string){
    // console.log("Filtrando...")
    this.avaliacoesFiltered = this.avaliacoes;
    if(idTipoAvaliacao != undefined) {
      this.avaliacoesFiltered = this.avaliacoesFiltered.filter(avs => avs.idTipoAvaliacao == idTipoAvaliacao);
      // console.log(this.avaliacoesFiltered)
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];
    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  navigateToEdit(id: string){
    if(this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id){
      this.router.navigate(['/vbmappmsassmt/create', { idPaciente: this.paciente.id, idMsAssmt: id }])
    } else if (this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id) {
      this.loadingService.show();
      this.router.navigate(['/esdmchecklist/create', {  idPaciente: this.paciente.id, paciente: this.paciente, idChecklist: id }])
    }else {
      this.router.navigate(['/avaliacao/edit/' + id]);
    }
  }

  navigateToRead(id: string){
    if(this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id){
      this.router.navigate(['/vbmappmsassmt', { idPaciente: this.paciente.id, idMsAssmt: id }])
    } else if (this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id){
      this.router.navigate(['/esdmchecklist', { idPaciente: this.paciente.id, idESDMChecklist: id }])
    } else {
      this.router.navigate(['/avaliacao/' + id]);
    }
  }

  deleteAvaliacao(id: string): void {
    const avaliacao = this.avaliacoesFiltered.find(av => av.id == id);
    this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id
    const tipoAvaliacao = this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao;

    if (tipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id) {
      this.deleteAvaliacaoMarco(id);
    } else if (tipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id) {
      this.deleteAvaliacaoESDM(id);
    } else {
      this.confirmAndDeleteAvaliacao(id);
    }
  }

  // Função genérica para confirmar e deletar uma avaliação padrão
  confirmAndDeleteAvaliacao(id: string): void {
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.show();
        this.avaliacaoService.findById(id).subscribe(avaliacao => {
          avaliacao.ativo = false;
          this.avaliacaoService.update(avaliacao).subscribe(() => {
            this.removeAvaliacaoFromList(id);
            this.loadingService.hide();
            this.avaliacaoService.showMessage('Avaliação inativada com sucesso!');
          });
        });
      }
    });
  }

  // Remover uma avaliação da lista local
  removeAvaliacaoFromList(id: string): void {
    this.avaliacoesFiltered = this.avaliacoesFiltered.filter(av => av.id !== id);
    this.avaliacoes = [...this.avaliacoesFiltered]; // Atualiza a lista completa
    this.ordenaAvaliacoes(); // Reorganiza as avaliações após a remoção
  }

  deleteAvaliacaoMarco(id: string): void {
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.show();
        this.avaliacaoMarcoService.findById(id).subscribe(avaliacao => {
          avaliacao.status = false;
          this.avaliacaoMarcoService.update(avaliacao).subscribe(() => {
            this.removeAvaliacaoFromList(id);
            this.loadingService.hide();
            this.avaliacaoService.showMessage('Avaliação inativada com sucesso!', false);
          });
        });
      }
    });
  }

  deleteAvaliacaoESDM(id: string): void {
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadingService.show();
        this.esdmchecklistService.findById(id).subscribe(esdmchecklist => {
          esdmchecklist.status = false;
          this.esdmchecklistService.update(esdmchecklist).subscribe(() => {
            this.removeAvaliacaoFromList(id);
            this.loadingService.hide();
            this.avaliacaoService.showMessage('Avaliação inativada com sucesso!', false);
          });
        });
      }
    });
  }

  possuiAvaliacoes(idTipoAvaliacao: string){
    return this.avaliacoes.filter(a => a.idTipoAvaliacao == idTipoAvaliacao).length > 0;
  }

  generatePDF(id: string) {
    this.loadingService.show();
    this.idAvaliacao = id;
    this.avaliacao = this.avaliacoes.find(a => a.id == id);
    if(this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id){
      this.viewVbmappPDF = true;
    } else if (this.avaliacoesFiltered.find(av => av.id == id).idTipoAvaliacao == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id) {
      this.viewESDMPDF = true;  
    } else {
      this.viewAvaliacaoPDF = true;
    }
  }

  onPDFGenerated() {
    this.viewAvaliacaoPDF = false;
    this.viewVbmappPDF = false;
  }

}
