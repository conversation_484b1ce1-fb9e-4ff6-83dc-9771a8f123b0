import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatTableDataSource } from '@angular/material/table';
import { Paciente } from '../../paciente/paciente-model';
import { PacienteService } from '../../paciente/paciente.service';
import { Funcao } from '../../funcao/funcao-model';
import { FuncaoService } from '../../funcao/funcao.service';
import { Profissional } from '../../profissional/profissional-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Component, OnInit } from '@angular/core';

export interface ReportTable {
  paciente: string;
  profissional: string;
  email: string;
  fone: string;
  funcao: string;
}

@Component({
  selector: 'app-pacientes-por-funcao',
  templateUrl: './pacientes-por-funcao.component.html',
  styleUrls: ['./pacientes-por-funcao.component.css']
})


export class PacientesPorFuncaoComponent implements OnInit {

  constructor(private pacienteService: PacienteService,
      // private profissionalService: ProfissionalService,
      private funcaoService: FuncaoService) { }

  public profissionais: Profissional[];
  public funcoes: Funcao[];
  public funcoesSelected: string[];
  // public profissionaisSelected: Profissional[];
  public pacientes: Paciente[];
  public pacientesFiltered: Paciente[] = [];

  displayedColumns: string[] = ['Paciente', 'Profissional', 'E-mail', 'Telefone', 'Função'];
  allColumns: string[] = ['Paciente', 'Profissional', 'E-mail', 'Telefone', 'Função'];
  reportElements: ReportTable[] = [];
  datasource = new MatTableDataSource();

  ngOnInit(): void {
    // this.profissionalService.find().subscribe(profs => {
    //   this.profissionais = profs;
    // })

    this.funcaoService.find().subscribe(fun => {
      this.funcoes = fun;
    })
  }
  toogleColumn(event: MatCheckboxChange){
    this.displayedColumns=[];
    
    // if(event.checked){
    //   this.displayedColumns.push(event.source.value);
    // } else {
    //   this.displayedColumns.splice(this.displayedColumns.indexOf(event.source.value), 1);
    // }
  }


  searchAll(){
    this.reportElements = [];
    this.datasource.data = [...this.reportElements]
    // this.funcoesSelected = []
    this.pacientes = [];
    this.pacienteService.find().subscribe(ps => {
      this.pacientes = ps.filter(p => p.ativo == true);
      //console.log(ps.length);
      // let funcoes = "";
      for(const [, p] of this.pacientes.entries()){
        for(const [, e] of p.equipe.entries()){
          // funcoes = "";
          for(const [, f] of e.funcao.entries()){
            // funcoes += f.nome + "; ";
            this.reportElements.push({
                paciente: p.nome,
                profissional: e.nome,
                email: e.email,
                fone: e.telefone,
                funcao: f.nome
            })
          }
        }
      }
      this.datasource.data = [...this.reportElements]
    });
  }

  search(){
    this.reportElements = [];
    this.datasource.data = [...this.reportElements]
    // this.funcoesSelected = []
    this.pacientes = [];
    // console.log(this.funcoesSelected)
    let manter: boolean;
    this.pacienteService.find().subscribe(ps => {
      for(const [,p] of ps.entries()){
        for(let i=0; i < p.equipe.length; i++){
          manter = false;
          p.equipe[i].funcao = [...p.equipe[i].funcao.filter(f => this.funcoesSelected.includes(f.nome))]
          if(p.equipe[i].funcao.length == 0){
            p.equipe.splice(i, 1);
            i--;
          }
        }
      }
      this.pacientes = ps.filter(p => p.ativo == true);
      for(const [, p] of this.pacientes.entries()){
        for(const [, e] of p.equipe.entries()){
          for(const [, f] of e.funcao.entries()){
            this.reportElements.push({
                paciente: p.nome,
                profissional: e.nome,
                email: e.email,
                fone: e.telefone,
                funcao: f.nome
            })
          }
        }
      }
      this.datasource.data = [...this.reportElements]
      // console.log(this.reportElements);
      // this.pacientes.filter(p => p.equipe)
    })
    // this.profissionaisSelected = this.profissionais.filter(p => p.funcao.filter(this.hasFuncao).length > 0);
  }

  clear(){
    this.reportElements = [];
    this.datasource.data = [...this.reportElements]
    this.funcoesSelected = []
    this.pacientes = [];
  }

}
