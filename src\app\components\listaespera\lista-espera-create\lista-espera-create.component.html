<mat-card> 
    <mat-card-title class="title"><PERSON><PERSON> Candida<PERSON></mat-card-title>
    <form>
        <div fxLayout="row wrap" fxLayoutAlign="space-around stretch" fxFlex="100" fxLayoutGap="10">
            <mat-form-field style="width: 40%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="listaEspera.nome" name="nome">
            </mat-form-field>
            
            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Data de Nascimento" 
                    [(ngModel)]="listaEspera.dataNascimento" name="dataNascimento"
                    [matDatepicker]="picker">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            
            <mat-form-field style="width: 30%;">
                <input class="input" matInput placeholder="Local" 
                    [(ngModel)]="listaEspera.local" name="local">
            </mat-form-field>

            <mat-form-field  style="width: 40%;">
                <input class="input" matInput placeholder="Nome do Responsável" 
                    [(ngModel)]="listaEspera.nomeResponsavel" name="nomeResponsavel">
            </mat-form-field>

            <mat-form-field  style="width: 20%;">
                <input class="input" matInput placeholder="Telefone" 
                    [(ngModel)]="listaEspera.telefone" name="telefone">
            </mat-form-field>

            <mat-form-field  style="width: 30%;">
                <input class="input" matInput placeholder="Email" 
                    [(ngModel)]="listaEspera.email" name="email">
            </mat-form-field>
    
            <mat-form-field  style="width: 10%;">
                <mat-label>Preferência de Turno</mat-label>
                <mat-select class="select" placeholder="preferenciaTurno" 
                    [(ngModel)]="listaEspera.preferenciaTurno"
                    name="preferenciaTurno">
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option value="Manhã" >
                    Manhã
                    </mat-option>
                    <mat-option value="Tarde" >
                        Tarde
                    </mat-option>
                    <mat-option value="Noite" >
                        Noite
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%;">
                <mat-label>Status</mat-label>
                <mat-select class="select" placeholder="status" 
                    [(ngModel)]="listaEspera.status"
                    name="status">
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option value="Novo" >
                    Novo
                    </mat-option>
                    <mat-option value="Aguardando avaliação" >
                        Aguardando avaliação
                    </mat-option>
                    <mat-option value="Em avaliação" >
                        Em avaliação
                    </mat-option>
                    <mat-option value="Aguardando atendimento" >
                        Aguardando atendimento
                    </mat-option>
                    <mat-option value="Em atendimento" >
                        Em atendimento
                    </mat-option>
                    <mat-option value="Desistiu" >
                        Desistiu
                    </mat-option>
                </mat-select>
            </mat-form-field>
            
            <mat-form-field style="width: 15%;">
                <mat-label>Terapeuta</mat-label>
                <mat-select class="select" placeholder="terapeuta" 
                    [(ngModel)]="listaEspera.terapeuta"
                    name="terapeuta">
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option *ngFor="let terapeuta of terapeutas" [value]="terapeuta.nome" >
                    {{terapeuta.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Data do Contato" 
                    [(ngModel)]="listaEspera.dataContato" name="dataContato"
                    [matDatepicker]="picker">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
            </mat-form-field> 

            <mat-form-field style="width: 95%;">
                <textarea class="input" matInput placeholder="Observação" 
                    [(ngModel)]="listaEspera.observacao" name="observacao">
                </textarea>
            </mat-form-field>
        </div>
    </form>
    <button mat-raised-button (click)="createCandidato()" color="primary">
        Salvar
    </button>
    
    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>