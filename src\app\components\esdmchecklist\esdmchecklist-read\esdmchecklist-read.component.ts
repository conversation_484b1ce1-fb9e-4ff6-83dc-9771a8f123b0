import { PacienteService } from './../../paciente/paciente.service';
import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { ESDMChkLstCompetencia } from './../esdmchklst-competencia-model';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { ESDMChecklist } from './../esdmchecklist-model';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { NivelService } from './../../nivel/nivel.service';
import { CompetenciaService } from './../../competencia/competencia.service';
import { EsdmchecklistService } from './../esdmchecklist.service';
import { Paciente } from './../../paciente/paciente-model';
import { forkJoin, Observable } from 'rxjs';
import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef, Input } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'app-esdmchecklist-read',
  templateUrl: './esdmchecklist-read.component.html',
  styleUrls: ['./esdmchecklist-read.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class EsdmchecklistReadComponent implements OnInit {

  public esdmchecklists: ESDMChecklist[];
  
  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  public niveis: Nivel[];
  public dominios: Dominio[];
  public dominiosTab: Dominio[] = [];
  public chklstcompView: ESDMChkLstCompetencia[] = [];

  public esdmchecklist: ESDMChecklist = new ESDMChecklist();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>();

  dataSource: MatTableDataSource<ESDMChkLstCompetencia>;

  viewPDF: boolean = false;

  idPaciente: string;
  idESDMChecklist: string;
  indexAnamnese: number;

  public hasAccessCreate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;
  
  //active = 1;
  
  //activeDominio = 1;

  @Input()
  $pacienteSearch: Observable<Paciente>;
  $idChklstSearch: string;
  
  @ViewChild('esdmchecklistAvaliacao', {static: false})
  el!: ElementRef;

  displayedColumns = ['id', 'nome', 'status']
  idOrganizacao: any;

  constructor(private esdmchecklistService: EsdmchecklistService,
    private competenciaService: CompetenciaService,
    private nivelService: NivelService,
    public authService: AuthService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private pacienteService: PacienteService,
    private loadingService: LoadingService
  ) { }

  ngOnInit(): void {
    this.loadingService.reset();
    let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    this.idESDMChecklist = this.route.snapshot.paramMap.get('idESDMChecklist');
  
    this.loadingService.show();
    this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
  
    // Crie uma lista de observables que vão aguardar a finalização de todas as requisições
    const observables = [];
  
    // Adicione a requisição de paciente à lista de observables
    const pacienteObservable = this.pacienteService.findById(idPaciente).pipe(
      tap(paciente => {
        this.paciente = paciente;
        this.esdmchecklist.idPaciente = paciente.id;
        this.esdmchecklist.paciente = paciente;

      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','create');
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','update');
      this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','delete');
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','read');
      })
    );
    observables.push(pacienteObservable);
  
    // Adicione a requisição de checklist à lista de observables
    const checklistObservable = this.esdmchecklistService.findByPaciente(idPaciente).pipe(
      tap(chklsts => {
        if (chklsts.length > 0) {
          this.esdmchecklists = chklsts.filter(c => c.status != false);
          if (this.idESDMChecklist == undefined) {
            this.esdmchecklist = this.esdmchecklists[0] != null ? this.esdmchecklists[0] : new ESDMChecklist();
          } else {
            this.esdmchecklist = this.esdmchecklists.find(chklst => chklst.id == this.idESDMChecklist);
          }
  
          if (this.esdmchecklist.checklist.length > 0) {
            this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
              (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE'));
          }
        }
      })
    );
    observables.push(checklistObservable);
  
    // Adicione a requisição de níveis e domínios à lista de observables
    const nivelObservable = this.nivelService.find().pipe(
      tap(niveis => {
        this.niveis = niveis;
        this.nivel = this.niveis.find(n => n.id == 'N1');
  
        niveis.forEach(nivel => {
          this.competenciaService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id, dominios);
            this.dominios = this.dominioMap.get('N1');
            if (this.dominios) {
              this.dominio = this.dominios.find(dom => dom.id == 'CRE');
            }
          });
        });
      })
    );
    observables.push(nivelObservable);
  
    // Adicione a requisição de profissionais à lista de observables
    const profissionaisObservable = this.profissionalService.find().pipe(
      tap(profissionais => {
        this.profissionais = profissionais;
      })
    );
    observables.push(profissionaisObservable);
  
    // Utilize forkJoin para garantir que todas as requisições sejam concluídas antes de esconder o loading
    forkJoin(observables).subscribe(() => {
      this.loadingService.hide();
    });
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
   // console.log(funcoes)
    return funcoes;
  }

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];//this.dominios.find(dom => dom.id == 'CRE');  
    this.filterChecklist();

    /*
    this.competenciaService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];
      this.filterChecklist();
    })
    */
  }

  filterChecklist(){
    this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
      (chklst.competencia.id_nivel==this.nivel.id 
        && chklst.competencia.id_dominio == this.dominio.id))
  }

  delete(): void{
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.loadingService.show();
        this.esdmchecklist.status = false;
        this.esdmchecklistService.update(this.esdmchecklist).subscribe(
          () => {
            this.esdmchecklistService.showMessage('Checklist excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/paciente/'+this.paciente.id, {   
              tab: 'checklist'
            }])
            this.loadingService.hide();
          }
        );
      }
    });
  }

  do() {

  }

  edit(){
    // console.log(this.esdmchecklist.id);
    this.router.navigate(['/esdmchecklist/create', {  idPaciente: this.esdmchecklist.idPaciente,
                                                      paciente: this.esdmchecklist.paciente,
                                                      idChecklist: this.esdmchecklist.id
                                                   }])
  }

  new(){
    //console.log(this.esdmchecklist.paciente.id);
    this.router.navigate(['/esdmchecklist/create', {  idPaciente: this.paciente.id,
                                                      paciente: this.paciente }])
  }

  setChecklist(){
    //Carrega novo chechklist no default N1-CRE
    //this.nivelField.value=   //nativeElement.children[0].selected=true;
    //this.dominioField.nativeElement.children[0].selected=true;
    //this.nivel.id = 'N1';
    //this.dominio.id = 'CRE'
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'CRE');
    this.filterChecklist()
  }

  generatePDF() {
    this.loadingService.show();
    this.viewPDF = true;  
  }

  onPDFGenerated() {
    this.viewPDF = false;
  }

  exitEdit(){
    this.router.navigate(['/paciente/' + this.paciente.id, {
      tab:"avaliacao"
    }]);
  }

 /* save(){
    if(this.esdmchecklist.id == undefined){
      this.esdmchecklistService.create(this.esdmchecklist).subscribe(() => {
        this.esdmchecklistService.showMessage('Checklist criado com sucesso!');
        this.router.navigate(['/paciente']);
      });
    } else {
      this.esdmchecklistService.update(this.esdmchecklist).subscribe(() => {
        this.esdmchecklistService.showMessage('Checklist atualizado com sucesso!');
        this.router.navigate(['/paciente']);
      });
    }
    console.log(this.esdmchecklist);
  }

  cancel(){

  }
  */
  /*
  applyFilter(idNivel: string, id_dominio: string) {
    this.dataSource.filterPredicate = (data: ESDMChkLstCompetencia, filter: string) => {
      return data.competencia.id_nivel == filter.substring(0,2) && data.competencia.id_dominio == filter.substring(2,5);
    }
    //const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = idNivel+id_dominio;
  }

  tabChanged(tabChangeEvent: MatTabChangeEvent): void {
    console.log('tabChangeEvent => ', tabChangeEvent);
    console.log('index => ', tabChangeEvent.index);
    console.log('label => ', tabChangeEvent.tab.textLabel);
    
  }

  tabChange(idNivel: string){
    console.log(idNivel)
  }
  */
}
