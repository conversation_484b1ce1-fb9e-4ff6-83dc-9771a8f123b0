import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'secondsFormat'
})
export class SecondsFormatPipe implements PipeTransform {

  transform(value: number): string {
    const hours: number = Math.floor(value / 3600);
    const minutes: number = Math.floor((value % 3600) / 60);
    const seconds: number = value % 60;

    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(seconds).padStart(2, '0');

    if (hours > 0) {
      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}h`;
    } else if (minutes > 0) {
      return `${formattedMinutes}:${formattedSeconds}m`;
    } else {
      return `${formattedMinutes}:${formattedSeconds}s`;
    }
  }
}