import { RespostaHabilidadeAvaliacao } from './resposta-habilidade-avaliacao-model';

export class Avaliacao {
    id?: string;
    data: Date;
    idTipoAvaliacao: string;
    idPaciente: string;
    idProfissional:string;
    respostasHabilidades: RespostaHabilidadeAvaliacao[]; 
    ativo: boolean;

    constructor(){
        this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
        this.respostasHabilidades = [];
        this.ativo = true;
    }
}