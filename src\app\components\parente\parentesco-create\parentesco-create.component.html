<mat-card> 
    <mat-card-title class="title">{{ parentesco.id == undefined ? "Novo Parentesco" : parentesco.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <mat-form-field style="width: 60%; padding: 10px">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="parentesco.nome" name="nome" required>
                <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>  
            </mat-form-field>
        </div>
    </form>

    <button mat-raised-button (click)="save()" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>