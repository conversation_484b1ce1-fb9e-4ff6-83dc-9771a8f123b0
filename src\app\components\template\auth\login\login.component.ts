import { FirebaseUserModel } from './../user-model';
import { AuthService } from './../auth.service';
import { Component, OnInit } from '@angular/core';
import { Router, Params } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {

  ngOnInit(): void {
  }

  get user(): FirebaseUserModel{
    //console.log(this.headerService.user.name);
    //console.log(this.headerService.getUser())
    
    return this.authService.getUser();
    //return this.headerService.getUser();


    /*console.log(this.userService.getCurrentUser())
    this.userService.getCurrentUser()
      .then( (user) => {
        return user;
      }, (error) => {
        console.log('Usuário não está logado.')
        return null;
      });
      return null;*/
  }

  loginForm: FormGroup;
  errorMessage: string = '';

  constructor(
    public authService: AuthService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.createForm();
  }

  createForm() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email] ],
      password: ['',Validators.required]
    });
  }

  tryFacebookLogin(){
    /*
    this.authService.doFacebookLogin()
    .then(res => {
      this.router.navigate(['/user']);
    })
    */
  }

  tryTwitterLogin(){
    /*
    this.authService.doTwitterLogin()
    .then(res => {
      this.router.navigate(['/user']);
    })
    */ 
  }

  tryGoogleLogin(){
    this.authService.doGoogleLogin()
    .then(res => {
      this.router.navigate(['']);
    })
  }

  resetPassword(value){
    //console.log(this.loginForm.get("email").value);
    
    if(this.loginForm.get("email").errors?.email){
      this.errorMessage = "E-mail inválido!"
    } else if(this.loginForm.get("email").errors?.required){
      this.errorMessage = "Digite o e-mail para o qual deseja redefinir a senha!"
    } else {
      this.authService.resetPassword(this.loginForm.get("email").value);
      this.authService.showMessage("E-mail para redefinição de senha enviado com sucesso! Favor acessar sua caixa de e-mail e seguir os passos antes de um novo login.",false);
      this.errorMessage="";
    }
  }

  tryLogin(value){
    this.authService.doLogin(value)
    .then(res => {
      // console.log("Login...")
      // this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      // this.router.onSameUrlNavigation = 'reload'; 
      this.router.navigate(['/home'], {queryParams: {loginRedirect: true}});
      // window.location.reload();
      // this.router.navigate(['/home', {hasFullView: false}]);
    }, err => {
      console.log(err);
      //this.errorMessage = err.message;
      this.errorMessage = "E-mail e/ou senha inválido(s)!";
    })
  }

}
