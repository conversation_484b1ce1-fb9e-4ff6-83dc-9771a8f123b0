import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, NgForm, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../template/auth/auth.service';
import { StatusAgenda } from '../statusagenda-model';
import { StatusAgendaService } from '../statusagenda.service';

@Component({
  selector: 'app-statusagenda-create',
  templateUrl: './statusagenda-create.component.html',
  styleUrls: ['./statusagenda-create.component.css']
})
export class StatusagendaCreateComponent implements OnInit {
  
  
  statusAgenda: StatusAgenda = new StatusAgenda();
  statusAgendaList: StatusAgenda[] = [];

  hasAccessUpdate: boolean = false;

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  confirmaCobrancaFC = new FormControl('', [Validators.required]);
  default = new FormControl(false, [Validators.required]);

  constructor(private statusAgendaService: StatusAgendaService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let idstatusAgenda = this.route.snapshot.paramMap.get('id');  

    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Tipo de Procedimento.Cadastro de tipos de procedimentos','update');

    if(idstatusAgenda == undefined) { //Create
      this.statusAgenda = new StatusAgenda();
      this.statusAgenda.ativo = true;
    } else {  //Edit
        await this.statusAgendaService.findById(idstatusAgenda).subscribe(async statusAgenda => {
          this.statusAgenda = statusAgenda; 
        })
    }

  }

  save(){
    let statusList: StatusAgenda[];
    let statusUpdate: StatusAgenda;

    if(this.statusAgenda.default){
      this.statusAgendaService.find().subscribe(lista => {
        
         statusList = lista;

         statusUpdate = statusList.find(status => status.default && status.id != this.statusAgenda.id)

         if(statusUpdate){
           statusUpdate.default = false;
           
           this.statusAgendaService.update(statusUpdate).subscribe(retorno => {
            this.saveStatus()
           });
           
         }
      })
      
    }else{
      this.saveStatus();
    }

    
  }

   async saveStatus(){

    await this.verificaValorDefault();

    let valid: boolean = true;
    if(this.form.valid){  
      if(this.statusAgenda.id == undefined){
        this.statusAgendaService.create(this.statusAgenda).subscribe(() => {
          this.statusAgendaService.showMessage('Status criado com sucesso!');
          this.router.navigate(['/status-agenda']);
        });
      } else {
          this.statusAgendaService.update(this.statusAgenda).subscribe(() => {
          this.statusAgendaService.showMessage('Status alterado com sucesso!');
          this.router.navigate(['/status-agenda']);
        });
      }
        
    } else {
      this.statusAgendaService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/status-agenda']);
  }

  async verificaValorDefault(){
    
  }

}