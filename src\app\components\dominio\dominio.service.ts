import { environment } from './../../../environments/environment';
import { Dominio } from './dominio-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DominioService {

  dominiolUrl = `${environment.API_URL}/dominio`;
  
  public dominios: BehaviorSubject<Dominio[]> = 
    new BehaviorSubject<Dominio[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(dominio: Dominio): Observable<Dominio>{
    // console.log(dominio);
    return this.http.post<Dominio>(this.dominiolUrl, dominio).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Dominio>{
    return this.http.get<Dominio>(this.dominiolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Dominio[]>{
    return this.http.get<Dominio[]>(this.dominiolUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Dominio>{
    return this.http.delete<Dominio>(this.dominiolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}