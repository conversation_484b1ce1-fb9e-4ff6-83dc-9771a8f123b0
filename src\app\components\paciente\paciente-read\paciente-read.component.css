.header-image {
    background-size: cover;
}

.card {
    max-width: 400px;
    border: none;
}

.subtitle{
  font-weight: 500;
  font-size: large;
}


form{
    display: flex;
    flex-flow: row wrap;
}



mat-form-field{
    padding: 10px;
}

div {
    display: flex;
    flex-direction: row;
    flex-flow: row wrap;
    vertical-align: bottom;
    align-items: flex-end;
}

.example-radio-group {
    display: flex;
    flex-direction: row;
    margin: 15px 0;
  }
  
  .example-radio-button {
    margin: 1px;
  }

  .example-form {
    min-width: 150px;
    max-width: 500px;
    width: 100%;
  }
  
  .example-full-width {
    width: 100%;
  }

  form {
    display: flex;
    flex-direction: column;
  }

  .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin: .5em;
}

.label-aviso {
    background-color: #ff902b;
}

.label-ok {
    background-color: #27c24c;
}

.label-atrasado {
    background-color: #f05050;
}

mat-form-field{
  padding: 10px;
  font-weight: 300;
  font-size: small;
}

.mat-card-title{
  text-overflow: ellipsis;
  overflow: hidden;
  
}

.md-mini-fab-right {
  top: 10px !important;
  right: 20px !important;
  left: auto !important;
  bottom: auto !important;
  position: absolute !important;
}

button{
  margin-right: 15px;
  margin-top: 20px;
}