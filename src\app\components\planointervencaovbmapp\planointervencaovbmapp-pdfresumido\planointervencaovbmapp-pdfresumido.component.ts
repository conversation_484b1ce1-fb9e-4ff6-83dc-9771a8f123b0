import { LoadingService } from './../../../shared/service/loading.service';
import { OrganizacaoService } from './../../organizacao/organizacao.service';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { DominioService } from './../../dominio/dominio.service';
import { Objetivo } from './../../objetivo/objetivo-model';
import { Dominio } from './../../dominio/dominio-model';
import { Router, ActivatedRoute } from '@angular/router';
import { PlanoIntervencaoVBMAPP } from './../planointervencaovbmapp-model';
import { PlanointervencaovbmappService } from './../planointervencaovbmapp.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import * as moment from 'moment';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';

@Component({
  selector: 'app-planointervencaovbmapp-pdfresumido',
  templateUrl: './planointervencaovbmapp-pdfresumido.component.html',
  styleUrls: ['./planointervencaovbmapp-pdfresumido.component.css']
})
export class PlanointervencaovbmappPdfresumidoComponent implements OnInit {

  public planointervencao: any;
  public dominioMap: Map<string, Objetivo[]> = new Map<string, Objetivo[]>();
  public dominios: Dominio[];
  public data: Date = new Date();
  public dia: string;
  public mes: string;
  public ano: string; 
  public assinatura: string; 
  public profissional: Profissional;

  @ViewChild("pdf") htmlData: ElementRef;
  objetivosOrdenados: any;
  tiposAvaliacoes: import("../../avaliacao/tipo-avaliacao-model").TipoAvaliacao[];
  paciente: import("../../paciente/paciente-model").Paciente;

  constructor(
    private planointervencaovbmappService: PlanointervencaovbmappService,
    private profissionalService: ProfissionalService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private loadingService: LoadingService,
    public authService: AuthService,
    private organizationService: OrganizacaoService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  async ngOnInit(): Promise<void> {
    let idPlano = this.route.snapshot.paramMap.get("id");
    moment.locale('pt-br');
    this.dia = moment(this.data).format('DD');
    this.mes = moment(this.data).format('MMMM');
    this.ano = moment(this.data).format('YYYY');

    try {
      this.loadingService.show();
      let tiposAvaliacoes = await this.tipoAvaliacaoService.find().toPromise();
      this.tiposAvaliacoes = tiposAvaliacoes.filter(ta => ta.ativo);
  
      this.planointervencaovbmappService.findById(idPlano).subscribe(plano => {
        this.planointervencao = plano;
        this.paciente = plano.paciente;
        this.profissionalService.findById(plano.idProfissional).subscribe(p => {
          this.profissional = p;
          if(this.profissional.assinatura != undefined && this.profissional.assinatura != ""){
            this.assinatura = this.profissional.assinatura.replace(/\r\n|\r|\n/g," <br /> ")
            document.getElementById('assinatura').innerHTML = this.assinatura;
          } else {
            this.organizationService.findById(this.authService.user.organizationId).subscribe(o => {
              this.assinatura =   this.profissional.nome + "<br />" +
                                  o.nome
              document.getElementById('assinatura').innerHTML = this.assinatura;
            })
          }
        })
        this.ordenarObjetivos();
      })
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  get user(): FirebaseUserModel{
    return this.authService.getUser();
  }

  async ordenarObjetivos() {    
    this.objetivosOrdenados = this.planointervencao?.objetivos;

    this.objetivosOrdenados?.forEach(obj => {
      if (!obj?.habilidades?.length) {
        obj.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == obj.idTipoAvaliacao)?.nome;
      } else {
        obj.habilidades.forEach(h => h.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == obj.habilidades[0].idTipoAvaliacao)?.nome);
      } 
    });
    
    this.objetivosOrdenados?.sort((a, b) => {
      const getNomeTipoAvaliacao = (obj) => 
        obj.habilidades?.length ? obj.habilidades[0].nomeTipoAvaliacao.replace(/\[.*?\]\s*/, '') || "" : "";
    
      let nomeTipoAvaliacaoA = getNomeTipoAvaliacao(a);
      let nomeTipoAvaliacaoB = getNomeTipoAvaliacao(b);
    
      // Ajusta nome do ESDM para garantir ordenação correta sem empurrá-lo para o final
      if (a.idTipoAvaliacao === "5") {
        nomeTipoAvaliacaoA = "Early Start Denver Model";
      }
      if (b.idTipoAvaliacao === "5") {
        nomeTipoAvaliacaoB = "Early Start Denver Model";
      }
    
      // Ordena pelo nome do tipo de avaliação (alfabética)
      const compareTipoAvaliacao = nomeTipoAvaliacaoA.localeCompare(nomeTipoAvaliacaoB);
      if (compareTipoAvaliacao !== 0) return compareTipoAvaliacao;

      // Ordenação para objetivos ESDM
      if (a.idTipoAvaliacao === "5" || b.idTipoAvaliacao === "5") {
        const dominioA = a.dominio?.ordem || Infinity;
        const dominioB = b.dominio?.ordem || Infinity;
        
        if (dominioA !== dominioB) {
          return dominioA - dominioB; // Ordena por domínio
        }
  
        const nivelA = parseInt(a.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        const nivelB = parseInt(b.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        
        if (nivelA !== nivelB) {
          return nivelA - nivelB; // Ordena por nível
        }
  
        const idA = parseInt(a.id.replace(/\D/g, ''), 10) || Infinity;
        const idB = parseInt(b.id.replace(/\D/g, ''), 10) || Infinity;
  
        return idA - idB; // Ordena pelo número final do ID
      }
    
      // Se os tipos de avaliação forem iguais, ordena pelo ID do dominio da primeira habilidade (numérico)
      const idDominioA = a.habilidades?.length ? a.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      const idDominioB = b.habilidades?.length ? b.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      
      if (idDominioA !== idDominioB) return idDominioA - idDominioB;

      // Se os tipos de avaliação forem iguais, e o dominio for igual, ordena pelo ID do nivel da primeira habilidade (numérico)
      const idNivelA = a.habilidades?.length ? a.habilidades[0].idNivelAvaliacao || Infinity : Infinity;
      const idNivelB = b.habilidades?.length ? b.habilidades[0].idNivelAvaliacao || Infinity : Infinity;

      if (idNivelA !== idNivelB) return idNivelA - idNivelB;
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID da primeira habilidade (numérico)
      const idHabilidadeA = a.habilidades?.length ? parseInt(a.habilidades[0].id, 10) || Infinity : Infinity;
      const idHabilidadeB = b.habilidades?.length ? parseInt(b.habilidades[0].id, 10) || Infinity : Infinity;
    
      return idHabilidadeA - idHabilidadeB;
    });
  }

}