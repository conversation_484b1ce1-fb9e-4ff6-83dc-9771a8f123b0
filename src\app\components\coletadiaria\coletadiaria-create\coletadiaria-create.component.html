<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <!-- INÍCIO DADOS BÁSICOS DA COLETA -->
        <div fxLayout="column" fxLayoutAlign="space-between stretch"> 
            <form #ngForm>
                <mat-form-field  class="paciente-field">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" 
                        [(ngModel)]="paciente.id"
                        name="paciente" disabled required>
                        <mat-option [value]="paciente.id" >
                            {{paciente.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  class="profissional-field">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="idProfissional"
                        name="profissionalFC" required
                        (selectionChange) = "setProfissional($event)">
                        <mat-option></mat-option>
                        <ng-container *ngFor="let profissional of profissionaisDoPaciente">
                            <mat-option [value]="profissional.id">
                                {{profissional.nome}}
                            </mat-option>
                        </ng-container>
                    </mat-select>
                    <mat-error *ngIf="profissionalFC.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  class="data-field">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data" 
                        [min] = "planoIntervencao.data"
                        [max] = "today"
                        [(ngModel)]="data" name="data"
                        (dateChange)="setColetasPorData()"
                        [matDatepicker]="picker" required [attr.disabled] = "cantChangeData">
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="dataFC.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field  class="plano-intervencao-field">
                    <mat-label>Plano de Intervcenção</mat-label>
                    <mat-select placeholder="Plano de Intervenção" 
                        [(ngModel)]="planoIntervencao.id" disabled
                        name="plano" >
                        <mat-option [value]="planoIntervencao.id" >
                            {{planoIntervencao.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <button mat-mini-fab alt="Sair da edição" color="primary" class="back-arrow" style="margin-left: 30px;" (click)="exitEdit()">
                    <mat-icon>arrow_back</mat-icon>
                </button>
            </form>  

        </div>
        <!-- FIM DADOS BÁSICOS DA COLETA -->

        <!-- INÍCIO DAS SESSÕES -->
        <div class="mat-elevation-z0">
            <button mat-button
                    color="primary"
                    (click)="addColetaDiaria()"
                    class="new-session v-middle">
                    <mat-icon>add</mat-icon>
                    Nova sessão
            </button>
            <mat-tab-group [selectedIndex]="selected.value"
               (selectedIndexChange)="selected.setValue($event)">
                <mat-tab *ngFor="let tab of tabs; let idxSessao = index" [label]="tab">
                    <!-- INÍCIO DE SESSÃO -->
                    <div style="margin-top: 10px; display: flex; flex-direction: row; flex-wrap: wrap;">
                        <mat-form-field  class="hora-inicio-field">
                            <mat-label>Hora Início</mat-label>
                            <mat-select placeholder="Hora Início" 
                                [(ngModel)]="coletasdiarias[idxSessao].horaInicio"
                                name="horainicio" >
                                <ng-container *ngFor="let hour of hours">
                                    <mat-option [value]="hour">
                                        {{hour}}
                                    </mat-option>
                                </ng-container>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field  class="hora-fim-field">
                            <mat-label>Hora Término</mat-label>
                            <mat-select placeholder="Hora Término" 
                                [(ngModel)]="coletasdiarias[idxSessao].horaTermino"
                                name="horatermino" >
                                <ng-container *ngFor="let hour of hours">
                                    <mat-option [value]="hour">
                                        {{hour}}
                                    </mat-option>
                                </ng-container>
                            </mat-select>
                        </mat-form-field>
                        <button mat-button color="primary" (click)="startSession(idxSessao)"
                            class="begin-session v-middle"
                            *ngIf="coletasdiarias[idxSessao].id == undefined">
                            <mat-icon>timer</mat-icon>
                            Iniciar sessão
                        </button>
                    </div>
                    <div style="display: flex; flex-direction: row; flex-wrap: wrap;">
                        <table class="dominio">
                        <!-- DOMÍNIOS, OBJETIVOS E ETAPAS -->
                        <ng-container *ngFor="let idDominio of dominioMap[idxSessao].keys()">
                                <tr>
                                    <td class="dominio">
                                        {{ dominioMap[idxSessao].get(idDominio)[0].dominio.nome.toUpperCase() }}
                                    </td>
                                </tr>
                                <div *ngFor="let objetivo of dominioMap[idxSessao].get(idDominio)">
                                    <tr>
                                        <td [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
                                            {{ objetivo.id }} - {{ objetivo.nome }}
                                            <ng-container *ngIf="objetivo.status == 'Adquirido'">
                                                <table class="etapa">
                                                    <tr>
                                                        <td class="etapa v-middle">
                                                            <mat-icon>construction</mat-icon>
                                                            Objetivo em manutenção
                                                        </td>
                                                    </tr>
                                                </table>
                                            </ng-container>
                                            <div *ngFor="let etapa of objetivo.etapa; let i=index">
                                                <table class="etapa">
                                                    <tr>
                                                        <th></th>
                                                        <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                        .etapa[coletasdiarias[idxSessao].objetivos
                                                                                            [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                            .etapa.indexOf(etapa)].periodo; let idx = index">
                                                            <th class="periodo">P{{idx+1}}</th>
                                                        </ng-container>
                                                    </tr>
                                                    <tr>
                                                        <td class="etapa">{{ coletasdiarias[idxSessao].objetivos
                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                .etapa[coletasdiarias[idxSessao].objetivos
                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                        .etapa.indexOf(etapa)].id.slice(-3) }} - 
                                                            {{ coletasdiarias[idxSessao].objetivos
                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                    .etapa[coletasdiarias[idxSessao].objetivos
                                                                            [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                            .etapa.indexOf(etapa)].nome }}
                                                        </td>
                                                        <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                        .etapa[coletasdiarias[idxSessao].objetivos
                                                                                            [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                            .etapa.indexOf(etapa)].periodo; let idxP = index">
                                                            <td style="text-align: center;" [matTooltip]="!coletasdiarias[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : ''">
                                                                <a style="text-decoration: none; cursor: pointer;" (click)="setRespostaPeriodo(idxSessao, idxP, objetivo, etapa)"
                                                                [ngClass]="{'disabled': !coletasdiarias[idxSessao]?.id}">
                                                                    <mat-icon>{{ getPeriodoIcon(periodo) }}</mat-icon>
                                                                </a>
                                                            </td>
                                                        </ng-container>
                                                    </tr>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </div>
                            </ng-container>
                        </table>
                    </div>
                    <!-- FIM DE SESSÃO -->
                </mat-tab>
            </mat-tab-group>
        </div>    
        <div style="width: 100%; display: table; margin: 0%; margin-top: 10px;">
            <div style="display: table-row-group;">
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SFT – Suporte físico total
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            SFP – Suporte físico parcial
                        </div>
                </div>
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SV – Suporte verbal
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            SG – Suporte gestual
                        </div>
                </div>
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SP – Suporte posicional
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            &nbsp;
                        </div>
                </div>
            </div>
        </div>
        <!-- FIM DAS SESSÕES -->    
    </mat-card-content>
    
    <!--mat-card-actions>
        <button mat-raised-button color="primary" (click)="save(-1)">Salvar</button>
        <button mat-raised-button (click)="cancel()">Cancelar</button>
      </mat-card-actions-->
</mat-card>