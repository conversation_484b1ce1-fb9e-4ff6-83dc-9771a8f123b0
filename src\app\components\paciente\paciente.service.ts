import { AuthService } from './../template/auth/auth.service';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Paciente } from './paciente-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import * as firebase from 'firebase/app';
import { auth } from 'firebase';

@Injectable({
  providedIn: 'root'
})

//TODO: Não permitir pacientes com o mesmo nome

export class PacienteService {

  pacienteUrl = `${environment.API_URL}/paciente`;
  
  public pacientes: BehaviorSubject<Paciente[]> = 
    new BehaviorSubject<Paciente[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    console.log(e)
    return EMPTY;
  }

  create(paciente: Paciente): Observable<Paciente>{
    //console.log(paciente);
    return this.http.post<Paciente>(this.pacienteUrl, paciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(paciente: Paciente): Observable<Paciente>{
    //console.log(paciente);
    return this.http.put<Paciente>(this.pacienteUrl + "/" + paciente.id, paciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Paciente>{
    return this.http.get<Paciente>(this.pacienteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Paciente[]>{
    return this.http.get<Paciente[]>(this.pacienteUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }

  getResumoPacientes(): Observable<any[]> {
    return this.http.get<any[]>(`${this.pacienteUrl}/resumo`).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }

  delete(id: string): Observable<Paciente>{
    return this.http.delete<Paciente>(this.pacienteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  //retorna a idade do paciente em meses
  getIdadeMeses(dataNasc: Date){
    let dataNascimento = new Date(dataNasc);
    return ((new Date().getFullYear() - dataNascimento.getFullYear())*12)+(new Date().getMonth()-dataNascimento.getMonth())
  }
}