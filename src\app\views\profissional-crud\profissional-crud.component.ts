import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-profissional-crud',
  templateUrl: './profissional-crud.component.html',
  styleUrls: ['./profissional-crud.component.css']
})
export class ProfissionalCrudComponent implements OnInit {

  constructor(private router: Router,
    public headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Profissional',
        icon: 'assignment_ind',
        routeUrl: '/profissional'
      }
    }

  ngOnInit(): void {
  }

}
