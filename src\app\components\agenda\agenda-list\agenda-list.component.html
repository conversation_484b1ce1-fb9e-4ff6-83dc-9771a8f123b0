<mwl-demo-utils-calendar-header [(view)]="view" [(viewDate)]="viewDate">
</mwl-demo-utils-calendar-header>

<div>
  
  <div [ngSwitch]="view" style="width: 75%; float: right;">
      <mwl-calendar-month-view
        *ngSwitchCase="CalendarView.Month"
        [viewDate]="viewDate"
        [events]="events"
        [refresh]="refresh"
        [activeDayIsOpen]="activeDayIsOpen"
        [dayStartWeek] = 1
        (dayClicked)="dayClicked($event.day)"
        (eventClicked)="handleEvent('Clicked', $event.event)"
        (eventTimesChanged)="eventTimesChanged($event)"
      >
      </mwl-calendar-month-view>
      <mwl-calendar-week-view
        *ngSwitchCase="CalendarView.Week"
        [viewDate]="viewDate"
        [events]="events"
        [refresh]="refresh"
        [dayStartHour]="dayStartHour"
        [dayEndHour]="dayEndHour"
        (eventClicked)="handleEvent('Clicked', $event.event)"
        (eventTimesChanged)="eventTimesChanged($event)"
      >
      </mwl-calendar-week-view>
      <mwl-calendar-day-view
        *ngSwitchCase="CalendarView.Day"
        [viewDate]="viewDate"
        [events]="events"
        [refresh]="refresh"
        [dayStartHour]="dayStartHour"
        [dayEndHour]="dayEndHour"
        (eventClicked)="handleEvent('Clicked', $event.event)"
        (eventTimesChanged)="eventTimesChanged($event)"
      >
      
      </mwl-calendar-day-view>
      
      <button mat-fab  class="md-fab-bottom-right2" routerLink="/atendimento" color="primary">
        <mat-icon>add</mat-icon>
      </button>
    </div>

</div>

<div style="width: 20%; text-align:left; margin-left: 5px;" *ngIf="view == CalendarView.Day">
  <mat-card>
    <mat-calendar [selected]="viewDate" (selectedChange)="viewDate = $event"></mat-calendar>
  </mat-card>      
</div>

<div style="width: 20%; text-align:left; margin-left: 5px; padding-top: 30px;">
  <mat-card>
    <h3><strong> Filtro </strong></h3>
    <hr>
    <mat-card-content>
      <mat-radio-group>
        <mat-radio-button (change)="filtrarCalendario('CLINICA')" value="1">Clínica</mat-radio-button>
        <br>
        <br>
        <mat-radio-button (change)="filtrarCalendario('PROFISSIONAL')" value="2" checked>Profissional</mat-radio-button>
      </mat-radio-group>
     
    </mat-card-content>    
  </mat-card>      
</div>
