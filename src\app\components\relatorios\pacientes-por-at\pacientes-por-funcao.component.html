<mat-card>
    <mat-card-header>
        <mat-card-title><PERSON><PERSON><PERSON> (por função)</mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
            <form #ngForm>
                <mat-form-field  style="width: 20%; padding: 10px;">
                    <mat-label>Funções</mat-label>
                    <mat-select class="select" placeholder="Funções" 
                        [(ngModel)]="funcoesSelected"
                        name="funcoes" multiple>
                        <mat-option *ngFor="let funcao of funcoes" [value]="funcao.nome" >
                            {{ funcao.nome }}    
                        </mat-option>
                    </mat-select>
        
                </mat-form-field>
                
                <!-- <ng-container *ngFor="let column of allColumns">
                    <mat-checkbox [checked]="displayedColumns.includes(column)" 
                        (change)="toogleColumn($event)"
                        [value]="column">
                        {{ column }}
                    </mat-checkbox>
                </ng-container> -->
                <mat-form-field  style="width: 20%; padding: 10px;">
                    <mat-label>Colunas a exibir</mat-label>
                    <mat-select class="select" placeholder="Colunas a exibir" 
                        [(ngModel)]="displayedColumns"
                        name="colunas" multiple>
                        <mat-option *ngFor="let coluna of allColumns" [value]="coluna" >
                            {{ coluna }}    
                        </mat-option>
                    </mat-select>
        
                </mat-form-field>

                <button mat-icon-button (click)="search()" style="padding: 10px;" color="primary" matTooltip="Buscar">
                    <mat-icon>search</mat-icon>
                </button>

                <!-- <button mat-icon-button (click)="searchAll()" style="padding: 10px;" color="primary" matTooltip="Buscar Todos">
                    <mat-icon>list</mat-icon>
                </button> -->
                
                <button mat-icon-button (click)="clear()" style="padding: 10px;" color="primary" matTooltip="Limpar filtro">
                    <mat-icon>clear</mat-icon>
                </button>
            </form>
        </div>
        
        <table mat-table [dataSource]="datasource" style="width: 100%;" class="mat-elevation-z8"
            *ngIf="datasource.data.length > 0">
            <!-- Paciente Column -->
            <ng-container matColumnDef="Paciente">
                <th mat-header-cell *matHeaderCellDef> Paciente </th>
                <td mat-cell *matCellDef="let element"> {{element.paciente}} </td>
            </ng-container>
            
            <!-- Profissional Column -->
            <ng-container matColumnDef="Profissional">
                <th mat-header-cell *matHeaderCellDef> Profissional </th>
                <td mat-cell *matCellDef="let element"> {{element.profissional}} </td>
            </ng-container>
            
            <!-- Email Column -->
            <ng-container matColumnDef="E-mail">
                <th mat-header-cell *matHeaderCellDef> E-mail </th>
                <td mat-cell *matCellDef="let element"> {{element.email}} </td>
            </ng-container>
            
            <!-- Profissional Column -->
            <ng-container matColumnDef="Telefone">
                <th mat-header-cell *matHeaderCellDef> Telefone </th>
                <td mat-cell *matCellDef="let element"> {{element.fone}} </td>
            </ng-container>
            
            <!-- Função Column -->
            <ng-container matColumnDef="Função">
                <th mat-header-cell *matHeaderCellDef> Função </th>
                <td mat-cell *matCellDef="let element"> {{element.funcao}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
        <!-- <div #pdf style="display: flex; flex-direction: column; width: 100%;">     
            <ng-container *ngFor="let paciente of pacientes">
                <ng-container *ngFor="let profissional of paciente.equipe">
                    {{ paciente.nome }} - {{ profissional.nome }}
                    <ng-container *ngFor="let funcao of profissional.funcao">
                        - {{ funcao.nome }}
                    </ng-container>
                    <br>
                </ng-container>
            </ng-container>
        </div> -->
    </mat-card-content>
</mat-card>