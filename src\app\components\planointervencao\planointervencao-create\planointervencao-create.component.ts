import { FirebaseUserModel } from './../../template/auth/user-model';
import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { Etapa } from './../../etapa/etapa-model';
import { MatTableDataSource } from '@angular/material/table';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { EsdmchecklistService } from './../../esdmchecklist/esdmchecklist.service';
import { ESDMChkLstCompetencia } from './../../esdmchecklist/esdmchklst-competencia-model';
import { ESDMChecklist } from './../../esdmchecklist/esdmchecklist-model';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { Paciente } from './../../paciente/paciente-model';
import { Objetivo } from './../../objetivo/objetivo-model';
import { PlanoIntervencao } from './../planointervencao-model';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { PacienteService } from './../../paciente/paciente.service';
import { NivelService } from './../../nivel/nivel.service';
import { ObjetivoService } from './../../objetivo/objetivo.service';
import { PlanoIntervencaoService } from './../planointervencao.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { MatSelectChange } from '@angular/material/select';
import { ObjetivoCreateComponent } from '../../objetivo/objetivo-create/objetivo-create.component';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-planointervencao-create',
  templateUrl: './planointervencao-create.component.html',
  styleUrls: ['./planointervencao-create.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class PlanointervencaoCreateComponent implements OnInit {

  public planointervencao: PlanoIntervencao = new PlanoIntervencao();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();

  checkFiltro: boolean = false;

  //public pacientes: Paciente[];
  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  profissionaisDoPaciente: Profissional[];
  public niveis: Nivel[];
  public dominios: Dominio[];
  public objetivosMap: Map<string,Objetivo> = new Map<string,Objetivo>();
  public chklstcompView: ESDMChkLstCompetencia[] = [];
  public esdmchecklists: ESDMChecklist[];
  public esdmchecklist: ESDMChecklist = new ESDMChecklist();
  public idPaciente: string;

  public hasAccessCreate: boolean;

  saveDisabled: boolean;

  //Form Controls
  data = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  datasourceObjetivos = new MatTableDataSource();

  displayedColumnsChkLst = ['id', 'nome', 'status']
  displayedColumnsObjs = ['index', 'expand', 'etapas', 'idNome', 'action']

  constructor(private planointervencaoService: PlanoIntervencaoService,
    private objetivoService: ObjetivoService,
    private esdmchecklistService: EsdmchecklistService,
    private nivelService: NivelService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

    public isTableExpanded = false;

    toggleTableRow(row: any){
      //console.log(row)
      //this.datasourceObjetivos.data.find(o => o.id ==row.id)
      row.isExpanded = !row.isExpanded;
    }

    toggleTableRows() {
      this.isTableExpanded = !this.isTableExpanded;
  
      this.datasourceObjetivos.data.forEach((row: any) => {
        row.isExpanded = this.isTableExpanded;
      })
    }

    percentualEtapas(row: Objetivo){
      return this.countEtapasAdquiridas(row) / this.countEtapas(row)
    }

    countEtapas(row: Objetivo){
      //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
      return row.etapa.length
    }

    countEtapasAdquiridas(row: Objetivo){
      return row.etapa.filter(e => (e.status=='Adquirida')).length
    }

    ngOnInit(): void {
      this.loadingService.show();
      this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
      this.planointervencao.idPaciente = this.idPaciente;
  
      let idPlanoIntervencao = this.route.snapshot.paramMap.get('idPlanoIntervencao');

      //Carrego os profissionais vinculados ao paciente
      this.pacienteService.findById(this.idPaciente).subscribe(paciente => {
        this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create')

        this.profissionaisDoPaciente = paciente.equipe;
      })

        //Carregando Profissionais
        this.profissionalService.find().subscribe(profissionais => {
          this.profissionais = profissionais;
          if(idPlanoIntervencao == undefined) {
            //Caso seja um profisisonal, seto como o criador
            if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
              if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
                this.planointervencao.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
                this.planointervencao.idProfissional = this.planointervencao.profissional.id;
              }
            }
          }
        })

      //Aplicando a data de hoje ao Plano de Intervenção
      this.planointervencao.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
  
      if(idPlanoIntervencao != undefined) {
        //Carrego o plano de intervenção a ser editado
        this.planointervencaoService.findById(idPlanoIntervencao).subscribe(plano => {
          this.planointervencao = plano;
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          //console.log(this.planointervencao)

        //ColapseAll nos objetivos
        this.planointervencao.objetivos.forEach(o => {
          o.isExpanded = false;
        })

          /*this.chklstcompView = this.planointervencao.checklist.filter(chklst => 
            (chklst.objetivo.id_nivel=='N1' && chklst.objetivo.id_dominio == 'CRE'))*/
        })
      }

      //Carregando os Objetivos
      this.objetivoService.find().subscribe(objetivos => {
        for(let objetivo of objetivos){
          this.objetivosMap.set(objetivo.id, objetivo)
        }
      })
      
  
      //Carregando Competências do último checklist 
      //console.log(idPaciente);
      //console.log(moment(new Date()).format('YYYY-MM-DD'))
      this.esdmchecklistService.findLastByPacienteData(this.idPaciente, moment(new Date()).format('YYYY-MM-DD')).subscribe(chklsts => {
        //console.log(chklsts)
        if(chklsts.length > 0){
          this.esdmchecklists = chklsts.filter(msassmt => msassmt.status != false);
          
          if (this.esdmchecklists.length > 0){
            //Atribuindo o primeiro checklist para visualização
            this.esdmchecklist = this.esdmchecklists[0];
            //console.log(this.esdmchecklist)
  
            //Atribuindo a primeira visão de competências (N1-CRE)
            this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
              (chklst.competencia.id_nivel=='N1' && chklst.competencia.id_dominio == 'CRE'))
            }
          }
      })
  
  
      //Carregando Níveis
      this.nivelService.find().subscribe(niveis => {
        this.niveis = niveis;
        this.nivel = this.niveis.find(n => n.id == 'N1');
      })
  
      //Carregando Domínios
      this.objetivoService.findDominiosByNivel("N1").subscribe(dominios => {
        this.dominios = dominios;
        this.dominio = this.dominios.find(dom => dom.id == 'CRE');
      })
  
      //Carregando Pacientes
      /*this.pacienteService.find().subscribe(pacientes => {
        this.pacientes = pacientes;
        this.planointervencao.paciente = this.pacientes.find(p => p.id == idPaciente)
        this.planointervencao.idPaciente = idPaciente;
      })*/
      this.pacienteService.findById(this.idPaciente).subscribe(paciente => {
        this.paciente = paciente;
        this.planointervencao.paciente = paciente;
        this.planointervencao.idPaciente = this.idPaciente;
      })
      this.loadingService.hide();
    }

    getFuncoesUsuario(paciente: Paciente): string[]{
      let funcoes: string[] = [];
  
      let profissional: Profissional;
  
      //Verifico se existe uma equipe estabelecida
      if(paciente.equipe != undefined){
        //Procuro o usuário logado dentro da equipe
        profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
    
        //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
        if (profissional != undefined){  
          paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
            funcoes.push(funcao.nome);
          })
        }
      }
  
     // console.log(funcoes)
  
  
      return funcoes;
    }

    addObjetivo(idObjetivo: any){
      if(this.validForm()){
        //Verifico se o array de objetivos está vazio. Caso esteja, inicializo.
        if(this.planointervencao.objetivos == undefined){
          this.planointervencao.objetivos = [];
        }
        //console.log(idObjetivo)
        //console.log(this.objetivosMap.get(idObjetivo))
        //Verifico se o objetivo já foi adicionado anteriormente
        if( this.planointervencao.objetivos.find(obj => obj.id == idObjetivo) == undefined) {
          this.planointervencao.objetivos.push(this.objetivosMap.get(idObjetivo))

          //Ordenando o array de objetivos
          this.planointervencao.objetivos.sort(function (a, b) {
            //console.log( ( (a.dominio.ordem.toString().length == 2 ? "0" + a.dominio.ordem : a.dominio.ordem) + a.id_dominio + a.id_nivel + a.id.substr(4,2) ) )
            if( ( (a.dominio.ordem.toString().length == 2 ? "0" + a.dominio.ordem : a.dominio.ordem) + a.id_dominio + a.id_nivel + a.id.substr(4,2) ) 
                  > ( (b.dominio.ordem.toString().length == 2 ? "0" + b.dominio.ordem : b.dominio.ordem) + b.id_dominio + b.id_nivel + b.id.substr(4,2) ) ) {
                    return 1;
            }
            if( ( (a.dominio.ordem.toString().length == 2 ? "0" + a.dominio.ordem : a.dominio.ordem) + a.id_dominio + a.id_nivel + a.id.substr(4,2) ) 
                  < ( (b.dominio.ordem.toString().length == 2 ? "0" + b.dominio.ordem : b.dominio.ordem) + b.id_dominio + b.id_nivel + b.id.substr(4,2) ) ) {
                    return -1;
            }
            return 0;
          })
          /*
          results.sort(function (a, b) {
          if (a.ordem > b.ordem) {
            return 1;
          }
          if (a.ordem < b.ordem) {
            return -1;
          }
          // a must be equal to b
          return 0;
        });
          */
          this.planointervencao.objetivos = [...this.planointervencao.objetivos];
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
          this.save();
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    }

    async deleteObjetivo(id: number){
      if(this.validForm()){
        this.planointervencao.objetivos.splice(id, 1);
        this.planointervencao.objetivos = [...this.planointervencao.objetivos];
        this.datasourceObjetivos.data = this.planointervencao.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        await this.save();
        // Verifica se o plano de intervensão esta sem objetivos, se sim apaga o plano, para evitar planos salvos sem objetivos.
        if(this.planointervencao.objetivos.length == 0){
          this.planointervencaoService.delete(this.planointervencao.id).subscribe(retorno => {
          });
        }
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
      }
    }

    editarObjetivo(id: number){
      localStorage.removeItem('objetivoPersonalizado');

       var objetivo:Objetivo = this.datasourceObjetivos.data[id] as Objetivo;
       this.openDialog(objetivo, id);
       
      // this.router.navigate(['/objetivo/update/' + this.planointervencao.objetivos[id].id])
    }

    openDialog(objetivo: Objetivo, id: number) {
      this.dialog.open(ObjetivoCreateComponent, {
       data:{
        obj: objetivo
       } 
      }).afterClosed().subscribe(() => {
        if(localStorage.getItem("objetivoPersonalizado")){
          this.planointervencao.objetivos[id] = (JSON.parse(localStorage.getItem("objetivoPersonalizado")) as Objetivo);
          this.datasourceObjetivos.data = this.planointervencao.objetivos;
          this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
          this.save();
        }
      });      
    }

    setChecklistPorData(){

      let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
  
      let idPlanoIntervencao = this.route.snapshot.paramMap.get('idPlanoIntervencao');

      //Carregando Competências do último checklist referente a data informada 
      //console.log(idPaciente);
      this.esdmchecklistService.findLastByPacienteData(idPaciente, moment(this.planointervencao.data).format('YYYY-MM-DD')).subscribe(chklsts => {
        //console.log(chklsts)
        if(chklsts.length > 0){
          this.esdmchecklists = chklsts.filter(msassmt => msassmt.status != false);
          
          if (this.esdmchecklists.length > 0){
            //Atribuindo o primeiro checklist para visualização
            this.esdmchecklist = this.esdmchecklists[0];
            //console.log(this.esdmchecklist)
  
            this.setChecklist();
          }

        }
      })
      if(this.form.valid){
        this.save();
      }
    }

    setDominios(){
      this.objetivoService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
        this.dominios = dominios;
        this.dominio = this.dominios[0];
        this.filterChecklist();
      })
    }
  
    filterChecklist(){
      this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
        (chklst.competencia.id_nivel==this.nivel.id 
          && chklst.competencia.id_dominio == this.dominio.id))
    }

    setProfissional(event:MatSelectChange){
      //console.log(event)
      this.planointervencao.profissional = this.profissionais.find(p => p.id == event.value);
      //this.planointervencao.idProfissional = event.value.id;
      if(this.planointervencao.objetivos != undefined){
        //console.log('salva')
        this.save();
      } 
    }

    setChecklist(){
      //Carrega novo chechklist no default N1-CRE
      //this.nivelField.value=   //nativeElement.children[0].selected=true;
      //this.dominioField.nativeElement.children[0].selected=true;
      //this.nivel.id = 'N1';
      //this.dominio.id = 'CRE'
      this.nivel = this.niveis.find(n => n.id == 'N1');
      this.dominio = this.dominios.find(dom => dom.id == 'CRE');
      this.filterChecklist()
    }

    validForm(){
      if(this.form.valid){
        return true;
      } else {
        this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
        return false;
      }
    }
    
    changeEtapaStatus(etapa: Etapa){

      //Verifico se o usuário possui autorização de alterar o status da etapa
      if(this.hasAccessCreate){
        let obj: Objetivo;
        if(this.validForm()){
          if(etapa.status == undefined || etapa.status == 'Não adquirida'){
            etapa.status = "Adquirida"
          } else {
            etapa.status = "Não adquirida"
          }
          //Verifico se todas as etapas foram adquiridas e, caso postivo, coloco o objetivo como adquirido
          obj = this.planointervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
          //console.log(obj.id);
          if (obj.etapa.find(e => e.status != "Adquirida") == undefined){
            obj.status = "Adquirido";
          } else {
            obj.status = "Não adquirido";
          }
          this.save();
        }
      }
    }

    async save(): Promise<void>{
      // console.log(this.saveDisabled)
        if(this.form.valid){
          this.loadingService.show();
          this.saveDisabled=true;
          if(this.planointervencao.id == undefined){
            await this.planointervencaoService.create(this.planointervencao).subscribe((id) => {
              this.saveDisabled=false;
              this.loadingService.hide();
              this.planointervencao.id = id;
              //console.log(id)
              //this.planointervencaoService.showMessage('Plano de intervenção criado com sucesso!');
              //this.router.navigate(['/paciente/' + id]);
            });
          } else {
            await this.planointervencaoService.update(this.planointervencao).subscribe((paciente) => {
              this.saveDisabled=false;
              this.loadingService.hide();
              //this.planointervencaoService.showMessage('Plano de intervenção alterado com sucesso!');
              //this.router.navigate(['/paciente/' + paciente.id]);
            });
          }
        } else {
          this.planointervencaoService.showMessage('Existem campos inválidos no formulário!',true);
        } 
    }

    exitEdit(){
      
      if(!this.planointervencao.objetivos || this.planointervencao.objetivos.length == 0){
        this.router.navigate(['/paciente/' + this.idPaciente, {
          tab:"vbmapp_plano"
        }])
      }

      if(this.planointervencao.id == undefined){
        this.router.navigate(['/paciente/' + this.idPaciente, {
          tab:"esdm_plano"
        }])
      } else {
        this.router.navigate(['/paciente/' + this.idPaciente, {
          tab:"esdm_plano",
          idPlanoIntervencao: this.planointervencao.id
        }])
      }
    }

    filtrarObjetivos(){
      if(this.checkFiltro){
        this.chklstcompView = this.esdmchecklist.checklist;
        this.chklstcompView = this.chklstcompView.filter(checkList => checkList.valor == 'N' || checkList.valor == 'P');
      }else{
        this.filterChecklist();
      }
    }

    msIsSelected(objetivo: Objetivo): boolean{
      if(this.planointervencao.objetivos == undefined || this.planointervencao.objetivos.find(o => o.id == objetivo.id) == undefined){
        return false;
      } else {
        return true;
      }
    }

}
