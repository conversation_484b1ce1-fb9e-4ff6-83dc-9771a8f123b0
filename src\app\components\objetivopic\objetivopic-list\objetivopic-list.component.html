<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        
        <div class="mat-elevation-z0"
            *ngIf="hasAccessRead && objetivos && objetivos.length > 0">
            <table mat-table [dataSource]="objetivos" multiTemplateDataRows style="width: 100%;"> 
                <!-- Nome Column --> 
                <ng-container matColumnDef="compAlvo">
                    <th mat-header-cell *matHeaderCellDef>Comportamento Alvo</th>
                    <td mat-cell *matCellDef="let row">
                        {{ row.comportamentoAlvo }}
                    </td>
                </ng-container>
                
                <!-- Definição Operacional Column --> 
                <ng-container matColumnDef="defOp">
                    <th mat-header-cell *matHeaderCellDef>Definição Operacional</th>
                    <td mat-cell *matCellDef="let row">{{ row.definicaoOperacional }}</td>
                </ng-container>

                <!-- Meta Column -->
                <ng-container matColumnDef="meta">
                    <th mat-header-cell *matHeaderCellDef>Meta</th>
                    <td mat-cell *matCellDef="let row;">{{ row.meta }}</td>
                </ng-container>
        
                <!-- TipoColeta Column --> 
                <ng-container matColumnDef="tipoColeta">
                    <th mat-header-cell *matHeaderCellDef>Tipo de Coleta</th>
                    <td mat-cell *matCellDef="let row">{{ row.tipoColeta }}</td>
                </ng-container>
        
                <!-- Action Column -->
                <ng-container matColumnDef="action" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a (click)="editObjetivo(row)" class="edit"
                            *ngIf="hasAccessUpdate">
                            <i class="material-icons">edit</i>
                        </a>
                        <a (click)="deleteObjetivo(row)" class="delete"
                            *ngIf="hasAccessDelete">
                            <i class="material-icons">delete</i>
                        </a>
                    </td>
                </ng-container> 
                
                <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;" style="padding-top: 10px;"></tr>
            </table>
        </div>        
    </mat-card-content>
</mat-card>