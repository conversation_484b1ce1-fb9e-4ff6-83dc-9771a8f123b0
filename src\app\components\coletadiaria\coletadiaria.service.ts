import { PlanoIntervencaoService } from './../planointervencao/planointervencao.service';
import { Objetivo } from './../objetivo/objetivo-model';
import { map, catchError } from 'rxjs/operators';
import { ColetaDiaria } from './coleta-diaria-model';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ColetadiariaService {
  
    coletadiariaUrl = `${environment.API_URL}/coletadiaria`;
    
    constructor(private snackbar: MatSnackBar,
      private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }    

    setStatusEtapaIndividual(coletadiaria: ColetaDiaria): void {
      let positivos: number;
      let negativos: number;
      let total: number;

      coletadiaria.objetivos.forEach(objetivo => {
        objetivo.etapa.forEach(etapa => {
          positivos = etapa.periodo.filter(function(p) {
                        return p == "+"
                      }).length;
          negativos = etapa.periodo.filter(function(p) {
                        return p == "-"
                      }).length;
          etapa.positivos = positivos;
          etapa.negativos = negativos;
          etapa.percentual = positivos/(negativos + positivos);
          if((negativos + positivos) > 0){
            if(etapa.percentual > 0.8){
              etapa.status = "Adquirida"
            } else {
              etapa.status = "Recusada"
            }
          } else {
            etapa.status = ""
          } /*else{
            //Se a etapa não teve nenhuma tentativa, excluo a mesma
            objetivo.etapa.splice(objetivo.etapa.indexOf(etapa), 1);
          }*/
        })
        /*if(objetivo.etapa.length==0){
          //Se o objetivo não tem etapas trabalhadas, excluo o objetivo
          coletadiaria.objetivos.splice(coletadiaria.objetivos.indexOf(objetivo), 1);
        }*/
      })
    }

    create(coletadiaria: ColetaDiaria): Observable<string>{
      this.setStatusEtapaIndividual(coletadiaria);
      // console.log(coletadiaria);
      return this.http.post<string>(this.coletadiariaUrl, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(coletadiaria: ColetaDiaria): Observable<ColetaDiaria>{
      this.setStatusEtapaIndividual(coletadiaria);
      // console.log(coletadiaria);
      return this.http.put<ColetaDiaria>(this.coletadiariaUrl + "/" + coletadiaria.id, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findById(id: string): Observable<ColetaDiaria>{
      return this.http.get<ColetaDiaria>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPaciente(idPaciente: string): Observable<ColetaDiaria>{
      return this.http.get<ColetaDiaria>(this.coletadiariaUrl + '/paciente/' + idPaciente).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPlanoIntervencao(idPlanoIntervencao: string): Observable<ColetaDiaria[]>{
      return this.http.get<ColetaDiaria[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findLastByPlanoIntervencao(idPlanoIntervencao: string): Observable<ColetaDiaria[]>{
      return this.http.get<ColetaDiaria[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao + '/last' ).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findResumoByPlanoIntervencao(idPlano: string): Observable<any[]> {
      return this.http.get<any[]>(`${this.coletadiariaUrl}/planointervencao/${idPlano}/resumo`).pipe(
        map(res => res),
        catchError(() => EMPTY)
      );
    }

    findResumoByPlanoIntervencaoPeriodo(
      idPlano: string,
      inicio: string,
      fim: string
    ): Observable<any[]> {
      const today = new Date();
      const defaultFim = fim+'T24:00:00.000Z' || today.toISOString(); // yyyy-mm-dd
      const defaultInicio = inicio+'T00:00:00.000Z' || new Date(today.getTime() - 30 * 86400000).toISOString();

      let params = new HttpParams()
        .set('inicio', defaultInicio)
        .set('fim', defaultFim);

      return this.http.get<any[]>(
        `${this.coletadiariaUrl}/planointervencao/${idPlano}/periodo`,
        {
          params,
        }
      ).pipe(
        map(res => res),
        catchError(() => EMPTY)
      );
    }

    findByPlanoIntervencaoData(idPlanoIntervencao: string, data: string): Observable<ColetaDiaria[]>{
      return this.http.get<ColetaDiaria[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao + "/data/" + data).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByProfissionalPeriod(idProfissional: string, dataInicial: string, dataFinal: string): Observable<ColetaDiaria[]>{
      return this.http.get<ColetaDiaria[]>(this.coletadiariaUrl + '/profissional/' + idProfissional + "/startdate/" + dataInicial
       + "/enddate/" +  dataFinal).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<ColetaDiaria[]>{
      return this.http.get<ColetaDiaria[]>(this.coletadiariaUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    delete(id: string): Observable<ColetaDiaria>{
      return this.http.delete<ColetaDiaria>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
}
