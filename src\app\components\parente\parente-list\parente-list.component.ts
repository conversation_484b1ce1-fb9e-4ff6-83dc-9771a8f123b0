import { LoadingService } from './../../../shared/service/loading.service';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ParenteService } from './../parente.service';
import { Parente } from './../parente-model';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-parente-list',
  templateUrl: './parente-list.component.html',
  styleUrls: ['./parente-list.component.css']
})
export class ParenteListComponent implements OnInit {

  public parentes: Parente[];

  displayedColumns = ['nome', 'email', 'telefone', 'parentesco', 'action']

  constructor(private parenteService: ParenteService,
    public dialog: MatDialog,
    private router: Router,
    public loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      const parentes = await this.parenteService.find().toPromise();
      this.parentes = parentes.filter(p => p.ativo == true);
    } catch(error) {
      console.log("Erro: ", error)
      this.loadingService.hide();
    } finally {
      this.loadingService.hide();
    }
  }

  edit(parente: Parente){
    this.router.navigate(['/parente/' + parente.id]);
  }

  delete(parente: Parente): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        parente.ativo = false;
        this.parenteService.update(parente).subscribe((p) => {
          this.parenteService.showMessage('Parente inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/parente/update/'+this.parente.id]);
          this.router.navigate(['/parente']);
        });
      }
    });
  }

}
