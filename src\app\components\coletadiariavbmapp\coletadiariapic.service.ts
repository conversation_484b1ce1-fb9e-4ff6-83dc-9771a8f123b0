import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from './coletadiariapic-model';
import { map, catchError } from 'rxjs/operators';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from '../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ColetadiariaPICService {

  coletadiariaUrl = `${environment.API_URL}/coletadiaria_pic`;
    
    constructor(private snackbar: MatSnackBar,
      private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }    
  
    findById(id: string): Observable<SessaoColetaDiariaPIC>{
      return this.http.get<SessaoColetaDiariaPIC>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPaciente(idPaciente: string): Observable<SessaoColetaDiariaPIC>{
      return this.http.get<SessaoColetaDiariaPIC>(this.coletadiariaUrl + '/paciente/' + idPaciente).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPIC(idPlanoIntervencao: string): Observable<SessaoColetaDiariaPIC[]>{
      return this.http.get<SessaoColetaDiariaPIC[]>(this.coletadiariaUrl + '/pic/' + idPlanoIntervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findLastByPIC(idPlanoIntervencao: string): Observable<SessaoColetaDiariaPIC[]>{
      return this.http.get<SessaoColetaDiariaPIC[]>(this.coletadiariaUrl + '/pic/' + idPlanoIntervencao + '/last' ).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findAllByPIC(idPlanoIntervencao: string): Observable<SessaoColetaDiariaPIC[]>{
      return this.http.get<SessaoColetaDiariaPIC[]>(this.coletadiariaUrl + '/pic/all/' + idPlanoIntervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPICPeriodo(
      idPlano: string,
      inicio: string,
      fim: string
    ): Observable<any[]> {
      const today = new Date();
      const defaultFim = fim+'T24:00:00.000Z' || today.toISOString();
      const defaultInicio = inicio+'T00:00:00.000Z' || new Date(today.getTime() - 30 * 86400000).toISOString();

      let params = new HttpParams()
        .set('inicio', defaultInicio)
        .set('fim', defaultFim);

      return this.http.get<any[]>(
        `${this.coletadiariaUrl}/pic/${idPlano}/periodo`,
        {
          params,
        }
      ).pipe(
        map(res => res),
        catchError(() => EMPTY)
      );
    }

    findByPlanoIntervencaoData(idPlanoIntervencao: string, data: string): Observable<SessaoColetaDiariaPIC[]>{
      return this.http.get<SessaoColetaDiariaPIC[]>(this.coletadiariaUrl + '/pic/' + idPlanoIntervencao + "/data/" + data).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<SessaoColetaDiariaPIC[]>{
      return this.http.get<SessaoColetaDiariaPIC[]>(this.coletadiariaUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    create(coletadiaria: SessaoColetaDiariaPIC): Observable<string>{
      return this.http.post<string>(this.coletadiariaUrl, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(coletadiaria: SessaoColetaDiariaPIC): Observable<SessaoColetaDiariaPIC>{
      return this.http.put<SessaoColetaDiariaPIC>(this.coletadiariaUrl + "/" + coletadiaria.id, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    delete(id: string): Observable<SessaoColetaDiariaPIC>{
      return this.http.delete<SessaoColetaDiariaPIC>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
}
