import { Atendimento } from './../../eventoatendimento/atendimento-model';
import { EventoAtendimento } from './../../eventoatendimento/eventoatendimento-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AuthService } from './../../template/auth/auth.service';
import { EventoatendimentoService } from './../../eventoatendimento/eventoatendimento.service';
import { CalendarEvent, CalendarEventTitleFormatter, CalendarView, CalendarDayViewBeforeRenderEvent,
  CalendarMonthViewBeforeRenderEvent,
  CalendarWeekViewBeforeRenderEvent } from 'angular-calendar';
import { ChangeDetectionStrategy, Component, OnInit, Injectable, ViewEncapsulation, ChangeDetectorRef, ElementRef, ViewChild } from '@angular/core';
import { addDays, addMinutes, differenceInMinutes, endOfWeek, startOfDay, startOfHour } from 'date-fns';
import { WeekViewHourSegment, ViewPeriod } from 'calendar-utils';
import { fromEvent } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import RRule from 'rrule';
import * as moment from 'moment';
import { colors } from '../colors';



function floorToNearest(amount: number, precision: number) {
  return Math.floor(amount / precision) * precision;
}

function ceilToNearest(amount: number, precision: number) {
  return Math.ceil(amount / precision) * precision;
}



@Injectable()
export class CustomEventTitleFormatter extends CalendarEventTitleFormatter {
  weekTooltip(event: CalendarEvent, title: string) {
    //console.log(event)
    //console.log(event.meta)
    //if (!event.meta.tmpEvent) {
      return super.weekTooltip(event, title);
    //}
  }

  dayTooltip(event: CalendarEvent, title: string) {
    //if (!event.meta.tmpEvent) {
      return super.dayTooltip(event, title);
    //}
  }
}


@Component({
  selector: 'app-agenda-view',
  templateUrl: './agenda-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CalendarEventTitleFormatter,
      useClass: CustomEventTitleFormatter,
    },
  ],
  styleUrls: ['./agenda-view.component.css'],
  //encapsulation: ViewEncapsulation.None,
})
export class AgendaViewComponent implements OnInit {
  @ViewChild('scrollContainer') scrollContainer: ElementRef<HTMLElement>;


  view: CalendarView = CalendarView.Week;

  viewDate: Date = new Date();

  //events: EventoAtendimento[] = [];
  events: CalendarEvent[] = [];

  viewPeriod: ViewPeriod;


  weekStartsOn: 0 = 0;

  dragToCreateActive = false;

  clickedDate: Date;


  changeDay(date: Date) {
    this.viewDate = date;
    this.view = CalendarView.Day;
  }

  constructor(private eventoatendimentoService: EventoatendimentoService,
    private profissionalService: ProfissionalService,
    private cdr: ChangeDetectorRef,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute) { }

  ngOnInit(): void {
    //console.log((this.authService.getUser() as FirebaseUserModel).uid)

    //Verifico se o usuário logado é um profissional e, caso seja, recupero o mesmo
    if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
      this.profissionalService.findByUserId((this.authService.getUser() as FirebaseUserModel).uid).subscribe(p => {
        //console.log(p);
        // console.log(p[0].nome)
        //Recupero a agenda do profissional
        this.eventoatendimentoService.findByProfissional(p[0].id).subscribe(atendimentos => {
          for(const [,atend] of atendimentos.entries()){
            atend.start = new Date(atend.start)
            atend.end = new Date(atend.end)
            this.events.push(atend);
            //console.log(atend)
          }
          //console.log("1")
          //this.events = atendimentos;
          this.events = [...this.events];
          this.refresh();
          // console.log(this.events);
        })
      })
      
    }
    
  }

  ngAfterViewInit() {
    this.scrollToCurrentView();
  }

  viewChanged() {
    this.cdr.detectChanges();
    this.scrollToCurrentView();
  }

  eventClicked({ event }: { event: CalendarEvent }): void {
    this.router.navigate(['/eventoatendimento/' + event.id]);
  }

  private scrollToCurrentView() {
    if (this.view === CalendarView.Week || CalendarView.Day) {
      // each hour is 60px high, so to get the pixels to scroll it's just the amount of minutes since midnight
      const minutesSinceStartOfDay = differenceInMinutes(
        startOfHour(new Date()),
        startOfDay(new Date())
      );
      const headerHeight = this.view === CalendarView.Week ? 60 : 0;
      this.scrollContainer.nativeElement.scrollTop =
        minutesSinceStartOfDay + headerHeight;
    }
  }

  hourClickedDate(date: Date){
    this.clickedDate = date;
    // console.log(this.clickedDate)
    // console.log('/eventoatendimento/create/'+this.clickedDate)
    this.router.navigate(['/eventoatendimento/create/', {data: this.clickedDate}]);

  }

  /*
  startDragToCreate(
    segment: WeekViewHourSegment,
    mouseDownEvent: MouseEvent,
    segmentElement: HTMLElement
  ) {
    const dragToSelectEvent: CalendarEvent = {
      id: this.events.length,
      title: 'New event',
      start: segment.date,
      meta: {
        tmpEvent: true,
      },
    };
    this.events = [...this.events, dragToSelectEvent];
    const segmentPosition = segmentElement.getBoundingClientRect();
    this.dragToCreateActive = true;
    const endOfView = endOfWeek(this.viewDate, {
      weekStartsOn: this.weekStartsOn,
    });

    fromEvent(document, 'mousemove')
      .pipe(
        finalize(() => {
          delete dragToSelectEvent.meta.tmpEvent;
          this.dragToCreateActive = false;
          this.refresh();
        }),
        takeUntil(fromEvent(document, 'mouseup'))
      )
      .subscribe((mouseMoveEvent: MouseEvent) => {
        const minutesDiff = ceilToNearest(
          mouseMoveEvent.clientY - segmentPosition.top,
          30
        );

        const daysDiff =
          floorToNearest(
            mouseMoveEvent.clientX - segmentPosition.left,
            segmentPosition.width
          ) / segmentPosition.width;

        const newEnd = addDays(addMinutes(segment.date, minutesDiff), daysDiff);
        if (newEnd > segment.date && newEnd < endOfView) {
          dragToSelectEvent.end = newEnd;
        }
        this.refresh();
      });
  }
  */

  private refresh() {
    this.events = [...this.events];
    this.cdr.detectChanges();
  }

  updateCalendarEvents(
    viewRender:
      | CalendarMonthViewBeforeRenderEvent
      | CalendarWeekViewBeforeRenderEvent
      | CalendarDayViewBeforeRenderEvent
  ): void {
    if (
      !this.viewPeriod ||
      !moment(this.viewPeriod.start).isSame(viewRender.period.start) ||
      !moment(this.viewPeriod.end).isSame(viewRender.period.end)
    ) {
      this.viewPeriod = viewRender.period;
      //this.calendarEvents = [];

      (this.events as Atendimento[]).filter(e => e.recorrente == true).forEach((event) => {
        // console.log(event)
        const rule: RRule = new RRule({
          ...event.rrule,
          //dtstart: moment(viewRender.period.start).startOf('day').toDate(),
          dtstart: moment(event.rrule.dtstart).startOf('day').toDate(),
          //until: moment(viewRender.period.end).endOf('day').toDate(),
        });
        const { title, color } = event;

        rule.all().forEach((date) => {
          this.events.push({
            title,
            color,
            start: moment(date).toDate(),
          });
        });
        this.refresh();
      });
      this.cdr.detectChanges();
    }
  }

}
