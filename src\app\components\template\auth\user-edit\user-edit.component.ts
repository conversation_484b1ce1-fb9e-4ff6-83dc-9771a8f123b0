import { Profissional } from './../../../profissional/profissional-model';
import { OrganizacaoService } from './../../../organizacao/organizacao.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from './../auth.service';
import { UserService } from './../user.service';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { Organizacao } from './../../../organizacao/organizacao-model';
import { FirebaseUserModel } from './../user-model';
import { Component, OnInit } from '@angular/core';
import { UserOrganizacoesModel } from '../user-organizacoes-model';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../confirm-dialog/confirm-dialog.component';
import { UserOrganizacoesService } from '../user-organizacoes-service';

@Component({
  selector: 'app-user-edit',
  templateUrl: './user-edit.component.html',
  styleUrls: ['./user-edit.component.css']
})
export class UserEditComponent implements OnInit {

  displayedColumns = ['nome','default', 'action']

  user: FirebaseUserModel = new FirebaseUserModel();
  organizacoes: Organizacao[] = [];
  allOrganizacoes: Organizacao[] = [];
  organizacoesSelecionadas: UserOrganizacoesModel[] = [];
  organizationIdSelecionada: String;
  organizacoesSelecionadasExcluidas: UserOrganizacoesModel[] = [];
  //profileForm: FormGroup;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);

  constructor(
    private userService: UserService,
    private organozationService: OrganizacaoService,
    private userOrganizacoesService: UserOrganizacoesService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog
    //private location : Location,
    //private headerService: HeaderService,
    //private fb: FormBuilder
  ) {

  }

  ngOnInit(): void {

    //Recupero o usuário a ser editado
    let uid = this.route.snapshot.paramMap.get('uid');
    this.authService.findUserByUID(uid).subscribe(u => {
      //console.log(u[0])
      this.user = new FirebaseUserModel();
      this.user.image = u[0].photoURL;
      this.user.name = u[0].displayName;
      this.user.provider = u[0].providerData[0].providerId;
      this.user.email = u[0].email;
      this.user.uid = u[0].uid;
      
      this.user.admin = u[0].customClaims ? u[0].customClaims.admin : false;
      this.user.organizationId = u[0].customClaims ? u[0].customClaims.organization : "";
      this.user.superadmin = u[0].customClaims ? u[0].customClaims.superadmin : false;
      this.user.gsupervisor = u[0].customClaims ? u[0].customClaims.gsupervisor : false;
    });

    //Recupero as organizações
    this.organozationService.find().subscribe(orgs => {
      this.organizacoes = orgs;
      this.allOrganizacoes = orgs;
      this.carregarOrganizacoesUsuario(uid);
      
    })

    //this.createForm(this.user.name, this.user.email, this.user.uid, this.user.admin, this.user.superadmin, this.user.organizationId);
  }

  /*
  createForm(nome, email, uid, admin, superadmin, organizationId) {
    this.profileForm = this.fb.group({
      nome: [nome, Validators.required ],
      email: new FormControl({value: email, disabled: true }),
      //email: [email, Validators.required, disabled: true],
      uid: new FormControl({value: uid, disabled: true }),
      admin: [admin?"Sim":"Não", Validators.required ],
      superadmin: [superadmin?"Sim":"Não", Validators.required ],
      organizationId: [organizationId, Validators.required ]
    });
  }*/

  async save(){
    await this.salvarUserOrganizacoes();
    let p: Profissional = new Profissional();
    p.email = this.user.email;
    //Atualizo o profile do usuário no Auth do Firebase
    this.authService.updateUser(this.user).subscribe(async us => {
      if(this.user.superadmin){
        await this.authService.grantSuperAdminAccess(p).subscribe(async u => {
          if(this.user.admin){
            await this.authService.grantOrganizationAdminAccess(p,this.user.organizationId).subscribe(u => {
              //this.user = u;
              this.authService.showMessage("Usuário gravado com sucesso!")
              //console.log(u);
              this.router.navigate(["/admin/users"]);
            })
          } else {
            await this.authService.revokeOrganizationAdminAccess(p).subscribe(async u => {
              await this.authService.setOrganizacao(p, this.user.organizationId).subscribe(u => {
                this.authService.showMessage("Usuário gravado com sucesso!")
                //console.log(u);
                this.router.navigate(["/admin/users"]);
              });
            })
          }
        })
      } else {
        await this.authService.revokeSuperAdminAccess(p).subscribe(async u => {
          if(this.user.admin){
            await this.authService.grantOrganizationAdminAccess(p,this.user.organizationId).subscribe(u => {
              //this.user = u;
              this.authService.showMessage("Usuário gravado com sucesso!")
              //console.log(u);
              this.router.navigate(["/admin/users"]);
            })
          } else {
            await this.authService.revokeOrganizationAdminAccess(p).subscribe(async u => {
              await this.authService.setOrganizacao(p, this.user.organizationId).subscribe(u => {
                this.authService.showMessage("Usuário gravado com sucesso!")
                //console.log(u);
                this.router.navigate(["/admin/users"]);
              });
            })
          }
        })
      }
    });
  }

  cancel(){
    this.router.navigate(["/admin/users"]);
  }

  redefinePassword(){
    //console.log(this.authService.getUser().email);
    this.authService.resetPassword(this.authService.getUser().email)
      .then(res => {
        //console.log(res);
        this.authService.showMessage("Um e-mail foi enviado para você com as instruções para a redefinição de senha.")
      }, err => console.log(err))
  }

  addOrganizacao(){

    // if(this.user.gsupervisor || this.organizacoesSelecionadas.length < 1){

      var orgSelecinada =  this.mapOrganizacaoToUserOrganizacao(this.organizacoes.find( org => org.id == this.organizationIdSelecionada));
      if(this.organizacoesSelecionadas.length == 0){
        orgSelecinada.default = true;
        this.user.organizationId = orgSelecinada.idOrganizacao;
      }
      this.organizacoesSelecionadas.push(orgSelecinada);
      this.organizacoesSelecionadas = Array.from(this.organizacoesSelecionadas);
      this.organizacoes = this.organizacoes.filter(org => org.id !== this.organizationIdSelecionada);

    // }else{
      // this.organozationService.showMessage("Apenas supervisores gerais podem acessar mais de 1 organização", true)
    // }

  }

  recuperarNomeOrganizacao(id:String){
    return this.allOrganizacoes.find(org => org.id == id)?.nome;
  }

  mapOrganizacaoToUserOrganizacao(org:Organizacao){
    var userOrg = new UserOrganizacoesModel();
    userOrg.idUser = this.user.uid;
    userOrg.idOrganizacao = org.id;
    return userOrg;
  }
  
  async removerOrganizacaoSelecionada(id:String){
    await this.organizacoesSelecionadasExcluidas.push(this.organizacoesSelecionadas.find(org => org.idOrganizacao == id));
   
    this.organizacoesSelecionadas = this.organizacoesSelecionadas.filter(org => org.idOrganizacao != id);
    this.organizacoes.push(this.allOrganizacoes.find(org => org.id == id));
    this.organizacoes.sort((o1,o2) => {
      if(o1.nome > o2.nome){
        return 1;
      }else{
        return -1;
      }
    });
    if(this.organizacoesSelecionadas.length > 0 && this.organizacoesSelecionadas.filter(org => org.default).length == 0){
      this.organizacoesSelecionadas[0].default = true;
      this.user.organizationId = this.organizacoesSelecionadas[0].idOrganizacao;
     }

     
  
  }

  definirOrgPadrao(id:String){
    this.organizacoesSelecionadas.filter(org => {
      if(org.default){
        org.default = false;
      }

      if(org.idOrganizacao == id){
        org.default = true;
      }
    });

    this.organizacoesSelecionadas = Array.from(this.organizacoesSelecionadas);
    this.user.organizationId = this.allOrganizacoes.find( org => id == org.id).id;
  }

  validaOrgSelecionadas(){
    if(this.user.gsupervisor && this.organizacoesSelecionadas.length > 1){
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
          width: '400px',
          data: {
            msg: 'O usuário possui mais de 1 organização, ao confirmar será mantida apenas a organização padrão.'
          }
        });

        dialogRef.afterClosed().subscribe(async result => {
            if(result){  
              this.organizacoesSelecionadas = this.organizacoesSelecionadas.filter(org => org.default);  
            }else{
              this.user.gsupervisor = true;
            }
        })  
    }
  }


  async salvarUserOrganizacoes(){
    this.organizacoesSelecionadas.forEach(org => {
       if(org.id != null){
        this.userOrganizacoesService.update(org).subscribe(retorno => {
          //console.log(retorno);
        })
      }else{
        this.userOrganizacoesService.create(org).subscribe(retorno => {
          //console.log(retorno);
        })
      }
    })

    if(this.organizacoesSelecionadasExcluidas.length > 0){
      this.organizacoesSelecionadasExcluidas.forEach(orgExcl => {
        orgExcl.ativo = false;
        this.userOrganizacoesService.update(orgExcl).subscribe();
        //console.log(orgExcl);
      })
    }
    
  }

  carregarOrganizacoesUsuario(idUser:string){
    this.userOrganizacoesService.findByUser(idUser).subscribe(retorno => {
      this.organizacoesSelecionadas = retorno.filter(org => org.ativo != false);
      //remove da lista as organizações ja selecionadas
      this.organizacoesSelecionadas.forEach(orgSel=> {
        this.organizacoes = this.organizacoes.filter(org => org.id !== orgSel.idOrganizacao);
        if(this.organizacoes.length > 0){
          this.organizationIdSelecionada = this.organizacoes[0].id;
        }
      })
    })
  }
}
