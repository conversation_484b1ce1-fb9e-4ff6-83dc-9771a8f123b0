import { AuthService } from './../../components/template/auth/auth.service';
import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-paciente-crud',
  templateUrl: './paciente-crud.component.html',
  styleUrls: ['./paciente-crud.component.css']
})
export class PacienteCrudComponent implements OnInit {

  public hasAccessCreate: boolean = false;

  constructor(private router: Router,
    private authService: AuthService,
    public headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Paciente',
        icon: 'child_care',
        routeUrl: '/paciente'
      }
    }

  ngOnInit(): void {
    this.hasAccessCreate = this.authService.verifySimpleAccess(['*'], 'Paciente.Cadastro de pacientes', 'create');
    //console.log( (JSON.parse(localStorage.getItem("user")) as Profissional).email )
  }
  
  getUser() {
  }

}
