import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../components/template/auth/auth.service';
import { HeaderService } from '../../components/template/header/header.service';

@Component({
  selector: 'app-statusagenda-crud',
  templateUrl: './statusagenda-crud.component.html',
  styleUrls: ['./statusagenda-crud.component.css']
})
export class StatusagendaCrudComponent implements OnInit {

  hasAccessCreate: boolean = false;

  constructor(private router: Router,
    public authService: AuthService,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Status Agenda',
        icon: 'ballot',
        routeUrl: '/status-agenda'
      }
    }

  ngOnInit(): void {
    this.hasAccessCreate = this.authService.verifySimpleAccess(['*'], 'StatusAgenda.Cadastro Status','create')
  }

}
