export class Anamnese{
    id?: string;
    descricao: string;
    grupos: Grupo[]
}

export class Grupo{
    descricao: string;
    perguntas: Pergunta[]

}

export class Pergunta{
    numeracao: number;
    descricao: string;
    campo: Campo;

    constructor(){
        this.campo = new Campo();
    }
}

export class Campo{
    tipo: string;
    tamanho: number;
    valores: Valor[];

    constructor(){
        this.valores = [];
    }
}

export class Valor{
    value: string;
    descricao: string;
    constructor(){
        this.value = "";
        this.descricao = ""
    }
}

