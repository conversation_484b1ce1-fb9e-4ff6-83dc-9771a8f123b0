<div class="counter-container">
    <!-- Descrição (ícone com tooltip) -->
    <div class="tooltip-container">
        <mat-icon class="custom-cursor" mat-icon-button aria-label="Descrição">
            <mat-icon style="opacity: 0.8;">help_outline</mat-icon>
        </mat-icon>
        <span class="tooltip-descricao-text" [innerHTML]="respDescription()"></span>
    </div>

    <div class="counter">
        <!-- Controles do contador -->
        <div class="counter-controls">
            <button (click)="decreaseCounter()"><i style="font-size: 20px; color: #004A7F; padding-left: 10px;">-</i></button>
            <span>{{ coletaDiariaPIC.qtdObservada }}</span>
            <button (click)="increaseCounter()"><i style="font-size: 20px; color: #004A7F; padding-right: 10px;">+</i></button>
        </div>

        <!-- Label do comportamento alvo -->
        <div class="counter-label truncatable" [title]="objetivoPIC.comportamentoAlvo">
            {{ objetivoPIC.comportamentoAlvo }}
        </div>
    </div>

    <!-- Checkbox para confirmação -->
    <div *ngIf="!confirmed" class="confirm-checkbox-container">
        <label for="confirm-coleta">Objetivo observado?</label>
        <input type="checkbox" id="confirm-coleta" (change)="toggleConfirmation($event)">
    </div>
</div>
