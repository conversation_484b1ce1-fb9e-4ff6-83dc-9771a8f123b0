<mat-sidenav-container class="container">
    <!--mat-sidenav class="sidenav" mode="side" [opened]="!hasFullView || !navService.navData.hidden" -->
    <mat-sidenav class="sidenav" mode="side" [opened]="!navService.navData.hidden" 
        fixedInViewport="true" fixedTopGap="64" >


    <!--mat-sidenav #drawer class="sidenav" fixedInViewport="true" 
            [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'" 
            [mode]="(isHandset$ | async) ? 'over' : 'side'" 
            [opened]="!(isHandset$ | async)"
            fixedTopGap="64"-->
        <mat-nav-list class="nav-list">
            <a mat-list-item routerLink="/"> 
                <i class="material-icons">
                    home
                </i>
                Início
            </a>
            <!--a mat-list-item routerLink="/listaespera"> 
                <i class="material-icons">
                    groups
                </i>
                Lista de Espera
            </a--> 
            <!--a mat-list-item routerLink="/agenda"
                *ngIf="authService.verifySimpleAccess(['*'], 'Atendimento.Cadastro de atendimentos','read')"> 
                <i class="material-icons">
                    calendar_today
                </i>
                Atendimentos 
            </a--> 
            <a mat-list-item routerLink="/paciente"> 
                <i class="material-icons">
                    child_care
                </i>
                Pacientes
            </a> 
            <a mat-list-item routerLink="/profissional"
                *ngIf="authService.verifySimpleAccess(['*'], 'Profissional.Cadastro de profissionais','read')">  
                <i class="material-icons">
                    assignment_ind
                </i>
                Profissionais
            </a> 
            <a mat-list-item routerLink="/parente"
                *ngIf="authService.verifySimpleAccess(['*'], 'Parente.Cadastro de parentes','read')">  
                <i class="material-icons">
                    family_restroom
                </i>
                Parentes
            </a> 
        <!--/mat-nav-list-->
            <!--mat-accordion multi>
                <mat-expansion-panel class="mat-elevation-z0">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <a mat-list-item> 
                                ESDM
                            </a>
                          </mat-panel-title>
                    </mat-expansion-panel-header>
                    <mat-nav-list class="nav-list">
                    </mat-nav-list>
                </mat-expansion-panel>
            </mat-accordion-->
            <ng-container *ngIf="authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','read') || authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read')">
                <mat-expansion-panel class="mat-elevation-z0">
                    <mat-expansion-panel-header class="mat-expansion-class">
                            <a mat-list-item 
                                *ngIf="authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','read') || authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read')"> 
                                <i class="material-icons">
                                    school
                                </i>
                                Biblioteca
                            </a>  
                    </mat-expansion-panel-header>
                    <a mat-list-item routerLink="/objetivo"
                        *ngIf="authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','read')"> 
                        <i class="material-icons">
                            source
                        </i>
                        Objetivos ESDM
                    </a>  
                    <a mat-list-item routerLink="/vbmapp_objetivo"
                        *ngIf="authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read')"> 
                        <i class="material-icons">
                            source
                        </i>
                        Objetivos ABA
                    </a>  
                    <a mat-list-item routerLink="/objetivo_comportamental"
                        *ngIf="authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read')"> 
                        <i class="material-icons">
                            source
                        </i>
                        Objetivos Comp.
                    </a>  
                </mat-expansion-panel>
            </ng-container>
            <!-- <a mat-list-item routerLink="/agenda/lista"
                *ngIf="authService.verifySimpleAccess(['*'], 'Agenda.Agendamento de Pacientes','read')">  
                <i class="material-icons">
                    calendar_today
                </i>
                Agenda
            </a> -->
        </mat-nav-list>
    </mat-sidenav>


    <mat-sidenav-content fixedInViewport="true" class="content">
        <router-outlet></router-outlet>
    </mat-sidenav-content>
</mat-sidenav-container> 