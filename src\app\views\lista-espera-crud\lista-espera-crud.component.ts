import { HeaderService } from './../../components/template/header/header.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-lista-espera-crud',
  templateUrl: './lista-espera-crud.component.html',
  styleUrls: ['./lista-espera-crud.component.css']
})
export class ListaEsperaCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Lista de Espera',
        icon: 'groups',
        routeUrl: '/listaespera'
      }
    }

  ngOnInit(): void {
  }

  navigateToParceiroCreate(): void{

  }

}
