<mat-toolbar class="header mat-elevation-z4">
    <span>
        <a  routerLink="/">
            <img class="logo mobile-hide" [src]="logoSrc" alt="logo">
        </a>
    </span>
    <span class="sidenav-group v-middle" >
        <a class="v-middle" (click)="toogleNavSidebar()" matTooltip="Ocultar/exibir menu lateral">
            <i class="material-icons v-middle">
                menu
            </i>
        </a>
    </span>
    <span class="title-group v-middle" >
        <a class="v-middle" routerLink="{{ routeUrl }}">
            <i class="material-icons v-middle">
                {{icon}}
            </i>
            {{ title }}
        </a>
    </span>
    <span class="menu-group v-middle" >
        <ng-container *ngIf="user != null && (user.admin || user.superadmin || user.gsupervisor )">
            <button mat-icon-button [matMenuTriggerFor]="menuReports" 
                matTooltip="Consultas">
                
                    <i class="material-icons v-middle settings">
                        manage_search
                    </i>
                
              </button>
              
              <mat-menu #menuReports="matMenu">
                <!--button mat-menu-item (click)=manageUsers()
                    *ngIf="user != null && user.superadmin">
                    <mat-icon>groups</mat-icon>
                    <span>Usuários (Geral)</span>
                </button>
                <mat-divider *ngIf="user != null && user.superadmin"></mat-divider>
                <button mat-menu-item (click)=manageOrganization()>
                    <mat-icon>domain</mat-icon>
                    <span>Organização</span>
                </button>
                <button mat-menu-item (click)=manageAdmin()>
                    <mat-icon>admin_panel_settings</mat-icon>
                    <span>Administradores</span>
                </button-->
                <!-- <button mat-menu-item (click)=manageFuncoes()>
                    <mat-icon>psychology</mat-icon>
                    <span>Funções</span> 
                </button> -->
                <!--button mat-menu-item (click)=manageTiposProcedimentos()>
                    <mat-icon>ballot</mat-icon>
                    <span>Tipos de Procedimento</span> 
                </button-->
                <button mat-menu-item (click)="reportPacientesPorFuncao()">
                    <mat-icon>search</mat-icon>
                    <span>Equipe de Pacientes (por função)</span> 
                </button>
                <button mat-menu-item (click)="reportColeta()">
                    <mat-icon>search</mat-icon>
                    <span>Relatório de Coletas</span> 
                </button>
                <!-- <button mat-menu-item (click)=manageEstimulos()
                    *ngIf="hasAccessCreateEstimulo">
                    <mat-icon>toys</mat-icon>
                    <span>Estímulos</span> 
                </button> -->
              </mat-menu>
        </ng-container>
        <ng-container *ngIf="user != null && (user.admin || user.superadmin 
            || hasAccessCreateEstimulos
            || hasAccessCreateParentes )">
            <button mat-icon-button [matMenuTriggerFor]="menuSettings" 
                matTooltip="Cadastros">
                
                    <i class="material-icons v-middle settings">
                        storage
                    </i>
                
              </button>
              
              <mat-menu #menuSettings="matMenu">
                <!--button mat-menu-item (click)=manageUsers()
                    *ngIf="user != null && user.superadmin">
                    <mat-icon>groups</mat-icon>
                    <span>Usuários (Geral)</span>
                </button>
                <mat-divider *ngIf="user != null && user.superadmin"></mat-divider>
                <button mat-menu-item (click)=manageOrganization()>
                    <mat-icon>domain</mat-icon>
                    <span>Organização</span>
                </button>
                <button mat-menu-item (click)=manageAdmin()>
                    <mat-icon>admin_panel_settings</mat-icon>
                    <span>Administradores</span>
                </button-->
                <!-- <button mat-menu-item (click)=manageFuncoes()>
                    <mat-icon>psychology</mat-icon>
                    <span>Funções</span> 
                </button> -->
                <!--button mat-menu-item (click)=manageTiposProcedimentos()>
                    <mat-icon>ballot</mat-icon>
                    <span>Tipos de Procedimento</span> 
                </button-->
                <button mat-menu-item (click)=manageParentesco()
                    *ngIf="authService.verifySimpleAccess(['*'], 'Parentesco.Cadastro de parentesco','create')">
                    <mat-icon>contacts</mat-icon>
                    <span>Parentesco</span> 
                </button>
                <button mat-menu-item (click)=manageEstimulos()
                    *ngIf="authService.verifySimpleAccess(['*'], 'Estímulo.Cadastro de estímulos','create')">
                    <mat-icon>toys</mat-icon>
                    <span>Estímulos</span> 
                </button>
                <button mat-menu-item (click)=manageEstimulosCategory()
                    *ngIf="authService.verifySimpleAccess(['*'], 'Estímulo.Cadastro de estímulos','create')">
                    <mat-icon>library_books</mat-icon>
                    <span>Categorias de Estímulos</span> 
                </button>
              </mat-menu>
        </ng-container>
    <!--/span>
    <span class="menu-group-settings v-middle" -->
        <ng-container *ngIf="user != null && (user.admin || user.superadmin)">
            <button mat-icon-button [matMenuTriggerFor]="menuSettings" 
                matTooltip="Configurações">
                
                    <i class="material-icons v-middle settings">
                        settings
                    </i>
                
              </button>
              
              <mat-menu #menuSettings="matMenu">
                <button mat-menu-item (click)=manageUsers()
                    *ngIf="user != null && user.superadmin">
                    <mat-icon>groups</mat-icon>
                    <span>Usuários (Geral)</span>
                </button>
                <mat-divider *ngIf="user != null && user.superadmin"></mat-divider>
                <button mat-menu-item (click)=manageOrganization()>
                    <mat-icon>domain</mat-icon>
                    <span>Organização</span>
                </button>
                <button mat-menu-item (click)=manageAdmin()>
                    <mat-icon>admin_panel_settings</mat-icon>
                    <span>Administradores</span>
                </button>
                <button mat-menu-item (click)=manageFuncoes()>
                    <mat-icon>psychology</mat-icon>
                    <span>Funções</span> 
                </button>
                <button mat-menu-item (click)=manageAnamnese()>
                    <mat-icon>assignment</mat-icon>
                    <span>Template Anamneses</span> 
                </button>
                <!-- <button mat-menu-item (click)=manageTiposProcedimentos()>
                    <mat-icon>enhanced_encryption</mat-icon>
                    <span>Procedimentos</span> 
                </button>
                <button mat-menu-item (click)=manageStatusAgenda()>
                    <mat-icon>list_alt</mat-icon>
                    <span>Status Agenda</span> 
                </button>
                <button mat-menu-item (click)=manageLocalAtendimento()>
                    <mat-icon>event_seat</mat-icon>
                    <span>Local Atendimento</span> 
                </button> -->
                <!--button mat-menu-item (click)=manageFuncoes()>
                    <mat-icon>psychology</mat-icon>
                    <span>Funções</span> 
                </button-->
                <!--button mat-menu-item (click)=manageTiposProcedimentos()>
                    <mat-icon>ballot</mat-icon>
                    <span>Tipos de Procedimento</span> 
                </button-->
                <!--button mat-menu-item (click)=manageParentesco()>
                    <mat-icon>contacts</mat-icon>
                    <span>Parentesco</span> 
                </button-->
              </mat-menu>
        </ng-container>
    </span>
    <span class="user-group v-middle" style="display: flex; flex-direction: row; margin: auto;">
        <ng-container *ngIf="user">
            <!--small class="v-middle" style="font-size: small; padding: 5px;">{{ user.name }}</small-->
            <button mat-icon-button [matMenuTriggerFor]="menu" 
                matTooltip="{{user.name}}">
                <img class="userImage v-middle" [src]="user.image" *ngIf="user.image" alt="{{user.name}}">
                <img class="userImage v-middle" src="assets/img/Beni_icon.png" *ngIf="!user.image" alt="User image">
                <!-- <img class="userImage v-middle" src="assets/img/{{ user.organizationId }}_icon.png"  *ngIf="!user.image" alt="User image"> -->
                <!--mat-icon>more_vert</mat-icon-->
              </button>
              <mat-menu #menu="matMenu">
                <button mat-menu-item (click)=profile()>
                    <mat-icon>assignment_ind</mat-icon>
                    <span>Perfil</span>
                </button>
                <button mat-menu-item (click) = "logout()">
                  <mat-icon>logout</mat-icon>
                  <span>Logout</span>
                </button> 
                
                <div mat-menu-item (click)="$event.stopPropagation();" *ngIf="organizacoes && organizacoes.length > 1" style="width: 200px;">
                    <mat-select placeholder="Organização" 
                        [(ngModel)]="organizacaoIdSelecionada"
                        (ngModelChange)="alterarOrganizacao()" name="organiacao">
                        <mat-option *ngFor="let org of organizacoes" [value]="org" >
                            {{org.idOrganizacao}}
                        </mat-option>
                    </mat-select>
                
                </div>
                                
                <!--button mat-menu-item>
                  <mat-icon>notifications_off</mat-icon>
                  <span>Disable alerts</span>
                </button-->
              </mat-menu>
        </ng-container>


    </span>
</mat-toolbar>
