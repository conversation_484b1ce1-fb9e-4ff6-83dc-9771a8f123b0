import { Any, JsonObject, JsonProperty } from 'json2typescript';
import { Permissao } from './permissao-model';
import { Funcionalidade } from './funcionalidade-model';
@JsonObject("FuncaoJSON")
export class FuncaoJSON{
    @JsonProperty("id", String, true)
    id?: string ="";

    @JsonProperty("nome", String, false)
    nome: string ="";

    @JsonProperty("ativo", Boolean, false)
    ativo: boolean = true;

    @JsonProperty("permission", [Map], false)
    //permission: [string, [string,string]] = undefined;
    permission: Map< string, Map<string,string> > = undefined;

    constructor(){
        //this.permission = {};
    }
}