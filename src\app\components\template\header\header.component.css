.header{
    position: sticky;
    position: -webkit-sticky; /* For macOS/iOS Safari */
    top: 0; /* Sets the sticky toolbar to be on top */
    z-index: 1000;
}

.header a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #3f51b5;
}

.header .logo{
    max-height: 65px;
}

@media (max-width: 600px) 
{
    .mobile-hide{
        display: none;
    }

    .header .sidenav-group{
        padding-left: 0px;
        cursor: pointer;
    }
}

.header .userImage{
    max-height: 65px;
    padding: 5px;
    border-radius: 70%;
    margin: auto;
    background-color: lightgray;
    
}

.header .user-group{
    padding-left: 25px;
    text-align: right;
    width: fit-content;
}

@media (min-width: 601px) { 
    .header .sidenav-group{
        padding-left: 25px;
        cursor: pointer;
    }
}
.header .title-group{
    padding-left: 25px;
    width: 20%;
}

.header .menu-group{
    padding-left: 25px;
    padding-right: 25px;
    width: 60%;
    text-align: right;
}

.settings {
    color: slate<PERSON>rey;
    font-size: 25px;
}

.header .title-group i{
    padding-right: 5px;
}

.multiline-tooltip{
    white-space: pre-line;
}