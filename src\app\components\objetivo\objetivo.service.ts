import { environment } from './../../../environments/environment';
import { Dominio } from './../dominio/dominio-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Objetivo } from './objetivo-model';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ObjetivoService {

  objetivolUrl = `${environment.API_URL}/objetivo`;
  
  public objetivos: BehaviorSubject<any[]> = 
    new BehaviorSubject<Objetivo[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(objetivo: Objetivo): Observable<Objetivo>{
    //console.log(objetivo);
    return this.http.post<Objetivo>(this.objetivolUrl, objetivo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(objetivo: Objetivo): Observable<Objetivo>{
    return this.http.put<Objetivo>(this.objetivolUrl + "/" + objetivo.id, objetivo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Objetivo>{
    return this.http.get<Objetivo>(this.objetivolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findDominiosByNivel(id_nivel: string): Observable<Dominio[]>{
    return this.http.get<Dominio[]>(this.objetivolUrl + '/' + id_nivel + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByNivelDominio(id_nivel: string, id_dominio: string): Observable<Objetivo>{
    return this.http.get<Objetivo>(this.objetivolUrl + '/' + id_nivel + 'nivel/'
        + id_dominio + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Objetivo[]>{
    return this.http.get<Objetivo[]>(this.objetivolUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Objetivo>{
    return this.http.delete<Objetivo>(this.objetivolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
