import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from './../profissional.service';
import { Profissional } from './../profissional-model';
import { Component, OnInit } from '@angular/core';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-profissional-list',
  templateUrl: './profissional-list.component.html',
  styleUrls: ['./profissional-list.component.css']
})
export class ProfissionalListComponent implements OnInit {

  public profissionais: Profissional[];

  displayedColumns = ['nome', 'email', 'funcoes', 'action']

  constructor(private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      const profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = profissionais;
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  getFuncoes(profissional: Profissional): string{
    var funcoes = "";
    profissional.funcao.forEach(funcao => {
      if(funcoes == ""){
        funcoes = funcao.nome 
      } else {
        funcoes = funcoes + "; " + funcao.nome 
      }
    })
    return funcoes;
  }

  edit(profissional: Profissional){
    this.router.navigate(['/profissional/' + profissional.id]);
  }

  delete(profissional: Profissional): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        profissional.ativo = false;
        this.profissionalService.update(profissional).subscribe((p) => {
          this.profissionalService.showMessage('Profissional inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/profissional']);
        });
      }
    });
  }

}
