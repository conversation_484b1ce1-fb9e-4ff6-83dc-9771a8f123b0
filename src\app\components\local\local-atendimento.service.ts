import { BehaviorSubject, EMPTY, Observable } from "rxjs";
import { LocalAtendimento } from './local-atendimento-model';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LocalAtendimentoService{
    
    localAtendimentoUrl = `${environment.API_URL}/localAtendimento`;
  
  public funcoes: BehaviorSubject<LocalAtendimento[]> = 
    new BehaviorSubject<LocalAtendimento[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(localAtendimento: LocalAtendimento): Observable<LocalAtendimento>{
    //console.log(localAtendimento);
    return this.http.post<LocalAtendimento>(this.localAtendimentoUrl, localAtendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(localAtendimento: LocalAtendimento): Observable<LocalAtendimento>{
    //console.log(localAtendimento);
    /*const convMap = {};

    (localAtendimento.permission as Map <string, Map<string, string>>).forEach((val: Map<string, string>, key: string) => {
      convMap[key] = {}
      val.forEach( (val2: string, key2: string) => {
        convMap[key][key2] = val2; 
      })
    })
    console.log(convMap);
    localAtendimento.permission = convMap;*/
    return this.http.put<LocalAtendimento>(this.localAtendimentoUrl + "/" + localAtendimento.id, localAtendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<LocalAtendimento>{
    return this.http.get<LocalAtendimento>(this.localAtendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<LocalAtendimento[]>{
    return this.http.get<LocalAtendimento[]>(this.localAtendimentoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<LocalAtendimento>{
    return this.http.delete<LocalAtendimento>(this.localAtendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}