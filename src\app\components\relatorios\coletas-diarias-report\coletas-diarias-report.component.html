<mat-card>
    <mat-card-header>
        <mat-card-title>Relatório de Coletas</mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
            <form #ngForm>
                <mat-form-field  style="width: 20%; padding: 10px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select class="select" placeholder="Profissional" 
                        [(ngModel)]="profissional"
                        name="profissional" >
                        <mat-option *ngFor="let prof of profissionais" [value]="prof" >
                            {{ prof.nome }}    
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 10px;">
                    <mat-label>Paciente</mat-label>
                    <mat-select class="select" placeholder="Paciente" 
                        [(ngModel)]="paciente"
                        name="profissional" >
                        <mat-option *ngFor="let pac of pacientes" [value]="pac" >
                            {{ pac.nome }}    
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  class="data-field">
                    <mat-label>Data Inicial</mat-label>
                    <input class="input" matInput placeholder="Data Inicial" 
                        [max] = "today"
                        [(ngModel)]="dataInicial" name="dataInicial"
                        [matDatepicker]="pickerInicial" required>
                        <mat-datepicker-toggle matSuffix [for]="pickerInicial"></mat-datepicker-toggle>
                        <mat-datepicker #pickerInicial></mat-datepicker>
                </mat-form-field>

                <mat-form-field  class="data-field">
                    <mat-label>Data Final</mat-label>
                    <input class="input" matInput placeholder="Data Final" 
                        [max] = "today"
                        [(ngModel)]="dataFinal" name="dataFinal"
                        [matDatepicker]="pickerFinal" required>
                        <mat-datepicker-toggle matSuffix [for]="pickerFinal"></mat-datepicker-toggle>
                        <mat-datepicker #pickerFinal></mat-datepicker>
                </mat-form-field>
                
                <!-- <ng-container *ngFor="let column of allColumns">
                    <mat-checkbox [checked]="displayedColumns.includes(column)" 
                        (change)="toogleColumn($event)"
                        [value]="column">
                        {{ column }}
                    </mat-checkbox>
                </ng-container> -->
                <mat-form-field  style="width: 20%; padding: 10px;">
                    <mat-label>Colunas a exibir</mat-label>
                    <mat-select class="select" placeholder="Colunas a exibir" 
                        [(ngModel)]="displayedColumns"
                        name="colunas" multiple>
                        <mat-option *ngFor="let coluna of allColumns" [value]="coluna" >
                            {{ coluna }}    
                        </mat-option>
                    </mat-select>
        
                </mat-form-field>

                <button mat-icon-button (click)="search()" style="padding: 10px;" color="primary" matTooltip="Buscar">
                    <mat-icon>search</mat-icon>
                </button>

                <!-- <button mat-icon-button (click)="searchAll()" style="padding: 10px;" color="primary" matTooltip="Buscar Todos">
                    <mat-icon>list</mat-icon>
                </button> -->
                
                <button mat-icon-button (click)="clear()" style="padding: 10px;" color="primary" matTooltip="Limpar filtro">
                    <mat-icon>clear</mat-icon>
                </button>
            </form>
        </div>
        
        <!-- 
            data: string;
            profissional: string;
            paciente: string;
            score: string;
            percentual: string;
            status: string;
         -->
        <table mat-table [dataSource]="datasource" style="width: 100%;" class="mat-elevation-z8"
            *ngIf="datasource.data.length > 0">
            <!-- Data Column -->
            <ng-container matColumnDef="Data">
                <th mat-header-cell *matHeaderCellDef> Data </th>
                <td mat-cell *matCellDef="let element"> {{element.data}} </td>
            </ng-container>
            
            <!-- Profissional Column -->
            <ng-container matColumnDef="Profissional">
                <th mat-header-cell *matHeaderCellDef> Profissional </th>
                <td mat-cell *matCellDef="let element"> {{element.profissional}} </td>
            </ng-container>
            
            <!-- Paciente Column -->
            <ng-container matColumnDef="Paciente">
                <th mat-header-cell *matHeaderCellDef> Paciente </th>
                <td mat-cell *matCellDef="let element"> {{element.paciente}} </td>
            </ng-container>
            
            <!-- Objetivo Column -->
            <ng-container matColumnDef="Objetivo">
                <th mat-header-cell *matHeaderCellDef> Objetivo </th>
                <td mat-cell *matCellDef="let element"> {{element.objetivo}} </td>
            </ng-container>
            
            <!-- Etapa Column -->
            <ng-container matColumnDef="Etapa">
                <th mat-header-cell *matHeaderCellDef> Etapa </th>
                <td mat-cell *matCellDef="let element"> {{element.etapa}} </td>
            </ng-container>
            
            <!-- Score Column -->
            <ng-container matColumnDef="Score">
                <th mat-header-cell *matHeaderCellDef> Score </th>
                <td mat-cell *matCellDef="let element"> {{element.score}} </td>
            </ng-container>
            
            <!-- Percentual Column -->
            <ng-container matColumnDef="%">
                <th mat-header-cell *matHeaderCellDef> Percentual </th>
                <td mat-cell *matCellDef="let element"> {{element.percentual | number:'1.0-2'}}% </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="Status">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element"> {{element.status }} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
    </mat-card-content>
</mat-card>
