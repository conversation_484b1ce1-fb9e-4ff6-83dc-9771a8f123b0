import { LoadingService } from 'src/app/shared/service/loading.service';
import { startWith, map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { AuthService } from './../../template/auth/auth.service';
import { ParentescoService } from './../../parente/parentesco.service';
import { Parente } from './../../parente/parente-model';
import { Profissional } from './../../profissional/profissional-model';
import { ParenteService } from './../../parente/parente.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { MAT_DATE_FORMATS, DateAdapter } from '@angular/material/core';
import { APP_DATE_FORMATS, AppDateAdapter } from './../../../shared/format-datepicker';
import { Funcao } from './../../funcao/funcao-model';
import { Router } from '@angular/router';
import { PacienteService } from './../paciente.service';
import { Paciente } from './../paciente-model';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { EnderecoService } from './../../../shared/service/endereco-service';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';


@Component({
  selector: 'app-paciente-create',
  templateUrl: './paciente-create.component.html',
  styleUrls: ['./paciente-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class PacienteCreateComponent implements OnInit {

  paciente: Paciente = new Paciente();
  pacientes: Paciente[] = [];
  cpfMask = [/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'-',/\d/,/\d/];
  telefoneMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  celularMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  cepMask = [/\d/,/\d/,'.',/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/];
  tipoTelefone = 'C';

  profissionais: Profissional[] = [];
  profissional: Profissional = new Profissional();
  profissionalLogado: Profissional = new Profissional();
  profissionalAdd: Profissional;
  
  parentes: Parente[] = [];
  parente: Parente = new Parente();
  //parenteAdd: Parente;

  funcaoProfissional: Funcao = new Funcao();
  //parentescoParente: string;
  //parentescos: Parentesco[] = [];

  displayedColumnsEquipe = ['nome', 'funcao', 'action'];
  displayedColumnsParentes = ['nome', 'parentesco', 'action'];

  //Form Controls
  nome = new FormControl('', [Validators.required]);
  dataNascimento = new FormControl('', [Validators.required]);
  sexo = new FormControl('', [Validators.required]);
  parenteFC = new FormControl();
  tiposIntervencao = new FormControl('', [Validators.required]);

  confirmDialog: ConfirmDialogComponent;

  filteredParents: Observable<Parente[]>;

  public hasAccesCreate: boolean;
  public hasAccesUpdate: boolean;
  public hasAccesDelete: boolean;

  @ViewChild(NgForm) form;

  /*
  getErrorMessage() {
    if (this.nome.hasError('required')) {
      return 'Nome é obrigatório.';
    }

    if(this.dataNascimento.hasError('required')){
      return 'Data de nascimento é obrigatória.';
    }

    if(this.sexo.hasError('required')){
      return 'Sexo é obrigatória.';
    }
  }
  */

  constructor(private pacienteService: PacienteService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    private loadingService: LoadingService,
    public authService: AuthService,
    private parenteService: ParenteService,
    private router: Router,
    private enderecoService: EnderecoService,
    private dialog: MatDialog) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    
    try {
      // Carregando profissionais
      const profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = profissionais;
      
      // Recupero o profissional que está logado
      const userEmail = this.authService.getUser().email;
      this.profissional = this.profissionais.find(prof => prof.email === userEmail);
      this.profissionalLogado = this.profissional;
      
      // Inicializo as roles, caso esteja nulo ainda
      if (this.paciente.roles == null) {
        this.paciente.roles = new Map<string, string>();
      }
      
      // Adiciono profissional a roles (owner)
      if (this.profissional?.uid && !this.paciente.roles[this.profissional.uid]) {
        this.paciente.roles[this.profissional.uid] = "owner";
      }
      
      //console.log(this.paciente.roles);
      
      // Carregando pacientes
      this.pacientes = await this.pacienteService.find().toPromise();
      
      // Verifico se o usuário tem os acessos necessários
      this.hasAccesCreate = this.authService.verifySimpleAccess(['*'], 'Paciente.Cadastro de pacientes','insert')
      this.hasAccesDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de parentes (família)','delete')

      // Carregando parentes
      const parentes = await this.parenteService.find().toPromise();
      this.parentes = parentes;
      this.filteredParents = this.parenteFC.valueChanges.pipe(
        startWith(''),
        map(value => typeof value === 'string' ? value : value.nome),
        map(nome => nome ? this._filter(nome) : this.parentes.slice())
      );
  
    } catch (error) {
      console.error("Erro: ", error);
    } finally {
      this.loadingService.hide();
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    //console.log(funcoes)


    return funcoes;
  }

  addProfissional(){
    if(this.profissional.id != undefined){
      //console.log('Adicionando profissional a equipe')
      
      //Se a equipe estiver vazia, inicializo a equipe
      if(this.paciente.equipe == undefined){
        this.paciente.equipe = [];
      }
      
      //Adiciono o profissional no array de equipe, mas antes verifico se ele já não foi incluído antes
      if(this.paciente.equipe.length == 0 || (this.profissional.id != undefined && this.paciente.equipe.filter(p => p.id == this.profissional.id).length == 0)){
        //Adiciono a função que o profissional vai exercer na equipe
        //console.log(this.funcaoProfissional)
        this.profissionalAdd = this.profissional;
        this.profissionalAdd.funcao = [this.funcaoProfissional];
        //console.log(this.profissionalAdd);

        //console.log(this.paciente.roles);
        //Inicalizo as roles, caso esteja nulo ainda
        //console.log(this.paciente.roles)
        if(this.paciente.roles == null){
          this.paciente.roles = new Map<string, string>();
        }
        //Adiciono profissional a roles (leitura)
        //console.log(this.paciente.roles)
        if(this.profissional.uid != undefined){ //Se tem usuário de login
          if(this.paciente.roles[this.profissional.uid] == null){ //Se o usuário não está nas roles
            this.paciente.roles[this.profissional.uid] = "read";
          }
        }
        //console.log(this.paciente.roles)
    
        //Adiciono o profissional no array de equipe
        this.paciente.equipe.push(this.profissionalAdd);
        this.paciente.equipe = [...this.paciente.equipe];
        this.profissional = new Profissional();
      } else {
        this.pacienteService.showMessage("Profissional já incluído anteriormente!", true);
        this.profissional = new Profissional();
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o profissional a ser adicionado na equipe do paciente!", true);
    }
  }

  deleteProfissional(id: number){
    var profissional: Profissional;

    //Pego o profissional a ser excluído da equipe
    profissional = this.paciente.equipe[id];

    //console.log(this.paciente.roles)
    var roles = new Map(Object.entries(this.paciente.roles));

    //Retiro o acesso do profissional, exceto caso ele seja o "owner"
    if(roles.get(profissional.uid) != "owner"){
      roles.delete(profissional.uid);
    }

    this.paciente.roles = new Map<string, string>();
    Array.from(roles.keys()).forEach(key => {
      this.paciente.roles[key] = roles.get(key);
    })
        
    //console.log(this.paciente.roles)

    this.paciente.equipe.splice(id, 1);
    this.paciente.equipe = [...this.paciente.equipe];
    //console.log(this.paciente.roles)
  }

  displayFn(parente: Parente): string {
    return parente && parente.nome ? parente.nome + " (" + parente.parentesco + ")" : '';
  }

  private _filter(value: string): Parente[] {
    const filterValue = value.toLowerCase();

    return this.parentes.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
    
  }

  addParente(){
    //console.log('Adicionando parente a família')
    //Verifico se algum parente foi selecionado
    if(this.parente.id != undefined){
      //Se parentes estiver vazia, iinicializo parentes
      if(this.paciente.parentes == undefined){
        this.paciente.parentes = [];
      }
      
      //console.log(this.parente);
  
      //Adiciono o parente no array de família, mas antes verifico se ele já não foi incluído antes
      if(this.paciente.parentes.length == 0 || (this.parente.id != undefined && this.paciente.parentes.filter(p => p.id == this.parente.id).length == 0)){
        //Inicalizo as roles, caso esteja nulo ainda
        //console.log(this.paciente.roles)
        if(this.paciente.roles == null){
          this.paciente.roles = new Map<string, string>();
        }

        //Adiciono parente a roles (leitura)
        if(this.parente.uid != undefined){ //Se tem usuário de login
          //console.log(this.parente.uid)
          if(this.paciente.roles[this.parente.uid] == null){ //Se o usuário não está nas roles
            //console.log(this.parente.uid)
            this.paciente.roles[this.parente.uid] = "read";
          }
        }

        this.paciente.parentes.push(this.parente);
        this.paciente.parentes = [...this.paciente.parentes];
        this.parente = new Parente();
        //console.log(this.paciente.roles)
      } else {
        this.pacienteService.showMessage("Parente já incluído anteriormente!", true);
        this.parente = new Parente();
        //this.parenteFC.setValue(undefined);
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o parente a ser associado ao paciente!", true);
    }
    
  }

  deleteParente(id: number){
    var parente: Parente;

    //Pego o parente a ser excluído do paciente
    parente = this.paciente.parentes[id];

    //console.log(this.paciente.roles)
    var roles = new Map(Object.entries(this.paciente.roles));

    roles.delete(parente.uid);

    this.paciente.roles = new Map<string, string>();
    Array.from(roles.keys()).forEach(key => {
      this.paciente.roles[key] = roles.get(key);
    })
        
    //console.log(this.paciente.roles)

    this.paciente.parentes.splice(id, 1);
    this.paciente.parentes = [...this.paciente.parentes];
  }

  /*setFuncaoFamila(){
    this.parentescoParente = this.parente.funcao[0]
  }*/

  async save(): Promise<void> {
    let duplicados = this.pacientes.filter(p => p.nome.trim() == this.paciente.nome.trim() && p.ativo == true);
    
    if (this.form.valid) {
      if (this.paciente.id == undefined) {
        // Aguarda a verificação se o profissional está na equipe
        await this.checkProfissionalInEquipe(this.profissionalLogado);

        // Verifica se a equipe está vazia
        if (!this.paciente.equipe || this.paciente.equipe.length === 0) {
          this.pacienteService.showMessage('A equipe não pode estar vazia na criação de um paciente!', true);
          return; // Sai da função sem prosseguir com a criação
        }
  
        if (duplicados.length > 0) {
          this.havePacienteNameNew();
        } else {
          this.paciente.ativo = true;
          this.pacienteService.create(this.paciente).subscribe((id) => {
            this.pacienteService.showMessage('Paciente criado com sucesso!');
            this.router.navigate(['/paciente/' + id]);
          });
        }
      } else {
        this.pacienteService.update(this.paciente).subscribe((paciente) => {
          this.pacienteService.showMessage('Paciente alterado com sucesso!');
          this.router.navigate(['/paciente/' + paciente.id]);
        });
      }
    } else {
      this.pacienteService.showMessage('Existem campos inválidos no formulário!', true);
    }
  }    
  
  async checkProfissionalInEquipe(profissional: Profissional): Promise<void> {
    // Caso ainda n tenha equipe eu crio
    if(this.paciente.equipe == undefined){
      this.paciente.equipe = [];
    }
    
    // Verifica se o profissional está na equipe
    if (!this.paciente.equipe?.find(prof => prof.uid == profissional.uid) && profissional.funcao.find(funcao => funcao.id != '3')) {
      const dialogRef = this.dialog.open(ConfirmDialogComponent, {
        width: '400px',
        data: {
          valida: true,
          msg: 'Você não faz parte da equipe responsável por este Paciente. Lembre-se de que, sem estar na equipe, o acesso aos dados completos do Paciente é restrito. Deseja solicitar a inclusão na equipe?'
        }
      });
  
      // Espera a resposta do diálogo
      const result = await dialogRef.afterClosed().toPromise();
  
      // Se o usuário confirmar, adiciona o profissional à equipe
      if (result) {
        profissional.funcao = [this.profissional.funcao.find(funcao => funcao.id == '1')];
        this.paciente.equipe.push(profissional);
      }
    }
  }    

  havePacienteNameNew(){
    let dialogRef = null;
    dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        valida: true,
        msg: 'Já existe um paciente cadastrado com o nome "<strong>' + this.paciente.nome + '</strong>", tem certeza que deseja criar outro?'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.paciente.ativo=true;
        this.pacienteService.create(this.paciente).subscribe((id) => {
          this.pacienteService.showMessage('Paciente criado com sucesso!');
          this.router.navigate(['/paciente/' + id]);
        });
      }
    });
  }

  validaMaskTelefone(){
    if(this.paciente.telefone.replace('_','').length < 15){
      this.tipoTelefone = 'T';
    }else{
      this.tipoTelefone = 'C';
    }
  }

  consultaCep(){
    this.enderecoService.find(this.paciente.endereco.cep.replace('.','').replace('-','')).subscribe(data => {
      this.paciente.endereco.logradouro = data.logradouro;
      this.paciente.endereco.complemento = data.complemento;
      this.paciente.endereco.bairro = data.bairro;
      this.paciente.endereco.cidade = data.localidade;
      this.paciente.endereco.uf = data.uf;
      this.paciente.endereco.pais = 'Brasil';
    }, () => {
      
    })
  }

  cancel(): void{
    this.router.navigate(['/paciente']);
  }

  do() {

  }

}
