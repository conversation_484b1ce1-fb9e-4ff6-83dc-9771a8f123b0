import { FirebaseUserModel } from 'src/app/components/template/auth/user-model';
import { Profissional } from './../../profissional/profissional-model';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/components/template/auth/auth.service';
import { PacienteService } from './../../paciente/paciente.service';
import { PICService } from '../pic.service';
import { Paciente } from 'src/app/components/paciente/paciente-model';
import { PIC } from '../pic-model';
import { Component, OnInit } from '@angular/core';
import moment from 'moment';
import { ProfissionalService } from '../../profissional/profissional.service';
import { SessaoColetaDiariaPIC } from '../../coletadiariavbmapp/coletadiariapic-model';
import { ColetadiariaPICService } from '../../coletadiariavbmapp/coletadiariapic.service';
import { ESDMChecklistGraph } from '../../esdmchecklist/esdmchecklist-graph-model';
import { ObjetivoComportamental } from '../../objetivopic/objetivo-comportamental-model';
import { ChangeDetectorRef } from '@angular/core';
import { forkJoin } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { LoadingService } from 'src/app/shared/service/loading.service';


@Component({
  selector: 'app-planopic-graph',
  templateUrl: './planopic-graph.component.html',
  styleUrls: ['./planopic-graph.component.css']
})
export class PlanoPICGraphComponent implements OnInit {

  public pic: PIC = new PIC();
  public idPlanoIntervencao: string;
  public coletas: SessaoColetaDiariaPIC[];

  public paciente: Paciente = new Paciente();
  public profissional: Profissional = new Profissional();

  public objetivo: ObjetivoComportamental = new ObjetivoComportamental();

  public colors: any[] = [];

  public graphs: { [key: string]: ESDMChecklistGraph } = {}; // Gráficos por objetivo

  constructor(
    private picService: PICService,
    private coletaDiariaPICService: ColetadiariaPICService,
    private pacienteService: PacienteService,
    private profissionalService: ProfissionalService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    this.loadingService.show();

    this.idPlanoIntervencao = this.route.snapshot.paramMap.get('id');

    this.picService.findById(this.idPlanoIntervencao).pipe(
      switchMap(plano => {
        this.pic = plano;
        return forkJoin([
          this.pacienteService.findById(plano.idPaciente),
          this.profissionalService.findById(plano.idProfissional),
          this.coletaDiariaPICService.findByPIC(this.idPlanoIntervencao)
        ]);
      })
    ).subscribe(async ([paciente, profissional, coletas]) => {
      this.paciente = paciente;
      this.profissional = profissional;
      this.coletas = coletas;
      await this.organizeColetas();
      this.loadingService.hide();
      this.cdr.detectChanges(); // Força a atualização da view
    });
  }

  async organizeColetas() {
    const organizadosPorObjetivo = {};

    this.coletas.forEach(sessao => {
      sessao.objetivosColeta.forEach(objetivo => {
        const idObjetivo = objetivo.idObjetivo;
        const dataSessao = sessao.data;
        const horaInicio = sessao.horaInicio;
        const horaTermino = sessao.horaTermino;

        const qtdObservadaValida = objetivo.qtdObservada !== null && objetivo.qtdObservada !== undefined;
        const duracoesValidas = objetivo.duracoes && objetivo.duracoes.length > 0;

        if (qtdObservadaValida || duracoesValidas) {
          if (!organizadosPorObjetivo[idObjetivo]) {
            organizadosPorObjetivo[idObjetivo] = [];
          }

          // Verifica se já existe uma coleta com a mesma data
          const coletaExistente = organizadosPorObjetivo[idObjetivo].find(coleta =>
            coleta.data === dataSessao
          );

          if (coletaExistente) {
            // Se já existir, consolida os dados (soma qtdObservada e combina durações)
            if (qtdObservadaValida) {
              coletaExistente.qtdObservada = (coletaExistente.qtdObservada || 0) + objetivo.qtdObservada;
            }
  
            if (duracoesValidas) {
              coletaExistente.duracoes = coletaExistente.duracoes.concat(objetivo.duracoes);
            }

            if (horaInicio < coletaExistente.horaInicio) {
              coletaExistente.horaInicio = horaInicio;
            }
  
            if (horaTermino > coletaExistente.horaTermino) {
              coletaExistente.horaTermino = horaTermino;
            }
            
          } else {
            // Se não existir, adiciona uma nova entrada de coleta
            const novaColeta = {
              data: dataSessao,
              horaInicio: sessao.horaInicio,
              horaTermino: sessao.horaTermino,
              sessao: sessao.sessao
            };

            if (qtdObservadaValida) {
              novaColeta['qtdObservada'] = objetivo.qtdObservada;
            }

            if (duracoesValidas) {
              novaColeta['duracoes'] = objetivo.duracoes;
            }

            organizadosPorObjetivo[idObjetivo].push(novaColeta);
          }
        }
      });
    });

    // Ordena as coletas por data e hora de início
    for (const idObjetivo in organizadosPorObjetivo) {
      if (organizadosPorObjetivo.hasOwnProperty(idObjetivo)) {
        organizadosPorObjetivo[idObjetivo].sort((a, b) => {
          const dataA = new Date(a.data).getTime();
          const dataB = new Date(b.data).getTime();

          // Se as datas forem iguais, comparar as horas de início diretamente
          if (dataA === dataB) {
            const horaInicioA = a.horaInicio;
            const horaInicioB = b.horaInicio;

            // Comparar as strings das horas de início
            if (horaInicioA < horaInicioB) return -1;
            if (horaInicioA > horaInicioB) return 1;
            return 0;
          }

          return dataA - dataB;
        });
      }
    }

    // Configura e atualiza os gráficos de uma só vez, depois que todas as coletas estão organizadas
    for (const idObjetivo in organizadosPorObjetivo) {
      if (organizadosPorObjetivo.hasOwnProperty(idObjetivo)) {
        // Inicializa o gráfico para cada objetivo
        await this.setGraph(idObjetivo);
        
        // Atualiza o gráfico com todos os dados de coleta para o objetivo
        organizadosPorObjetivo[idObjetivo].forEach(async coleta => {
          await this.updateGraph(idObjetivo, coleta);
        });
      }
    }

    // console.log(organizadosPorObjetivo);
  }

  async setGraph(idObjetivo: string) {
    if (!this.graphs[idObjetivo]) {
      let objetivo = this.pic.objetivos.find(o => o.id === idObjetivo);
      let textTittle;
      let textDatasets;
      const graph = new ESDMChecklistGraph();

      if(objetivo?.tipoColeta == 'Registro de Eventos') {
        graph.chartType = 'line';
        textDatasets = 'Quantidade';
        textTittle = 'Evolução do Objetivo - ' + objetivo?.comportamentoAlvo;
      } else {
        graph.chartType = 'line';
        textDatasets = 'Mínimo';
        if(objetivo?.tipoColeta == 'Cronometragem') {
          textTittle = 'Evolução do Objetivo - ' + objetivo?.comportamentoAlvo + ' (Segundos)';
        } else {
          textTittle = 'Evolução do Objetivo - ' + objetivo?.comportamentoAlvo + ' (' + objetivo?.medidaIntervalo + ')';
        }
      }

      graph.datasets = [{
        data: [],
        label: textDatasets
      }];
      graph.labels = [];

      if(objetivo?.tipoColeta == 'Registro de Eventos'){
        graph.options = {
          legend: {
            display: true,
            position: 'top'
          },
          title: {
            display: true,
            text: textTittle,
            fontSize: 16
          },
          plugins: {
            datalabels: {
              display: false // Desativa os valores dentro das barras
            }
          },
          scales: {
            xAxes: [{
              type: 'category', // Usa 'category' para manipular strings no eixo X
              gridLines: {
                display: true,
                color: '#f0f0f0',
                lineWidth: 1
              },
            }],
            yAxes: [{
              gridLines: {
                display: true,
                color: '#f0f0f0',
                lineWidth: 1
              },
              ticks: {
                beginAtZero: true,
                fontSize: 12,
                stepSize: 1,
              }
            }]
          },
          elements: {
            line: {
              tension: 0.4
            }
          }
        };
      } else {
        // Se não existirem datasets para a média, mínimo e máximo, cria eles
        if (!graph.datasets[1]) {
          graph.datasets[1] = {
            data: [],
            label: `Média`,
          };
        }
    
        if (!graph.datasets[2]) {
          graph.datasets[2] = {
            data: [],
            label: `Máximo`,
          };
        }
        // Configura as escalas e tooltips para exibir as durações em HH:MM:SS
        graph.options = {
          legend: {
            display: true,
            position: 'top'
          },
          title: {
            display: true,
            text: textTittle,
            fontSize: 16
          },
          plugins: {
            datalabels: {
              display: false // Desativa os valores dentro das barras
            }
          },
          tooltips: {
            callbacks: {
              label: function(tooltipItem, data) {
                let totalSeconds = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index] as number;
                let textTooltip = 'Média das durações:';
                
                if (tooltipItem.datasetIndex == 0) {
                  textTooltip = 'Menor Duração:';
                } else if (tooltipItem.datasetIndex == 2) {
                  textTooltip = 'Maior Duração:';
                }

                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                return `${textTooltip} ${hours}h${minutes < 10 ? '0' : ''}${minutes}m${seconds < 10 ? '0' : ''}${seconds.toFixed(0)}s`;
              }
            }
          },
          scales: {
            yAxes: [{
              ticks: {
                callback: function(value: number) {
                  const hours = Math.floor(value / 3600);
                  const minutes = Math.floor((value % 3600) / 60);
                  const seconds = value % 60;
                  return `${hours}h${minutes < 10 ? '0' : ''}${minutes}m${seconds < 10 ? '0' : ''}${seconds}s`;
                },
                beginAtZero: true // Começa o eixo Y no zero
              },
            }]
          }
        };
      }

      this.graphs[idObjetivo] = graph;
    }
  }

  async updateGraph(idObjetivo: string, coleta) {
    const graph = this.graphs[idObjetivo];
    const objetivo = this.pic.objetivos.find(o => o.id === idObjetivo);
    let valorMedio = coleta.qtdObservada !== undefined 
      ? coleta.qtdObservada 
      : this.calcularMediaDuracoes(coleta.duracoes);
  
    if (valorMedio !== null) {
      // Formata a data e as horas como array de strings, onde cada string será uma linha no label
      const dataHora = [
        moment(coleta.data).format('DD/MM/YYYY'),
        `\n` + coleta.horaInicio + ' - ' + coleta.horaTermino
      ];
  
      graph.labels.push(dataHora); // Adiciona o array ao labels
      
      // Adiciona o valor médio ao gráfico
      if (objetivo?.tipoColeta == 'Registro de Eventos'){
        graph.datasets[0].data.push(valorMedio);
      } else {
        graph.datasets[1].data.push(valorMedio);
      }
  
      // Se houver durações, calcula a duração mínima e máxima e adiciona aos datasets
      if (coleta.duracoes && coleta.duracoes.length > 0) {
        const duracoesEmSegundos = coleta.duracoes.map(d => d.duracao);
        let maxDuracao = Math.max(...duracoesEmSegundos);
        let minDuracao = Math.min(...duracoesEmSegundos);
  
        graph.datasets[0].data.push(minDuracao); // Adiciona a duração mínima
        graph.datasets[2].data.push(maxDuracao); // Adiciona a duração máxima
      }
    }
  }  

  calcularMediaDuracoes(duracoes: {duracao: number, manual: boolean}[]): number | null {
    if (!duracoes || duracoes.length === 0) {
      return null; // Retorna null se não houver durações válidas
    }

    // Calcula a média das durações, somando apenas o valor da propriedade 'duracao'
    const total = duracoes.reduce((acc, item) => acc + item.duracao, 0);
    return total / duracoes.length;
  }

  formatarDuracao(duracao: number, medidaIntervalo: string): string {
    let totalSeconds = duracao;
  
    if (medidaIntervalo === 'Minutos') {
      totalSeconds = duracao * 60;
    } else if (medidaIntervalo === 'Horas') {
      totalSeconds = duracao * 3600;
    }
  
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
  
    return `${hours}h${minutes < 10 ? '0' : ''}${minutes}m${seconds < 10 ? '0' : ''}${seconds.toFixed(0)}s`;
  }

  get user(): FirebaseUserModel {
    return this.authService.getUser();
  }

  getFuncoesUsuario(paciente: Paciente): string[] {
    let funcoes: string[] = [];
    let profissional: Profissional;

    if (paciente.equipe != undefined) {
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);

      if (profissional != undefined) {
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        });
      }
    }

    return funcoes;
  }
}
