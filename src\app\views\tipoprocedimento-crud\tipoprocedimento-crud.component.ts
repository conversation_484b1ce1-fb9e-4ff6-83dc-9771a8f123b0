import { HeaderService } from './../../components/template/header/header.service';
import { AuthService } from './../../components/template/auth/auth.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-tipoprocedimento-crud',
  templateUrl: './tipoprocedimento-crud.component.html',
  styleUrls: ['./tipoprocedimento-crud.component.css']
})
export class TipoprocedimentoCrudComponent implements OnInit {

  public hasAccessCreate: boolean = false;

  constructor(private router: Router,
    public authService: AuthService,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Tipos de procedimento',
        icon: 'ballot',
        routeUrl: '/tipoprocedimento'
      }
    }

  ngOnInit(): void {
    this.hasAccessCreate = this.authService.verifySimpleAccess(['*'], 'Tipo de Procedimento.Cadastro de tipos de procedimentos','create')
  }

}
