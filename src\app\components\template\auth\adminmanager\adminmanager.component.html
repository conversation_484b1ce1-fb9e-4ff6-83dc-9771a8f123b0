<mat-card style="width: 95%; margin: auto;">
    <mat-card-header>
      <mat-card-title>Usuários Administradores</mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <table mat-table [dataSource]="datasourceAdmins"> 
            <!-- Nome Column -->
            <ng-container matColumnDef="nome">
                <th mat-header-cell *matHeaderCellDef>Nome</th>
                <td mat-cell *matCellDef="let row" >{{row.nome}}</td>
            </ng-container>
    
          <!-- Action Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Ações</th>
            <td mat-cell *matCellDef="let row">
                <a (click)="delete(row)" class="delete">
                    <i class="material-icons">
                        delete
                    </i>
                </a>
            </td>
          </ng-container>
      
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <form>
            <mat-form-field  style="width: 15%; padding: 20px;">
                <mat-label>Usuários</mat-label>
                <mat-select placeholder="Profissional" 
                    [(ngModel)]="user"
                    name="user">
                    <mat-option *ngFor="let user of users" [value]="user" (click)="add()">
                        {{user.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </form>















      <!--div style="display: flex; flex-direction: column; width: 100%; text-align: center;">
        <div *ngIf='user.provider == "password"' style="width: 100%;">
          <form [formGroup]="profileForm" style="display: flex; flex-direction: column;">
              <mat-form-field >
                <mat-label>Nome</mat-label>
                <input class="input" matInput placeholder="Nome" 
                    formControlName="nome"  name="nome" required>
              </mat-form-field>
              <mat-form-field >
                <mat-label>E-mail</mat-label>
                <input class="input" matInput placeholder="E-mail" type="email"
                    formControlName="email"  name="email">
              </mat-form-field>
              <mat-form-field >
                <mat-label>Telefone</mat-label>
                <input class="input" matInput placeholder="Telefone"
                    formControlName="telefone"  name="telefone" required>
              </mat-form-field>
          </form>
          <mat-list>
            <div class="funcoes">Funções</div>
            <mat-list-item *ngFor="let funcao of pessoa.funcao" class="v-middle">
              <mat-icon style="padding-right: 5px;">star</mat-icon> {{ funcao.nome }}
            </mat-list-item>
          </mat-list>
          <button mat-flat-button	color="primary" (click)="save(profileForm.value)">Salvar</button>
          <br><br>
          <a (click)="redefinePassword()" style="cursor: pointer">Redefinir senha</a>
        </div>
      </div-->
    </mat-card-content>
  </mat-card>