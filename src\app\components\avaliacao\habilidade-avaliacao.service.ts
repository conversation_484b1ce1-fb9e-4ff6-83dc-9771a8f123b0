import { catchError, map } from 'rxjs/operators';
import { HabilidadeAvaliacao } from './habilidade-avaliacao-model';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from '../../../environments/environment';
import { Injectable } from '@angular/core';
import { EMPTY, Observable } from 'rxjs';
import { DominioAvaliacao } from './dominio-avaliacao-model';

@Injectable({
  providedIn: 'root'
})

export class HabilidadeAvaliacaoService {

  habilidadeUrl = `${environment.API_URL}/habilidade_avaliacao`;
  // dominioUrl = `${environment.API_URL}/dominio_avaliacao`;
  // nivelUrl = `${environment.API_URL}/nivel_avaliacao`;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(habilidade: HabilidadeAvaliacao): Observable<string>{
    return this.http.post<string>(this.habilidadeUrl, habilidade).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(habilidade: HabilidadeAvaliacao): Observable<HabilidadeAvaliacao>{
    return this.http.put<HabilidadeAvaliacao>(this.habilidadeUrl + "/" + habilidade.id, habilidade).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<HabilidadeAvaliacao>{
    return this.http.get<HabilidadeAvaliacao>(this.habilidadeUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findDominiosByNivel(idNivelAvaliacao: string): Observable<DominioAvaliacao[]>{
    return this.http.get<DominioAvaliacao[]>(this.habilidadeUrl + '/' + idNivelAvaliacao + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  async findByTipoAvaliacao(idTipoAvaliacao: string): Promise<Observable<HabilidadeAvaliacao[]>>{
    return this.http.get<HabilidadeAvaliacao[]>(this.habilidadeUrl + '/tipo_avaliacao/' + idTipoAvaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByNivelDominio(idNivelAvaliacao: string, idDominioAvaliacao: string): Observable<HabilidadeAvaliacao[]>{
    return this.http.get<HabilidadeAvaliacao[]>(this.habilidadeUrl + '/' + idNivelAvaliacao + '/nivel/' + idDominioAvaliacao + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<HabilidadeAvaliacao[]>{
    return this.http.get<HabilidadeAvaliacao[]>(this.habilidadeUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<HabilidadeAvaliacao>{
    return this.http.delete<HabilidadeAvaliacao>(this.habilidadeUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
