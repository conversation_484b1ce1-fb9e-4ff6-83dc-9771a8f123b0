import { AuthService } from './../auth/auth.service';
import { FirebaseUserModel } from './../auth/user-model';
import { HeaderData } from './header-data-model';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HeaderService {

  private _headerData = new BehaviorSubject<HeaderData>({
    title: 'Início',
    icon: 'home',
    routeUrl: ''
  })

  private _user: BehaviorSubject<FirebaseUserModel> = new BehaviorSubject<FirebaseUserModel>(null);

  constructor(private authService: AuthService) { }

  get headerData(): HeaderData {
    return this._headerData.value;
  }

  set headerData(headerData: HeaderData){
    this._headerData.next(headerData)
  }
  /*
  verifySimpleAccess(funcao: string, funcionalidade: string, nivel: string): boolean{
    let keys: string[];
    let access = false;
    
    if(funcao == "*"){ 
      //Verifico se ele possui a permissão em qualquer uma de suas funções
      keys = Array.from((this.getUser() as FirebaseUserModel).permission.keys());
      console.log(keys)
      keys.forEach(key => {
        console.log(key)
        //console.log((this.getUser() as FirebaseUserModel).permission.get(key))
        
        if((this.getUser() as FirebaseUserModel).permission.get(key).get(funcionalidade).get(nivel) != "X"){
          access = true;
        }
        
      })

    }
    return access;
  }*/

  /*
  setUser(user: any){
    let u: FirebaseUserModel = new FirebaseUserModel();
    u.image = user.photoURL;
    u.name = user.displayName;
    u.provider = user.providerData[0].providerId;
    u.email = user.email;
    u.uid = user.uid;
    user.getIdTokenResult().then ((idTokenResult) => {
      u.admin = idTokenResult.claims.admin? true: false;
      //console.log("admin: " + u.admin);
    })

    //u.permission = this._user.value.permission;
    this._user.next(u)
    //this.user = user;
    console.log(this._user.value)
  }*/

  /*setUserPermission(permission: Map<string, Map< string, Map<string,string> >>){
    console.log(permission)
    this._user.value.permission = permission;
  }*/

  /*getUser(): any{
    console.log(this._user.value)
    return this._user.value;
  }*/
}
