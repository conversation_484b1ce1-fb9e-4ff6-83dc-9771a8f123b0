<h2 mat-dialog-title>Editar Nota</h2>

<mat-dialog-content>
  <form #form="ngForm">
    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>Tí<PERSON><PERSON></mat-label>
      <input matInput placeholder="Título" [(ngModel)]="notaEditada.titulo" name="titulo" #titulo="ngModel" required>
      <mat-error *ngIf="titulo.invalid && titulo.touched">T<PERSON><PERSON><PERSON> é obrigatório</mat-error>
    </mat-form-field>

    <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap; padding-top: 20px; justify-content: space-between;">
      <mat-form-field appearance="outline" style="width: 48%;">
        <mat-label>Data</mat-label>
        <input matInput [matDatepicker]="picker" placeholder="Data" [(ngModel)]="notaEditada.data" name="data" #data="ngModel" required>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="data.invalid && data.touched">Data é obrigatória</mat-error>
      </mat-form-field>
  
      <mat-form-field *ngIf="isTeamMember()" appearance="outline" style="width: 48%;">
        <mat-label>Nível de acesso</mat-label>
        <mat-select [(ngModel)]="notaEditada.nivelAcesso" name="nivelAcesso" required>
          <mat-option [value]="0">Público</mat-option>
          <mat-option [value]="1">Apenas equipe</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>Descrição</mat-label>
      <textarea matInput placeholder="Descrição" [(ngModel)]="notaEditada.descricao" name="descricao" #descricao="ngModel" rows="4" required cdkTextareaAutosize cdkAutosizeMinRows="4" cdkAutosizeMaxRows="10"></textarea>
      <mat-error *ngIf="descricao.invalid && descricao.touched">Descrição é obrigatória</mat-error>
    </mat-form-field>

    <mat-dialog-actions align="end" style="padding-bottom: 20px;">
      <button mat-button type="button" (click)="onCancel()">Cancelar</button>
      <button mat-raised-button color="primary" [disabled]="form.invalid" (click)="onSave()">Salvar</button>
    </mat-dialog-actions>
  </form>
</mat-dialog-content>
