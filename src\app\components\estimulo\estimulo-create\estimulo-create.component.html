<mat-card>
    <mat-card-title class="title">{{ estimulo == undefined || estimulo.nome == undefined ? "Novo Estímulo" :
        estimulo.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <mat-form-field style="width: 30%; padding: 10px;">
                <input type="text" placeholder="Categoria" matInput [(ngModel)]="estimulo.grupo" name="Categoria"
                    [formControl]="categoriaFC" [matAutocomplete]="auto" required>
                <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                    <mat-option *ngFor="let categoria of filteredCategorias | async" [value]="categoria">
                        {{categoria.nome}}
                    </mat-option>
                </mat-autocomplete>
                <mat-error *ngIf="categoriaFC.invalid">Categoria é obrigatória.</mat-error>
            </mat-form-field>

            <mat-form-field style="width: 60%; padding: 10px">
                <input class="input" matInput placeholder="Nome" [(ngModel)]="estimulo.nome" name="nome" required>
                <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>
            </mat-form-field>
        </div>
    </form>

    <button mat-raised-button (click)="save(false)" color="primary">
        Salvar e Incluir Novo
    </button>

    <button mat-raised-button (click)="save(true)" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>