<div fxFlex.gt-sm="100%" fxFlex="100">
    <mat-card class="mat-elevation-z0">
        <mat-card-title>
            <div style="display: flex;">
                <div  mat-card-avatar *ngIf="paciente.sexo=='M'" class="header-image" style=" background-image: url('/assets/img/menino_perfil.svg');"></div>
                <div  mat-card-avatar *ngIf="paciente.sexo=='F'" class="header-image" style="background-image: url('/assets/img/menina_perfil.svg');"></div>
                <div style="margin-top: 10px; margin-left: 5px;">
                    {{ paciente.nome }}
                </div>
            </div>
        </mat-card-title>
        <mat-card-subtitle>
            <span style="color: black;">({{idade+' meses'}} - {{paciente.dataNascimento | date: 'dd/MM/yyyy'}})</span>
        </mat-card-subtitle>
        <mat-card-content> 
                
                <div>
                    <div fxFlex="15"></div>
                    <div [ngClass]="[paciente.kpiChkLst == 'green' ? 'label label-ok' : 'label', 
                                    paciente.kpiChkLst == 'orange' ? 'label label-aviso' : 'label', 
                                    paciente.kpiChkLst == 'red' ? 'label label-atrasado' : 'label']">
                            <small>Avaliação</small>
                    </div>
                    <div [ngClass]="[paciente.kpiPlInterv == 'green' ? 'label label-ok' : 'label', 
                                    paciente.kpiPlInterv == 'orange' ? 'label label-aviso' : 'label', 
                                    paciente.kpiPlInterv == 'red' ? 'label label-atrasado' : 'label']">
                        <small>Plano</small>
                    </div>
                    <div [ngClass]="[paciente.kpiColeta == 'green' ? 'label label-ok' : 'label', 
                                    paciente.kpiColeta == 'orange' ? 'label label-aviso' : 'label', 
                                    paciente.kpiColeta == 'red' ? 'label label-atrasado' : 'label']">
                        <small>Coleta</small>
                    </div>
                </div>
        </mat-card-content>
        <button mat-mini-fab  class="md-mini-fab-back-right" (click)="back()"
            color="primary">
            <mat-icon>keyboard_backspace</mat-icon>
        </button>
    </mat-card>
    <mat-card id="conteudo">
        <mat-card-title style="text-align: center;">
            {{anamnese.descricao}}
        </mat-card-title>
        <mat-card-content *ngFor="let grupo of anamnese.grupos; index as indexGrupo">
            <h3><b>{{grupo.descricao}}</b></h3>
            <div style="width: 100%;">
                <ng-container *ngFor="let pergunta of grupo.perguntas; index as indexPergunta">
                    <!--Campo de Texto -->  
                    <mat-form-field *ngIf="pergunta.campo.tipo == 'TEXT'" style="margin: 10px" [ngStyle]="{ 'width': pergunta.campo.tamanho+'%' }">
                        <input class="input" matInput placeholder="{{pergunta.descricao}}" 
                            [(ngModel)]="anamnese.grupos[indexGrupo].perguntas[indexPergunta].resposta" [disabled]="edicao">
                    </mat-form-field>
                    
                    <!--Campo de Número -->  
                    <mat-form-field *ngIf="pergunta.campo.tipo == 'NUMBER'" style="margin: 10px"  [ngStyle]="{ 'width': pergunta.campo.tamanho+'%' }">
                        <input class="input" type="number" matInput placeholder="{{pergunta.descricao}}" 
                            [(ngModel)]="anamnese.grupos[indexGrupo].perguntas[indexPergunta].resposta" [disabled]="edicao">
                    </mat-form-field>

                    <!--Campo de Seleção -->  
                    <mat-form-field *ngIf="pergunta.campo.tipo == 'SELECAO'" style="margin: 10px"  [ngStyle]="{ 'width': pergunta.campo.tamanho+'%' }">
                        <mat-label>{{pergunta.descricao}}</mat-label>
                        <mat-select class="select" placeholder="{{pergunta.descricao}}" 
                            [(ngModel)]="anamnese.grupos[indexGrupo].perguntas[indexPergunta].resposta" [disabled]="edicao">
                            <mat-option value="" >    
                            </mat-option>
                            <mat-option *ngFor="let valor of pergunta.campo.valores; index as indexValores" [value]="valor.value" >
                                {{valor.descricao}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </ng-container>
             </div>
        </mat-card-content>
        <button mat-raised-button (click)="save()" *ngIf="!edicao" color="primary">
            Salvar
        </button>
        
        <button mat-raised-button *ngIf="!edicao" (click)="cancel()">
            Cancelar
        </button>
        <button mat-mini-fab  class="md-mini-fab-right" *ngIf="edicao && hasAccessUpdate" [matMenuTriggerFor]="menu"
            color="primary">
            <mat-icon>filter_list</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
            <button mat-menu-item  (click)="editar()"> 
                <mat-icon>edit</mat-icon> Apenas editar
            </button>
            <button mat-menu-item (click)="editarAtualizar()" >
                <mat-icon>update</mat-icon> Atualizar (Novas Perguntas)
            </button>
            <button mat-menu-item (click)="atualizarRefazer()" >
                <mat-icon>refresh</mat-icon> Atualizar e Refazer
            </button>
            <button mat-menu-item (click)="gerarPDF()" >
                <mat-icon>printer</mat-icon> Imprimir
            </button>
        </mat-menu>
    </mat-card>
    <app-anamnese-relatorio *ngIf="viewRelatorio" [paciente]="paciente" [anamnese]="anamnese" id="view-relatorio">

    </app-anamnese-relatorio>
</div>
