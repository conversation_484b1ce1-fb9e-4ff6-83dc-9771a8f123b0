import { Profissional } from './../profissional/profissional-model';
import { Objetivo } from './../objetivo/objetivo-model';
import { Paciente } from './../paciente/paciente-model';
export class PlanoIntervencao {  
    id?: string;
    idPaciente: string;
    paciente: Paciente;
    idProfissional:string;
    profissional: Profissional;
    data: Date;
    objetivos: Objetivo[];
    status: boolean;
    constructor(){
        this.paciente = new Paciente();
        this.profissional = new Profissional();
    }
}