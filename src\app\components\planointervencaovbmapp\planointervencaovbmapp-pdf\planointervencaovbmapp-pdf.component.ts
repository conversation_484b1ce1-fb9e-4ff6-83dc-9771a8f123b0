import { Nivel } from './../../nivel/nivel-model';
import { DominioVBMAPPService } from './../../dominiovbmapp/dominiovbmapp.service';
import { PlanointervencaovbmappService } from './../planointervencaovbmapp.service';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { PlanoIntervencaoVBMAPP } from './../planointervencaovbmapp-model';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { Router, ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { jsPDF } from 'jspdf';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { fi } from 'date-fns/locale';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-planointervencaovbmapp-pdf',
  templateUrl: './planointervencaovbmapp-pdf.component.html',
  styleUrls: ['./planointervencaovbmapp-pdf.component.css']
})
export class PlanointervencaovbmappPdfComponent implements OnInit {

  public planointervencao;
  public dominioMap: Map<string, ObjetivoVBMAPP[]> = new Map<string, ObjetivoVBMAPP[]>();

  public tiposAvaliacoes: TipoAvaliacao[];
  public dominios: DominioVBMAPP[];
  public pdfGerado: boolean = false;
  public dataAtual: Date = new Date();

  @ViewChild("pdf") htmlData: ElementRef;

  displayedColumns = ['conteudo'];

  constructor(private planointervencaoService: PlanointervencaovbmappService,
    public authService: AuthService,
    private dominioService: DominioVBMAPPService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    let idPlano = this.route.snapshot.paramMap.get("id");
    this.loadingService.show();

    try {
      //Carregado Tipos de Avaliações
      const tipos = await this.tipoAvaliacaoService.find().toPromise();
      this.tiposAvaliacoes = tipos.filter(ta => ta.ativo == true);
      
      const plano = await this.planointervencaoService.findById(idPlano).toPromise();
      this.planointervencao = plano;
      // if (this.planointervencao.habilidades){
        //   this.planointervencao.habilidades = this.planointervencao.habilidades.filter(h => h != undefined)
        // } 
        // this.planointervencao.habilidades = this.planointervencao.habilidades.filter(h => h != undefined)
        
        //Acrescentando id_dominio e dominio na raiz do objetivo para podermos compatibilizar com a versão anterior da estrutura de armazenamento de objetivos
        if(plano.objetivos != undefined){
          for(const [, obj] of plano.objetivos?.entries()){
            if(obj.id_dominio == undefined && obj.marco != undefined){
              // console.log(obj.marco)
              obj.id_dominio = obj.marco.id_dominio;
              obj.dominio = obj.marco.dominio;
            } else {
              obj.marco = { "id_dominio": obj.id_dominio,
                "dominio": (obj.dominio as DominioVBMAPP),
                "nome": "",
                "objetivo": "",
                "id_nivel": (obj.id_nivel as string),
                "nivel": (obj.nivel as Nivel),
                "ordem": 0
              };
            }
          }
        }
        
        //Converto os marcos do plano para habilidades
        if(this.planointervencao.marcos != undefined){
          for(const[,m] of this.planointervencao.marcos?.entries()){
            //Inicializo o vetor de habilidades caso esteja vazio
            if(this.planointervencao.habilidades == undefined){
              this.planointervencao.habilidades = [];
            }
            this.planointervencao.habilidades.push({
              id: m.id,
              nome: m.nome,
              ordem: m.ordem,
              idNivelAvaliacao: m.id_nivel,
              nivel: null,
              idDominioAvaliacao: m.id_dominio,
              dominio: null,
              sigla: m.id,
              idTipoAvaliacao: tipos.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id,
              dominioResposta:  [
                {
                  id: "1",
                  idHabilidadeAvaliacao: null,
                  nome: "Adquirido totalmente",
                  sigla: "A",
                  valor: 1
                },
                {
                  id: "2",
                  idHabilidadeAvaliacao: null,
                  nome: "Não adquirido",
                  sigla: "N",
                  valor: 0
                },
                {
                  id: "3",
                  idHabilidadeAvaliacao: null,
                  nome: "Adquirido parcialmente",
                  sigla: "P",
                  valor: 0.5
                },
                {
                  id: "4",
                  idHabilidadeAvaliacao: null,
                  nome: "Não observado",
                  sigla: "X",
                  valor: undefined
                }
              ]
            })
          }
        }

        await this.ordenarObjetivos();

        // console.log(this.planointervencao)
        await this.setObjetivosPorDominio();
        
        const doms = await this.dominioService.find().toPromise();
        this.dominios = doms;

      } catch (error) {
        console.log("Error", error);
      } finally {
        this.loadingService.hide();
      }
  }

  async ordenarObjetivos() {
    this.planointervencao?.objetivos?.forEach(obj => {
      if (!obj?.habilidades?.length) return;
      obj.habilidades[0].nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == obj.habilidades[0].idTipoAvaliacao)?.nome;
    });
    
    this.planointervencao?.objetivos?.sort((a, b) => {
      const getNomeTipoAvaliacao = (obj) => 
        obj.habilidades?.length ? obj.habilidades[0].nomeTipoAvaliacao.replace(/\[.*?\]\s*/, '') || "" : "";
    
      let nomeTipoAvaliacaoA = getNomeTipoAvaliacao(a);
      let nomeTipoAvaliacaoB = getNomeTipoAvaliacao(b);
    
      // Ajusta nome do ESDM para garantir ordenação correta sem empurrá-lo para o final
      if (a.idTipoAvaliacao === "5") {
        nomeTipoAvaliacaoA = "Early Start Denver Model";
      }
      if (b.idTipoAvaliacao === "5") {
        nomeTipoAvaliacaoB = "Early Start Denver Model";
      }
    
      // Ordena pelo nome do tipo de avaliação (alfabética)
      const compareTipoAvaliacao = nomeTipoAvaliacaoA.localeCompare(nomeTipoAvaliacaoB);
      if (compareTipoAvaliacao !== 0) return compareTipoAvaliacao;

      // Ordenação para objetivos ESDM
      if (a.idTipoAvaliacao === "5" || b.idTipoAvaliacao === "5") {
        const dominioA = a.dominio?.ordem || Infinity;
        const dominioB = b.dominio?.ordem || Infinity;
        
        if (dominioA !== dominioB) {
          return dominioA - dominioB; // Ordena por domínio
        }
  
        const nivelA = parseInt(a.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        const nivelB = parseInt(b.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        
        if (nivelA !== nivelB) {
          return nivelA - nivelB; // Ordena por nível
        }
  
        const idA = parseInt(a.id.replace(/\D/g, ''), 10) || Infinity;
        const idB = parseInt(b.id.replace(/\D/g, ''), 10) || Infinity;
  
        return idA - idB; // Ordena pelo número final do ID
      }
    
      // Se os tipos de avaliação forem iguais, ordena pelo ID do dominio da primeira habilidade (numérico)
      const idDominioA = a.habilidades?.length ? a.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      const idDominioB = b.habilidades?.length ? b.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      
      if (idDominioA !== idDominioB) return idDominioA - idDominioB;

      // Se os tipos de avaliação forem iguais, e o dominio for igual, ordena pelo ID do nivel da primeira habilidade (numérico)
      const idNivelA = a.habilidades?.length ? a.habilidades[0].idNivelAvaliacao || Infinity : Infinity;
      const idNivelB = b.habilidades?.length ? b.habilidades[0].idNivelAvaliacao || Infinity : Infinity;

      if (idNivelA !== idNivelB) return idNivelA - idNivelB;
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID da primeira habilidade (numérico)
      const idHabilidadeA = a.habilidades?.length ? parseInt(a.habilidades[0].id, 10) || Infinity : Infinity;
      const idHabilidadeB = b.habilidades?.length ? parseInt(b.habilidades[0].id, 10) || Infinity : Infinity;
    
      return idHabilidadeA - idHabilidadeB;
    });
  }

  get user(): FirebaseUserModel{    
    return this.authService.getUser();
  }

  getNomeTipoAvaliacao(h: any){
    // console.log(idTipoAvaliacao)
    if(h == undefined){
      return '';
    } else {
      return this.tiposAvaliacoes?.find(ta => ta.id == h.idTipoAvaliacao)?.nome
    }
  }

  async setObjetivosPorDominio(){
    let objetivos: ObjetivoVBMAPP[] = [];
    if(this.planointervencao.objetivos != undefined){

      this.planointervencao.objetivos.forEach(objetivo => {
  
        if(objetivo.marco.dominio == undefined && this.dominioMap.get("SD") != undefined) {
          objetivos = this.dominioMap.get("SD");
          objetivos.push(objetivo);
          this.dominioMap.set("SD", objetivos)
        } else if(objetivo.marco.dominio == undefined && this.dominioMap.get("SD") == undefined){
          this.dominioMap.set("SD", [objetivo])
        }
  
        // console.log(objetivo)
        //objetivos = [];
        if(objetivo.marco.dominio == undefined && this.dominioMap.get("SD") != undefined) {
          objetivos = this.dominioMap.get("SD");
          objetivos.push(objetivo);
          this.dominioMap.set("SD", objetivos)
        } else if(objetivo.marco.dominio == undefined && this.dominioMap.get("SD") == undefined){
          this.dominioMap.set("SD", [objetivo])
        }
  
        if(objetivo.marco.dominio != undefined && this.dominioMap.get(objetivo.dominio.id) != undefined){
          objetivos = this.dominioMap.get(objetivo.dominio.id);
          objetivos.push(objetivo);
          this.dominioMap.set(objetivo.id_dominio, objetivos)
        } else if(objetivo.marco.dominio != undefined && this.dominioMap.get(objetivo.marco.dominio.id) == undefined){
          this.dominioMap.set(objetivo.id_dominio, [objetivo])
        }
      })
    }

    
  }

  getObjetivosPorDominio(id: string){
    //console.log(this.dominioMap.get(id))
    return this.dominioMap.get(id);
  }

  getTipoAvaliacao(idTipoAvaliacao: string){
    return this.tiposAvaliacoes.find(av => av.id == idTipoAvaliacao).nome
  }

}
