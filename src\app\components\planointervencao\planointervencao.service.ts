import { EsdmchecklistService } from './../esdmchecklist/esdmchecklist.service';
import { PacienteService } from './../paciente/paciente.service';
import { Etapa } from './../etapa/etapa-model';
import { ColetaDiaria } from './../coletadiaria/coleta-diaria-model';
import { Objetivo } from './../objetivo/objetivo-model';
import { ColetadiariaService } from './../coletadiaria/coletadiaria.service';
import { PlanoIntervencao } from './planointervencao-model';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class PlanoIntervencaoService {

  planointervencaoUrl = `${environment.API_URL}/planointervencao`;
  //                  etapa.id      data    status[]
  public etapaMap: Map<string, Map<string, string[]>>;
  
  public planointervencao: BehaviorSubject<PlanoIntervencao[]> = 
    new BehaviorSubject<PlanoIntervencao[]>([]);

  constructor(private snackbar: MatSnackBar,
    private pacienteService: PacienteService,
    private esdmchecklistService: EsdmchecklistService,
    private http: HttpClient,
    private coletaDiariaService: ColetadiariaService) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(planointervencao: PlanoIntervencao): Observable<string>{
    //console.log(planointervencao);
    return this.http.post<PlanoIntervencao>(this.planointervencaoUrl, planointervencao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(planointervencao: PlanoIntervencao): Observable<PlanoIntervencao>{
    //console.log(planointervencao);
    return this.http.put<PlanoIntervencao>(this.planointervencaoUrl + "/" + planointervencao.id, planointervencao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<PlanoIntervencao>{
    return this.http.get<PlanoIntervencao>(this.planointervencaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByPaciente(idPaciente: string): Observable<PlanoIntervencao[]>{
    return this.http.get<PlanoIntervencao[]>(this.planointervencaoUrl + "/paciente/" + idPaciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findResumoByPaciente(idPaciente: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.planointervencaoUrl}/paciente/${idPaciente}/resumo`).pipe(
      map(obj => obj),
      catchError(() => EMPTY)
    );
  }

  findLastByPaciente(idPaciente: string): Observable<PlanoIntervencao[]>{
    return this.http.get<PlanoIntervencao[]>(this.planointervencaoUrl + "/paciente/" + idPaciente + "/last").pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<PlanoIntervencao[]>{
    return this.http.get<PlanoIntervencao[]>(this.planointervencaoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<PlanoIntervencao>{
    return this.http.delete<PlanoIntervencao>(this.planointervencaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  //setStatusEtapaPlano(idPlanoIntervencao: string) {
  setStatusEtapaPlano(plano: PlanoIntervencao) {
    let coletasDiarias: ColetaDiaria[];
    let mapDatas: Map<string, string[]>;
    let sessoes: string[];
    let self = this;
    let etapasAdquiridas: string[] = [];
    let objAdq: boolean = true;
    let etapaAdq: boolean = true;
    let houveAlteracao: boolean = false;
    let dataKeys: string[];
    let mapDatasColetas: string[] = [];

    //Recuperar as coletas diárias de um plano de intervenção ordenada por data
    const promise = new Promise<void>((resolve, reject) => {
      this.coletaDiariaService.findByPlanoIntervencao(plano.id).subscribe(coletas => {
        coletasDiarias = coletas

        //Atualizo os kpis (kpiPlInterv, kpiChkLst, kpiColeta)
        
        //Coloco as datas de coletas retonadas em um array, retirando a duplicidade de datas (considerando apenas dias e não sessões)
        for(const [, coleta] of coletas.entries()) { 
          if(mapDatasColetas.indexOf(moment(coleta.data).format('YYYY-MM-DD')) == -1){
            mapDatasColetas.push(moment(coleta.data).format('YYYY-MM-DD'));
          }
        }

        this.pacienteService.findById(plano.idPaciente).subscribe(paciente => {
          var now = moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate()));
          var kpiColeta = ""

          this.esdmchecklistService.findLastByPacienteData(paciente.id, moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate())).format('YYYY-MM-DD')).subscribe(chklsts => {

            //Atualizo o kpiPlInterv
            const now = moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate()));
            //console.log(plano.data)
            var duration = moment.duration(now.diff(plano.data));
            var days = duration.asDays();
            //console.log(days)
            if(days <= 90){
              paciente.kpiPlInterv = "green";
            } else if (days > 90 && days <= 120){
              paciente.kpiPlInterv = "orange";
            } else {
              paciente.kpiPlInterv = "red";
            }
            
            //Atualizo o kpiChkLst
            //console.log(chklsts.filter(chklst => chklst.status != false)[0].data)
            duration = moment.duration(now.diff(chklsts.filter(chklst => chklst.status != false)[0].data));
            days = duration.asDays();
            //console.log(days)
            if(days <= 90){
              paciente.kpiChkLst = "green";
            } else if (days > 90 && days <= 120){
              paciente.kpiChkLst = "orange";
            } else {
              paciente.kpiChkLst = "red";
            }
          

            //console.log(mapDatasColetas);
            //console.log(mapDatasColetas.find(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ))
            //console.log(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ).length)
            if(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ).length >= 3){
              kpiColeta = "green"
            } else if(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 14 ).length >= 3){
              kpiColeta = "orange"
            } else {
              kpiColeta = "red"
            }

            //console.log(kpiColeta);

          
            paciente.kpiColeta = kpiColeta;
            //console.log(paciente)
            this.pacienteService.update(paciente).subscribe(p => {

            });
          })
        })
        //Fim da atualizção dos kpis (kpiPlInterv, kpiChkLst, kpiColeta)



        this.setMapEtapasporData(coletas);
        resolve();
      })
    }).then(function() {
      //console.log(self.etapaMap)
      //self.findById(idPlanoIntervencao).subscribe(plano => {
        for(const [, objetivo] of plano.objetivos.entries()) { //plano.objetivos.forEach(objetivo => {
          //Pulo o objetivo caso ele já tenha sido adquirido
          if(objetivo.status != "Adquirido"){
            //console.log(objetivo)
            for(const [,etapa] of objetivo.etapa.entries()) { //objetivo.etapa.forEach(etapa => {
              //Pulo o objetivo caso ele já tenha sido adquirido
              if(etapa.status != "Adquirida") {
                //console.log(etapa)
                //Recupero as coletas com a etapa em questão (mapeadas anteriormente em etapaMap)
                mapDatas = self.etapaMap.get(etapa.id);
                if(mapDatas != undefined) { //Se a etapa não foi encontrada no mapDatas, significa que ela não foi trabalhada ainda
                  //Verifico se existem pelo menos 3 datas de coleta. Caso não existam descarto, 
                  //já que precisamos das 3 datas com todas as sessões adquiridas.
                  dataKeys = Array.from(mapDatas.keys());
                  if(mapDatas.size >= 3){
                    for(let i=0; i < 3; i++) { 
                      //Zero as sessões para avaliar se alguma foi recusada nesta data
                      sessoes=[];
                      //Parto do princípio que a etapa foi adquirida
                      etapaAdq=true;
                      //Coloco os resultados das sessões das últimas 3 datas no array sessoes
                      //(mapDatas.values().next().value as string[]).forEach(sessao =>{
                      //console.log(dataKeys[i])
                      mapDatas.get(dataKeys[i]).forEach(sessao => {
                        sessoes.push(sessao);
                      })
                      
                      //console.log(etapa.id)
                      //console.log(sessoes);

                      //Retiro as sessões com status vazio
                      sessoes = sessoes.filter(element => (element != "" && element != undefined));
                      //console.log(sessoes);
                      if( (sessoes.length == 0) || (sessoes.filter(element => element == "Adquirida").length < sessoes.length) ){
                        etapaAdq = false;
                      }
                      
                    }                    
                    //if( (sessoes.length > 0) && (sessoes.filter(element => element == "Adquirida").length == sessoes.length) ){
                    if(etapaAdq == true){
                      etapasAdquiridas.push(etapa.id);
                    }
                  }
                }
              }
            }
          }
        }
        //Atualizar etapas do plano de intervenção
        //console.log(etapasAdquiridas)
        etapasAdquiridas.forEach(etapa => {
          houveAlteracao = true;
          //console.log(etapa)
          //Atribuo o status "Adquirida" a etapa em questão
          plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6))
            .etapa.find(eta => eta.id == etapa).status = "Adquirida";
          
          //Verifico se todas as etapas do objetivo foram adquiridas e coloco o objetivo como "Adquirido"
          objAdq = true;
          for(const [,eta] of plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).etapa.entries()) { //plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).etapa.forEach(etapa => {
            //console.log(eta.id + " - " + eta.status)
            if(eta.status != "Adquirida"){
              //console.log("False")
              objAdq = false;
            }
          }
          if(objAdq){
            //console.log(plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).id)
            plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).status = "Adquirido";
          }
        })
        if(houveAlteracao == true){
          //console.log("atualizando...")
          self.update(plano).subscribe(p => {
            //console.log(p);
          })
        }
      //})
    
    })

  }

  private setMapEtapasporData(coletas: ColetaDiaria[]){
    let m: Map<string, string[]>;
    let data: string;
    let status: string[];
    this.etapaMap = new Map<string, Map<string, string[]>>();
    for(const [,coleta] of coletas.entries()) { //coletas.forEach(coleta => {
      for(const [, objetivo] of coleta.objetivos.entries()) {//coleta.objetivos.forEach(objetivo => {
        for(const [,etapa] of objetivo.etapa.entries()) {//objetivo.etapa.forEach(etapa => {
          //console.log(coleta.data)
          data = new Date(coleta.data).getDate() + "/" + (new Date(coleta.data).getMonth() + 1) + "/" + new Date(coleta.data).getFullYear();
          //console.log(data)
          //Verifico se já tenho a etapa cadastrada
          if(this.etapaMap.get(etapa.id) == undefined){
            //Caso não tenha a etapa cadastrada, faço o cadastro já com a data corrente
            m = new Map<string, string[]>();
            m.set(data, [etapa.status])
            this.etapaMap.set(etapa.id, m);
          } else {
            //Caso já tenha a etapa no Map, recupero o map da etapa e vejo se a data está cadastrada
            m = this.etapaMap.get(etapa.id);
            if(m.get(data) == undefined) {
              //Se a data não está cadastrada, faço o cadastro da mesma
              m.set(data, [etapa.status])
              this.etapaMap.set(etapa.id, m)
            } else {
              //Se a data está cadastrada, pego o array de datas e faço um push para o novo status
              status = m.get(data)
              status.push(etapa.status)
              this.etapaMap.set(etapa.id, m);
            }
          }
        }//})
      }//})
    }//})
    //console.log(this.etapaMap)
  }
  
/*
  async setObjetivosPorDominioEdit(sessaoId: number){
    let dMap = new Map<string, Objetivo[]>();
    let objetivos: Objetivo[] = [];

    await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    this.dominioMap.push(dMap);
  }*/
  
}
