<div class="mat-elevation-z4">
    <table mat-table [dataSource]="tiposprocedimento">  
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.nome}}</td>
        </ng-container>
        
        <!-- Minutos Column -->
        <ng-container matColumnDef="minutos" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Tempo (minutos)</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.minutos}}</td>
        </ng-container>
        
        <!-- Valor Column -->
        <ng-container matColumnDef="valor" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Valor</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.valor | currency }}</td>
        </ng-container>

        <!-- Permite Alterar valor Column -->
        <ng-container matColumnDef="permiteAlterarValor" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Permite Alteração valor</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.permiteAlterarValor | boolean}}</td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="edit(row)" class="edit">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row)" class="delete">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  