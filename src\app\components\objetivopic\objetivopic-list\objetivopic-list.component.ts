import { Component, OnInit } from '@angular/core';
import { ObjetivoComportamental } from '../objetivo-comportamental-model';
import { ObjetivoPICService } from '../objetivopic.service';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AuthService } from './../../template/auth/auth.service';
import { LoadingService } from '../../../shared/service/loading.service';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';

@Component({
  selector: 'app-objetivopic-list',
  templateUrl: './objetivopic-list.component.html',
  styleUrls: ['./objetivopic-list.component.css']
})
export class ObjetivopicListComponent implements OnInit {

  public objetivos: ObjetivoComportamental[];
  public objetivosView: ObjetivoComportamental[] = [];

  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessRead: boolean;

  displayedColumnsObjs = ['compAlvo', 'defOp', 'meta', 'tipoColeta', 'action']

  constructor(
    private objetivoPICService: ObjetivoPICService,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private loadingService: LoadingService
    ) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show()

    try {
      this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','update');
      this.hasAccessDelete = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','delete');
      this.hasAccessRead = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read');

      // Carregando Objetivos
      const objs = await this.objetivoPICService.find().toPromise();
      this.objetivoPICService.objetivosPIC.next(objs?.filter(o => o.ativo == true));

      // Assinatura para atualização automática da lista
      this.objetivoPICService.objetivosPIC.subscribe(objsFiltrados => {
        this.objetivos = objsFiltrados;
      });
    } catch (error) {
      console.log("Erro: ", error);
      this.loadingService.hide();
    } finally {
      this.loadingService.hide();
    }
  }

  deleteObjetivo(obj: ObjetivoComportamental): void{
    let objetivo: ObjetivoComportamental;
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        //Pego o objetivo e coloco ele como inativo
        objetivo = obj;
        objetivo.ativo = false;
        this.objetivoPICService.update(objetivo).subscribe(
          () => {
            this.objetivoPICService.showMessage('Objetivo excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/objetivo_comportamental']);
          }
        );
      }
    });
  }

  do() {

  }

  editObjetivo(obj: ObjetivoComportamental){
    //Colocar na sessão a lista de domínios por nível
    localStorage.removeItem("objetivo_comportamental")
    localStorage.setItem("objetivo_comportamental", JSON.stringify( this.objetivos ))
    this.router.navigate(['/pic_objetivo/update/' + obj.id])
  }

  new(){
    this.router.navigate(['/pic_objetivo/create'])
  }

}
