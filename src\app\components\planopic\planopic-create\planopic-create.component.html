<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="column" fxLayoutAlign="space-between stretch"> 
            <form #ngForm>
                <mat-form-field style="width: 15%; padding: 20px;">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" [(ngModel)]="pic.idPaciente" name="paciente" disabled required>
                        <mat-option [value]="pic.idPaciente">
                            {{paciente?.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                
                <mat-form-field style="width: 15%; padding-left: 20px;">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data" [(ngModel)]="pic.data" name="data" 
                           (dateChange)="setMsAssmtPorData()" [matDatepicker]="picker" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="data.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 20%; padding: 20px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" [(ngModel)]="pic.idProfissional" name="profissionalFC" required 
                                (selectionChange)="setProfissional($event)">
                        <ng-container *ngFor="let profissional of profissionaisDoPaciente">
                            <mat-option [value]="profissional.id">
                                {{profissional.nome}}
                            </mat-option>
                        </ng-container>
                    </mat-select>
                    <mat-error *ngIf="profissionalFC.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>
                
                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Status</mat-label>
                    <mat-select placeholder="Status" 
                        [(ngModel)]="pic.ativo"
                        name="ativo" required
                        (selectionChange) = "changeStatus($event)">
                        <mat-option [value]="false">
                            Rascunho 
                        </mat-option>
                        <mat-option [value]="true">
                            PIC liberado para coleta 
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="ativo.invalid">Status é obrigatório.</mat-error>  
                </mat-form-field>
                
                <button mat-mini-fab alt="Sair da edição" color="primary" style="margin: 15px;" (click)="exitEdit()">
                    <mat-icon>arrow_back</mat-icon>
                </button>
                
                <mat-form-field style="visibility: hidden;">
                    <input class="input" matInput placeholder="Paciente" [(ngModel)]="pic.idPaciente" name="paciente">
                </mat-form-field>

                <!-- OBJETIVOS SELECIONADOS -->
                <div class="mat-elevation-z0" style="padding-bottom: 30px; width: 100%;">

                    <div style="display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; width: 100%; padding: 10px 0;">
  
                        <!-- Título -->
                        <div style="flex: 1;">
                            <h1 style="margin: 0;">Objetivos Adicionados</h1>
                        </div>
                    </div>

                    <table *ngIf="pic.objetivos != undefined" mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows style="width: 100%;"> 
                    
                        <!-- Índice Column -->
                        <ng-container matColumnDef="index">
                            <th mat-header-cell *matHeaderCellDef>#</th>
                            <td mat-cell *matCellDef="let row; let i = index">{{ pic.objetivos.indexOf(row) + 1 }}</td>
                        </ng-container>
                
                        <!-- Nome Column --> 
                        <ng-container matColumnDef="compAlvo">
                            <th mat-header-cell *matHeaderCellDef>Comportamento Alvo</th>
                            <td mat-cell *matCellDef="let row">
                                {{ row.comportamentoAlvo }}
                            </td>
                        </ng-container>
                
                        <!-- Definição Operacional Column --> 
                        <ng-container matColumnDef="defOp">
                            <th mat-header-cell *matHeaderCellDef>Definição Operacional</th>
                            <td mat-cell *matCellDef="let row">{{ row.definicaoOperacional }}</td>
                        </ng-container>
                
                        <!-- TipoColeta Column --> 
                        <ng-container matColumnDef="tipoColeta">
                            <th mat-header-cell *matHeaderCellDef>Tipo de Coleta</th>
                            <td mat-cell *matCellDef="let row">{{ row.tipoColeta }}</td>
                        </ng-container>
                
                        <!-- Action Column -->
                        <ng-container matColumnDef="action" fxFlex="30">
                            <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                            <td mat-cell *matCellDef="let row" fxFlex="30">
                                <a (click)="editObjetivo(row)" class="edit"
                                    *ngIf="hasAccessUpdate">
                                    <i class="material-icons">edit</i>
                                </a>
                                <a (click)="deleteObjetivo(row)" class="delete"
                                    *ngIf="hasAccessDelete">
                                    <i class="material-icons">delete</i>
                                </a>
                            </td>
                        </ng-container> 
                        
                        <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;" style="padding-top: 10px;"></tr>
                    </table>
                </div>

                <p *ngIf="pic.objetivos == undefined || pic.objetivos?.length == 0" style="text-align: center; color: #808080; margin: 0px 0px 0px 0px; font-size: 20px;">Nenhum Objetivo Adicionado!</p>

                <!-- OBJETIVOS LIST  -->
                <div class="mat-elevation-z0"
                    *ngIf="hasAccessCreate">
                    <strong>Objetivos</strong><br>
                    <small>Adicione os objetivos a serem trabalhados neste plano selecionando-os abaixo.</small>
                    <button mat-mini-fab matTooltip="Adicionar Novo Objetivo" color="primary" 
                        *ngIf="hasAccessCreateObjetivo"
                        style="margin: 15px; float: right;" (click)="newObjetivo()">
                        <mat-icon>add</mat-icon>
                    </button>
                    <!-- Filtro de Objetivos -->
                    <app-objetivopic-filter [objetivosDoPlano]="listaDeObjetivosDoPlano"></app-objetivopic-filter>
                    
                    <table mat-table [dataSource]="objetivos" fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
                        <!-- Nome Column --> 
                        <ng-container matColumnDef="compAlvoObjSelList" fxFlex="30">
                            <th mat-header-cell *matHeaderCellDef fxFlex="30">Comportamento Alvo</th>
                            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.comportamentoAlvo}}</td>
                        </ng-container>

                        <!-- Habilidades Column -->
                        <ng-container matColumnDef="defOpObjSelList" fxFlex="30">
                            <th mat-header-cell *matHeaderCellDef fxFlex="30">Definição Operacional</th>
                            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.definicaoOperacional}}</td>
                        </ng-container> 

                        <!-- Estimulos Column -->
                        <ng-container matColumnDef="tipoColetaObjSelList" fxFlex="30">
                            <th mat-header-cell *matHeaderCellDef fxFlex="30">Tipo de Coleta</th>
                            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.tipoColeta}}</td>
                        </ng-container> 
                    
                        <tr mat-header-row *matHeaderRowDef="displayedColumnsObjsSelList"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumnsObjsSelList;" 
                        class="msEnabled"
                        (click)="addObjetivo(row.id)"></tr>
                    </table>
                </div> 

            </form>
        </div>
    </mat-card-content>
</mat-card>
