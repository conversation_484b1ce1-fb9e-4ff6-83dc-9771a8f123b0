import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import jsPDF from 'jspdf';
import { CompetenciaService } from '../../competencia/competencia.service';
import { Dominio } from '../../dominio/dominio-model';
import { ESDMChecklist } from '../../esdmchecklist/esdmchecklist-model';
import { EsdmchecklistService } from '../../esdmchecklist/esdmchecklist.service';
import { ESDMChkLstCompetencia } from '../../esdmchecklist/esdmchklst-competencia-model';
import { Nivel } from '../../nivel/nivel-model';
import { NivelService } from '../../nivel/nivel.service';
import { Paciente } from '../../paciente/paciente-model';
import { Profissional } from '../../profissional/profissional-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { AuthService } from '../../template/auth/auth.service';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-esdmchecklistpdf',
  templateUrl: './esdmchecklistpdf.component.html',
  styleUrls: ['./esdmchecklistpdf.component.css']
})
export class EsdmchecklistpdfComponent implements OnInit {

  public esdmchecklists: ESDMChecklist[];
  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  public niveis: Nivel[];
  public dominios: Dominio[];
  public dominiosTab: Dominio[] = [];
  public chklstcompView: ESDMChkLstCompetencia[] = [];

  public esdmchecklist: ESDMChecklist = new ESDMChecklist();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>();

  dataSource: MatTableDataSource<ESDMChkLstCompetencia>;

  idPaciente: string;

  @Input()
  idAvaliacao: string;

  @Input()
  pacienteSearch: Paciente = new Paciente();

  @Output()
  pdfGenerated: EventEmitter<void> = new EventEmitter<void>();
  
  @ViewChild('esdmchecklistAvaliacao', {static: false})
  el!: ElementRef;

  idOrganizacao: any;

  constructor(private esdmchecklistService: EsdmchecklistService,
    private competenciaService: CompetenciaService,
    private nivelService: NivelService,
    public authService: AuthService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService) { }

    async ngOnInit(): Promise<void> {
      try {
        this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
  
        // Atribuindo o paciente vindo por parâmetro
        this.paciente = this.pacienteSearch;
  
        // Carregando Checklist
        this.esdmchecklist = await this.esdmchecklistService.findById(this.idAvaliacao).toPromise();
  
        // Atribuindo a primeira visão de competências (N1-CRE)
        if (this.esdmchecklist.checklist.length > 0) {
          this.chklstcompView = this.esdmchecklist.checklist.filter(chklst =>
            (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE')
          );
        }
  
        // Carregando Níveis
        this.niveis = await this.nivelService.find().toPromise();
        this.nivel = this.niveis.find(n => n.id == 'N1');
  
        // Carregando Domínios dos Níveis (TAB)
        await Promise.all(this.niveis.map(async (nivel) => {
          const dominios = await this.competenciaService.findDominiosByNivel(nivel.id).toPromise();
          this.dominioMap.set(nivel.id, dominios);
          // Carregando Domínios para Combo (Default: N1-CRE)
          if (nivel.id === 'N1') {
            this.dominios = dominios;
            this.dominio = this.dominios.find(dom => dom.id == 'CRE');
          }
        }));
  
        // Carregando Profissionais
        this.profissionais = await this.profissionalService.find().toPromise();
  
        // Após carregar todos os dados, gerar o PDF
        this.generatePDF();
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
      }
    }

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];  
    this.filterChecklist();
  }

  filterChecklist(){
    this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
      (chklst.competencia.id_nivel==this.nivel.id 
        && chklst.competencia.id_dominio == this.dominio.id))
  }

  setChecklist(){
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'CRE');
    this.filterChecklist()
  }

  async generatePDF() {
    try {
      this.cdr.detectChanges();
      let pdf = new jsPDF('l', 'pt', 'a4', true);
      await pdf.html(this.el.nativeElement, {
        callback: (pdf) => {
          pdf.save('Checklist.pdf');
          this.pdfGenerated.emit();
        }
      });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
    } finally {
      this.loadingService.hide();
    }
  }  

  getchecklistInfosPDF(id_nivel, id_dominio){
    let checklistInfosPDF = this.esdmchecklist.checklist.filter(chklst => chklst.competencia.id_nivel == id_nivel && chklst.competencia.id_dominio == id_dominio);
    
    return checklistInfosPDF
  }

  getStyleByNivelId(nivelId: string): any {
    const styles = {
      'N4': { height: '1175px' },
    };

    return styles[nivelId] || {};
  }

  getStyleByNivelIdForGlossario(nivelId: string): any {
    const styles = {
      'N1': { height: '1740px' },
      'N2': { height: '2370px' },
      'N3': { height: '1770px' },
      'N4': { height: '1780px' },
    };

    return styles[nivelId] || {};
  }

  getStyleByNivelIdAndCompetencia(nivelId: string, competenciaId: string): any {
    const styles = {
      // Formatação PDF ESDM
      'N1': {
        'CSO03': { marginBottom: '20px' },
        'CSO08': { marginBottom: '20px' },
        'MGR03': { marginBottom: '50px' },
        'MGR07': { marginBottom: '50px' },
      },
      'N2': {
        'CSA03': { marginBottom: '40px' },
        'CSA08': { marginBottom: '40px' },
        'JOI06': { marginBottom: '60px' },
        'IPH14': { marginBottom: '40px' },
        'IPH19': { marginBottom: '40px' },
      },
      'N3': {
        'CAP02': { marginBottom: '40px' },
        'CAP09': { marginBottom: '45px' },
        'MFI05': { marginBottom: '20px' },
      },
      'N4': {
        'CEX10': { marginBottom: '25px' },
        'CEX26': { marginBottom: '25px' },
        'MFI02': { marginBottom: '40px' },
        'MFI12': { marginBottom: '40px' },
      },
    };

    return (styles[nivelId] && styles[nivelId][competenciaId]) ? styles[nivelId][competenciaId] : { marginBottom: '1px' };
  }
}
