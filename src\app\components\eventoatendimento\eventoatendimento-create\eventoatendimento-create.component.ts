import { RRule } from 'rrule';
import { MatSelectChange } from '@angular/material/select';
import { colors } from './../../agenda/demo-utils/colors';
import { startWith, map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { TipoProcedimento } from './../../tipoprocedimento/tipoprocedimento-model';
import { TipoprocedimentoService } from './../../tipoprocedimento/tipoprocedimento.service';
import { PacienteService } from './../../paciente/paciente.service';
import { Paciente } from './../../paciente/paciente-model';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { Atendimento } from './../atendimento-model';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from './../../template/auth/auth.service';
import { EventoatendimentoService } from './../eventoatendimento.service';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { EventoAtendimento } from './../eventoatendimento-model';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter } from 'angular-calendar';
import { Component, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';

@Component({
  selector: 'app-eventoatendimento-create',
  templateUrl: './eventoatendimento-create.component.html',
  styleUrls: ['./eventoatendimento-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class EventoatendimentoCreateComponent implements OnInit {

  atendimento: Atendimento = new Atendimento();
  profissional: Profissional = new Profissional();
  atendimentos: Atendimento[] = [];
  profissionais: Profissional[] = [];
  pacientes: Paciente[] = [];
  tiposProcedimentos: TipoProcedimento[] = [];
  horaInicio: string;
  freq: string;
  ocorrencias: number;
  daily = RRule.DAILY;
  weekly = RRule.WEEKLY;
  monthly = RRule.MONTHLY;
  //rule: RRule = new RRule();
  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  dataInicio = new FormControl('', [Validators.required]);
  dataFim = new FormControl('', [Validators.required]);
  titulo = new FormControl('', [Validators.required]);
  status = new FormControl('', [Validators.required]);
  tipoProcedimento = new FormControl('', [Validators.required]);
  statusFC = new FormControl('', [Validators.required]);
  profissionaisFC = new FormControl('', [Validators.required]);
  pacienteFC = new FormControl('', [Validators.required]);
  timeFC = new FormControl('', [Validators.required]);
  recorrenteFC = new FormControl('false');

  filteredPacientes: Observable<Paciente[]>;

  hasAccessUpdate: boolean = false;

  //events: CalendarEvent[] = [];

  constructor(private eventoAtendimentoService: EventoatendimentoService,
    private profissionaisService: ProfissionalService,
    private pacienteService: PacienteService,
    private tipoProcedimentoService: TipoprocedimentoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let idAtendimento = this.route.snapshot.paramMap.get('id');  
    let data: Date = new Date(this.route.snapshot.paramMap.get('data'));  
    //console.log(data)

    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Atendimento.Cadastro de atendimentos','update');

    //console.log("Dia: " + RRule.DAILY)
    //console.log("Semana: " + RRule.WEEKLY)
    //console.log("Mês: " + RRule.MONTHLY)
    //console.log(this.authService.getUser().uid);

    this.profissionaisService.find().subscribe(profs => {
      this.profissionais = profs;
      this.pacienteService.find().subscribe(pacs => {
        this.pacientes = pacs;
        this.tipoProcedimentoService.find().subscribe(procs => {
          this.tiposProcedimentos = procs;
        })

        this.filteredPacientes = this.pacienteFC.valueChanges
        .pipe(
          startWith(''),
          map(value => typeof value === 'string' ? value : value.nome),
          map(nome => nome ? this._filter(nome) : this.pacientes.slice())
        );
        
        if(idAtendimento == undefined) { //Create
          this.atendimento = new Atendimento();
          if(!this.authService.getUser().familia) {
            this.addProfissional(this.authService.getUser().uid);
          }
          this.atendimento.start = data;
          this.horaInicio = new Date(this.atendimento.start).getHours() + ":" + new Date(this.atendimento.start).getMinutes();
          this.atendimento.status = "Marcado não confirmado"
          this.atendimento.rrule = {
            freq: RRule.MONTHLY,
            count: undefined,
            dtstart: undefined,
            until: undefined
          };
          //this.atendimento.ativo = true;
          
        } else {  //Edit
          this.eventoAtendimentoService.findById(idAtendimento).subscribe(atend => {
            this.atendimento = atend;
            //console.log(atend.start)
            //console.log(new Date(atend.start).getHours())
            //console.log(new Date(atend.start).getMinutes())
            this.horaInicio = new Date(atend.start).getHours() + ":" + new Date(atend.start).getMinutes();
            if(this.atendimento.status == undefined || this.atendimento.status == ""){
              this.atendimento.status = "Marcado não confirmado"
            }

            //Ajustando a recorrência
            this.freq = this.atendimento.rrule.freq;
            this.ocorrencias = this.atendimento.rrule.count;
          })
        }
        
      })
    })
  }

  setProcedimento(event:MatSelectChange){
      this.atendimento.procedimento = this.tiposProcedimentos.find(p => p.id == event.value);
      //coleta.idProfissional = coleta.profissional.id;
  }

  displayFn(paciente: Paciente): string {
    return paciente && paciente.nome ? paciente.nome : '';
  }

  private _filter(value: string): Paciente[] {
    const filterValue = value.toLowerCase();

    return this.pacientes.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
    
  }


  delete(id: string){

  }

  addProfissional(id?: string){
    if(id != undefined){
      //console.log(this.profissionais.find(p => p.uid == id));
      this.profissional = this.profissionais.find(p => p.uid == id);
    }

    //Verifico se algum profissional foi selecionado
    if(this.profissional.id != undefined){    
      //Se os profissionais estiver vazio, inicializo
      if(this.atendimento.profissionais == undefined){
        this.atendimento.profissionais = [];
      }
      
      //Adiciono o profissional no array de profissionais, mas antes verifico se ele já não foi incluído antes
      if(this.atendimento.profissionais.length == 0 || (this.profissional.id != undefined && this.atendimento.profissionais.filter(p => p.id == this.profissional.id).length == 0)){
        //Adiciono o profissional no array de equipe
        this.atendimento.profissionais.push(this.profissional);
        this.atendimento.profissionais = [...this.atendimento.profissionais];
        this.profissional = new Profissional();
      } else {
        this.pacienteService.showMessage("Profissional já incluído anteriormente!", true);
        this.profissional = new Profissional();
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o profissional a ser adicionado na equipe do paciente!", true);
    }

  }

  deleteProfissional(id: number){
    var profissional: Profissional;

    //Pego o profissional a ser excluído da equipe
    profissional = this.atendimento.profissionais[id];

    this.atendimento.profissionais.splice(id, 1);
    this.atendimento.profissionais = [...this.atendimento.profissionais];
  }

  save(){
    //console.log(this.horaInicio)
    this.atendimento.start = new Date (this.atendimento.start);
    this.atendimento.start.setHours(parseInt(this.horaInicio.substr(0,2)),parseInt(this.horaInicio.substr(3,2)),0,0);
    //console.log(this.atendimento.start)
    //console.log(this.form.valid)

    //Ajustando recorrência
    this.atendimento.rrule = {
      freq: this.freq,
      count: this.ocorrencias,
      dtstart: this.atendimento.start,
      //until: Date;
    }

    if(this.form.valid){
      //Ajustando a data de início
      this.atendimento.start = new Date(this.atendimento.start);
      

      //Ajustando a hota de fim
      // console.log(this.atendimento.procedimento.minutos)
      this.atendimento.end = new Date(moment(this.atendimento.start).add(this.atendimento.procedimento.minutos, 'minutes').toDate());
      // console.log(this.atendimento.end)
      
      //Inserindo id do Paciente
      this.atendimento.idPaciente = this.atendimento.paciente.id;
      this.atendimento.title = this.atendimento.paciente.nome + " (" + this.atendimento.procedimento.nome + ")";

      //Inserindo ids dos Profissionais
      this.atendimento.idProfissionais = [];
      for(const [,idp] of this.atendimento.profissionais.entries()){
        this.atendimento.idProfissionais.push(idp.id);
      }

      (this.atendimento as EventoAtendimento).color = colors.blue;


      if(this.atendimento.id == undefined){        
        this.eventoAtendimentoService.create(this.atendimento).subscribe((id) => {

          //Se for um evento recorrente, crio todos os atendimentos
          if(this.atendimento.recorrente){
            //console.log(this.atendimento.rrule)
            const rule: RRule = new RRule({
              ...this.atendimento.rrule,
              //dtstart: moment(viewRender.period.start).startOf('day').toDate(),
              dtstart: moment(this.atendimento.rrule.dtstart).startOf('day').toDate(),
              //until: moment(viewRender.period.end).endOf('day').toDate(),
            });
            //const { title, color } = event;
            var ratend: Atendimento;
            for(const [, date] of rule.all().entries()){
            //rule.all().forEach((date) => {
              // console.log(date);
              
              ratend = {...this.atendimento};
              ratend.start = date;
              ratend.end = new Date(moment(ratend.start).add(ratend.procedimento.minutos, 'minutes').toDate());
              ratend.idOrigem = this.atendimento.id;
              this.eventoAtendimentoService.create(ratend).subscribe((id) => {
                this.eventoAtendimentoService.showMessage('Atendimento recorrente criado com sucesso!');
              });
              
            };//);
          }
          this.eventoAtendimentoService.showMessage('Atendimento criado com sucesso!');
          //this.router.navigate(['/agenda']);
        });
      } else {
        
        this.eventoAtendimentoService.update(this.atendimento).subscribe((funcao) => {
          //Se for um evento recorrente, crio todos os atendimentos
          if(this.atendimento.recorrente){
            // console.log(this.atendimento.rrule)
            const rule: RRule = new RRule({
              ...this.atendimento.rrule,
              //dtstart: moment(viewRender.period.start).startOf('day').toDate(),
              dtstart: moment(this.atendimento.rrule.dtstart).startOf('day').toDate(),
              //until: moment(viewRender.period.end).endOf('day').toDate(),
            });
            //const { title, color } = event;
            var ratend: Atendimento;
            for(const [, date] of rule.all().entries()){
            //rule.all().forEach((date) => {
              // console.log(date);
              
              ratend = {...this.atendimento};
              ratend.start = date;
              ratend.end = new Date(moment(ratend.start).add(ratend.procedimento.minutos, 'minutes').toDate());
              ratend.idOrigem = this.atendimento.id;
              //this.eventoAtendimentoService.create(ratend).subscribe((id) => {
              //  this.eventoAtendimentoService.showMessage('Atendimento recorrente criado com sucesso!');
              //});
              
            }//);
          }
          this.eventoAtendimentoService.showMessage('Atendimento alterado com sucesso!');
          //this.router.navigate(['/agenda']);
        });
      }
    } else {
      this.eventoAtendimentoService.showMessage('Existem campos inválidos no formulário!',true);  
    }
  }

  cancel() {
    this.router.navigate(['/agenda']);
  }

}
