<div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 90%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">Plano de Intervenção - Gráfico de Evolução</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle">Paciente: {{ planointervencao.paciente.nome }}</p>
        </div>
        <div style="text-align: center; width: 33%;">
            <p class="subtitle">Profissional: {{ planointervencao.profissional.nome }}</p>
        </div>
        <div style="text-align: right; width: 33%;">
            <p class="subtitle">Data: {{ planointervencao.data | date: 'dd/MM/yyyy' }}</p>
        </div>
    </div>
    <div style="width: 100%; display: flex;   justify-content: center; align-items: center;"> 
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 50%;">
            <canvas baseChart 
                [datasets]="graph.datasets" 
                [labels]="graph.labels" 
                [options]="graph.options"
                legend="true" 
                [chartType]="graph.chartType"
                [colors]="colors" height="250"
                style="display: flex;">
            </canvas>
        </div> 
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 2%;">
        </div>
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 48%; height: 80%;">
            <p><b>Legenda</b></p>
            <div class="div-table">
                <div class="div-table-row">
                    <div class="div-indice-cell">
                        #
                    </div>
                    <div class="div-header-cell">
                        OBJETIVO
                    </div>
                </div>
                <div class="div-table-row" *ngFor="let objetivo of planointervencao.objetivos; let idx=index;">
                    <div class="div-indice-data-cell">
                        {{ objetivo.id }}
                    </div>
                    <div class="div-data-cell">
                        {{ objetivo.nome }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
