import { Etapa } from './../etapa/etapa-model';
import { Dominio } from '../dominio/dominio-model';
import { Nivel } from '../nivel/nivel-model';


export class Objetivo {
    id?: string;
    sigla: string;
    idTipoAvaliacao: string;
    nome: string;
    id_nivel: string;
    id_dominio: string;
    nivel?: Nivel;
    dominio?: Dominio;
    descricao: string;
    descricao_plano: string;
    etapa: Etapa[];
    oportunidadesEtapa: number;
    status: string;
    ativo: boolean;
    isExpanded: boolean;
}