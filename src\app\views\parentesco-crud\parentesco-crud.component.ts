import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-parentesco-crud',
  templateUrl: './parentesco-crud.component.html',
  styleUrls: ['./parentesco-crud.component.css']
})
export class ParentescoCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Parentesco',
        icon: 'contacts',
        routeUrl: '/parentesco'
      }
    }
  ngOnInit(): void {
  }

}
