import { LoadingService } from './../../../shared/service/loading.service';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { FuncaoService } from './../funcao.service';
import { Funcao } from './../funcao-model';
import { Component, OnInit } from '@angular/core';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Profissional } from '../../profissional/profissional-model';
import { AlertDialogComponent } from '../../template/alert-dialog/alert-dialog.component';

@Component({
  selector: 'app-funcao-list',
  templateUrl: './funcao-list.component.html',
  styleUrls: ['./funcao-list.component.css']
})
export class FuncaoListComponent implements OnInit {

  public funcoes: Funcao[];
  profissionais: Profissional[];

  displayedColumns = ['nome', 'action']

  constructor(private funcaoService: FuncaoService,
    private profissionalService: ProfissionalService,
    private loadingService: LoadingService,
    public dialog: MatDialog,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      this.funcoes = await this.funcaoService.find().toPromise();
      this.funcoes = this.funcoes.filter(funcao => funcao.ativo);
      this.profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = this.profissionais.filter(profissional => profissional.ativo);
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  edit(funcao: Funcao){
    this.router.navigate(['/funcao/' + funcao.id]);
  }

  delete(funcao: Funcao): void{
    let textDialog = '';
    let profissionaisDaFuncao = [];
    this.profissionais.forEach(profissional => {
      profissional.funcao.forEach(funcaoProfissional => {
        if(funcaoProfissional.id == funcao.id){
          // console.log("Profissional possui essa funcao");
          profissionaisDaFuncao.push(profissional);
        }
      })
    })
    
    if(profissionaisDaFuncao.length > 1){
      textDialog = 'Esta função não pode ser excluída porque há profissionais relacionados a ela:<br> ' 
        + profissionaisDaFuncao.map(p => p.nome).join(',<br>') + '.<br> Para excluir a função, remova-a dos profissionais associados.'
    } else {
      textDialog = 'Esta função não pode ser excluída porque há um profissional relacionado a ela:<br> ' 
      + profissionaisDaFuncao.map(p => p.nome).join(',<br>') + '.<br> Para excluir a função, remova-a do profissional associado.';
    }

    if (profissionaisDaFuncao.length > 0) {
      // Exibe a mensagem com os nomes dos profissionais
      const dialogRef = this.dialog.open(AlertDialogComponent, {
        width: 'auto',
        height: 'auto',
        data: {
          valida: false,
          msg: textDialog
        }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result === 'ok') {
        }
      });
  
    } else {
      const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
        width: '250px',
        data: false
      });
      dialogRef.afterClosed().subscribe(result => {
        if(result){
          funcao.ativo = false;
          this.funcaoService.update(funcao).subscribe((p) => {
            this.funcaoService.showMessage('Função inativada com sucesso!');
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            //this.router.navigate(['/profissional/update/'+this.profissional.id]);
            this.router.navigate(['/funcao']);
          });
        }
      });
    }
  }

}
