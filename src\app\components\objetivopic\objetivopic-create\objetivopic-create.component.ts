import { ObjetivoComportamental } from '../objetivo-comportamental-model';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { ActivatedRoute, Router } from '@angular/router';
import { DominioAvaliacaoService } from '../../avaliacao/dominio-avaliacao.service';
import { HabilidadeAvaliacaoService } from '../../avaliacao/habilidade-avaliacao.service';
import { NivelAvaliacaoService } from '../../avaliacao/nivel-avaliacao.service';
import { ObjetivoPICService } from '../objetivopic.service';
import { PICService } from '../../planopic/pic.service';
import { FormControl, NgForm, Validators } from '@angular/forms';
import { PIC } from '../../planopic/pic-model';
import { AuthService } from '../../template/auth/auth.service';
import { v4 as uuidv4 } from 'uuid';
import { PacienteService } from '../../paciente/paciente.service';
import { Paciente } from '../../paciente/paciente-model';
import { LoadingService } from '../../../shared/service/loading.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, OnInit, ViewChild, Optional, Inject } from '@angular/core';

export interface DialogData {
  idPaciente: string,
  idPICPlan: string,
  idObjetivo: string
}

@Component({
  selector: 'app-objetivopic-create',
  templateUrl: './objetivopic-create.component.html',
  styleUrls: ['./objetivopic-create.component.css']
})
export class ObjetivoPICCreateComponent implements OnInit {

  public idPICPlan: string;
  public idPaciente;
  public tipoAvaliacao: TipoAvaliacao;
  
  objetivo: ObjetivoComportamental = new ObjetivoComportamental();
  objetivos: ObjetivoComportamental[] = [];
  pic: PIC = new PIC();
  paciente: Paciente = new Paciente()

  public hasAccessUpdate: boolean;

  public noExistInList: boolean;
  public ExistInListEdit: boolean;
  public precadastro: boolean;
  public updatePrecadastro: boolean;
  public isModal: boolean = false;

  compAlvoFC = new FormControl('', [Validators.required]);
  defOpFC = new FormControl('', [Validators.required]);
  metaFC = new FormControl('', [Validators.required]);
  estAntFC = new FormControl('', [Validators.required]);
  estConsFC = new FormControl('', [Validators.required]);
  tipoColetaFC = new FormControl('', [Validators.required]);
  intervaloFC = new FormControl('', [Validators.required]);
  tipoAmostFC = new FormControl('', [Validators.required]);
  medidaFC = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form: { valid: any; control: { markAllAsTouched: () => void; }; };

  constructor(
    private objetivoPICService: ObjetivoPICService,
    private picService: PICService,
    private pacienteService: PacienteService,
    public habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router,
    private loadingService: LoadingService,
    @Optional() private dialogRef: MatDialogRef<ObjetivoPICCreateComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    let idObjetivo = this.route.snapshot.paramMap.get("id");
    this.idPaciente = this.route.snapshot.paramMap.get("idPaciente");

    if (this.data) {
      idObjetivo = this.data?.idObjetivo;
      this.idPICPlan = this.data?.idPICPlan;
      this.idPaciente = this.data?.idPaciente;
    }
    
    if (this.idPICPlan) {
      this.isModal = true;
      this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Paciente.Cadastro de PIC','update')
      
      if (!this.hasAccessUpdate) {
        this.disableFormControls();
      }

      this.pacienteService.findById(this.idPaciente).subscribe(paciente => this.paciente = paciente)
      const plano = await this.picService.findById(this.idPICPlan).toPromise();
      this.pic = plano;
      this.pic.objetivos = this.pic.objetivos || []; // Initialize objetivos if undefined

      if (!idObjetivo) {
        this.objetivo = new ObjetivoComportamental();
        this.objetivo.status = true;
      } else {
        try {
          this.objetivo = this.pic.objetivos.find(o => o.id === idObjetivo);
        } catch (error) {
            console.error('Erro ao buscar plano:', error);
        } finally {
          this.loadingService.hide();
        }
      }

    } else {
      this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','update')

      if (!this.hasAccessUpdate) {
        this.disableFormControls();
      }
      if (!idObjetivo) {
        this.objetivo = new ObjetivoComportamental();
        this.objetivo.status = true;
      } else {
        try {
          this.objetivo = await this.objetivoPICService.findById(idObjetivo).toPromise();
        } catch (error) {
          console.error('Erro ao buscar objetivo:', error);
        } finally {
          this.loadingService.hide();
        }
      }
    }

    this.compAlvoFC.setValue(this.objetivo.comportamentoAlvo || '');
    this.defOpFC.setValue(this.objetivo.definicaoOperacional || '');
    this.metaFC.setValue(this.objetivo.meta || '');
    this.estAntFC.setValue(this.objetivo.estrategiasAntecedentes || '');
    this.estConsFC.setValue(this.objetivo.estrategiasConsequencia || '');
    this.tipoColetaFC.setValue(this.objetivo.tipoColeta || '');
    this.intervaloFC.setValue(this.objetivo.intervalo || '');
    this.tipoAmostFC.setValue(this.objetivo.tipoAmostragem || '');
    this.medidaFC.setValue(this.objetivo.medidaIntervalo || '');

    await this.checkObjExistInList();
    this.loadingService.hide();
  }

  private disableFormControls(): void {
      this.compAlvoFC.disable();
      this.defOpFC.disable();
      this.metaFC.disable();
      this.estAntFC.disable();
      this.estConsFC.disable();
      this.intervaloFC.disable();
      this.medidaFC.disable();
      this.tipoColetaFC.disable();
      this.tipoAmostFC.disable();
  }

  async checkObjExistInList() {
    let idObjetivo = this.route.snapshot.paramMap.get('id');
    if (this.data?.idObjetivo) {
      idObjetivo = this.data.idObjetivo;
    }
    let findObjetivo = await this.objetivoPICService.findById(this.objetivo?.id as string).toPromise();

    if (this.idPICPlan) {
      if (!idObjetivo || !findObjetivo) {
        this.noExistInList = true;
        return;
      } else {
        this.ExistInListEdit = true;
        return;
      }
    }

    this.noExistInList = false;
    return;
  }

  async save() {
    let valid = this.form.valid && this.objetivo.comportamentoAlvo && this.objetivo.definicaoOperacional && this.objetivo.meta && this.objetivo.estrategiasAntecedentes && this.objetivo.estrategiasConsequencia && this.objetivo.tipoColeta;
    const idObjetivo = this.route.snapshot.paramMap.get('id');

    if (this.objetivo.tipoColeta == 'Amostragem de Tempo') {
      valid = valid && this.objetivo.intervalo && this.objetivo.medidaIntervalo && this.objetivo.tipoAmostragem;
    }

    if (this.objetivo.tipoColeta == 'Cronometragem') {
      this.objetivo.intervalo = '';
      this.objetivo.medidaIntervalo = '';
      this.objetivo.tipoAmostragem = '';
    }

    if (valid) {
      if (this.idPICPlan) {
        if (!this.objetivo.id) { // Create
          this.objetivo.id = uuidv4();
          this.objetivoPICService.create(this.objetivo)
          this.pic.objetivos.push(this.objetivo);
        } else { // Edit
          const index = this.pic.objetivos.findIndex(o => o.id === this.objetivo.id);
          this.pic.objetivos[index] = this.objetivo;
        }
        try {
          const plano = await this.picService.update(this.pic).toPromise();
          this.objetivoPICService.showMessage('Objetivo salvo com sucesso!');
          this.router.navigate(['/pic/create', {
            idPaciente: this.pic.idPaciente,
            idPICPlan: this.pic.id,
            tab: 'objetivos'
          }]);
        } catch (error) {
          console.error('Erro ao salvar objetivo:', error);
          this.objetivoPICService.showMessage('Erro ao salvar objetivo!', true);
        }
      } else {
        if (!this.objetivo.id) { // Create
          this.objetivo.ativo = true;
          const id = await this.objetivoPICService.create(this.objetivo).toPromise();
          this.objetivo.id = id;
          await this.objetivoPICService.update(this.objetivo).toPromise();
          this.objetivoPICService.showMessage('Objetivo criado com sucesso!');
          this.router.navigate(['/objetivo_comportamental']);
        } else {
          await this.objetivoPICService.update(this.objetivo).toPromise();
          this.objetivoPICService.showMessage('Objetivo alterado com sucesso!');
          this.router.navigate(['/objetivo_comportamental']);
        }
      }
    } else {
      this.form.control.markAllAsTouched();
      this.objetivoPICService.showMessage('Existem campos inválidos no formulário!', true);
    }
  }

  async addObjetivoPersonalizado(){
    let valid = this.form.valid && this.objetivo.comportamentoAlvo && this.objetivo.definicaoOperacional && this.objetivo.meta && this.objetivo.estrategiasAntecedentes && this.objetivo.estrategiasConsequencia && this.objetivo.tipoColeta;
    const idObjetivo = this.route.snapshot.paramMap.get('id');

    if (this.objetivo.tipoColeta == 'Amostragem de Tempo') {
      valid = valid && this.objetivo.intervalo && this.objetivo.medidaIntervalo && this.objetivo.tipoAmostragem;
    }

    if (this.objetivo.tipoColeta == 'Cronometragem') {
      this.objetivo.intervalo = '';
      this.objetivo.medidaIntervalo = '';
      this.objetivo.tipoAmostragem = '';
    }

    if (valid) {
      try {
        this.objetivo.ativo = true;
        if (this.precadastro) {
          const id = await this.objetivoPICService.create(this.objetivo).toPromise();
          this.objetivo.id = id;
          await this.objetivoPICService.update(this.objetivo).toPromise();
          this.picService.showMessage('Objetivo adicionado no pré-cadastro com sucesso!');
        } else if (this.updatePrecadastro) {
          await this.objetivoPICService.update(this.objetivo).toPromise();
          this.picService.showMessage('Objetivo alterado com sucesso!');
        } else {
          if (this.objetivo.id == null) {
            this.objetivo.id = uuidv4();
            this.picService.showMessage('Objetivo criado com sucesso!');
          } else {
            this.picService.showMessage('Objetivo alterado com sucesso!');
          }
        }
        localStorage.setItem("pic_objetivo",JSON.stringify(this.objetivo));
        this.dialogRef.close();
      } catch (error) {
        this.picService.showMessage('Erro ao salvar o objetivo', true);
      }
    } else {
      this.form.control.markAllAsTouched();
      this.picService.showMessage('Existem campos inválidos no formulário!', true);
      this.loadingService.hide();
    }
  }

  cancel() {
    if (this.idPICPlan) {
      this.router.navigate(['/pic/create', {
        idPaciente: this.idPaciente,
        idPICPlan: this.idPICPlan
      }]);
    } else {
      this.router.navigate(['/objetivo_comportamental']);
    }
  }
}
