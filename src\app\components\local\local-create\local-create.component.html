<mat-card>
    <mat-card-header>
        <mat-card-title>Tipo de Procedimento: {{localAtendimento.nome}}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field style="width: 60%;">
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="localAtendimento.nome" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field>                
            </form>
        </div>
    </mat-card-content>
    <div style="display: inline; width: 100%;">
        <mat-form-field  style="width: 20%;">
            <mat-label>Permite choque de horário:</mat-label>
            <mat-select [(ngModel)]= "localAtendimento.permiteChoqueHorario" name="permiteChoqueHorario">
                <mat-option [value]="true">Sim</mat-option>                        
                <mat-option [value]="false">Não</mat-option>                        
            </mat-select>
        </mat-form-field>
        <mat-icon style="font-size: 20px; cursor: default;" matTooltip="Permite que sejam agendados 2 atendimentos no mesmo horário.">
            inform
        </mat-icon>
    </div>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>