import { TerapeutaService } from './../../terapeuta/terapeuta.service';
import { Terapeuta } from './../../terapeuta/terapeuta-model';
import { Router } from '@angular/router';
import { ListaesperaService } from './../listaespera.service';
import { ListaEspera } from './../listaespera-model';
import { Component, OnInit } from '@angular/core';
import {DateAdapter, MAT_DATE_FORMATS} from '@angular/material/core';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';

@Component({
  selector: 'app-lista-espera-create',
  templateUrl: './lista-espera-create.component.html',
  styleUrls: ['./lista-espera-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ListaEsperaCreateComponent implements OnInit {

  listaEspera: ListaEspera = {
    nome: '',
    dataNascimento: null,
    dataContato: null,
    local: '',
    nomeResponsavel: '',
    telefone: '',
    email: '',
    preferenciaTurno: '',
    observacao: '',
    terapeuta: '',
    status: ''
  };

  terapeutas: Terapeuta[];

  constructor(private listaEsperaService: ListaesperaService,
    private terapeutaService: TerapeutaService,
    private router: Router) { }

  ngOnInit(): void {
    this.terapeutaService.find().subscribe(terapeutas => {
      this.terapeutas = terapeutas;
    });
  }

  createCandidato(): void{
    this.listaEsperaService.create(this.listaEspera).subscribe(() => {
      this.listaEsperaService.showMessage('Candidato criado com sucesso!');
      this.router.navigate(['/listaespera']);
    });
    
  }

  cancel(): void{
    this.router.navigate(['/listaespera']);
  }

}
