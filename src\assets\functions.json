[{"nome": "Paciente", "funcionalidades": [{"nome": "Cadastro de pacientes"}, {"nome": "Cadastro de ESDM Checklist"}, {"nome": "Cadastro de Plano de Intervenção"}, {"nome": "Cadastro de PIC"}, {"nome": "Cadastro de Avaliação de Marco (VBMAPP)"}, {"nome": "Estabelecer equipe"}, {"nome": "Cadastro de parentes (família)"}, {"nome": "Registro de notas"}, {"nome": "Coleta de Dados"}]}, {"nome": "Profissional", "funcionalidades": [{"nome": "Cadastro de profissionais"}]}, {"nome": "<PERSON><PERSON><PERSON>", "funcionalidades": [{"nome": "Cadastro de parentes"}]}, {"nome": "Objetivo", "funcionalidades": [{"nome": "Cadastro de objetivos (ESDM)"}, {"nome": "Cadastro de objetivos (VBMAPP)"}]}, {"nome": "Tipo de Procedimento", "funcionalidades": [{"nome": "Cadastro de tipos de procedimentos"}]}, {"nome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funcionalidades": [{"nome": "Cadastro de estímulos"}]}, {"nome": "Parentesco", "funcionalidades": [{"nome": "Cadastro de parentesco"}]}, {"nome": "Organizacao", "funcionalidades": [{"nome": "Cadastro de organizacao"}]}, {"nome": "Atendimento", "funcionalidades": [{"nome": "Cadastro de atendimentos"}]}, {"nome": "Agenda", "funcionalidades": [{"nome": "Agendamento de Pacientes"}]}]