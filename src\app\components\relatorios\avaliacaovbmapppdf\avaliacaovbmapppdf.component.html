<!-- HTML para a impressão do PDF -->
<div id="vbmappMarcos" #vbmappMarcos>
    <div *ngFor="let nivel of niveis" class="folha-horizontal-pdf">
        <div class="cabecalho-pdf">
            <img class="cabecalho-img-pdf" src="assets/img/{{ idOrganizacao }}.png" width="100px">
            <div class="cabecalho-dados-pdf">
                Avaliações&nbsp;de&nbsp;Marcos<br>
                {{paciente.nome}}<br>
                Data: {{vbmappmsassmt.data| date: 'dd/MM/yyyy'}}
            </div>
        </div>

        <div style="margin: 20px;">
            <h3 class="level-legend-container-pdf">
                <b>{{nivel.nome}}</b>
                <div class="legend-container-pdf">
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf adquirido-pdf"></div>
                        <p class="legend-text-pdf">Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf parcialmente-pdf"></div>
                        <p class="legend-text-pdf">Parcialmente Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-adquirido-pdf"></div>
                        <p class="legend-text-pdf">Não Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-observado-pdf"></div>
                        <p class="legend-text-pdf">Não Observado</p>
                    </div>
                </div>
            </h3>

            <div class="table-container-pdf">
                <table>
                    <tbody>
                        <tr>
                            <ng-container *ngFor="let dominio of dominioMap | filterdominio : nivel.id">
                                <td class="table-dados-pdf">
                                    <div *ngFor="let vbmapp of getVbmappInfosPDF(nivel.id, dominio.id)">
                                        <p class="label label-N-pdf" *ngIf="vbmapp.valor == 'N'">{{ vbmapp.marco.id }}</p>
                                        <p class="label label-P-pdf" *ngIf="vbmapp.valor == 'P'">{{ vbmapp.marco.id }}</p>
                                        <p class="label label-A-pdf" *ngIf="vbmapp.valor == 'A'">{{ vbmapp.marco.id }}</p>
                                        <p class="label label-X-pdf" *ngIf="vbmapp.valor == 'X' || vbmapp.valor == undefined" disabled>{{ vbmapp.marco.id }}</p>
                                    </div>
                                </td>
                            </ng-container>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Glossário -->
    <div class="glossario-pdf">
        <h3>Glossário</h3>
        <div *ngFor="let nivel of niveis" style="margin-top: 10px;" [ngStyle]="getStyleByNivelIdForGlossario(nivel.id)">
            <h5 style="white-space: nowrap;">{{ nivel.nome }}</h5>
            <div *ngFor="let dominio of dominioMap | filterdominio : nivel.id">
                <div class="glossario-dominio-pdf">
                    <p class="glossario-dominio-text-pdf">{{ dominio.id }} - {{ dominio.nome }}</p>
                </div>
                <div class="glossario-marco-pdf">
                    <div style="width: 48%;">
                        <ng-container *ngFor="let vbmapp of getVbmappInfosPDF(nivel.id, dominio.id); let i = index">
                            
                            <div *ngIf="i < (getVbmappInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleForAvaliacao(vbmapp.marco.id)">
                                <p class="glossario-marco-text-pdf">{{ vbmapp.marco.id }} - {{ vbmapp.marco.nome | sanitize }}</p>
                            </div>
                            
                        </ng-container>
                    </div>
                    <div style="width: 48%;">
                        <ng-container *ngFor="let vbmapp of getVbmappInfosPDF(nivel.id, dominio.id); let i = index">
                            
                            <div *ngIf="i >= (getVbmappInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleForAvaliacao(vbmapp.marco.id)">
                                <p class="glossario-marco-text-pdf">{{ vbmapp.marco.id }} - {{ vbmapp.marco.nome | sanitize }}</p>
                            </div>
                            
                        </ng-container>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
