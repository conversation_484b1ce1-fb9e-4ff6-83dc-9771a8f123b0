import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DeleteConfirmDialogComponent } from '../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { StatusAgenda } from '../statusagenda-model';
import { StatusAgendaService } from '../statusagenda.service';

@Component({
  selector: 'app-statusagenda-list',
  templateUrl: './statusagenda-list.component.html',
  styleUrls: ['./statusagenda-list.component.css']
})
export class StatusagendaListComponent implements OnInit {

  public statusAgendasList: StatusAgenda[];

  displayedColumns = ['nome', 'confirmaCobranca', 'cor','default','action']

  constructor(private statusagendaService: StatusAgendaService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {
    this.statusagendaService.find().subscribe(status => {
      this.statusAgendasList = status.filter(stat => stat.ativo);
    })
  }

  edit(satausAgenda: StatusAgenda){
    this.router.navigate(['/status-agenda/' + satausAgenda.id]);
  }

  delete(satausAgenda: StatusAgenda): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        satausAgenda.ativo = false;
        this.statusagendaService.update(satausAgenda).subscribe((p) => {
          this.statusagendaService.showMessage('Status inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/status-agenda']);
        });
      }
    });
  }

}
