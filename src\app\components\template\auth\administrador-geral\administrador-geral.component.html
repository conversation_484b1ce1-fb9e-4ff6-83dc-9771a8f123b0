<mat-card style="width: 95%; margin: auto;">
    <mat-card-header>
      <mat-card-title>Usu<PERSON>rios</mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <table mat-table [dataSource]="datasourceAdmins"> 
            <!-- Nome Column -->
            <ng-container matColumnDef="nome">
                <th mat-header-cell *matHeaderCellDef>Nome</th>
                <td mat-cell *matCellDef="let row" >{{row.name}}</td>
            </ng-container>

            <!-- Email Column -->
            <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>E-mail</th>
                <td mat-cell *matCellDef="let row" >{{row.email}}</td>
            </ng-container>

            <!-- gsupervisor Column -->
            <ng-container matColumnDef="gsupervisor">
                <th mat-header-cell *matHeaderCellDef>Supervisor Geral</th>
                <td mat-cell *matCellDef="let row" >{{row.gsupervisor?"Sim":"Não"}}</td>
            </ng-container>

            <!-- admin Column -->
            <ng-container matColumnDef="admin">
                <th mat-header-cell *matHeaderCellDef>Admin</th>
                <td mat-cell *matCellDef="let row" >{{row.admin?"Sim":"Não"}}</td>
            </ng-container>

            <!-- superadmin Column -->
            <ng-container matColumnDef="superadmin">
                <th mat-header-cell *matHeaderCellDef>SuperAd.</th>
                <td mat-cell *matCellDef="let row" >{{row.superadmin?"Sim":"Não"}}</td>
            </ng-container>

            <!-- organization Column -->
            <ng-container matColumnDef="organization">
                <th mat-header-cell *matHeaderCellDef>Organização</th>
                <td mat-cell *matCellDef="let row" >{{row.organizationId}}</td>
            </ng-container>
    
          <!-- Action Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Ações</th>
            <td mat-cell *matCellDef="let row"> 
                <a (click)="edit(row)" class="edit">
                    <i class="material-icons">
                        edit
                    </i>
                </a>
                <a (click)="do()" class="delete">
                    <i class="material-icons">
                        delete
                    </i>
                </a>
            </td>
          </ng-container>
      
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!--form>
            <mat-form-field  style="width: 15%; padding: 20px;">
                <mat-label>Usuários</mat-label>
                <mat-select placeholder="Profissional" 
                    [(ngModel)]="user"
                    name="user">
                    <mat-option *ngFor="let user of users" [value]="user" >
                        {{user.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <button mat-mini-fab (click)="add()" color="primary">
                <mat-icon>
                    add
                </mat-icon>
            </button>
        </form-->
    </mat-card-content>
  </mat-card>