import { DominioVBMAPP } from './../dominiovbmapp/dominiovbmapp-model';
import { Nivel } from './../nivel/nivel-model';
import { map, catchError } from 'rxjs/operators';
import { VBMAPPMsAssmtService } from './../vbmappmsassmt/vbmappmsassmt.service';
import { HttpClient } from '@angular/common/http';
import { PacienteService } from './../paciente/paciente.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PlanoIntervencaoVBMAPP } from './planointervencaovbmapp-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import { environment } from './../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PlanointervencaovbmappService {
  planointervencaoUrl = `${environment.API_URL}/vbmapp_planointervencao`;
  //                  etapa.id      data    status[]
  public etapaMap: Map<string, Map<string, string[]>>;
  
  public planointervencao: BehaviorSubject<PlanoIntervencaoVBMAPP[]> = 
    new BehaviorSubject<PlanoIntervencaoVBMAPP[]>([]);

  constructor(private snackbar: MatSnackBar,
    private pacienteService: PacienteService,
    private vbmappMsAssmt: VBMAPPMsAssmtService,
    //private esdmchecklistService: EsdmchecklistService,
    //private coletaDiariaService: ColetadiariaService,
    private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }
  
    create(planointervencao: PlanoIntervencaoVBMAPP): Observable<string>{
      //console.log(planointervencao);
      return this.http.post<PlanoIntervencaoVBMAPP>(this.planointervencaoUrl, planointervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(planointervencao: PlanoIntervencaoVBMAPP): Observable<PlanoIntervencaoVBMAPP>{
      // console.log(planointervencao);
      return this.http.put<PlanoIntervencaoVBMAPP>(this.planointervencaoUrl + "/" + planointervencao.id, planointervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findById(id: string): Observable<PlanoIntervencaoVBMAPP>{
      // this.http.get<PlanoIntervencaoVBMAPP>(this.planointervencaoUrl + '/' + id).subscribe(plano => {
      //   for(const [, obj] of plano.objetivos.entries()){
      //     if(obj.id_dominio == undefined){
      //       obj.id_dominio = obj.marco.id_dominio;
      //       obj.dominio = obj.marco.dominio;
      //     } else {
      //       obj.marco = { "id_dominio": obj.id_dominio,
      //                     "dominio": (obj.dominio as DominioVBMAPP),
      //                     "nome": "",
      //                     "objetivo": "",
      //                     "id_nivel": (obj.id_nivel as string),
      //                     "nivel": (obj.nivel as Nivel),
      //                     "ordem": 0
      //                   };
      //     }
      //   }
      //   return (plano);
      // })
      
      return this.http.get<PlanoIntervencaoVBMAPP>(this.planointervencaoUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findByPaciente(idPaciente: string): Observable<PlanoIntervencaoVBMAPP[]>{
      return this.http.get<PlanoIntervencaoVBMAPP[]>(this.planointervencaoUrl + "/paciente/" + idPaciente).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findResumoByPaciente(idPaciente: string): Observable<any[]> {
      return this.http.get<any[]>(`${this.planointervencaoUrl}/paciente/${idPaciente}/resumo`).pipe(
        map(obj => obj),
        catchError(() => EMPTY)
      );
    }
  
    findLastByPaciente(idPaciente: string): Observable<PlanoIntervencaoVBMAPP[]>{
      return this.http.get<PlanoIntervencaoVBMAPP[]>(this.planointervencaoUrl + "/paciente/" + idPaciente + "/last").pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<PlanoIntervencaoVBMAPP[]>{
      return this.http.get<PlanoIntervencaoVBMAPP[]>(this.planointervencaoUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    delete(id: string): Observable<PlanoIntervencaoVBMAPP>{
      return this.http.delete<PlanoIntervencaoVBMAPP>(this.planointervencaoUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    //setStatusEtapaPlano(idPlanoIntervencao: string) {
    /*
    setStatusEtapaPlano(plano: PlanoIntervencaoVBMAPP) {
      let coletasDiarias: ColetaDiaria[];
      let mapDatas: Map<string, string[]>;
      let sessoes: string[];
      let self = this;
      let etapasAdquiridas: string[] = [];
      let objAdq: boolean = true;
      let etapaAdq: boolean = true;
      let houveAlteracao: boolean = false;
      let dataKeys: string[];
      let mapDatasColetas: string[] = [];
  
      //Recuperar as coletas diárias de um plano de intervenção ordenada por data
      const promise = new Promise((resolve, reject) => {
        this.coletaDiariaService.findByPlanoIntervencao(plano.id).subscribe(coletas => {
          coletasDiarias = coletas
  
          //Atualizo os kpis (kpiPlInterv, kpiChkLst, kpiColeta)
          
          //Coloco as datas de coletas retonadas em um array, retirando a duplicidade de datas (considerando apenas dias e não sessões)
          for(const [, coleta] of coletas.entries()) { 
            if(mapDatasColetas.indexOf(moment(coleta.data).format('YYYY-MM-DD')) == -1){
              mapDatasColetas.push(moment(coleta.data).format('YYYY-MM-DD'));
            }
          }
  
          this.pacienteService.findById(plano.idPaciente).subscribe(paciente => {
            var now = moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate()));
            var kpiColeta = ""
  
            this.esdmchecklistService.findLastByPacienteData(paciente.id, moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate())).format('YYYY-MM-DD')).subscribe(chklsts => {
  
              //Atualizo o kpiPlInterv
              const now = moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate()));
              console.log(plano.data)
              var duration = moment.duration(now.diff(plano.data));
              var days = duration.asDays();
              console.log(days)
              if(days <= 90){
                paciente.kpiPlInterv = "green";
              } else if (days > 90 && days <= 120){
                paciente.kpiPlInterv = "orange";
              } else {
                paciente.kpiPlInterv = "red";
              }
              
              //Atualizo o kpiChkLst
              console.log(chklsts[0].data)
              duration = moment.duration(now.diff(chklsts[0].data));
              days = duration.asDays();
              console.log(days)
              if(days <= 90){
                paciente.kpiChkLst = "green";
              } else if (days > 90 && days <= 120){
                paciente.kpiChkLst = "orange";
              } else {
                paciente.kpiChkLst = "red";
              }
            
  
              //console.log(mapDatasColetas);
              //console.log(mapDatasColetas.find(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ))
              //console.log(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ).length)
              if(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 7 ).length >= 3){
                kpiColeta = "green"
              } else if(mapDatasColetas.filter(d => moment.duration(now.diff(new Date(d))).asDays() <= 14 ).length >= 3){
                kpiColeta = "orange"
              } else {
                kpiColeta = "red"
              }
  
              //console.log(kpiColeta);
  
            
              paciente.kpiColeta = kpiColeta;
              //console.log(paciente)
              this.pacienteService.update(paciente).subscribe(p => {
  
              });
            })
          })
          //Fim da atualizção dos kpis (kpiPlInterv, kpiChkLst, kpiColeta)
  
  
  
          this.setMapEtapasporData(coletas);
          resolve();
        })
      }).then(function() {
        //console.log(self.etapaMap)
        //self.findById(idPlanoIntervencao).subscribe(plano => {
          for(const [, objetivo] of plano.objetivos.entries()) { //plano.objetivos.forEach(objetivo => {
            //Pulo o objetivo caso ele já tenha sido adquirido
            if(objetivo.status != "Adquirido"){
              //console.log(objetivo)
              for(const [,etapa] of objetivo.etapa.entries()) { //objetivo.etapa.forEach(etapa => {
                //Pulo o objetivo caso ele já tenha sido adquirido
                if(etapa.status != "Adquirida") {
                  //console.log(etapa)
                  //Recupero as coletas com a etapa em questão (mapeadas anteriormente em etapaMap)
                  mapDatas = self.etapaMap.get(etapa.id);
                  if(mapDatas != undefined) { //Se a etapa não foi encontrada no mapDatas, significa que ela não foi trabalhada ainda
                    //Verifico se existem pelo menos 3 datas de coleta. Caso não existam descarto, 
                    //já que precisamos das 3 datas com todas as sessões adquiridas.
                    dataKeys = Array.from(mapDatas.keys());
                    if(mapDatas.size >= 3){
                      for(let i=0; i < 3; i++) { 
                        //Zero as sessões para avaliar se alguma foi recusada nesta data
                        sessoes=[];
                        //Parto do princípio que a etapa foi adquirida
                        etapaAdq=true;
                        //Coloco os resultados das sessões das últimas 3 datas no array sessoes
                        //(mapDatas.values().next().value as string[]).forEach(sessao =>{
                        //console.log(dataKeys[i])
                        mapDatas.get(dataKeys[i]).forEach(sessao => {
                          sessoes.push(sessao);
                        })
                        
                        //console.log(etapa.id)
                        //console.log(sessoes);
  
                        //Retiro as sessões com status vazio
                        sessoes = sessoes.filter(element => (element != "" && element != undefined));
                        //console.log(sessoes);
                        if( (sessoes.length == 0) || (sessoes.filter(element => element == "Adquirida").length < sessoes.length) ){
                          etapaAdq = false;
                        }
                        
                      }                    
                      //if( (sessoes.length > 0) && (sessoes.filter(element => element == "Adquirida").length == sessoes.length) ){
                      if(etapaAdq == true){
                        etapasAdquiridas.push(etapa.id);
                      }
                    }
                  }
                }
              }
            }
          }
          //Atualizar etapas do plano de intervenção
          //console.log(etapasAdquiridas)
          etapasAdquiridas.forEach(etapa => {
            houveAlteracao = true;
            //console.log(etapa)
            //Atribuo o status "Adquirida" a etapa em questão
            plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6))
              .etapa.find(eta => eta.id == etapa).status = "Adquirida";
            
            //Verifico se todas as etapas do objetivo foram adquiridas e coloco o objetivo como "Adquirido"
            objAdq = true;
            for(const [,eta] of plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).etapa.entries()) { //plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).etapa.forEach(etapa => {
              //console.log(eta.id + " - " + eta.status)
              if(eta.status != "Adquirida"){
                //console.log("False")
                objAdq = false;
              }
            }
            if(objAdq){
              //console.log(plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).id)
              plano.objetivos.find(objetivo => objetivo.id == etapa.substr(0,6)).status = "Adquirido";
            }
          })
          if(houveAlteracao == true){
            //console.log("atualizando...")
            self.update(plano).subscribe(p => {
              //console.log(p);
            })
          }
        //})
      
      })
  
    }
    
    private setMapEtapasporData(coletas: ColetaDiaria[]){
      let m: Map<string, string[]>;
      let data: string;
      let status: string[];
      this.etapaMap = new Map<string, Map<string, string[]>>();
      for(const [,coleta] of coletas.entries()) { //coletas.forEach(coleta => {
        for(const [, objetivo] of coleta.objetivos.entries()) {//coleta.objetivos.forEach(objetivo => {
          for(const [,etapa] of objetivo.etapa.entries()) {//objetivo.etapa.forEach(etapa => {
            //console.log(coleta.data)
            data = new Date(coleta.data).getDate() + "/" + (new Date(coleta.data).getMonth() + 1) + "/" + new Date(coleta.data).getFullYear();
            //console.log(data)
            //Verifico se já tenho a etapa cadastrada
            if(this.etapaMap.get(etapa.id) == undefined){
              //Caso não tenha a etapa cadastrada, faço o cadastro já com a data corrente
              m = new Map<string, string[]>();
              m.set(data, [etapa.status])
              this.etapaMap.set(etapa.id, m);
            } else {
              //Caso já tenha a etapa no Map, recupero o map da etapa e vejo se a data está cadastrada
              m = this.etapaMap.get(etapa.id);
              if(m.get(data) == undefined) {
                //Se a data não está cadastrada, faço o cadastro da mesma
                m.set(data, [etapa.status])
                this.etapaMap.set(etapa.id, m)
              } else {
                //Se a data está cadastrada, pego o array de datas e faço um push para o novo status
                status = m.get(data)
                status.push(etapa.status)
                this.etapaMap.set(etapa.id, m);
              }
            }
          }//})
        }//})
      }//})
      //console.log(this.etapaMap)
    }
    */
}
