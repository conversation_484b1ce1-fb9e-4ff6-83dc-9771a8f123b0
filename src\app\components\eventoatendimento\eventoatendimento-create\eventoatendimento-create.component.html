<mat-card>
    <mat-card-header>
        <mat-card-title>
            <ng-container *ngIf="atendimento.id == undefined">
                Novo atendimento
            </ng-container>
            <ng-container *ngIf="atendimento.id != undefined">
                {{atendimento.paciente.nome}} ({{atendimento.procedimento.nome}})
            </ng-container>
        </mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <!--
                id?: string | number;
                start: Date;
                end?: Date;
                title: string;
                status: string; //Desmarcado pelo paciente, Não compareceu, Atendido, Marcado confirmado, Marcado não confirmado
                procedimento: TipoProcedimento; 
                profissionais: Profissional[];
                idProfissionais: string[];
                paciente: Paciente;
                idPaciente: string;
            -->
            <form #ngForm> 
                <mat-form-field style="width: 15%; margin-left: 10px;">
                    <input class="input" matInput placeholder="Data" 
                    [(ngModel)]="atendimento.start" name="start"
                    [matDatepicker]="picker" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="dataInicio.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 15%; margin-left: 10px;">
                    <mat-label>Hora</mat-label>
                    <input matInput [ngxTimepicker]="hora"
                            [(ngModel)]="horaInicio"
                            [format]="24"
                            placeholder="Hora"
                            [formControl]="timeFC" required>
                            <mat-error *ngIf="timeFC.invalid">Hora é obrigatória.</mat-error>  
                </mat-form-field>
                <ngx-material-timepicker #hora></ngx-material-timepicker>

                <mat-form-field  style="width: 45%; padding-left: 10px;">
                    <!--mat-label>Paciente</mat-label-->
                    <input type="text"
                        placeholder="Paciente" 
                        matInput
                        [(ngModel)]="atendimento.paciente"
                        name="Paciente"
                        [formControl]="pacienteFC" 
                        [matAutocomplete]="auto" required>
                    <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                        <mat-option *ngFor="let paciente of filteredPacientes | async" [value]="paciente" >
                            {{paciente.nome}}
                        </mat-option>
                    </mat-autocomplete>
                    <mat-label>Paciente</mat-label>
                    <mat-error *ngIf="pacienteFC.invalid">Paciente é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <mat-label>Status</mat-label>
                    <mat-select placeholder="Status" 
                        [(ngModel)]="atendimento.status"
                        name="status" required>
                        <mat-option value="Desmarcado pelo paciente" >Desmarcado pelo paciente</mat-option>
                        <mat-option value="Não compareceu" >Não compareceu</mat-option>
                        <mat-option value="Atendido" >Atendido</mat-option>
                        <mat-option value="Marcado confirmado" >Marcado confirmado</mat-option>
                        <mat-option value="Marcado não confirmado" >Marcado não confirmado</mat-option>
                    </mat-select>
                    <mat-error *ngIf="statusFC.invalid">Status é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <mat-label>Procedimento</mat-label>
                    <mat-select placeholder="Procedimento" 
                        [(ngModel)]="atendimento.idProcedimento"
                        (selectionChange) = "setProcedimento($event)"
                        name="Procedimento" required>
                        <mat-option *ngFor="let proc of tiposProcedimentos" [value]="proc.id" >
                            {{proc.nome}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="tipoProcedimento.invalid">Tipo de Procedimento é obrigatório.</mat-error>  
                </mat-form-field>

                <!--mat-label>Repetir</mat-label-->
                <mat-checkbox placeholder="Repetir" 
                    [(ngModel)]="atendimento.recorrente"
                    [formControl]="recorrenteFC"
                    style="width: 100%; padding-left: 10px;"
                    name="Repetir">
                        Repetir
                </mat-checkbox>

                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <mat-label>Periodicidade:</mat-label>
                    <mat-select 
                        [(ngModel)]="freq"
                        name="frequencia" [disabled] = "!atendimento.recorrente">
                        <mat-option [value] ="daily" >Diariamente</mat-option>
                        <mat-option [value] ="weekly" >Semanalmente</mat-option>
                        <mat-option [value] ="monthly" >Mensalmente</mat-option>
                        <!--mat-option [value]="Bimestralmente" >Bimestralmente</mat-option>
                        <mat-option value="Marcado não confirmado" >Marcado não confirmado</mat-option-->
                    </mat-select>
                </mat-form-field>
                
                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <mat-label>Ocorrências:</mat-label>
                    <input type="text"
                        matInput
                        [(ngModel)]="ocorrencias"
                        name="ocorrencias"
                        [disabled] = "!atendimento.recorrente">
                </mat-form-field>



                <!-- Lista de Profissionais -->
                <mat-card class="mat-elevation-z0"  style="margin-top: 10px; width: 100%;">
                    <mat-card-title class="subtitle">Profissional(is)</mat-card-title>
                    <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
                        <mat-card fxFlex="31" *ngFor="let profissional of atendimento.profissionais; index as id"
                        class="mat-elevation-z0" style="margin: 10px; width: 27%;"
                        fxFlexAlign="stretch" fxLayoutGap="20px">
                            <mat-card-header> 
                                <div mat-card-avatar style="margin: 10px;">
                                    <div *ngIf="profissional.sexo=='M'; then menino else menina"></div>
                                    <ng-template #menino><img src="/assets/img/menino_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                                    <ng-template #menina><img src="/assets/img/menina_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                                </div>
                                <mat-card-title class="mat-card-title">
                                    <small style="text-align: left;" _ngcontent-c9>
                                        {{profissional.nome}}
                                        <a style="padding-left: 10px; color: gray; cursor: pointer;"
                                            (click)="deleteProfissional(atendimento.profissionais.indexOf(profissional))"
                                            *ngIf="hasAccessUpdate">
                                            <mat-icon>person_remove</mat-icon>
                                        </a>
                                    </small>
                                </mat-card-title>
                            </mat-card-header>
                        </mat-card>
                    </div>
                    <!-- Fim da lista de profissionais-->
                    <!-- Início da adição de profissionais-->
                    <mat-form-field  style="width: 40%; padding: 20px;">
                        <mat-label>Profissional</mat-label>
                        <mat-select placeholder="Profissional" 
                            [(ngModel)]="profissional"
                            name="profissional" >
                            <mat-option *ngFor="let profissional of profissionais" [value]="profissional" >
                                {{profissional.nome}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    <a (click)="addProfissional()" class="add"
                        *ngIf="hasAccessUpdate">
                        <i class="material-icons">
                            add
                        </i>
                    </a>
                </mat-card> 
                <!-- Fim da adição de profissionais-->

            </form>
        </div>
    </mat-card-content>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>