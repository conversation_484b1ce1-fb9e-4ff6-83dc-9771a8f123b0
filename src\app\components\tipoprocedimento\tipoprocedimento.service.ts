import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { TipoProcedimento } from './tipoprocedimento-model';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TipoprocedimentoService {

  tipoprocedimentoUrl = `${environment.API_URL}/tipoprocedimento`;
  
  public funcoes: BehaviorSubject<TipoProcedimento[]> = 
    new BehaviorSubject<TipoProcedimento[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(tipoprocedimento: TipoProcedimento): Observable<TipoProcedimento>{
    //console.log(tipoprocedimento);
    return this.http.post<TipoProcedimento>(this.tipoprocedimentoUrl, tipoprocedimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(tipoprocedimento: TipoProcedimento): Observable<TipoProcedimento>{
    //console.log(tipoprocedimento);
    return this.http.put<TipoProcedimento>(this.tipoprocedimentoUrl + "/" + tipoprocedimento.id, tipoprocedimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<TipoProcedimento>{
    return this.http.get<TipoProcedimento>(this.tipoprocedimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<TipoProcedimento[]>{
    return this.http.get<TipoProcedimento[]>(this.tipoprocedimentoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<TipoProcedimento>{
    return this.http.delete<TipoProcedimento>(this.tipoprocedimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
