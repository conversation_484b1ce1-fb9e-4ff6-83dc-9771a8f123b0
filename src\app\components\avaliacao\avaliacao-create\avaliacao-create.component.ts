import { fi } from 'date-fns/locale';
import { AvaliacaoService } from './../avaliacao.service';
import { RespostaHabilidadeAvaliacao } from './../resposta-habilidade-avaliacao-model';
import { Avaliacao } from './../avaliacao-model';
import { TipoAvaliacao } from './../tipo-avaliacao-model';
import { DominioAvaliacaoService } from './../dominio-avaliacao.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../template/auth/auth.service';
import { NivelAvaliacaoService } from '../nivel-avaliacao.service';
import { NivelAvalicao } from '../nivel-avaliacao-model';
import { DominioAvaliacao } from '../dominio-avaliacao-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Profissional } from '../../profissional/profissional-model';
import { HabilidadeAvaliacaoService } from '../habilidade-avaliacao.service';
import { TipoAvaliacaoService } from '../tipo-avaliacao.service';
import { PacienteService } from '../../paciente/paciente.service';
import { Paciente } from '../../paciente/paciente-model';
import { MatSelectChange } from '@angular/material/select';
import { HabilidadeAvaliacao } from '../habilidade-avaliacao-model';
import { DominioRespostaHabilidadeAvaliacao } from '../dominio-resposta-habilidade-avaliacao-model';
import { FormControl, NgForm, Validators } from '@angular/forms';
import { FirebaseUserModel } from '../../template/auth/user-model';
import moment from 'moment';
import { ChangeDetectorRef } from '@angular/core';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { error } from 'console';
import { differenceInDays } from 'date-fns';

@Component({
  selector: 'app-avaliacao-create',
  templateUrl: './avaliacao-create.component.html',
  styleUrls: ['./avaliacao-create.component.css']
})
export class AvaliacaoCreateComponent implements OnInit {
  public niveis: NivelAvalicao[] = [];
  public dominios: DominioAvaliacao[] = [];
  public profissionais: Profissional[] = [];
  public profissionaisDoPaciente: Profissional[] = [];
  public tipoAvaliacao: TipoAvaliacao;
  public habilidades: HabilidadeAvaliacao[] = [];
  public domoniosResposta: DominioRespostaHabilidadeAvaliacao[] = [];

  public nivel: NivelAvalicao = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();
  
  public pac: Paciente = new Paciente();

  public avaliacao: Avaliacao = new Avaliacao();

  public prevAvaliacao: Avaliacao; //= new Avaliacao();

  public habilidadesView: HabilidadeAvaliacao[] = [];
  public respostasHabilidadesView: RespostaHabilidadeAvaliacao[] = [];
  public prevRespostasHabilidadesView: RespostaHabilidadeAvaliacao[] = [];

  // public displayedColumns: string[] = ['id', 'nome', 'prevavaliacao', 'resposta']
  public displayedColumns: string[] = ['id', 'nome', 'oDescricao', 'prevavaliacao']

  disabledButtomNext: boolean = false;
  textoBotao: string = 'Próximo Domínio';
  todasHabilidades: any[] = [];
  allButton: any;
  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;

  //Form Controls
  paciente = new FormControl('', [Validators.required]);
  profissional = new FormControl('', [Validators.required]);
  data = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  constructor(
    private pacienteService: PacienteService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private profissionalService: ProfissionalService,
    public habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    public authService: AuthService,
    private avaliacaoService: AvaliacaoService,
    private router: Router,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService,
    ) { }

  async ngOnInit(): Promise<void> {
    try {
      let idTipoAvaliacao = this.route.snapshot.paramMap.get('idTipoAvaliacao');
      let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
      let idAvaliacao = this.route.snapshot.paramMap.get('idAvaliacao');
  
      //Carrego a lista de profissionais
      await this.profissionalService.find().subscribe(profissionais => {
        this.profissionais = profissionais;
  
        if(idAvaliacao == undefined) { //Create
          //Caso seja um profisisonal, seto como o criador
          if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
            if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
              // this.avaliacao.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
              this.avaliacao.idProfissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid).id;
              // setProfissional($event)
            }
          }
    
          //Carrego os níveis do tipo de avaliação
          this.nivelAvaliacaoService.findByTipoAvaliacao(idTipoAvaliacao).subscribe(n => {
            this.niveis = n;
            this.nivel = n[0];
            // console.log(this.nivel)
            
            this.setDominios();
            //Carrego os domínios do tipo de avaliação
            // this.dominioAvaliacaoService.findByTipoAvaliacao(idTipoAvaliacao).subscribe( d => {
              // this.dominios = d;
              // console.log(this.dominios)
    
              //Recupero o tipo de avaliação
              this.tipoAvaliacaoService.findById(idTipoAvaliacao).subscribe(tipo => {
                this.tipoAvaliacao = tipo;
              })
    
              //Carregando Paciente
              this.pacienteService.findById(idPaciente).subscribe(async paciente => {
                this.pac = paciente;
                this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update')
                this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')
                
                // this.avaliacao.paciente = paciente;
                this.avaliacao.idPaciente = paciente.id;
                this.avaliacao.idTipoAvaliacao = idTipoAvaliacao;
  
                //Carrego os profissionais vinculados ao paciente
                this.profissionaisDoPaciente = paciente.equipe; 
        
                //Carregando Habilidades
                (await this.habilidadeAvaliacaoService.findByTipoAvaliacao(idTipoAvaliacao)).subscribe(async habilidades => {
                  this.habilidades = habilidades;
                  let resp: RespostaHabilidadeAvaliacao;
                  for(const [,habilidade] of habilidades.entries()) {
                    resp = new RespostaHabilidadeAvaliacao();
                    resp.idHabilidadeAvaliacao = habilidade.id;
                    resp.valor = undefined;
                    (resp as any).habilidade = habilidade;
                    this.avaliacao.respostasHabilidades.push(resp);
                  }
                  // console.log(this.habilidades);
                  //Seto o primeiro domínio do nível
                  if(this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) ).length > 0){
                    // console.log("Passou")
                    // console.log(this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao)
    
                    // console.log(this.dominios.find(d => {
                    //   return "" + this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao === "" + d.id;
                    // }))
                    this.dominio = this.dominios.find(d => {
                      return "" + this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao === "" + d.id;
                    })
                    // console.log(this.dominio)
  
                    //Pego o checklist anterior
                    await this.avaliacaoService.findByPaciente(idPaciente).subscribe(async avaliacoes => {
                      let avaliacoesFiltered = avaliacoes.filter(a => a.idTipoAvaliacao == idTipoAvaliacao);
                      avaliacoesFiltered = await avaliacoesFiltered.filter(a => a.ativo == true);

                      if (avaliacoes && avaliacoes.length > 0) {
                        this.prevAvaliacao = avaliacoesFiltered[0];
                      }
                    })
                    this.filterHabilidades();
                  }
                  // console.log(this.habilidadesView);
                  
                  //Carrego os domínios máximos
                  for(const [, h] of habilidades.entries()) {
                    for(const [, d] of h.dominioResposta.entries()) {
                      if(this.domoniosResposta.filter( resp =>  resp.sigla === d.sigla).length == 0){
                        this.displayedColumns.push("resp-" + d.sigla)
                        this.domoniosResposta.push(d);
                      }
                    }
                  }
  
                  // Ordenar as colunas e os domínios de resposta
                  this.domoniosResposta = this.collectDominiosResposta(habilidades);
                  this.displayedColumns.sort((a, b) => a.localeCompare(b));
                });
              });
              // this.avaliacao.respostasHabilidades = [];
            // })
          })
        } else { //Edit
          // console.log("Edit")
          //Caregando a avaliação
          this.avaliacaoService.findById(idAvaliacao).subscribe(async avaliacao => {
            this.avaliacao = avaliacao;
  
            // Carregando avaliação anterior
            await this.avaliacaoService.findByPaciente(avaliacao.idPaciente).subscribe(async avaliacoes => {
              let avaliacoesFiltered = avaliacoes.filter(a => a.ativo == true && a.idTipoAvaliacao == avaliacao.idTipoAvaliacao);
              avaliacoesFiltered = await avaliacoesFiltered.filter(a => a.data < avaliacao.data);

              if (avaliacoes && avaliacoes.length > 0) {
                this.prevAvaliacao = avaliacoesFiltered[0];
              }
            })
    
            //Carregando Paciente
            this.pacienteService.findById(this.avaliacao.idPaciente).subscribe(paciente => {
              this.pac = paciente;
              this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update')
              this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')
  
              // Carregando os profissionais vinculados ao paciente
              this.profissionaisDoPaciente = paciente.equipe;
    
              //Carrego os níveis do tipo de avaliação
              this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao).subscribe(n => {
                this.niveis = n;
                this.nivel = n[0];
  
                this.setDominios();
              })
    
              //Recupero o tipo de avaliação
              this.tipoAvaliacaoService.findById(this.avaliacao.idTipoAvaliacao).subscribe(tipo => {
                this.tipoAvaliacao = tipo;
              })
    
              //Carrego os domínios do tipo de avaliação
              this.dominioAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao).subscribe(async  d => {
                // this.dominios = d;
    
                //Carregando Habilidades
                (await this.habilidadeAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao)).subscribe(async habilidades => {
                  this.habilidades = habilidades;
                  
                  if(!this.avaliacao.respostasHabilidades.find(r => r.idHabilidadeAvaliacao == "1234.1")) {
                    let resp: RespostaHabilidadeAvaliacao;
                    resp = new RespostaHabilidadeAvaliacao();
                    resp.idHabilidadeAvaliacao = "1234.1";
                    resp.valor = undefined;
                    (resp as any).habilidade = this.habilidades.find(h => h.id == "1234.1");
                    this.avaliacao.respostasHabilidades.splice(178, 0, resp);
                  }
                  
                  //Seto o primeiro domínio do nível
                  if(this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) ).length > 0){
                    // console.log("Passou")
                    // console.log(this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao)
    
                    // console.log(this.dominios.find(d => {
                    //   return "" + this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao === d.id;
                    // }))
                    this.dominio = this.dominios.find(d => {
                      return "" + this.habilidades.filter(h => parseInt(h.idNivelAvaliacao) === parseInt(this.nivel.id) )[0].idDominioAvaliacao === "" + d.id;
                    })
                    // console.log(this.dominio)
                    this.filterHabilidades();
                  }
                  
                  //Carrego os domínios de resposta
                  for(const [, h] of habilidades.entries()) {
                  // habilidades.forEach(h => {
                    for(const [, d] of h.dominioResposta.entries()) {
                    // h.dominioResposta.forEach(d => {
                      if(this.domoniosResposta.filter( resp =>  resp.sigla === d.sigla).length == 0){
                        this.displayedColumns.push("resp-" + d.sigla)
                        this.domoniosResposta.push(d);
                      }
                    }//)
                  }//)
                  this.domoniosResposta.sort(function(a, b) {
                    if( a.valor < b.valor) {
                      return 1;
                    } else {
                      return -1;
                    }
                  })
  
                  // Ordenar as colunas e os domínios de resposta
                  this.domoniosResposta = this.collectDominiosResposta(habilidades);
                  this.displayedColumns.sort((a, b) => a.localeCompare(b));
                })
              })
            });
          })
        }
      })
    } catch (error) {
      console.log("Error: " + error);
    }
  }

  collectDominiosResposta(habilidades: HabilidadeAvaliacao[]): DominioRespostaHabilidadeAvaliacao[] {
    const dominiosMap = new Map<string, DominioRespostaHabilidadeAvaliacao>();
  
    habilidades.forEach(habilidade => {
      habilidade.dominioResposta.forEach(dominio => {
        if (!dominiosMap.has(dominio.sigla)) {
          dominiosMap.set(dominio.sigla, dominio);
        }
      });
    });
    return Array.from(dominiosMap.values());
  }

  setProfissional(event:MatSelectChange){
    this.avaliacao.idProfissional = event.value;
  }

  async setDominios(): Promise<void> {
    this.habilidadeAvaliacaoService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];

      this.filterHabilidades();
      this.allButton = "";
    })
  }

  getDominioResposta(valor: string){
    let dr = this.domoniosResposta?.find(d => "" + d.valor == valor);

    if (dr == undefined){
      dr = this.domoniosResposta?.find(d => d.valor == undefined);
    }
    return dr;
  }

  filterHabilidades(){   
    const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
    const ultimoIndice = this.dominios.length - 1;
    const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
    const ultimoNivel = this.niveis.length - 1;
    this.disabledButtomNext = false;
    this.textoBotao = 'Próximo Domínio';

    //Altero o botão de acordo com o indice
    if (indiceAtual === ultimoIndice) {
      this.textoBotao = 'Próximo Nível e Domínio';
      if (nivelAtual === ultimoNivel) {
        this.disabledButtomNext = true;
        this.textoBotao = 'Último nível alcançado!'
      }
    } 

    this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel?.id, this.dominio?.id).subscribe(habilidades => {
      //Se eu tenho avaliação anterior e ela possui resultados, preencho o array com as respostas da availiação anterior, no nível/domínio atual 
      if(this.prevAvaliacao != undefined 
        && this.prevAvaliacao.respostasHabilidades != undefined
        && this.prevAvaliacao.respostasHabilidades.length > 0){

          this.prevRespostasHabilidadesView = this.prevAvaliacao.respostasHabilidades.filter(resposta => {
            return habilidades.filter(h => "" + h.idDominioAvaliacao === "" + this.dominio.id
                                    && "" + h.idNivelAvaliacao === "" + this.nivel.id
                                    && "" + resposta.idHabilidadeAvaliacao === "" + h.id).length > 0
        })
      }

      this.respostasHabilidadesView = this.avaliacao.respostasHabilidades.filter(resposta => {
        return habilidades.filter(h => "" + h.idDominioAvaliacao === "" + this.dominio.id
          && "" + h.idNivelAvaliacao === "" + this.nivel.id
          && "" + resposta.idHabilidadeAvaliacao === "" + h.id).length > 0
      })

      this.habilidadesView = this.habilidades.filter(habilidade => {
        return "" + habilidade.idDominioAvaliacao === "" + this.dominio.id
        && "" + habilidade.idNivelAvaliacao === "" + this.nivel.id
      })

      this.habilidadesView.sort(function(a, b) {
        if( a.ordem > b.ordem) {
          return 1;
        } else {
          return -1;
        }
      })

      this.todasHabilidades = this.habilidadesView;
    })
    this.allButton = "";
  }

  getRespostaHabilidade(id: string) : string{
    //console.log(this.prevvbmappmsassmt);
    // console.log(id);
    if(this.prevAvaliacao != undefined && this.prevAvaliacao?.respostasHabilidades != undefined && this.prevAvaliacao?.respostasHabilidades?.length > 0){
      const index = this.prevAvaliacao?.respostasHabilidades?.findIndex(e => e.idHabilidadeAvaliacao == id)
      return this.prevAvaliacao.respostasHabilidades[index]?.valor
    } else {
      // console.log("respostasHabilidades undefined");
      return undefined;
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente?.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  findIndexHabilidade(idHabilidade: number){
    // console.log(idHabilidade);
    const index = this.avaliacao.respostasHabilidades?.findIndex( resp => parseFloat(resp.idHabilidadeAvaliacao) == idHabilidade);
    return index !== -1 ? index : 0;
  }

  async save(exit: boolean): Promise<void> {
    let p, a: number;
    
    try {
      if(this.form.valid){
        if(this.avaliacao.id == undefined){
          this.avaliacaoService.create(this.avaliacao).subscribe((vbmappMsAssmtId) => {
            this.avaliacao.id = vbmappMsAssmtId
            this.avaliacaoService.showMessage('Avaliação criada com sucesso!');
            if(exit){
              if(this.avaliacao.id == undefined){
                this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
                  tab:"avaliacao"
                }]); 
              } else {
                this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
                  tab:"avaliacao",
                  idAvaliacao: this.avaliacao.id
                }]); 
              }
            }
          });
        } else {
          this.avaliacaoService.update(this.avaliacao).subscribe(() => {
            this.avaliacaoService.showMessage('Avaliação alterada com sucesso!');
            if(exit){
              if(this.avaliacao.id == undefined){
                this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
                  tab:"avaliacao"
                }]); 
              } else {
                this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
                  tab:"avaliacao",
                  idavaliacao: this.avaliacao.id
                }]); 
              }
            }
          });
        }
      } else {
        this.avaliacaoService.showMessage('Existem campos inválidos no formulário!',true);
      }
    } catch (error) {
      console.log("Error", error);
    }
  }

  cancel(){
    this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
      tab:"avaliacao"
    }]); 
    // if(this.avaliacao.id == undefined){
    //   this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
    //     tab:"avaliacao"
    //   }]); 
    // } else {
    //   this.router.navigate(['/paciente/'+this.avaliacao.idPaciente, {
    //     tab:"avaliacao",
    //     idAvaliacao: this.avaliacao.id
    //   }]); 
    // }
  }

  async nextDominio(): Promise<void> {
    try {
      // Encontre o índice do domínio atual
      const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
      const ultimoIndice = this.dominios.length - 1;
      
      // Verifique se não é o último domínio
      if (indiceAtual < ultimoIndice) {
        // Defina o próximo domínio
        this.dominio = this.dominios[indiceAtual + 1];
        this.filterHabilidades();
      } else {
        // É o último domínio do nível atual, então avança para o próximo nível
        const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
    
        // Ainda há níveis, então avança para o próximo nível e define o domínio para o primeiro do novo nível
        this.nivel = this.niveis[nivelAtual + 1];
        await this.setDominios();
      }

      if (indiceAtual === ultimoIndice) {
        this.textoBotao = 'Próximo Domínio';
      }
      this.allButton = "";
      window.scrollTo(0, 0);
    } catch (error) {
      console.log(error);
    }
  }

  async checkAllAcquired(value: string): Promise<void> {
    this.allButton = value;

    for (const [, ava] of this.respostasHabilidadesView.entries()) {
      this.avaliacao.respostasHabilidades[this.findIndexHabilidade(parseFloat(ava.idHabilidadeAvaliacao))].valor = "" + value;
    }

    // Reset da variável allButton após o hide do loading
    setTimeout(() => {
      this.allButton = "";
    }, 0);
  }

  async checkAllAcquiredFlex(value: string): Promise<void> {
    let max: string | undefined;
    let doms: DominioRespostaHabilidadeAvaliacao[];
    try {
      if (value == "1") {
        for (const [, ava] of this.respostasHabilidadesView.entries()) {
          max = (this.avaliacao.respostasHabilidades[this.findIndexHabilidade(parseFloat(ava.idHabilidadeAvaliacao))] as any).habilidade
              .dominioResposta
              .filter(dr => dr.valor !== undefined) // Filtra valores undefined
              .sort((a, b) => {
                return a.valor < b.valor ? 1 : -1;
              })[0]?.valor; // Usa optional chaining para evitar erro se array estiver vazio

          this.avaliacao.respostasHabilidades[this.findIndexHabilidade(parseFloat(ava.idHabilidadeAvaliacao))].valor = max ? "" + max : "0";
          max = undefined;
        }
      } else {
        for (const [, ava] of this.respostasHabilidadesView.entries()) {
          this.avaliacao.respostasHabilidades[this.findIndexHabilidade(parseFloat(ava.idHabilidadeAvaliacao))].valor = "0";
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  isValorWithinMax(idHabilidade: string, domRespValor: string, domRespSigla: string): boolean {
    const habilidadeIndex = this.todasHabilidades.findIndex(habilidade => habilidade.id == parseFloat(idHabilidade));
    if (habilidadeIndex === -1) {
      return false;
    }
    
    const habilidade = (this.todasHabilidades[habilidadeIndex] as any);
    if (!habilidade || !habilidade.dominioResposta) {
      return false;
    }

    // Verifica se existe um domínio de resposta com a sigla "NA"
    const hasNA = habilidade.dominioResposta.some(dom => dom.sigla === "NA");

    // Se a sigla do domResp for "NA", mas a habilidade não tiver um "NA" nos domínios, retorna false
    if (domRespSigla === "NA" && !hasNA) {
      return false;
    } else if (domRespSigla == "NA" && hasNA) {
      return true;
    }
  
    const maxValor = habilidade.dominioResposta
      .map(dom => parseInt(dom.valor, 10))
      .filter(v => !isNaN(v))
      .sort((a, b) => b - a)[0];

    return parseInt(domRespValor, 10) <= maxValor || domRespValor === undefined;
  }
  
  respDescription(idHabilidade: string): string {
    // console.log(idHabilidade)
    const habilidade = this.habilidadesView.find(h => h.id === idHabilidade);
    if (!habilidade || !habilidade.dominioResposta) {
      return '';
    }
    
    // Mapeia os domínios de resposta para o formato desejado
    return habilidade.dominioResposta
    .map(dom => `${dom.sigla}: ${dom.nome}`)
    .join('\n'); 
  }

  habilidadeHasNA(idHabilidade: string): string {
    const habilidade = this.todasHabilidades.find(h => h.id === parseFloat(idHabilidade));

    const hasNA = habilidade.dominioResposta.some(dom => dom.sigla === "NA");
  
    if (hasNA) {
      let valor = habilidade.dominioResposta.find(dom => dom.sigla === "NA").valor;
      return "" + valor;
    } else {
      return "";
    }
  }

  onDataChange(novaData: Date) {
    let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    if (!idPaciente) {
      idPaciente = this.avaliacao?.idPaciente;
    }
    const idTipoAvaliacao = this.avaliacao?.idTipoAvaliacao;
  
    if (idPaciente && idTipoAvaliacao && novaData) {
      this.avaliacaoService.findByPaciente(idPaciente).subscribe(avaliacoes => {
        // Filtro: tipo correto, ativas e com data anterior ou igual à novaData
        const avaliacoesFiltradas = avaliacoes.filter(a =>
          a.idTipoAvaliacao === idTipoAvaliacao &&
          a.ativo === true &&
          a.data &&
          new Date(a.data) < novaData
        );
  
        if (avaliacoesFiltradas.length === 0) {
          this.prevAvaliacao = null;
          return;
        }
  
        // Achar a avaliação mais próxima (anterior) da nova data
        const avaliacaoMaisProxima = avaliacoesFiltradas.reduce((anterior, atual) => {
          const diffAtual = Math.abs(new Date(atual.data).getTime() - novaData.getTime());
          const diffAnterior = Math.abs(new Date(anterior.data).getTime() - novaData.getTime());
          return diffAtual < diffAnterior ? atual : anterior;
        });
  
        this.prevAvaliacao = avaliacaoMaisProxima;
      });
    }
  }  
  
}
