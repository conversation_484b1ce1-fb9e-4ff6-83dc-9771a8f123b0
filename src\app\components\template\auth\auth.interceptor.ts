import { ReactiveFormsModule } from '@angular/forms';
import { Observable, from } from 'rxjs';
import { HttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';



import { AuthService } from './auth.service';
import { switchMap } from 'rxjs/operators';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    token: any;

    constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

    //console.log('Entrou no Intercept')
    /*
    const tokenPromise = this.token === undefined
                    ? this.authService.getToken()
                    : Promise.resolve(this.token);
    */
   const tokenPromise = this.authService.getToken();
    
    //console.log('Pegou o tokenPromise')
    //console.log(tokenPromise)
    return from(tokenPromise).pipe(
        switchMap((token) => {
            this.token = token;
            req = req.clone({
                setHeaders: {
                  'Content-Type' : 'application/json; charset=utf-8',
                  'Accept'       : 'application/json',
                  'Authorization': `${this.token}`,
                  "Cache-Control": "no-cache",
                  Pragma: "no-cache",
                },
              });
            //console.log("idToken: " + this.token);
            return next.handle(req);
        })
    )
  }
}