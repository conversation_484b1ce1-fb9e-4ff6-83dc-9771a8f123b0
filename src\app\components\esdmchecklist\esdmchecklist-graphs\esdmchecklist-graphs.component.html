<div fxLayout="row wrap" style="width: 100%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"
    *ngIf="hasAccessRead && esdmchecklists?.length > 0"> 
    <div fxLayout="row wrap" style="width: 25%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"> 
        <div fxLayout="column" style="width: 60%; height: fit-content; background-color: rgb(240, 240, 240); margin: 20px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.3);">

            <div style="text-align: center; padding-top: 10px;">
                <b>Tipo de Gráfico</b>
            </div>
            <div fxFlex="100" style="width: 100%; text-align: center;">
                <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select id="chartTypeSelect" [(ngModel)]="selectedChartType" (selectionChange)="updateChart()">
                        <mat-option value="horizontalBar">Barra Horizontal</mat-option>
                        <mat-option value="bar">Barra Vertical</mat-option>
                        <mat-option value="radar">Radar</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>

            <div style="text-align: center;">
                <h3><b>Filtros</b></h3>
            </div>

            <div style="text-align: center;">
                <strong>Checklist</strong>
            </div>
            <div>
                <section style="width: 100%;" fxFlex="100">
                    <ul style="text-align: center; padding-right: 30px;">
                        <li *ngFor="let checklist of esdmchecklists | slice:0:10; let i=index">
                            <mat-checkbox placeholder="Checklist" 
                                id="checklist-{{ i }}"
                                [checked] = "checked(i)"
                                name="{{checklist.data  | date: 'dd/MM/yyyy'}}" (change) = "setChecklist($event)">
                                {{checklist.data | date: 'dd/MM/yyyy'}} <br>
                            </mat-checkbox>
                        </li>
                    </ul>
                </section>
            </div>
            <div style="text-align: center;">
                <strong>Nível</strong>
            </div>
            <div fxFlex="100" style="width: 100%; text-align: center;">
                <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select
                        [(ngModel)]="nivel"
                        name="nivel" (selectionChange) = "setNivel()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel">
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxLayout="row" fxLayoutAlign="start center" style="width: 100%; text-align: center; padding-bottom: 15px;" *ngIf="selectedChartType != 'radar'">
                <label style="cursor: pointer; text-decoration: none;">
                  <input type="checkbox" [(ngModel)]="showPercentage" (change)="togglePercentage()">
                  Mostrar porcentagem
                </label>
            </div>
        </div>
    </div>
    <div fxLayout="row wrap" style="width: 80%; display: flex;" fxLayoutAlign="end center" fxLayoutGap="10"> 
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 60%;">
            <canvas baseChart 
                [datasets]="graph.datasets" 
                [labels]="graph.labels" 
                [options]="graph.options"
                legend="true" 
                [chartType]="graph.chartType"
                [colors]="colors" height="250"
                style="display: flex;">
            </canvas>
            <span *ngIf="dominioParc" style="color: red; font-weight: bold; font-size: 12px">* Domínio possui habilidades não observadas na avaliação</span>
        </div> 
    </div>
</div>
<div *ngIf="esdmchecklists?.length == 0 || esdmchecklists == undefined" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Aguardando a criação do primeiro checklist!</h1>
</div>
<!--div fxLayout="row wrap" style="width: 100%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10">   
    <div fxLayout="row wrap" style="display: flex; width: 20%;">
        <section style="width: 100%;" fxFlex="100">
            <ul>
                <li *ngFor="let checklist of esdmchecklists | slice:0:4; let i=index">
                    <mat-checkbox placeholder="Checklist" 
                        id="checklist-{{ i }}"
                        [checked] = "checked(i)"
                        name="{{checklist.data  | date: 'dd/MM/yyyy'}}" (change) = "setChecklist($event)">
                        {{checklist.data | date: 'dd/MM/yyyy'}} <br>
                    </mat-checkbox>
                </li>
            </ul>
        </section>
        <mat-form-field  style="padding: 20px;">
            <mat-label>Nível</mat-label>
            <mat-select placeholder="Nível" 
                [(ngModel)]="nivel"
                name="nivel" (selectionChange) = "setNivel()">
                <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                    {{nivel.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 60%;">
        <canvas baseChart 
            [datasets]="graph.datasets" 
            [labels]="graph.labels" 
            [options]="graph.options"
            legend="true" 
            [chartType]="graph.chartType"
            [colors]="colors" height="200">
        </canvas>
    </div> 
</div-->