import { Component, OnInit } from '@angular/core';
import { ColetadiariaService } from '../../coletadiaria/coletadiaria.service';
import { AuthService } from '../../template/auth/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ColetaDiaria } from '../../coletadiaria/coleta-diaria-model';
import { Profissional } from '../../profissional/profissional-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Paciente } from '../../paciente/paciente-model';
import { PacienteService } from '../../paciente/paciente.service';
import { MatTableDataSource } from '@angular/material/table';
import moment from 'moment';
import { ObjetivoVBMAPP } from '../../objetivovbmapp/objetivovbmapp-model';
import { TipoSuporte } from '../../tiposuporte/tiposuporte-model';
import { LoadingService } from 'src/app/shared/service/loading.service';

export interface ReportTable {
  data: string;
  profissional: string;
  paciente: string;
  objetivo: string;
  etapa: string;
  score: string;
  percentual: string;
  status: string;
}

@Component({
  selector: 'app-coletas-diarias-report',
  templateUrl: './coletas-diarias-report.component.html',
  styleUrls: ['./coletas-diarias-report.component.css']
})
export class ColetasDiariasReportComponent implements OnInit {

  public coletasDiarias: ColetaDiaria[];
  public dataInicial: Date;
  public dataFinal: Date;
  public profissionais: Profissional[];
  public profissional: Profissional;
  public pacientes: Paciente[];
  public paciente: Paciente;

  displayedColumns: string[] = ['Data', 'Profissional', 'Paciente', 'Objetivo', 'Etapa', 'Score', '%', 'Status'];
  allColumns: string[] = ['Data', 'Profissional', 'Paciente', 'Objetivo', 'Etapa','Score', '%', 'Status'];
  reportElements: ReportTable[] = [];
  datasource = new MatTableDataSource();

  public today: Date = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());

  constructor(private coletaDiariaService: ColetadiariaService,
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.loadingService.show();
    this.dataFinal = new Date();
    this.dataInicial = new Date(this.dataFinal.getFullYear(), this.dataFinal.getMonth(), 1);
    this.profissionalService.find().subscribe(profis => {
      this.profissionais = profis;
      this.pacienteService.find().subscribe(pacs => {
        this.pacientes = pacs;
        this.loadingService.hide();
      })
    })
    this.loadingService.hide();
  }

  checkMandatoryFields(){
    if(this.profissional == undefined){
      this.coletaDiariaService.showMessage('É necessário selecionar um profissional.', true);
    }

    if(this.dataInicial == undefined){
      this.coletaDiariaService.showMessage('É necessário definir uma data inicial para a pesquisa.', true);
    
    }
    if(this.dataFinal == undefined){
      this.coletaDiariaService.showMessage('É necessário definir uma data final para a pesquisa.', true);
    }

  }

  search(){
    this.reportElements = [];
    this.datasource.data = [...this.reportElements]
    this.coletasDiarias = [];
    this.checkMandatoryFields();
    let manter: boolean;

    this.coletaDiariaService.findByProfissionalPeriod(  this.profissional.id,
                                                        moment(this.dataInicial).format('YYYY-MM-DD'),
                                                        moment(this.dataFinal).format('YYYY-MM-DD')).subscribe(cols => {
      this.coletasDiarias = cols;
      for(const [, c] of cols.entries()){
        for(const [, o] of c.objetivos.entries()){

          //Coleta Denver
          if(o.etapa != undefined){
            for(const [, e] of o.etapa.entries()){
              if ((e.negativos + e.positivos) != 0){
                this.reportElements.push({
                  data: moment(c.data).format("DD/MM/YYYY"),
                  profissional: c.profissional.nome,
                  paciente: c.paciente.nome,
                  objetivo: o.nome,
                  etapa: e.id + " - " + e.nome,
                  score: e.positivos  + "/" + (e.negativos + e.positivos),
                  percentual: "" + (e.percentual * 100),
                  status: e.status
                })
              }
            }
          } else { //Coleta ABA
            for(const [, t] of (o as any as ObjetivoVBMAPP).tiposSuporte.entries()){

              for(const [, e] of (t as TipoSuporte).estimulos.entries()){
                if ((e.negativos + e.positivos) != 0){
                  this.reportElements.push({
                    data: moment(c.data).format("DD/MM/YYYY"),
                    profissional: c.profissional.nome,
                    paciente: c.paciente.nome,
                    objetivo: o.nome,
                    etapa: t.nome + " - " + e.nome,
                    score: e.positivos  + "/" + (e.negativos + e.positivos),
                    percentual: "" + (e.percentual * 100),
                    status: e.status
                  })
                }
              }
            }
          }
        }
      }
      if (this.paciente != undefined) {
        this.reportElements = this.reportElements.filter((el) => {
          manter = (el.paciente == this.paciente.nome);
          return manter;
        })
      }
      this.datasource.data = [...this.reportElements]
    })
  }

  clear(){
    this.reportElements = [];
    this.datasource.data = [...this.reportElements]
    this.coletasDiarias = [];
    this.paciente = undefined;
    this.profissional = undefined;
    this.dataInicial = undefined
    this.dataFinal = undefined

  }

}
