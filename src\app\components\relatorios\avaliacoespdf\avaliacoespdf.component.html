<!-- HTML para a impressão do PDF -->
<div id="avaliacaoPDF" #avaliacaoPDF>
    <div *ngFor="let nivel of niveis" class="folha-horizontal-pdf" [ngStyle]="getStyleByNivelId(nivel.id)">
        <div class="cabecalho-pdf">
            <img class="cabecalho-img-pdf" src="assets/img/{{ idOrganizacao }}.png" width="100px">
            <div class="cabecalho-dados-pdf">
                Avaliação&nbsp;-&nbsp;{{tipoAvaliacao?.nome}}<br>
                {{paciente?.nome}}<br>
                Data: {{avaliacao?.data| date: 'dd/MM/yyyy'}}
            </div>
        </div>

        <div style="margin: 20px;">
            <h3 class="level-legend-container-pdf">
                <b>{{nivel.nome}}</b>
                <div *ngIf="nivel.id != 14" class="legend-container-pdf">
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf adquirido-pdf"></div>
                        <p class="legend-text-pdf">Adquirido</p>
                        <p *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'" class="legend-text-pdf">(100%)</p>
                    </div>
                    <div class="legend-item-pdf" *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'">
                        <div class="color-box-pdf parcialmente3-pdf"></div>
                        <p class="legend-text-pdf">Quase Adquirido</p>
                        <p *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'" class="legend-text-pdf">(75%)</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf parcialmente-pdf"></div>
                        <p class="legend-text-pdf">Parcialmente Adquirido</p>
                        <p *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'" class="legend-text-pdf">(50%)</p>
                    </div>
                    <div class="legend-item-pdf" *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'">
                        <div class="color-box-pdf parcialmente1-pdf"></div>
                        <p class="legend-text-pdf">Quase parcialmente Adquirido</p>
                        <p *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'" class="legend-text-pdf">(25%)</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-adquirido-pdf"></div>
                        <p class="legend-text-pdf">Não Adquirido</p>
                        <p *ngIf="tipoAvaliacao.id == '2' || tipoAvaliacao.id == '7'" class="legend-text-pdf">(0%)</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-observado-pdf"></div>
                        <p class="legend-text-pdf">Não Observado</p>
                    </div>
                </div>

                <div *ngIf="nivel.id == 14" class="legend-container-pdf">
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf adquirido-pdf"></div>
                        <p class="legend-text-pdf">Demonstra consistentemente</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf parcialmente3-pdf"></div>
                        <p class="legend-text-pdf">Demonstra mas não constantemente</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf parcialmente-pdf"></div>
                        <p class="legend-text-pdf">Pode demonstrar</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-adquirido-pdf"></div>
                        <p class="legend-text-pdf">Raramente ou nunca</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-observado-pdf"></div>
                        <p class="legend-text-pdf">Não observado</p>
                    </div>
                </div>
            </h3>

            <div class="table-container-pdf">
                <table>
                    <tbody>
                        <tr>
                            <ng-container *ngFor="let dominio of dominiosPorNivel[nivel.id]">
                                <td class="table-dados-pdf" *ngIf="dominio.id <= 44 || dominio.id >= 57">
                                    <div *ngFor="let habilidade of getAvaliacaoInfosPDF(nivel.id, dominio.id)">

                                        <!-- Formatação PDF -->
                                        <br *ngIf="habilidade.sigla == 'Socialização-23' || habilidade.sigla == 'Desenvolvimento Motor-23' || habilidade.sigla == 'Linguagem-51' || habilidade.sigla == 'Auto cuidados-48' || habilidade.sigla == 'Cognição-63' || habilidade.sigla == 'Desenvolvimento Motor-134' || habilidade.sigla == 'Auto cuidados-90' || habilidade.sigla == 'B23'|| habilidade.sigla == 'C23'|| habilidade.sigla == 'D23'|| habilidade.sigla == 'F23'|| habilidade.sigla == 'G23'|| habilidade.sigla == 'H23'|| habilidade.sigla == 'L23'|| habilidade.sigla == 'R23'|| habilidade.sigla == 'Y23'|| habilidade.sigla == 'Z23'|| habilidade.sigla == 'C52' || habilidade.sigla == 'SP23' || habilidade.sigla == 'SL23' || (habilidade.sigla == 'CG23' && nivel.id == 14) || habilidade.sigla == 'AG23' || habilidade.sigla == 'VS23' || habilidade.sigla == 'IB23' || habilidade.sigla == 'HG23' || habilidade.sigla == 'SS23' || habilidade.sigla == 'MB23' || habilidade.sigla == 'CS23' || habilidade.sigla == 'FC23' || habilidade.sigla == 'CF23' || habilidade.sigla == 'TL23' || habilidade.sigla == 'CM23' || habilidade.sigla == 'RC23' || habilidade.sigla == 'RL23' || habilidade.sigla == 'LT23' || habilidade.sigla == 'MD23' || habilidade.sigla == 'LA23' || habilidade.sigla == 'CZ23' || habilidade.sigla == 'CL23' || habilidade.sigla == 'RE23' || habilidade.sigla == 'RO23' || habilidade.sigla == 'HS23' || habilidade.sigla == 'TC23' || habilidade.sigla == 'CG23' || habilidade.sigla == 'BC23' || habilidade.sigla == 'BC23' || habilidade.sigla == 'PR23' || habilidade.sigla == 'RO52' || habilidade.sigla == 'PR52' || habilidade.sigla == 'PE52' || habilidade.sigla == 'PE23' || habilidade.sigla == 'HB23' || habilidade.sigla == 'ST23' || habilidade.sigla == 'FX23' || habilidade.sigla == 'ES23' || habilidade.sigla == 'HI23' || habilidade.sigla == 'HR23' || habilidade.sigla == 'CR23' || habilidade.sigla == 'FE23' || habilidade.sigla == 'PM23' || habilidade.sigla == 'HO23' || habilidade.sigla == 'AC23' || habilidade.sigla == 'ML23' || habilidade.sigla == 'PA23' || habilidade.sigla == 'TR23' || habilidade.sigla == 'UE23' || habilidade.sigla == 'GF23' || habilidade.sigla == 'GP23' || habilidade.sigla == 'SE23' || habilidade.sigla == 'IS23' || habilidade.sigla == 'RI23'">

                                        <p [ngClass] = "[getClassRespostaHabilidadePDF(habilidade)]">{{ habilidade.sigla }}</p>

                                    </div>
                                </td>
                            </ng-container>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div *ngIf="nivel.id == 13">
            <br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
            <div class="cabecalho-pdf">
                <img class="cabecalho-img-pdf" src="assets/img/{{ idOrganizacao }}.png" width="100px">
                <div class="cabecalho-dados-pdf">
                    Avaliação&nbsp;-&nbsp;<span [innerHTML]="formattedTipoAvaliacaoNome"></span><br>
                    {{paciente?.nome}}<br>
                    Data: {{avaliacao?.data| date: 'dd/MM/yyyy'}}
                </div>
            </div>

            <div style="margin: 20px;">
                <h3 class="level-legend-container-pdf">
                    <b>{{ nivel.nome }}</b>
                    <div class="legend-container-pdf">
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf adquirido-pdf"></div>
                            <p class="legend-text-pdf">Adquirido</p>
                            <p class="legend-text-pdf">(100%)</p>
                        </div>
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf parcialmente3-pdf"></div>
                            <p class="legend-text-pdf">Quase Adquirido</p>
                            <p class="legend-text-pdf">(75%)</p>
                        </div>
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf parcialmente-pdf"></div>
                            <p class="legend-text-pdf">Parcialmente Adquirido</p>
                            <p class="legend-text-pdf">(50%)</p>
                        </div>
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf parcialmente1-pdf"></div>
                            <p class="legend-text-pdf">Quase parcialmente Adquirido</p>
                            <p class="legend-text-pdf">(25%)</p>
                        </div>
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf nao-adquirido-pdf"></div>
                            <p class="legend-text-pdf">Não Adquirido</p>
                            <p *ngIf="nivel.nome == 'ABLLS-R'" class="legend-text-pdf">(0%)</p>
                        </div>
                        <div class="legend-item-pdf">
                            <div class="color-box-pdf nao-observado-pdf"></div>
                            <p class="legend-text-pdf">Não Observado</p>
                        </div>
                    </div>
                </h3>

                <div class="table-container-pdf">
                    <table>
                        <tbody>
                            <tr>
                                <ng-container *ngFor="let dominio of dominiosPorNivel[nivel.id]">
                                    <td class="table-dados-pdf" *ngIf="dominio.id >= 45">
                                        <div *ngFor="let habilidade of getAvaliacaoInfosPDF(nivel.id, dominio.id)">

                                            <!-- Formatação PDF -->
                                            <br *ngIf="habilidade.sigla == 'R23'|| habilidade.sigla == 'Y23'|| habilidade.sigla == 'Z23'|| habilidade.sigla == 'C52'">

                                            <p [ngClass] = "[getClassRespostaHabilidadePDF(habilidade)]">{{ habilidade.sigla }}</p>

                                        </div>
                                    </td>
                                </ng-container>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Glossário -->
    <div class="glossario-pdf">
        <h3>Glossário</h3>
        <div *ngFor="let nivel of niveis" style="margin-top: 10px;" [ngStyle]="getStyleByNivelIdForGlossario(nivel.id)">
            <h5>{{ nivel.nome }}</h5>
            <div *ngFor="let dominio of dominiosPorNivel[nivel.id]">
                <div class="glossario-dominio-pdf">
                    <p class="glossario-dominio-text-pdf" *ngIf="tipoAvaliacao?.nome != 'ABLLS-R'">{{ dominio.id }} - {{ dominio.nome | sanitize }}</p>
                    <p class="glossario-dominio-text-pdf" *ngIf="tipoAvaliacao?.nome == 'ABLLS-R'">{{ dominio.sigla }} - {{ dominio.nome | sanitize }}</p>
                </div>
                <div class="glossario-habilidade-pdf">
                    <div style="width: 48%;">
                        <ng-container *ngFor="let habilidade of getAvaliacaoInfosPDF(nivel.id, dominio.id); let i = index">

                            <div *ngIf="i < (getAvaliacaoInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleForHabilidade(habilidade.sigla)">
                                <p class="glossario-habilidade-text-pdf">{{ habilidade.sigla }} - {{ habilidade.nome | sanitize }}</p>
                            </div>

                        </ng-container>
                    </div>
                    <div style="width: 48%;">
                        <ng-container *ngFor="let habilidade of getAvaliacaoInfosPDF(nivel.id, dominio.id); let i = index">

                            <div *ngIf="i >= (getAvaliacaoInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleForHabilidade(habilidade.sigla)">
                                <p class="glossario-habilidade-text-pdf">{{ habilidade.sigla }} - {{ habilidade.nome | sanitize }}</p>
                            </div>

                        </ng-container>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>