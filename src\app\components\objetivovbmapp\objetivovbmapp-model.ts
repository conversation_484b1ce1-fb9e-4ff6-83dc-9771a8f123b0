import { TipoSuporte } from './../tiposuporte/tiposuporte-model';
import { Estimulo } from './../estimulo/estimulo-model';
import { DominioVBMAPP } from './../dominiovbmapp/dominiovbmapp-model';
import { Nivel } from '../nivel/nivel-model';
import { MarcoVBMAPP } from '../marcovbmapp/marcovbmapp-model';
import { HabilidadeAvaliacao } from '../avaliacao/habilidade-avaliacao-model';


export class ObjetivoVBMAPP {
    id?: string;
    dataInclusao: Date;
    nome: string; //Programa
    descricao: string; //Meta

    sd: string;
    criterioSucesso: string;
    percOportunidadesSucesso: number;
    diasSucesso: number;
    pessoasSucesso: number;
    ambientesSucesso: number;
    oportunidadesEstimulo: number;
    estimulosFuturos: number;

    materiais: string;
    procedimento: string;
    correcaoErros: string;
    coletaDados: string;

    //descricao_plano: string;
    estimulos: Estimulo[];
    tiposSuporte: TipoSuporte[];
    tipoColeta:string;
    tentativas: number;
    qtdEstimulos: number;
    
    id_nivel?: string;
    nivel?: Nivel;
    id_dominio?: string;
    dominio?: DominioVBMAPP;
    id_marco?: string;
    marco?: MarcoVBMAPP;

    habilidades?: HabilidadeAvaliacao[];
    
    //etapa: Etapa[];
    status: string;
    ativo: boolean;

    isExpanded: boolean;
    ordem: number;

    constructor(){
        this.tiposSuporte = [];
        this.estimulos = [];
    }
}