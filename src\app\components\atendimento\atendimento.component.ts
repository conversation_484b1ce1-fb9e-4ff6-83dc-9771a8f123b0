import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, NgForm, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import RRule from 'rrule';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { RegistroDia } from 'src/app/shared/model/registro-dia';
import { LocalAtendimento } from '../local/local-atendimento-model';
import { LocalAtendimentoService } from '../local/local-atendimento.service';
import { Organizacao } from '../organizacao/organizacao-model';
import { OrganizacaoService } from '../organizacao/organizacao.service';
import { Paciente } from '../paciente/paciente-model';
import { PacienteService } from '../paciente/paciente.service';
import { Profissional } from '../profissional/profissional-model';
import { ProfissionalService } from '../profissional/profissional.service';
import { StatusAgenda } from '../statusagenda/statusagenda-model';
import { StatusAgendaService } from '../statusagenda/statusagenda.service';
import { AuthService } from '../template/auth/auth.service';
import { FirebaseUserModel } from '../template/auth/user-model';
import { UserService } from '../template/auth/user.service';
import { ConfirmDialogComponent } from '../template/confirm-dialog/confirm-dialog.component';
import { DeleteConfirmDialogComponent } from '../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { TipoProcedimento } from '../tipoprocedimento/tipoprocedimento-model';
import { TipoprocedimentoService } from '../tipoprocedimento/tipoprocedimento.service';
import { Atendimento } from './atendimento-model';
import { AtendimentoService } from './atendimento.service';
import { CalendarEventImp } from './calendarEventImp-model';
import { TimeLineAtendimentosComponent } from './time-line/time-line-atendimentos/time-line-atendimentos.component';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-atendimento',
  templateUrl: './atendimento.component.html',
  styleUrls: ['./atendimento.component.css']
})
export class AtendimentoComponent implements OnInit {
  atendimento: Atendimento = new Atendimento();
  profissional: Profissional = new Profissional();
  atendimentos: Atendimento[] = [];
  profissionais: Profissional[] = [];
  locais: LocalAtendimento[] = [];
  pacientes: Paciente[] = [];
  tiposProcedimentos: TipoProcedimento[] = [];
  statusAgendaList: StatusAgenda[] = [];
  horaInicio: string;
  horaTermino: string;
  statusSelect: string;
  procedimentoSelect: string;
  start: Date;
  end: Date;
  freq: string;
  ocorrencias: number;
  agendamentos: CalendarEventImp[] = [];
  confirmDialog: ConfirmDialogComponent;
  qtdRepeticao: number;
  edicao:boolean;
  localSelect:string;
  datasChoqueHorario:string[] = [];

  eventoAtendimento: CalendarEventImp = new CalendarEventImp();

  daily = RRule.DAILY;
  weekly = RRule.WEEKLY;
  monthly = RRule.MONTHLY;
  //rule: RRule = new RRule();
  @ViewChild(NgForm) form;

  //Campos Form

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);

  dataInicio = new FormControl('', [Validators.required]);
  dataFim = new FormControl('', [Validators.required]);
  titulo = new FormControl('', [Validators.required]);
  status = new FormControl('', [Validators.required]);
  tipoProcedimento = new FormControl('', [Validators.required]);
  statusFC = new FormControl('', [Validators.required]);
  profissionaisFC = new FormControl('', [Validators.required]);
  pacienteFC = new FormControl('', [Validators.required]);
  timeInicioFC = new FormControl('', [Validators.required]);
  timeTerminoFC = new FormControl('', [Validators.required]);
  qtdRepeticaoFC = new FormControl('', [Validators.required]);
  recorrenteFC = new FormControl('false');
  idEventoAtendimento: string;
  localFC = new FormControl('', [Validators.required]);

  filteredPacientes: Observable<Paciente[]>;
  filteredLocais: Observable<LocalAtendimento[]>;

  organizacao:Organizacao;
  diaSemana: RegistroDia;
  inicioAtendimentoClinica:Date;
  finalAtendimentoClinica:Date;

  hasAccessUpdate: boolean = false;

  constructor(
    public authService: AuthService,
    private pacienteService: PacienteService,
    private profissionalService: ProfissionalService,
    private tipoProcedimentoService: TipoprocedimentoService,
    private statusAgendaService: StatusAgendaService,
    private eventoAtendimentoService: AtendimentoService,
    private localAtendimentoService: LocalAtendimentoService,
    private userService: UserService,
    private organizacaoService: OrganizacaoService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private loadingService: LoadingService,
  ) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    this.qtdRepeticaoFC.disable();

    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Atendimento.Cadastro de atendimentos','update');

    //Pega data atual

    let dataAtual = new Date();
    dataAtual.setHours(0, 0, 0);

    //Pego o id da organização do usuário corrente
    let idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;

    //Carrega Organização
    this.organizacaoService.findById(idOrganizacao).subscribe(organizacao => {
      this.organizacao =  organizacao;
      if(this.organizacao.horarios){
        this.diaSemana = this.organizacao.horarios.find(registroDia => dataAtual.getDay()+1 == registroDia.diaSemana);     
      
        this.inicioAtendimentoClinica = new Date();
        this.finalAtendimentoClinica = new Date();
        this.inicioAtendimentoClinica.setHours(parseInt(this.diaSemana.horaInicio.split(':')[0]), parseInt(this.diaSemana.horaInicio.split(':')[1]));
        this.finalAtendimentoClinica.setHours(parseInt(this.diaSemana.horaTermino.split(':')[0]), parseInt(this.diaSemana.horaTermino.split(':')[1]));
      }
    })

    this.idEventoAtendimento = this.route.snapshot.paramMap.get('data');

    //Carrega pacientes
    await this.pacienteService.find().subscribe(pacientes => {
      this.pacientes = pacientes;
    });

    //Carrega Profissionais
    await this.profissionalService.find().subscribe(profissionais => {
      this.profissionais = profissionais;
    });

    //Carrega Procedimentos
    await this.tipoProcedimentoService.find().subscribe(procedimentos => {
      this.tiposProcedimentos = procedimentos;
    });

    //Carrega Status
    await this.statusAgendaService.find().subscribe(statusAgendaList => {
      this.statusAgendaList = statusAgendaList.filter(status => status.ativo);
      this.statusSelect = this.statusAgendaList.find(status => status.default).id;
    });

    //Carrega Locais
    await this.localAtendimentoService.find().subscribe(locais => {
      this.locais = locais.filter(local => local.isAtivo);
    });

    //Carregar Agendamentos 
    await this.eventoAtendimentoService.find().subscribe(atendimentos => {
      this.agendamentos = atendimentos.filter(atendimento => {
        return new Date(atendimento.start).getTime() >= dataAtual.getTime();
      })
    });

    this.filteredPacientes = this.pacienteFC.valueChanges
      .pipe(
        startWith(''),
        map(value => typeof value === 'string' ? value : value.nome),
        map(nome => nome ? this._filter(nome) : this.pacientes.slice())
      );
  
      //Valida edição
    if (this.idEventoAtendimento) {
      this.edicao = true;
     await this.eventoAtendimentoService.findById(this.idEventoAtendimento).subscribe(evento => {
        this.eventoAtendimento = evento;
        this.eventoAtendimento.start = new Date(evento.start);
        this.eventoAtendimento.end = new Date(evento.end);
        // console.log(evento)
        if(evento.idAtendRecorrencia){
          this.eventoAtendimento.idAtendRecorrencia = evento.idAtendRecorrencia;
        }
        this.statusSelect = evento.meta.status.id;
        this.horaInicio = new Date(this.eventoAtendimento.start).getHours() + ':' + String(new Date(this.eventoAtendimento.start).getMinutes()).padStart(2, "0");
        this.horaTermino = new Date(this.eventoAtendimento.end).getHours() + ':' + String(new Date(this.eventoAtendimento.end).getMinutes()).padStart(2, "0");

        this.procedimentoSelect = evento.meta.procedimento.id

        this.localSelect = evento.meta.idLocal;
        
        this.atendimento.profissionais = evento.meta.profissionais;
        this.loadingService.hide();
      })
    }else{
      this.edicao = false;
      await this.userService.getCurrentUser()
        .then(user => {      
          this.profissionalService.find().subscribe(profissionais => {
            this.profissional = profissionais.find(p => p.email == user.email);
            this.atendimento.profissionais.push(this.profissional);
            this.loadingService.hide();
          })
        });  
    }
  }

  private _filter(value: string): Paciente[] {
    const filterValue = value.toLowerCase();
    return this.pacientes.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
  }

  private _filterLocal(value: string): LocalAtendimento[] {
    const filterValue = value.toLowerCase();

    return this.locais.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
  }

  permiteOutrosProfissionais(){
    return !false;
  }


  displayFn(paciente: Paciente): string {
    return paciente && paciente.nome ? paciente.nome : '';
  }

  displayLc(local: LocalAtendimento): string {
    return local && local.nome ? local.nome : '';
  }

  setProcedimento(event: MatSelectChange) {
    this.atendimento.procedimento = this.tiposProcedimentos.find(p => p.id == event.value);
  }

  setLocal(event: MatSelectChange) {
    this.atendimento.local = this.locais.find(p => p.id == event.value);
  }


  addProfissional(id?: string) {
    if (id != undefined) {
      //console.log(this.profissionais.find(p => p.uid == id));
      this.profissional = this.profissionais.find(p => p.uid == id);
    }

    //Verifico se algum profissional foi selecionado
    if (this.profissional.id != undefined) {
      //Se os profissionais estiver vazio, inicializo
      if (this.atendimento.profissionais == undefined) {
        this.atendimento.profissionais = [];
      }

      //Adiciono o profissional no array de profissionais, mas antes verifico se ele já não foi incluído antes
      if (this.atendimento.profissionais.length == 0 || (this.profissional.id != undefined && this.atendimento.profissionais.filter(p => p.id == this.profissional.id).length == 0)) {
        //Adiciono o profissional no array de equipe
        this.atendimento.profissionais.push(this.profissional);
        this.atendimento.profissionais = [...this.atendimento.profissionais];
        this.profissional = new Profissional();
      } else {
        this.pacienteService.showMessage("Profissional já incluído anteriormente!", true);
        this.profissional = new Profissional();
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o profissional a ser adicionado na equipe do paciente!", true);
    }
  }

  deleteProfissional(id: number) {
    var profissional: Profissional;

    //Pego o profissional a ser excluído da equipe
    profissional = this.atendimento.profissionais[id];

    this.atendimento.profissionais.splice(id, 1);
    this.atendimento.profissionais = [...this.atendimento.profissionais];
  }

  getDataTime(data: Date, hora: String) {
    data.setHours(parseInt(hora.split(':')[0]), parseInt(hora.split(':')[1]));
    return data;
  }

  async mapearEventoAtendimento() {
    if (this.form.valid) {
      this.eventoAtendimento.start.setHours(parseInt(this.horaInicio.split(':')[0]), parseInt(this.horaInicio.split(':')[1]));
      this.eventoAtendimento.end.setHours(parseInt(this.horaTermino.split(':')[0]), parseInt(this.horaTermino.split(':')[1]));
      this.eventoAtendimento.meta.idPaciente = this.eventoAtendimento.meta.paciente.id;
 
      //Status
      let status = this.statusAgendaService.findById(this.statusSelect).toPromise();
      this.eventoAtendimento.meta.idStatus = (await status).id;
      this.eventoAtendimento.meta.status = (await status);

      //Procedimento
      let procedimento = this.tipoProcedimentoService.findById(this.procedimentoSelect).toPromise();
      this.eventoAtendimento.meta.idProcedimento = (await procedimento).id;
      this.eventoAtendimento.meta.procedimento = (await procedimento);

      //Local
      let local = this.localAtendimentoService.findById(this.localSelect).toPromise();
      this.eventoAtendimento.meta.idLocal = (await local).id;
      this.eventoAtendimento.meta.local = (await local);

      this.eventoAtendimento.meta.idLocal = this.eventoAtendimento.meta.local.id;
      this.eventoAtendimento.meta.profissionais = this.atendimento.profissionais;
      this.eventoAtendimento.draggable = true;
      this.eventoAtendimento.title = this.gerarTitulo();
    } else {
      this.eventoAtendimentoService.showMessage('Existem campos inválidos no formulário!', true);
    }
  }

  gerarTitulo() {
    let title = '';
    title = this.horaInicio + ' às ' + this.horaTermino + ' - ' +
      this.eventoAtendimento.meta.procedimento.nome

    return title;
  }

  buildAtendimentoClinica(){
    if(this.organizacao.horarios){
      this.diaSemana = this.organizacao.horarios.find(registroDia => this.eventoAtendimento.start.getDay()+1 == registroDia.diaSemana);     
    
      this.inicioAtendimentoClinica = new Date();
      this.finalAtendimentoClinica = new Date();

      this.inicioAtendimentoClinica.setDate(this.eventoAtendimento.start.getDate());
      this.inicioAtendimentoClinica.setMonth(this.eventoAtendimento.start.getMonth());
      this.inicioAtendimentoClinica.setFullYear(this.eventoAtendimento.start.getFullYear());

      this.finalAtendimentoClinica.setDate(this.eventoAtendimento.start.getDate());
      this.finalAtendimentoClinica.setMonth(this.eventoAtendimento.start.getMonth());
      this.finalAtendimentoClinica.setFullYear(this.eventoAtendimento.start.getFullYear());

      this.inicioAtendimentoClinica.setHours(parseInt(this.diaSemana.horaInicio.split(':')[0]), parseInt(this.diaSemana.horaInicio.split(':')[1]));
      this.finalAtendimentoClinica.setHours(parseInt(this.diaSemana.horaTermino.split(':')[0]), parseInt(this.diaSemana.horaTermino.split(':')[1]));
    }
  }

  async validaHorarioAtendimento(){
    await this.buildAtendimentoClinica();

    this.eventoAtendimento.start.setHours(parseInt(this.horaInicio.split(':')[0]), parseInt(this.horaInicio.split(':')[1]));
    this.eventoAtendimento.end.setHours(parseInt(this.horaTermino.split(':')[0]), parseInt(this.horaTermino.split(':')[1]));
    
    var validador: boolean = false;

    if(!this.diaSemana){
      return false;
    }

    if(this.eventoAtendimento.start.getTime() > this.finalAtendimentoClinica.getTime()){
      validador = true;
    }

    if(this.eventoAtendimento.start.getTime() < this.inicioAtendimentoClinica.getTime()){
      validador = true;
    }

    if(this.eventoAtendimento.end.getTime() > this.finalAtendimentoClinica.getTime()){
      validador = true;
    }

    // console.log(validador);
    return validador;
  }

  async save(){
    let atendimentoClinica = await this.validaHorarioAtendimento();
    if(atendimentoClinica){
      this.eventoAtendimentoService.showMessage("O atendimento esta fora do horário de atendimento da clinica", true);
    }else{
      let teste = await this.verificaChoqueHorario();
      if(teste){
        this.salvarComConfirmacao();
      }else{
        this.salvarAtendimento();
      }
    }
  }

  async salvarComConfirmacao() {
    if (!this.locais.find(local => local.id == this.localSelect).permiteChoqueHorario) {
      if (this.form.valid) {
        let dialogRef = null;
          dialogRef = this.dialog.open(ConfirmDialogComponent, {
            width: '400px',
            data: {
              valida: true,
              msg: 'Ja existem atendimentos no horário e local selecioados, tem certeza que deseja confirmar o agendamendo ?'
            }
          });
        dialogRef.afterClosed().subscribe(async result => {
          if(result){
            this.salvarAtendimento();
          }
        });
      }
    }
  }

  async salvarAtendimento(){
    var idAtendRecorrencia = null;
    await this.mapearEventoAtendimento();
      if (this.form.valid) {
        if (this.eventoAtendimento.id == undefined) {
          if(this.eventoAtendimento.meta.recorrente){
            let dataInicial = this.eventoAtendimento.start;
            let dataFinal = this.eventoAtendimento.end;
            if(this.freq == "3"){
              await this.eventoAtendimentoService.create(this.eventoAtendimento).subscribe(id => {
                idAtendRecorrencia = id;
              
                for(var i = 1; i <= this.qtdRepeticao; i++ ){    
                  var atend = new CalendarEventImp()
                  atend.start = this.eventoAtendimento.start;
                  atend.end = this.eventoAtendimento.end;
                  atend.color = this.eventoAtendimento.color;
                  atend.draggable = this.eventoAtendimento.draggable;
                  atend.resizable = this.eventoAtendimento.resizable;
                  atend.title = this.eventoAtendimento.title;
                  atend.meta = this.eventoAtendimento.meta;
                  atend.idAtendRecorrencia = idAtendRecorrencia;
                  var ms = 86400000 * i;
                  atend.start = new Date(dataInicial.getTime()+ms);
                  atend.end = new Date(dataFinal.getTime()+ms);
                  this.eventoAtendimentoService.create(atend).subscribe((id) => {

                  });
                }
              });
            }else if(this.freq == "2"){
              await this.eventoAtendimentoService.create(this.eventoAtendimento).subscribe(id => {
                idAtendRecorrencia = id;
                              
                var x = 7;
                var ms = 0;
                for(var i = 1; i <= this.qtdRepeticao; i++ ){     
                  var atend = new CalendarEventImp()
                  atend.start = this.eventoAtendimento.start;
                  atend.end = this.eventoAtendimento.end;
                  atend.color = this.eventoAtendimento.color;
                  atend.draggable = this.eventoAtendimento.draggable;
                  atend.resizable = this.eventoAtendimento.resizable;
                  atend.title = this.eventoAtendimento.title;
                  atend.meta = this.eventoAtendimento.meta;

                  ms = 86400000 * x;
                  x = x+7; 
                  atend.start = new Date(dataInicial.getTime()+ms);
                  atend.end = new Date(dataFinal.getTime()+ms);
                  atend.idAtendRecorrencia = idAtendRecorrencia;
                  this.eventoAtendimentoService.create(atend).subscribe((id) => {

                  });
                }
              });
            }else if(this.freq == "1"){
                var mes = new Date(dataInicial).getMonth()
                var ano = new Date(dataInicial).getFullYear()

                await this.eventoAtendimentoService.create(this.eventoAtendimento).subscribe(id => {
                  idAtendRecorrencia = id;                
      
                let dataIni = new Date(dataInicial);
                let dataFim = new Date(dataFinal);
                  for(var i = 1; i <= this.qtdRepeticao; i++ ){      

                    var atend = new CalendarEventImp()
                    atend.start = this.eventoAtendimento.start;
                    atend.end = this.eventoAtendimento.end;
                    atend.color = this.eventoAtendimento.color;
                    atend.draggable = this.eventoAtendimento.draggable;
                    atend.resizable = this.eventoAtendimento.resizable;
                    atend.title = this.eventoAtendimento.title;
                    atend.meta = this.eventoAtendimento.meta;
                    atend.idAtendRecorrencia = idAtendRecorrencia;
                    mes = mes + 1;
                              
                    if(mes == 12){
                      ano = ano + 1;
                    }
                    dataIni.setMonth(mes);
                    dataIni.setFullYear(ano);

                    dataFim.setMonth(mes);
                    dataFim.setFullYear(ano);

                    atend.start = dataIni;
                    atend.end = dataFim;

                    this.eventoAtendimentoService.create(atend).subscribe((id) => {

                    });
                  }
              });
            }
            this.eventoAtendimentoService.showMessage('Atendimento agendado com sucesso !');
              this.router.navigate(['/agenda/lista']);
          }else{
            this.eventoAtendimentoService.create(this.eventoAtendimento).subscribe((id) => {
              this.eventoAtendimentoService.showMessage('Atendimento agendado com sucesso !');
              this.router.navigate(['/agenda/lista']);
            });
           }
        } else {
          this.eventoAtendimentoService.update(this.eventoAtendimento).subscribe((funcao) => {
            this.eventoAtendimentoService.showMessage('Atendimento alterado com sucesso!');
            this.router.navigate(['/agenda/lista']);
          });
        }
      } else {
        this.eventoAtendimentoService.showMessage('Existem campos inválidos no formulário!', true);
      }
  }

  cancel() {
    this.router.navigate(['/agenda/lista']);
  }

  async montarData() {
    this.eventoAtendimento.start.setHours(parseInt(this.horaInicio.split(':')[0]), parseInt(this.horaInicio.split(':')[1]));
    this.eventoAtendimento.end.setHours(parseInt(this.horaTermino.split(':')[0]), parseInt(this.horaTermino.split(':')[1]));
  }


  verificaChoqueHorario() {
    let contaChoque = 0;
    if (this.eventoAtendimento.start && this.horaInicio && this.horaTermino) {
     
      this.eventoAtendimento.start.setHours(parseInt(this.horaInicio.split(':')[0]), parseInt(this.horaInicio.split(':')[1]));
      this.eventoAtendimento.end.setHours(parseInt(this.horaTermino.split(':')[0]), parseInt(this.horaTermino.split(':')[1]));

      for (var index in this.agendamentos) {

        let dataInicial = new Date(this.agendamentos[index].start);
        let dataFinal = new Date(this.agendamentos[index].end);

        if(this.eventoAtendimento.id != this.agendamentos[index].id){
          
          if ((this.eventoAtendimento.start.getTime() >= dataInicial.getTime() && this.eventoAtendimento.start.getTime() <= dataFinal.getTime() && this.localSelect == this.agendamentos[index].meta.idLocal)) {
            contaChoque = contaChoque + 1;
            this.datasChoqueHorario.push(this.agendamentos[index].id);
          }

          if ((this.eventoAtendimento.end.getTime() >= dataInicial.getTime() && this.eventoAtendimento.end.getTime() <= dataFinal.getTime() && this.localSelect == this.agendamentos[index].meta.idLocal)) {
            contaChoque = contaChoque + 1;
            this.datasChoqueHorario.push(this.agendamentos[index].id);
          }
        }
      }
    }  
    return contaChoque > 0 ? true : false;
  }


  checkRecorrente(){
    if(this.eventoAtendimento.meta.recorrente){
      this.qtdRepeticaoFC.enable();
    }else{
      this.qtdRepeticaoFC.disable();
      this.qtdRepeticao = null;
    }
  }

  async adicionaHoraFinal(){
    if(this.horaInicio && this.procedimentoSelect){
      this.loadingService.show();

      var dataFinal: Date = new Date();
      dataFinal.setDate(this.eventoAtendimento.start.getDate());
      dataFinal.setMonth(this.eventoAtendimento.start.getMonth());
      dataFinal.setFullYear(this.eventoAtendimento.start.getFullYear());
      dataFinal.setHours(parseInt(this.horaInicio.split(':')[0]), parseInt(this.horaInicio.split(':')[1]));

      let procedimento = this.tipoProcedimentoService.findById(this.procedimentoSelect).toPromise();
      this.eventoAtendimento.meta.idProcedimento = (await procedimento).id;
      this.eventoAtendimento.meta.procedimento = (await procedimento);

      dataFinal.setTime(dataFinal.getTime()+(await procedimento).minutos*60000);

      this.horaTermino = dataFinal.getHours() + ':' + String(dataFinal.getMinutes()).padStart(2, "0");

      this.eventoAtendimento.end = dataFinal;

      this.loadingService.hide();
    }
  }

  delete(eventoAtendimento: CalendarEventImp): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.eventoAtendimentoService.delete(eventoAtendimento.id).subscribe(async () => {
          //Verifica se existem atendimentos recorrentes
          var id = eventoAtendimento.idAtendRecorrencia ? eventoAtendimento.idAtendRecorrencia : eventoAtendimento.id;

          if(this.agendamentos.filter(agendamento => agendamento.idAtendRecorrencia == id && new Date(agendamento.start).getTime() > new Date().getTime()).length > 0){
            
            const dialogRef = this.dialog.open(ConfirmDialogComponent, {
              width: '250px',
              data: {
                msg: 'Esse agendamento possui recorrências futuras. Deseja excluir as recorrências ?'
              }
            });

            await dialogRef.afterClosed().subscribe(async result => {
              if(result){
                //Verifica se existem atendimentos recorrentes
                var id = eventoAtendimento.idAtendRecorrencia ? eventoAtendimento.idAtendRecorrencia : eventoAtendimento.id;

                await this.agendamentos.filter(agendamento => agendamento.idAtendRecorrencia == id && new Date(agendamento.start).getTime() > new Date().getTime()).forEach(async atend => {
                  this.eventoAtendimentoService.delete(atend.id).subscribe(() => {});
                })
                this.router.navigateByUrl('/', {skipLocationChange: true}).then(()=>
                this.router.navigate(['/agenda/lista']));
              }
            })
          }
          this.eventoAtendimentoService.showMessage('Atendimento excluido com sucesso');
          this.router.navigate(['/agenda/lista']);
        });
      }
    });
  }

  async visualizarChoqueHorario() {
    await this.verificaChoqueHorario();

    const dialogRef = this.dialog.open(TimeLineAtendimentosComponent,{
      data: {agendamentos: this.agendamentos, choques: this.datasChoqueHorario}
    });
  }
}
