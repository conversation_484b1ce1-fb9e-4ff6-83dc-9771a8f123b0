<mat-card>
    <mat-card-header>
        <!-- <div mat-card-avatar>
            <img class="logo" src="assets/img/CAPACITEAUTISMO.png">
        </div> -->
        <mat-card-title>{{ tipoAvaliacao?.nome }}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field  style="width: 15%; padding: 20px;">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" 
                        [(ngModel)]="avaliacao.idPaciente"
                        name="paciente" disabled required>
                        <!--mat-option *ngFor="let paciente of pacientes" [value]="paciente.id" >
                            {{paciente.nome}}
                        </mat-option-->
                        <mat-option [value]="avaliacao.idPaciente" >
                            {{pac?.nome}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="paciente.invalid">Paciente é obrigatório.</mat-error>  
                </mat-form-field>
                
                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="avaliacao.idProfissional"
                        name="profissional" (selectionChange) = "setProfissional($event)" required>  
                        <mat-option *ngFor="let profissional of profissionaisDoPaciente" [value]="profissional.id" >
                            {{profissional.nome}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="profissional.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>
                
                <mat-form-field style="width: 15%; padding: 20px;">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data"
                           [(ngModel)]="avaliacao.data" name="data"
                           (ngModelChange)="onDataChange($event)"
                           [matDatepicker]="picker" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="data.invalid">Data é obrigatória.</mat-error>
                </mat-form-field>
                
                <!--mat-form-field  style="width: 25%;  padding: 10px;">
                    <mat-label>assessment</mat-label>
                    <mat-select placeholder="assessment" 
                    [(ngModel)]="avaliacao.id"
                    name="assessment" (selectionChange) = "do()">
                    <mat-option *ngFor="let assessment of avaliacaos" [value]="assessment.id" >
                        {{assessment.data}}
                    </mat-option>
                </mat-select>
                </mat-form-field>
                <button  mat-mini-fab color="primary" style="margin: 10px;" (click)="new()">
                    <mat-icon>add</mat-icon>
                </button-->
                
                <mat-form-field  style="width: 10%; padding: 10px;">
                    <mat-label>Nível</mat-label>
                    <mat-select placeholder="Nivel" 
                        [(ngModel)]="nivel"
                        name="nivel" (selectionChange) = "setDominios()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Domínio</mat-label>
                    <mat-select placeholder="Dominio" 
                        [(ngModel)]="dominio"
                        name="dominio" (selectionChange) = "filterHabilidades()">
                        <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                            {{dominio.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </form>
        </div>
        <div class="mat-elevation-z4"
            *ngIf="hasAccessRead">
            
            <table mat-table [dataSource]="habilidadesView"> 
                <!-- Id Column --> 
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef class="custom-cursor">Id</th>
                    <td mat-cell *matCellDef="let row">{{row.sigla}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <ng-container matColumnDef="nome">
                    <th mat-header-cell *matHeaderCellDef class="custom-cursor">Nome</th>
                    <td mat-cell *matCellDef="let row">{{row.nome}}</td>
                </ng-container> 

                <!-- Descrição Column -->
                <ng-container matColumnDef="oDescricao">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row">
                        <div class="tooltip-container">
                            <mat-icon class="custom-cursor" mat-icon-button aria-label="Descrição">
                                <mat-icon style="opacity: 0.8;">help_outline</mat-icon>
                            </mat-icon>
                            <span class="tooltip-descricao-text">{{ respDescription(row.id) }}</span>
                        </div>
                    </td>
                </ng-container>
 
                <!-- prevavaliacao Column -->
                <ng-container matColumnDef="prevavaliacao">
                    <th mat-header-cell *matHeaderCellDef class="custom-cursor">{{ prevAvaliacao == undefined ? "" : prevAvaliacao.data | date:'dd/MM/yyyy' }}</th>
                    <td mat-cell *matCellDef="let row">
                        <!-- {{ findIndexHabilidade(row.id) }} -->
                        {{ getRespostaHabilidade(prevAvaliacao?.respostasHabilidades[findIndexHabilidade(row.id)]?.idHabilidadeAvaliacao) == undefined ?
                            "" : getDominioResposta(getRespostaHabilidade(prevAvaliacao?.respostasHabilidades[findIndexHabilidade(row.id)]?.idHabilidadeAvaliacao))?.sigla }}
                    </td>
                </ng-container> 

                <!-- Resposta Column -->
                <ng-container *ngFor="let domResp of domoniosResposta">
                    <ng-container matColumnDef="resp-{{ domResp.sigla }}">
                        <th mat-header-cell *matHeaderCellDef  class="mat-column-resp custom-cursor">{{ domResp.sigla }}</th>
                        <td mat-cell *matCellDef="let row">
                            <mat-radio-group
                            [(ngModel)]="avaliacao.respostasHabilidades[findIndexHabilidade(row.id)].valor"
                            name="valor{{findIndexHabilidade(row.id)}}">
                            
                                <ng-container *ngIf="isValorWithinMax(row.id, domResp.valor, domResp.sigla)"> 
                                    <mat-radio-button *ngIf="domResp.sigla == 'NA'" class="resp-true"
                                        value="{{habilidadeHasNA(row.id)}}">
                                    </mat-radio-button>
                                    <mat-radio-button *ngIf="domResp.sigla != 'NA'" class="resp-true"
                                        value="{{domResp.valor}}">
                                    </mat-radio-button>
                                </ng-container>

                                <ng-container *ngIf="!isValorWithinMax(row.id, domResp.valor, domResp.sigla)">
                                    <div class="tooltip-container">
                                        <mat-radio-button class="resp-false"
                                            value="{{domResp.valor}}">
                                        </mat-radio-button>
                                        <span class="tooltip-text">Valor não aplicável a essa habilidade!</span>
                                    </div>
                                </ng-container>
                              
                            </mat-radio-group>
                        </td>
                    </ng-container>
                </ng-container>          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>  
        </div>     
        <div *ngIf="tipoAvaliacao?.nome != 'ABLLS-R' && tipoAvaliacao?.nome != 'AFLS - The Assessment of Functional Living Skills'" style="width: 50%; display: flex; flex-direction: row; flex-wrap: wrap; margin: 0%; margin-top: 15px;">
            <ng-container *ngFor="let domResp of domoniosResposta">
                <div style="width: 50%;">
                    {{ domResp.sigla }} - {{ domResp.nome }}
                </div>
            </ng-container>
        </div>
    </mat-card-content>
    <mat-card-actions style="padding-bottom: 5px;">
        <button mat-raised-button color="primary" (click)="save(false)"
            *ngIf="hasAccessUpdate"
            id="btnSave"
            [disabled]="loading">
            Salvar
        </button>

        <button mat-raised-button color="primary" (click)="save(true)"
            *ngIf="hasAccessUpdate"
            id="btnSave"
            [disabled]="loading">
            Salvar & Sair
        </button>

        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessRead">
            Cancelar
        </button>

        <button mat-raised-button color="primary" (click)="nextDominio()" style="float: right;" [(disabled)]="disabledButtomNext"
            *ngIf="hasAccessUpdate">
            {{ textoBotao }}
        </button>

        <mat-form-field class="custom-select-primary all-label" style="float: right;" *ngIf="hasAccessUpdate && (tipoAvaliacao?.nome != 'ABLLS-R' && tipoAvaliacao?.nome != 'Socially Savvy' && tipoAvaliacao?.nome != 'AFLS - The Assessment of Functional Living Skills')">
            <mat-label>Marcar todos como...</mat-label>
            <mat-select (selectionChange)="checkAllAcquired($event.value)" [(ngModel)]="allButton">
                <mat-option value="0.5">Parcialmente adquirido (AV)</mat-option>
                <mat-option value="0">Não adquirido (N)</mat-option>
                <mat-option value="1">Adquirido (S)</mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field class="custom-select-primary all-label" style="float: right;" *ngIf="hasAccessUpdate && (tipoAvaliacao?.nome == 'Socially Savvy')">
            <mat-label>Marcar todos como...</mat-label>
            <mat-select (selectionChange)="checkAllAcquired($event.value)" [(ngModel)]="allButton">
                <mat-option value="0">Raramente ou nunca demonstra (0)</mat-option>
                <mat-option value="1">Pode demonstrar (1)</mat-option>
                <mat-option value="2">Demonstra mas não constantemente (2)</mat-option>
                <mat-option value="3">Demonstra consistentemente (3)</mat-option>
            </mat-select>
        </mat-form-field>
          
        <mat-form-field class="custom-select-primary all-label" style="float: right;" *ngIf="hasAccessUpdate && (tipoAvaliacao?.nome == 'ABLLS-R' || tipoAvaliacao?.nome == 'AFLS - The Assessment of Functional Living Skills')">
            <mat-label>Marcar todos como...</mat-label>
            <mat-select (selectionChange)="checkAllAcquiredFlex($event.value)" [(ngModel)]="allButton">
                <mat-option value="0">Marcar todos como não adquirido</mat-option>
                <mat-option value="1">Marcar todos como adquirido</mat-option>
            </mat-select>
        </mat-form-field>
        
      </mat-card-actions>
</mat-card>