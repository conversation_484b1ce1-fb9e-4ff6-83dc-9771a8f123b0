import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import { ESDMChecklist } from './esdmchecklist-model';

@Injectable({
  providedIn: 'root'
})
export class EsdmchecklistService {

  esdmchecklistUrl = `${environment.API_URL}/esdmchecklist`;
  
  public esdmchecklists: BehaviorSubject<ESDMChecklist[]> = 
    new BehaviorSubject<ESDMChecklist[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(esdmchecklist: ESDMChecklist): Observable<string>{
    // console.log(esdmchecklist);
    return this.http.post<ESDMChecklist>(this.esdmchecklistUrl, esdmchecklist).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(esdmchecklist: ESDMChecklist): Observable<ESDMChecklist>{
    // console.log(esdmchecklist);
    return this.http.put<ESDMChecklist>(this.esdmchecklistUrl + "/" + esdmchecklist.id, esdmchecklist).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<ESDMChecklist>{
    return this.http.get<ESDMChecklist>(this.esdmchecklistUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByPaciente(idPaciente: string): Observable<ESDMChecklist[]>{
    return this.http.get<ESDMChecklist[]>(this.esdmchecklistUrl + "/paciente/" + idPaciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  } 

  findResumoByPaciente(idPaciente: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.esdmchecklistUrl}/paciente/${idPaciente}/resumo`).pipe(
      map(obj => obj),
      catchError(() => EMPTY)
    );
  }

  findLastByPacienteData(idPaciente: string, data: string): Observable<ESDMChecklist[]>{
    return this.http.get<ESDMChecklist[]>(this.esdmchecklistUrl + "/paciente/" + idPaciente + "/data/" +data).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<ESDMChecklist[]>{
    return this.http.get<ESDMChecklist[]>(this.esdmchecklistUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<ESDMChecklist>{
    return this.http.delete<ESDMChecklist>(this.esdmchecklistUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
