import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { CompetenciaService } from './../../competencia/competencia.service';
import { AuthService } from './../../template/auth/auth.service';
import { NivelService } from './../../nivel/nivel.service';
import { ObjetivoService } from './../objetivo.service';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { Objetivo } from './../objetivo-model';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Component, OnInit } from '@angular/core';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { ca } from 'date-fns/locale';

@Component({
  selector: 'app-objetivo-list',
  templateUrl: './objetivo-list.component.html',
  styleUrls: ['./objetivo-list.component.css']
})
export class ObjetivoListComponent implements OnInit {

  public objetivos: Objetivo[];
  public objetivosView: Objetivo[] = [];

  public niveis: Nivel[];
  public dominios: Dominio[];
  //public dominiosTab: Dominio[] = [];

  //public objetivo: Objetivo = new Objetivo();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>(); 

  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;


  displayedColumns = ['id', 'nome', 'action']

 
  constructor(private objetivoService: ObjetivoService,
    private competenciaService: CompetenciaService,
    private nivelService: NivelService,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.reset();
    this.loadingService.show();
  
    this.hasAccessRead = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','read');
    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','update');

    try {
      // Carregando Objetivos
      const objs = await this.objetivoService.find().toPromise();
      this.objetivos = objs;
      // Atribuindo a primeira visão de competências (N1-CRE)
      this.objetivosView = this.objetivos.filter(obj => 
        (obj.id_nivel == 'N1' && obj.id_dominio == 'CRE')
      );
  
      // Carregando Níveis
      const niveis = await this.nivelService.find().toPromise();
      this.niveis = niveis;
      this.nivel = this.niveis.find(n => n.id == 'N1');
  
      // Carregando Domínios dos Níveis (TAB)
      const dominioPromises = niveis.map(nivel =>
        this.competenciaService.findDominiosByNivel(nivel.id).toPromise()
      );
  
      // Esperando que todas as promessas de domínios sejam resolvidas
      const dominiosResults = await Promise.all(dominioPromises);
  
      // Atualizando o mapa de domínios
      niveis.forEach((nivel, index) => {
        this.dominioMap.set(nivel.id, dominiosResults[index]);
      });
  
      // Carregando Domínios para Combo (Default: N1-CRE)
      this.dominios = this.dominioMap.get('N1');
      if (this.dominios) {
        this.dominio = this.dominios.find(dom => dom.id == 'CRE');
      }
    } catch (error) {
      console.error("Erro: ", error);
    } finally {
      this.loadingService.hide();
    }
  }
    

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];//this.dominios.find(dom => dom.id == 'CRE');  
    this.filterObjetivo();
  }

  filterObjetivo(){
    this.objetivosView = this.objetivos.filter(obj => 
    (obj.id_nivel==this.nivel.id 
      && obj.id_dominio == this.dominio.id))
  }

  
  delete(id: string): void{
    let objetivo: Objetivo;
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        //Pego o objetivo e coloco ele como inativo
        objetivo = this.objetivos.find(obj => obj.id == id);
        objetivo.ativo = false;
        this.objetivoService.update(objetivo).subscribe(
          () => {
            this.objetivoService.showMessage('Objetivo excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/objetivo']);
          }
        );
      }
    });
  }

  do() {

  }

  edit(id: string){
    //Colocar na sessão a lista de domínios por nível
    localStorage.removeItem("objetivos")
    localStorage.setItem("objetivos", JSON.stringify( this.objetivos ))
    this.router.navigate(['/objetivo/update/' + id])
  }

  new(){
    //console.log(this.esdmchecklist.paciente.id);
    this.router.navigate(['/objetivo/create'])
  }

  setChecklist(){
    //Carrega novo chechklist no default N1-CRE
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'CRE');
    this.filterObjetivo()
  }

}
