{"hosting": [{"target": "frontend", "public": "dist/frontend", "rewrites": [{"source": "**", "destination": "/index.html"}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, {"target": "frontend", "public": "dist/frontend", "ignore": ["**/.*"], "headers": [{"source": "*.[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f].+(css|js)", "headers": [{"key": "Cache-Control", "value": "public,max-age=31536000,immutable"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}]}]}