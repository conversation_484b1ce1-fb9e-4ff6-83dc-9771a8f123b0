
<div class="mat-elevation-z4">
    <table mat-table [dataSource]="pacientes">
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>

            <td mat-cell *matCellDef="let row"  fxFlex="70" (click)="navigateToRead(row.id)">
                <a (click)="navigateToRead(row.id)" class="edit tdlinked" style="cursor: pointer; height:100%;width:100%">
                    {{row.nome}}
                </a>
            </td>
        </ng-container>

        <!-- kpis Column -->
        <ng-container matColumnDef="kpis" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Indicadores</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">
                <a (click)="navigateToRead(row.id)" class="edit tdlinked" style="cursor: pointer; height:100%;width:100%">
                    <div class="kpis">
                        <div fxFlex="15"></div>
                        <div [ngClass]="[row.kpiChkLst == 'green' ? 'label label-ok' : 'label', 
                                        row.kpiChkLst == 'orange' ? 'label label-aviso' : 'label', 
                                        row.kpiChkLst == 'red' ? 'label label-atrasado' : 'label']" style="width: 20%;">
                            <small>Avaliação</small>
                        </div>
                        <div [ngClass]="[row.kpiPlInterv == 'green' ? 'label label-ok' : 'label', 
                                        row.kpiPlInterv == 'orange' ? 'label label-aviso' : 'label', 
                                        row.kpiPlInterv == 'red' ? 'label label-atrasado' : 'label']" style="width: 20%;">
                            <small>Plano</small>
                        </div>
                        <div [ngClass]="[row.kpiColeta == 'green' ? 'label label-ok' : 'label', 
                                        row.kpiColeta == 'orange' ? 'label label-aviso' : 'label', 
                                        row.kpiColeta == 'red' ? 'label label-atrasado' : 'label']" style="width: 20%;">
                            <small>Coleta</small>
                        </div>
                    </div>
                </a>
            </td>
        </ng-container>

        <!-- dataNascimento Column -->
        <ng-container matColumnDef="dataNascimento" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Data de Nascimento</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">
                <a (click)="navigateToRead(row.id)" class="edit tdlinked" style="cursor: pointer; height:100%;width:100%">
                    {{row.dataNascimento | date: 'dd/MM/yyyy'}}
                </a>
            </td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="addColeta(row.id)" class="createColeta" matTooltip="Coletar Dados" 
                *ngIf="authService.verifySimpleAccess(getFuncoesUsuario(row), 'Paciente.Coleta de Dados', 'create')">
                <i class="material-icons">
                    iso
                </i>
            </a>
            <a (click)="navigateToRead(row.id)" class="edit" matTooltip="Visualizar Paciente">
                <i class="material-icons">
                    remove_red_eye
                </i>
            </a>
            <a (click)="delete(row.id)" class="delete" matTooltip="Inativar Paciente"
                *ngIf="authService.verifySimpleAccess(getFuncoesUsuario(row), 'Paciente.Cadastro de pacientes', 'delete')">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  