import { LoadingService } from './../../../shared/service/loading.service';
import { Permissao } from './../permissao-model';
import { Funcionalidade } from './../funcionalidade-model';
import { MatSelectChange } from '@angular/material/select';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { FuncaoService } from './../funcao.service';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { Funcao } from './../funcao-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { AreaFuncionalidades } from './../area-funcionalidades-model';
import { Component, OnInit, ViewChild } from '@angular/core';
import data from '../../../../assets/functions.json'

@Component({
  selector: 'app-funcao-create',
  templateUrl: './funcao-create.component.html',
  styleUrls: ['./funcao-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class FuncaoCreateComponent implements OnInit {

  permissaoAreas: AreaFuncionalidades[];
  funcao: Funcao = new Funcao();

  displayedColumns = ['nome', 'read', 'create', 'update', 'delete']
  displayedColumns2 = ['nome']
  //datasource: MatTableDataSource<string>;
  datasource: string[];

  @ViewChild(NgForm) form;

  //Form Controls
  nome = new FormControl('', [Validators.required]);
  inicialName: string;
  funcoes: Funcao[];

  constructor(private funcaoService: FuncaoService,
    private loadingService: LoadingService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let idFuncao = this.route.snapshot.paramMap.get('id');
    this.loadingService.show(); 

    try {
      this.permissaoAreas = (data as AreaFuncionalidades[]);
      this.funcoes = await this.funcaoService.find().toPromise();
      this.funcoes = this.funcoes.filter(funcao => funcao.ativo);
  
      if(idFuncao == undefined) { //Create
        this.funcao = new Funcao();
        this.funcao.ativo = true;
  
        //Setando as permissões
        this.funcao.permission = new Map< string, Map<string, string> >();
  
        for(let area of this.permissaoAreas){
          area.funcionalidades.forEach(funcionalidade => {
            this.funcao.permission[area.nome + "." + funcionalidade.nome] = {};
            this.funcao.permission[area.nome + "." + funcionalidade.nome]["read"] = "X";
            this.funcao.permission[area.nome + "." + funcionalidade.nome]["create"] = "X";
            this.funcao.permission[area.nome + "." + funcionalidade.nome]["update"] = "X";
            this.funcao.permission[area.nome + "." + funcionalidade.nome]["delete"] = "X";                  
          })          
        }
      } else {  //Edit
        const funcao = await this.funcaoService.findById(idFuncao).toPromise();
        this.funcao = funcao;
        this.inicialName = this.funcao.nome;
        if(this.funcao.permission == undefined){
          //Setando as permissões
          this.funcao.permission = new Map< string, Map<string, string> >();

          for(let area of this.permissaoAreas){
            area.funcionalidades.forEach(funcionalidade => {
              this.funcao.permission[area.nome + "." + funcionalidade.nome] = {};
              this.funcao.permission[area.nome + "." + funcionalidade.nome]["read"] = "X";
              this.funcao.permission[area.nome + "." + funcionalidade.nome]["create"] = "X";
              this.funcao.permission[area.nome + "." + funcionalidade.nome]["update"] = "X";
              this.funcao.permission[area.nome + "." + funcionalidade.nome]["delete"] = "X";                  
            })
          }
        } else {
          //Verifico se alguma funcionalidade foi adicionada. Se foi, ela não estará deinida no array funcao.permission
          for(let area of this.permissaoAreas){
            area.funcionalidades.forEach(funcionalidade => {
              if (this.funcao.permission[area.nome + "." + funcionalidade.nome] == undefined) {
                //console.log(area.nome + "." + funcionalidade.nome);
                this.funcao.permission[area.nome + "." + funcionalidade.nome] = {};
                this.funcao.permission[area.nome + "." + funcionalidade.nome]["read"] = "X";
                this.funcao.permission[area.nome + "." + funcionalidade.nome]["create"] = "X";
                this.funcao.permission[area.nome + "." + funcionalidade.nome]["update"] = "X";
                this.funcao.permission[area.nome + "." + funcionalidade.nome]["delete"] = "X";                  
              }
            })
          }
        } 
      }
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  setPermission(areaFuncionalidade: string, permission: string, event: MatSelectChange){
    this.funcao.permission.get(areaFuncionalidade).set(permission, event.value)
    //this.funcao.funcionalidades.find(element => element.nome == areaFuncionalidade).permission.set(permission, event.value)
  }

  getPermission(areaFuncionalidade: string, permission: string, event: MatSelectChange){
    if(this.funcao.permission == undefined) {
      return;
    }
    //console.log(areaFuncionalidade)
    //console.log(this.funcao.permission)
    //return this.funcao.funcionalidades.find(element => element.nome == areaFuncionalidade).permission.get(permission)
    return this.funcao.permission.get(areaFuncionalidade).get(permission)
  }


  save(){
    //console.log("Saving...")
    //console.log(this.funcao)
    if(this.form.valid){
      if(this.funcao.id == undefined){
        if (this.isDuplicateName(this.funcao, this.funcoes)) {
          this.funcaoService.showMessage('Já existe uma função com esse nome!',true);
          return;
        }
        this.funcaoService.create(this.funcao).subscribe((id) => {
          this.funcaoService.showMessage('Função criada com sucesso!');
          this.router.navigate(['/funcao']);
        });
      } else {
        if (this.funcao.nome != this.inicialName && this.isDuplicateName(this.funcao, this.funcoes)) {
          this.funcaoService.showMessage('Já existe uma função com esse nome!',true);
          return;
        }
        this.funcaoService.update(this.funcao).subscribe((funcao) => {
          this.funcaoService.showMessage('Função alterada com sucesso!');
          this.router.navigate(['/funcao']);
        });
      }
    } else {
      this.funcaoService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/funcao']);
  }

  isDuplicateName(funcao: any, funcoes: any[]): boolean {
    return funcoes.some(p => 
      p.id !== funcao.id &&
      this.normalizeString(p.nome) === this.normalizeString(funcao.nome)
    );
  }

  // Função para normalizar uma string
  normalizeString(str: string): string {
    return str.trim().toLowerCase();
  }

}
