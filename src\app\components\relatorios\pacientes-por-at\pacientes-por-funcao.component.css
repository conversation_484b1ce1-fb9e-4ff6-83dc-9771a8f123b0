.mat-column-paciente {
    flex: 0 0 40% !important;
    width: 40% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-profissional {
    flex: 0 0 30% !important;
    width: 30% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-funcao {
    /* flex: 0 0 30% !important; */
    /* width: 30% !important; */
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-email {
    /* flex: 0 0 30% !important; */
    /* width: 30% !important; */
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-fone {
    /* flex: 0 0 30% !important; */
    /* width: 30% !important; */
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}