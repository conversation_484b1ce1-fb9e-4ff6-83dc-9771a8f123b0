import { Paciente } from 'src/app/components/paciente/paciente-model';
import { ParentescoService } from './../parentesco.service';
import { Parentesco } from './../parentesco-model';
import { ActivatedRoute, Router } from '@angular/router';
import { ParenteService } from './../parente.service';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { Parente } from './../parente-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Profissional } from '../../profissional/profissional-model';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-parente-create',
  templateUrl: './parente-create.component.html',
  styleUrls: ['./parente-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ParenteCreateComponent implements OnInit {

  parente: Parente = new Parente();
  parentes: Parente[] = [];
  profissionais: Profissional[] = [];
  loginUser: boolean;

  cpfMask = [/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'-',/\d/,/\d/];
  telefoneMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  celularMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  tipoTelefone = 'C';

  parentescos: Parentesco[] = [];
  parentesco: Parentesco = new Parentesco();

  @ViewChild(NgForm) form;

  //Form Controls
  nome = new FormControl('', [Validators.required]);
  dataNascimento = new FormControl('', [Validators.required]);
  parentescoFC = new FormControl('', [Validators.required]);
  sexo = new FormControl('', [Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  inicialName: string;

  constructor(private parenteService: ParenteService,
    private parentescoService: ParentescoService,
    private profissionalService: ProfissionalService,
    private route: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      let idParente = this.route.snapshot.paramMap.get('id');

      if(idParente == undefined) { //Create
        this.parente = new Parente();
        this.parente.ativo = true;
      } else {  //Edit
        const parente = await this.parenteService.findById(idParente).toPromise();
        this.parente = parente;
        this.inicialName = parente.nome;
      }

      const p = await this.parenteService.find().toPromise();
      this.parentes = p;

      const parentescos = await this.parentescoService.find().toPromise();
      this.parentescos = parentescos;

      //Recuperando a lista de profissionais
      const profs = await this.profissionalService.find().toPromise()
      this.profissionais = profs;
      
    } catch(error) {
      //console.log("Erro: ", error)
      this.loadingService.hide();
    } finally {
      this.loadingService.hide();
    }
  }

  async save() {
    this.loadingService.show();
    try {
      if (this.form.valid) {
        // Verificação de e-mail duplicado entre parentes
        const emailDuplicadoParente = this.isDuplicateEmail(this.parente, this.parentes);
        if (emailDuplicadoParente) {
          this.parenteService.showMessage('Já existe um parente cadastrado com esse e-mail!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação de e-mail duplicado entre profissionais
        const emailDuplicadoProf = this.isDuplicateEmailProf(this.parente, this.profissionais);
        if (emailDuplicadoProf) {
          this.parenteService.showMessage('Já existe um profissional cadastrado com esse e-mail!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação global de e-mail (em todo o sistema)
        const emailDuplicadoGlobal = await this.isDuplicateEmailGlobal(this.parente.email);
        if (emailDuplicadoGlobal) {
          this.parenteService.showMessage('Já existe um usuário cadastrado com esse e-mail no sistema!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação de nome duplicado local
        if (this.isDuplicateName(this.parente, this.parentes)) {
          this.haveParenteName(this.parente.id == undefined);
          this.loadingService.hide();
          return;
        }

        // Criação ou edição
        if (this.parente.id == undefined) {
          this.parenteService.create(this.parente).subscribe(() => {
            this.parenteService.showMessage('Parente criado com sucesso!');
            this.router.navigate(['/parente']);
            this.loadingService.hide();
          });
        } else {
          this.parenteService.update(this.parente).subscribe(() => {
            this.parenteService.showMessage('Parente alterado com sucesso!');
            this.router.navigate(['/parente']);
            this.loadingService.hide();
          });
        }
      } else {
        this.parenteService.showMessage('Existem campos inválidos no formulário!', true);
        this.loadingService.hide();
      }
    } catch (error) {
      console.log("Erro: ", error);
      this.loadingService.hide();
    }
  }

  haveParenteName(valor: boolean){
    let dialogRef;
    dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        valida: true,
        msg: 'Já existe um parente cadastrado com o nome "<strong>' + this.parente.nome + '</strong>", tem certeza que deseja criar outro?'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.parenteService.create(this.parente).subscribe((id) => {
          this.parenteService.showMessage('Parente ' + (valor? 'criado' : 'alterado') + ' com sucesso!');
          this.router.navigate(['/parente']);
        });
      }
    })
  }

  // Função para normalizar uma string
  normalizeString(str: string): string {
    return str.trim().toLowerCase();
  }

  // Verifica se já existe um parente com o mesmo nome (status diferente de false)
  isDuplicateName(parente: any, parentes: any[]): boolean {
    return parentes
      .filter(p => p.status !== false)
      .some(p => 
        p.id !== parente.id &&
        this.normalizeString(p.nome) === this.normalizeString(parente.nome)
      );
  }

  // Verifica se já existe um parente com o mesmo e-mail (status diferente de false)
  isDuplicateEmail(parente: any, parentes: any[]): boolean {
    return parentes
      .filter(p => p.status !== false)
      .some(p => 
        p.id !== parente.id &&
        this.normalizeString(p.email) === this.normalizeString(parente.email)
      );
  }

  // Verifica se já existe um profissional com o mesmo e-mail (status diferente de false)
  isDuplicateEmailProf(parente: any, profissionais: any[]): boolean {
    return profissionais
      .filter(p => p.status !== false)
      .some(p => 
        p.id !== parente.id &&
        this.normalizeString(p.email) === this.normalizeString(parente.email)
      );
  }

  async isDuplicateEmailGlobal(email: string): Promise<boolean> {
    const result = await this.profissionalService.verificarEmailGlobal(email).toPromise();
    return result && result.length > 0;
  }

  validaMaskTelefone(){
    
    if(this.parente.telefone.replace('_','').length < 15){
      this.tipoTelefone = 'T';
    }else{
      this.tipoTelefone = 'C';
    }
  }

  cancel() {
    this.router.navigate(['/parente']);
  }

}
