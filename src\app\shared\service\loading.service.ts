import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<boolean>(false);
  loading$: Observable<boolean> = this.loadingSubject.asObservable();

  private loadingCount = 0;

  show(): void {
    this.loadingCount++;
    if (this.loadingCount === 1) {  // Mostra o loading apenas quando a contagem é 1
      this.disableScroll();
      this.loadingSubject.next(true);
    }
  }

  hide(): void {
    if (this.loadingCount > 0) {
      this.loadingCount--;
      if (this.loadingCount === 0) {  // Oculta o loading quando a contagem chega a 0
        this.enableScroll();
        this.loadingSubject.next(false);
      }
    }
  }

  reset(): void {
    this.loadingCount = 0;
    this.enableScroll();
    this.loadingSubject.next(false);  // Garante que o loading seja oculto
  }

  private disableScroll() {
    document.body.style.overflowX = "hidden";
    document.body.style.overflowY = "hidden";
  }

  private enableScroll() {
    document.body.style.marginRight = "";
    document.body.style.overflowX = "auto";
    document.body.style.overflowY = "auto";
  }
}
