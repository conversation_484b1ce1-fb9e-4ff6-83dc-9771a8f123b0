.text-inside-grid {
    position: absolute;
    left: 5px;
}

.objetivo {
    padding: 10px; 
    margin: 10px; 
    background-color: whitesmoke;
}

.objetivoAdquirido {
    padding: 10px; 
    margin: 10px; 
    background-color: aquamarine;
}

.mat-elevation-z8 {    
    overflow-x: auto;
    width: 100%;
   }

.mat-grid-tile .mat-figure {
    justify-content: flex-start !important ;
    align-items: flex-start !important;
 }
  
p.title {
    font-family: Helvetica;
    font-size: 1.4em;
    font-weight: bold;
}

p.subtitle {
    font-family: Helvetica;
    font-size: 0.8em;
    font-weight: normal;
}

p.dominio {
    font-family: Helvetica;
    font-size: 1.2em;
    font-weight: bold;
    line-height: normal;
}

p.objetivo {
    font-family: Helvetica;
    font-size: 1em;
    font-weight: bold;
    line-height: normal;
}

p.desc_objetivo {
    font-family: Helvetica;
    font-size: 1em;
    font-weight: normal;
    line-height: normal;
}

p.objetivoAdquirido {
    font-family: Helvetica;
    font-size: 1em;
    font-weight: normal;
    line-height: normal;
    background-color: aquamarine;
    font-weight: bold;
  }

p.etapa_atual {
    font-family: Helvetica;
    font-size: 0.7em;
    font-weight: normal;
    line-height: normal;
    page-break-inside: avoid;
    width: max-content;
    background-color: palegoldenrod;
}

p.etapa {
    font-family: Helvetica;
    font-size: 0.7em;
    font-weight: normal;
    line-height: normal;
    page-break-inside: avoid;
}

div.subtitulo{
    display: flex; 
    flex-direction: row;
    width: 90%; 
    padding: 20px 0px 20px 0px;
}


html, body, .main, .tabs, .tabbed-content, .div { float: none; }

.row{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
}

div.blueTable {
    border: 1px solid #1C6EA4;
    background-color: #EEEEEE;
    width: 100%;
    text-align: left;
    border-collapse: collapse;
}
.divTable.blueTable .divTableCell, .divTable.blueTable .divTableHead {
    border: 1px solid #AAAAAA;
    padding: 3px 2px;
}
.divTable.blueTable .divTableBody .divTableCell {
    font-size: 13px;
}
.divTable.blueTable .divTableRow:nth-child(even) {
    background: #D0E4F5;
}
.divTable.blueTable .divTableHeadingColSpan {
    background: #165782;
  background: -moz-linear-gradient(top, #5081a1 0%, #2d678e 66%, #165782 100%);
  background: -webkit-linear-gradient(top, #5081a1 0%, #2d678e 66%, #165782 100%);
  background: linear-gradient(to bottom, #5081a1 0%, #2d678e 66%, #165782 100%);
  border-bottom: 2px solid #444444;
}

.divTable.blueTable .divTableHeading {
    /*background: #1C6EA4;*/
    background: gray;
    background: -moz-linear-gradient(top, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
    background: -webkit-linear-gradient(top, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
    background: linear-gradient(to bottom, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
    /* border-bottom: 2px solid #444444; */
}
.divTable.blueTable .divTableHeadingColSpan .divTableHead {
    font-size: 15px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
    border-left: 2px solid #D0E4F5;
}
.divTableCell.rowspanned {
    position: absolute;
    background-color: #FFFFFF;
    margin: auto;
    vertical-align: middle;
    text-align: center !important;
    align-content: center !important;
    top: 27px;
    bottom: 0;  
    left: 0;
    width: 400px !important;
}

.divTableCell.empty
{
  border: none;
  width: 400px !important;
  text-align: center !important;
  align-content: center !important;
}

.tablewrapper {
    position: relative;
  }

.divTable.blueTable .divTableHeading .divTableHead {
    font-size: 14px;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
    border-left: 2px solid #D0E4F5;
}
.divTable.blueTable .divTableHeading .divTableHead:first-child,  .divTable.blueTable .divTableHeadingColSpan .divTableHead:first-child {
    border-left: none;
}
  
.center {
    text-align: center;
}
/* DivTable.com */
.divTable{ display: table; width: 100%; break-inside:auto}
.divTableRow { display: table-row; break-inside:avoid; break-after:auto}
/* .divTableRow:nth-child(even) {
background: lightgray;
} */
.divTableHeading, .divTableHeadingColSpan { display: table-header-group;}
.divTableCell, .divTableHead { display: table-cell;}
.divTableFoot { display: table-footer-group;}
.divTableBody { display: table-row-group;}

@media print {
    .break-before {
        display: block;
        page-break-before: always;
        position: relative;
    }
    
    .pagebreak {
        clear: both;
        page-break-after: always;
    }
}