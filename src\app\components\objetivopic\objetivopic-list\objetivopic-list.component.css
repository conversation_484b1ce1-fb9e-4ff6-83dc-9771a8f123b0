.close {
    font-size: 1.4rem;
    opacity: 0.1;
    transition: opacity 0.3s;
}
.nav-link:hover > .close {
    opacity: 0.8;
}

table{
  width: 100%;
}
.mat-column-dominio {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: left;
}

.mat-column-marco {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: left;
}

.mat-column-tiposuporte {
  flex: 0 0 7% !important;
  width: 7% !important;
  text-align: left;
}

.mat-column-nome {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-habilidades {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-estimulos {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-action {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: center;
}

.tdlinked{
  display: block;
  text-decoration: none;
  cursor: pointer;
}