<mat-expansion-panel style="width: 100%; margin-bottom: 5px;">
    <mat-expansion-panel-header>
        <mat-panel-title> Filtros </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="group">
        <div style="flex-direction: row; width: 100%; justify-content: space-between;">
            
            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Meta" 
                    [(ngModel)] = "meta" name="meta"
                    (ngModelChange)="setFilter()">
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Comportamento Alvo" 
                    [(ngModel)] = "compAlvo" name="compAlvo"
                    (ngModelChange)="setFilter()">
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Definição Operacional" 
                    [(ngModel)] = "defOp" name="defOp"
                    (ngModelChange)="setFilter()">
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <mat-select 
                placeholder="Tipo de Coleta" 
                [(ngModel)] = "tipoColeta"
                name="tipoColeta" 
                (selectionChange) = "setTipoColeta($event)"
                (ngModelChange)="setFilter()">
                    <mat-option *ngFor="let tipoColeta of tiposColeta" [value]="tipoColeta">
                        {{tipoColeta}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
              
        </div>
    </div>

    <div class="v-middle" style="font-size: small; cursor: pointer; width: 10%; text-align: right;">
        <a (click)="cleanFilter()">Limpar filtros</a>
    </div>
</mat-expansion-panel>
