<mat-card>
    <mat-card-header>
        <mat-card-title>Tipo de Procedimento: {{tipoprocedimento.nome}}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field style="width: 60%;">
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="tipoprocedimento.nome" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <input class="input" matInput placeholder="Tempo (em minutos)" 
                        [(ngModel)]="tipoprocedimento.minutos" name="minutos" required
                        type="number">
                    <mat-error *ngIf="minutosFC.invalid">Tempo é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 15%; padding-left: 10px;">
                    <!--input class="numeric" matInput placeholder="Valor" 
                        [(ngModel)]="tipoprocedimento.valor" name="valor" required
                        [textMask]="{mask: numberMask}" 
                        type="text"-->
                    <input class="numeric" matInput placeholder="Valor" 
                        [(ngModel)]="tipoprocedimento.valor" name="valor" required
                        type="number"  step="any"
                        onblur="this.value = parseFloat(this.value).toFixed(2)"
                        onreadystatechange="this.value = parseFloat(this.value).toFixed(2)"
                        value="0,00">
                    <mat-error *ngIf="valorFC.invalid">Valor é obrigatório.</mat-error>  
                </mat-form-field>
                <div style="display: inline; width: 100%;">
                    <mat-form-field  style="width: 20%;">
                        <mat-label>Permite alteração de valor:</mat-label>
                        <mat-select [(ngModel)]= "tipoprocedimento.permiteAlterarValor" name="permiteAlterarValor">
                            <mat-option [value]="true">Sim</mat-option>                        
                            <mat-option [value]="false">Não</mat-option>                        
                        </mat-select>
                    </mat-form-field>
                    <mat-icon style="font-size: 20px; cursor: default;" matTooltip="Permite que na inclusão do atendimento seja possível alterar o valor padrão do procedimento.">
                        inform
                    </mat-icon>
                </div>
                
            </form>
        </div>
    </mat-card-content>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>