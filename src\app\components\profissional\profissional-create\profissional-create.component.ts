import { ParenteService } from './../../parente/parente.service';
import { FuncaoService } from './../../funcao/funcao.service';
import { Funcao } from './../../funcao/funcao-model';
import { Profissional } from './../profissional-model';
import { ProfissionalService } from './../profissional.service';
import { Router, ActivatedRoute } from '@angular/router';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Parente } from '../../parente/parente-model';
import { PacienteService } from '../../paciente/paciente.service';
import { Paciente } from '../../paciente/paciente-model';
import { AlertDialogComponent } from '../../template/alert-dialog/alert-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-profissional-create',
  templateUrl: './profissional-create.component.html',
  styleUrls: ['./profissional-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ProfissionalCreateComponent implements OnInit {

  profissional: Profissional = new Profissional();
  profissionais: Profissional[] = [];
  parentes: Parente[] = [];
  pacientes: any[] = [];
  pacientesProfissional: any;
  funcoes: Funcao[] = [];
  funcao: Funcao = new Funcao();
  loginUser: boolean;

  @ViewChild(NgForm) form;

  //Form Controls
  nome = new FormControl('', [Validators.required]);
  dataNascimento = new FormControl('', [Validators.required]);
  sexo = new FormControl('', [Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);

  confirmDialog: AlertDialogComponent;
  inicialName: string;

  constructor(
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    private parenteService: ParenteService,
    private funcaoService: FuncaoService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private loadingService: LoadingService
  ) { }

  async ngOnInit(): Promise<void> {
    let idProfissional = this.route.snapshot.paramMap.get('id');
    this.loadingService.show();

    try {
      // Recuperando a lista de profissionais
      const profs = await this.profissionalService.find().toPromise();
      this.profissionais = profs;
    
      if (idProfissional == undefined) { // Create
        this.profissional = new Profissional();
        this.profissional.ativo = true;
      } else {  // Edit
        const prof = await this.profissionalService.findById(idProfissional).toPromise();
        this.profissional = prof;
        this.inicialName = this.profissional.nome;
    
        // Após recuperar o profissional, filtramos os pacientes pela equipe
        const data = await this.pacienteService.find().toPromise();
        this.pacientes = data;  
    
        // Filtrando os pacientes que têm o profissional na equipe
        this.pacientesProfissional = this.pacientes.filter(paciente => 
          paciente.equipe?.some(equipeMember => equipeMember.id == this.profissional.id) && paciente.ativo
        );
      }
    
      const funcoes = await this.funcaoService.find().toPromise();
      this.funcoes = funcoes;
    
      const p = await this.parenteService.find().toPromise();
      this.parentes = p;
      
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }  

  deleteFuncao(indexFuncao: number) {
    // Lista para armazenar os pacientes onde o profissional está cadastrado com a função
    let pacientesPorFuncao = [];
    let id = this.profissional.funcao[indexFuncao].id;
    
    // console.log(this.profissional.funcao);
    // console.log(id);

    // Percorre os pacientes onde o profissional faz parte da equipe
    this.pacientesProfissional.forEach(paciente => {
      // Verifica se o profissional atual tem a função no paciente
      paciente.equipe.forEach(equipeMember => {
        // Verifica o id do profissional e da função
        if (equipeMember.id === this.profissional.id) {
          equipeMember.funcao.forEach(funcao => {
            if (funcao.id == id) {
              // Adiciona o paciente à lista se ele tem o profissional com a função específica
              pacientesPorFuncao.push(paciente);
            }
          });
        }
      });

      // console.log(pacientesPorFuncao)
    });
  
    if (pacientesPorFuncao.length > 0) {
      // Exibe a mensagem com os nomes dos pacientes
      const dialogRef = this.dialog.open(AlertDialogComponent, {
        width: 'auto',
        height: 'auto',
        data: {
          valida: false,
          msg: 'Esta função não pode ser excluída porque o profissional faz parte da equipe dos seguintes pacientes com a função selecionada:<br> ' 
              + pacientesPorFuncao.map(p => p.nome).join(',<br>') + '.<br> Você deve remover o profissional desses pacientes para que a função possa ser excluída.'
        }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result === 'ok') {
          //console.log('Usuário leu a mensagem e clicou em Ok.');
        }
      });
  
    } else {
      // Remover a função da lista de funções do profissional
      this.profissional.funcao.splice(indexFuncao, 1);
      this.profissional.funcao = [...this.profissional.funcao];
    }
  }  

  addFuncao(){
    //console.log('Adicionando função ao profissional.')
    
    //Se ainda não tiver funcao associada, inicializo a funcao
    if(this.profissional.funcao == undefined){
      this.profissional.funcao = [];
    }
    
    //Verifico de a função já está no array
    if(this.profissional.funcao.indexOf(this.funcao) == -1 && this.funcao.nome != undefined){
      //Adiciono a função no array de função
      this.profissional.funcao.push(this.funcao);
      this.profissional.funcao = [...this.profissional.funcao];
      this.funcao = new Funcao();
    }
  }

  async save() {
  this.loadingService.show();
    try {
      if (this.form.valid) {
        if (this.profissional.funcao == undefined || this.profissional.funcao.length == 0) {
          this.profissionalService.showMessage('Ao menos uma função precisa ser associada ao profissional!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação de e-mail duplicado entre profissionais
        const emailDuplicado = this.isDuplicateEmail(this.profissional, this.profissionais)
        if (emailDuplicado) {
          this.profissionalService.showMessage('Já existe um profissional cadastrado com esse email!',true);
          this.loadingService.hide();
          return;
        }

        // Verificação de e-mail entre parentes
        const emailParenteDuplicado = this.isDuplicateEmailParente(this.profissional, this.parentes);
        if (emailParenteDuplicado) {
          this.profissionalService.showMessage('Já existe um parente cadastrado com esse e-mail!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação global de e-mail
        const emailDuplicadoGlobal = await this.isDuplicateEmailGlobal(this.profissional.email);
        if (emailDuplicadoGlobal) {
          this.profissionalService.showMessage('Já existe um usuário cadastrado com esse e-mail no sistema!', true);
          this.loadingService.hide();
          return;
        }

        // Verificação de nome duplicado local
        if (this.isDuplicateName(this.profissional, this.profissionais)) {
          this.profissionalService.showMessage('Já existe um profissional cadastrado com esse nome!', true);
          this.loadingService.hide();
          return;
        }

        // Criação ou edição
        if (this.profissional.id == undefined) {
          this.profissionalService.create(this.profissional).subscribe(() => {
            this.profissionalService.showMessage('Profissional criado com sucesso!');
            this.router.navigate(['/profissional/']);
            this.loadingService.hide();
          });
        } else {
          this.profissionalService.update(this.profissional).subscribe(() => {
            this.profissionalService.showMessage('Profissional alterado com sucesso!');
            this.router.navigate(['/profissional/']);
            this.loadingService.hide();
          });
        }
      } else {
        this.profissionalService.showMessage('Existem campos inválidos no formulário!', true);
        this.loadingService.hide();
      }
    } catch (error) {
      console.log(error);
      this.loadingService.hide();
    }
  }

  // Função para normalizar uma string
  normalizeString(str: string): string {
    return str.trim().toLowerCase();
  }

  // Verifica se já existe um profissional com o mesmo nome (apenas "ativos")
  isDuplicateName(profissional: any, profissionais: any[]): boolean {
    return profissionais
      .filter(p => p.status != 'false')
      .some(p => 
        p.id !== profissional.id &&
        this.normalizeString(p.nome) === this.normalizeString(profissional.nome)
      );
  }

  // Verifica se já existe um profissional com o mesmo e-mail (apenas "ativos")
  isDuplicateEmail(profissional: any, profissionais: any[]): boolean {
    return profissionais
      .filter(p => p.ativo != 'false')
      .some(p => 
        p.id !== profissional.id &&
        this.normalizeString(p.email) === this.normalizeString(profissional.email)
      );
  }

  async isDuplicateEmailGlobal(email: string): Promise<boolean> {
    const result = await this.profissionalService.verificarEmailGlobal(email).toPromise();
    return result && result.length > 0;
  }

  // Verifica se já existe um profissional entre os parentes com o mesmo e-mail (apenas "ativos")
  isDuplicateEmailParente(profissional: any, parentes: any[]): boolean {
    return parentes.filter(parente => parente.ativo != false).some(parente => parente.email == profissional.email)
  }

  cancel() {
    this.router.navigate(['/profissional']);
  }

}
