import { AuthService } from './../../components/template/auth/auth.service';
import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-objetivo-crud',
  templateUrl: './objetivo-crud.component.html',
  styleUrls: ['./objetivo-crud.component.css']
})
export class ObjetivoCrudComponent implements OnInit {

  constructor(private router: Router,
    public authService: AuthService,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Objetivos (ESDM)',
        icon: 'source',
        routeUrl: '/objetivo'
      }
    }

  ngOnInit(): void {
  }

}
