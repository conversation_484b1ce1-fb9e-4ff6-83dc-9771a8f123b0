<mat-card class="mat-elevation-z0" *ngIf="planosIntervencao?.length > 0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row wrap" style="display: flex;"> 
            <div class="div-plano-intervencao" fxLayout="row wrap">
                <mat-form-field  class="field-plano-intervencao">
                    <mat-label>Plano de Intervenção</mat-label>
                    <mat-select placeholder="Plano de Intervenção" 
                        [(ngModel)]="planoIntervencao"
                        (selectionChange) = "setPlanoIntervencao()"
                        name="planoIntervencao">
                        <mat-option *ngFor="let plano of planosIntervencao" [value]="plano" >
                            {{plano.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="div-nova-coleta" fxLayout="row wrap">
                <!--ng-container *ngIf="planoIntervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="saveToPDF()">
                        <mat-icon>print</mat-icon>
                    </button>
                </ng-container-->
                <button mat-button color="primary" (click)="add()"
                    *ngIf="hasAccessCreate">
                    <mat-icon>iso</mat-icon> Coletar Dados
                </button>
            </div>
        </div>
        <div class="mat-elevation-z0" style="padding-bottom: 30px; " 
            *ngIf="hasAccessRead">
            <!--strong>Objetivos do Plano de Intervenção</strong-->
            <!--a mat-raised-button href="javascript:void()" (click)="toggleTableRows()" color="primary">Toggle Rows</a-->
            <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows > 
                <!-- Index Column --> 
                <ng-container matColumnDef="index">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">{{ planoIntervencao.objetivos.indexOf(row) + 1 }}</td>
                </ng-container>

                <!-- Expand Column --> 
                <ng-container matColumnDef="expand">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">
                        <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                            <mat-icon>{{ (row != null && row.isExpanded) ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                        </a>
                    </td>
                </ng-container>

                <!-- etapasSum Column --> 
                <ng-container matColumnDef="etapas">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">
                        <div [ngClass]="percentualEtapas(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEtapas(row) < 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        <div class="label label-ok div-etapas" *ngIf="percentualEtapas(row) == 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        
                    </td>
                </ng-container>

                <!-- Id Column --> 
                <ng-container matColumnDef="idNome">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':(row != null && row.isExpanded) ? 600 : normal}">{{row.id}} - {{row.nome}} </td>
                </ng-container>
        
                <!-- Nome Column -->
                <!--ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
                </ng-container--> 

                <!-- Action Column -->
                <!--ng-container matColumnDef="action" fxFlex="30">
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a style="padding-left: 10px; color: gray; cursor: pointer;" (click)="deleteObjetivo(planoIntervencao.objetivos.indexOf(row))">x</a>
                    </td>
                </ng-container--> 

                <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjs.length">
              
                      <div class="row objetivo-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                        <mat-list>
                          <div style="padding: 0px 0px 10px 0px;">{{element.descricao_plano}}</div>
                          <mat-list-item *ngFor="let etapa of element.etapa" style="padding: 0px !important; margin: 0px !important;">
                            <!--div matLine *ngIf="etapa.status=='Adquirida'"  class="div-mat-line"-->
                            <div matLine class="div-mat-line">
                                <div class="div-dados-etapa">
                                    <div class="div-dados-etapa-detail">
                                        <a *ngIf="etapa.status=='Adquirida'"  class="div-mat-line" class="v-middle" style="cursor: pointer; color: green; padding-right: 5px;"  (click)="changeEtapaStatus(etapa)">
                                            <mat-icon>
                                                check
                                            </mat-icon>
                                        </a>
                                        <a *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" class="v-middle" style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatus(etapa)">
                                            <mat-icon>
                                                miscellaneous_services
                                            </mat-icon>
                                        </a>
                                        {{etapa.id}} - {{etapa.nome}}
                                    </div>
                                    <div class="div-coleta">
                                        <ng-container *ngIf="hasColetasEtapa(etapa.id)">
                                            <div class="div-coleta-table">
                                                
                                        
                                                <ng-container *ngFor="let coleta of coletasDiarias">
                                                    <ng-container *ngFor="let objetivo of coleta.objetivos">
                                                        <ng-container *ngIf="objetivo.id == element.id">
                                                            <ng-container *ngFor="let e of objetivo.etapa">
                                                                <ng-container *ngIf="(etapa.id == e.id) && (e.status != '' && e.status != undefined)">
                                                                    <div class="div-coleta-table-row" matLine>
                                                                        <div class="div-coleta-data-cell"><b>Data</b></div>
                                                                        <div class="div-coleta-profissional-cell"><b>Profissional</b></div>
                                                                        <div class="div-coleta-score-cell"><b>Score</b></div>
                                                                        <div class="div-coleta-percentual-cell"><b>%</b></div>
                                                                        <div class="div-coleta-status-cell"><b>Status</b></div>
                                                                    </div>
                                                                    <div class="div-coleta-table-row" matLine>
                                                                        <div class="div-coleta-data-cell">{{ coleta.data | date:"dd/MM" }}
                                                                        ({{ coleta.sessao.substr(0,1) + coleta.sessao.substr(coleta.sessao.indexOf(" ")) }})</div>
                                                                        <div class="div-coleta-profissional-cell">{{ coleta.profissional?.nome }}</div>
                                                                        <div class="div-coleta-score-cell">{{ e.positivos }}/{{ e.positivos + e.negativos }}</div>
                                                                        <div class="div-coleta-percentual-cell">{{ e.percentual * 100 | number:'1.0-2' }}%</div>
                                                                        <div class="div-coleta-status-cell">{{ e.status }}</div>
                                                                    </div>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                            <!--div matline *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" class="div-mat-line">   
                                <div class="div-dados-etapa">
                                    <a class="v-middle" style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatus(etapa)">
                                        <mat-icon>
                                            miscellaneous_services
                                        </mat-icon>
                                    </a>
                                    {{etapa.id}} - {{etapa.nome}}
                                    <div class="div-coleta">
                                        <ng-container *ngIf="hasColetasEtapa(etapa.id)">
                                            <div style="width: 100%; display: table;">
                                                <div style="text-align: center; width: 100%; display: table-row;" matLine>
                                                    <div style="display: table-cell;"><b>Data</b></div>
                                                    <div style="display: table-cell;"><b>Score</b></div>
                                                    <div style="display: table-cell;"><b>%</b></div>
                                                    <div style="display: table-cell;"><b>Status</b></div>
                                                </div>
                                        
                                                <ng-container *ngFor="let coleta of coletasDiarias">
                                                    <ng-container *ngFor="let objetivo of coleta.objetivos">
                                                        <ng-container *ngIf="objetivo.id == element.id">
                                                            <ng-container *ngFor="let e of objetivo.etapa">
                                                                <ng-container *ngIf="(etapa.id == e.id) && (e.status != '' && e.status != undefined)">
                                                                    <div style="text-align: center; width: 100%; display: table-row;" matLine>
                                                                        <div style="display: table-cell;">{{ coleta.data | date:"dd/MM" }} 
                                                                            ({{ coleta.sessao }})</div>
                                                                        <div style="display: table-cell;">{{ e.positivos }}/{{ e.positivos + e.negativos }}</div>
                                                                        <div style="display: table-cell;">{{ e.percentual * 100 | number:'1.0-2' }}%</div>
                                                                        <div style="display: table-cell;">{{ e.status }}</div>
                                                                    </div>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                            </div-->
                          </mat-list-item>
                        </mat-list>
                      </div>
              
                    </td>
                  </ng-container>
          
              <!--tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr-->
              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
              <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="objetivo-detail-row"></tr>
            </table>
        </div> 
        
        <div class="mat-elevation-z0" style="padding-bottom: 30px; width: 100%; margin-top: 10px; display: table;">
            <div style="display: table-row-group;">
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SFT – Suporte físico total
                        </div>
                        <div style="display: table-cell; text-align: right;">
                            SFP – Suporte físico parcial
                        </div>
                </div>
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SV – Suporte verbal
                        </div>
                        <div style="display: table-cell; text-align: right;">
                            SG – Suporte gestual
                        </div>
                </div>
                <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SP – Suporte posicional
                        </div>
                        <div style="display: table-cell; text-align: right;">
                            &nbsp;
                        </div>
                </div>
            </div>
        </div>
    </mat-card-content>
</mat-card>
<div *ngIf="planosIntervencao?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Nenhum plano de intervenção criado!</h1>
</div>