import { Nivel } from './../../nivel/nivel-model';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { Estimulo } from './../../estimulo/estimulo-model';
import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { PlanointervencaovbmappService } from './../../planointervencaovbmapp/planointervencaovbmapp.service';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { PlanoIntervencaoVBMAPP } from './../../planointervencaovbmapp/planointervencaovbmapp-model';
import { ColetaDiariaVBMAPP } from './../../coletadiariavbmapp/coletadiariavbmapp-model';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { PacienteService } from './../../paciente/paciente.service';
import { Paciente } from './../../paciente/paciente-model';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { Profissional } from './../../profissional/profissional-model';
import { AppDateAdapter, APP_DATE_FORMATS } from '../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { PIC } from '../../planopic/pic-model';
import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from '../../coletadiariavbmapp/coletadiariapic-model';
import { PICService } from '../../planopic/pic.service';
import { LoadingService } from '../../../shared/service/loading.service';
import { PlanoIntervencaoService } from '../../planointervencao/planointervencao.service';

@Component({
  selector: 'app-coleta-diaria-pdf',
  templateUrl: './coleta-diaria-pdf.component.html',
  styleUrls: ['./coleta-diaria-pdf.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})

export class ColetaDiariaPdfComponent implements OnInit {

  public coletasdiarias: any[] = [];
  public profissionais: Profissional[] = [];
  public profissionaisDoPaciente: Profissional[];
  public idProfissional: string;
  public idPaciente: string;
  public profissionalLogado: Profissional = new Profissional();
  public paciente: Paciente = new Paciente();
  public data: Date;
  public today: Date = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
  public planoIntervencao: PlanoIntervencaoVBMAPP = new PlanoIntervencaoVBMAPP();
  public idPlanoIntervencao: string;

  public pic: PIC = new PIC();
  public coletasdiariasPIC: SessaoColetaDiariaPIC[] = [];

  public PICs: PIC[] = [];

  public cantChangeData: boolean = false;

  public dominioMap: Map<string, ObjetivoVBMAPP[]>[] = [];

  public domAtual: string = "";

  tabs = [];
  selected = new FormControl(0);
  
  public TittlePEI: string = "PEI";
  public TittlePIC: string = "PIC";

  //Form Controls
  dataFC = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  @ViewChild('coletaDiariaPDF', {static: false})
  el!: ElementRef;

  @Output() pdfGenerated: EventEmitter<void> = new EventEmitter<void>();

  idOrganizacao: string;

  constructor(private planoIntervencaoService: PlanointervencaovbmappService,
    private planoIntervencaoESDMService: PlanoIntervencaoService,
    private picService: PICService,
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    public dialog: MatDialog,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.reset()
    this.loadingService.show()
    const currentUser = this.authService.getUser() as FirebaseUserModel;
    this.idPlanoIntervencao = this.route.snapshot.paramMap.get('id');
    
    this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;

    try {
      let plano 

      //Recupero o Plano de Intervenção
      plano = await this.planoIntervencaoService.findById(this.idPlanoIntervencao).toPromise();
      if (!plano) {
        plano = await this.planoIntervencaoESDMService.findById(this.idPlanoIntervencao).toPromise();
      }
      
      if (plano?.id){
        //Acrescentando id_dominio e dominio na raiz do objetivo para podermos compatibilizar com a versão anterior da estrutura de armazenamento de objetivos
        for(const [, obj] of plano.objetivos.entries()){
          if(obj.id_dominio == undefined && obj.marco != undefined){
            obj.id_dominio = obj.marco.id_dominio;
            obj.dominio = obj.marco.dominio;
          } else {
            obj.marco = { "id_dominio": obj.id_dominio,
              "dominio": (obj.dominio as DominioVBMAPP),
              "nome": "",
              "objetivo": "",
              "id_nivel": (obj.id_nivel as string),
              "nivel": (obj.nivel as Nivel),
              "ordem": 0
            };
          }
        }
        this.planoIntervencao = plano;
      }
      
      this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
      
      let PICs;
      if(this.planoIntervencao?.id){
        PICs = await this.picService.findByPaciente(this.planoIntervencao.idPaciente).toPromise();
        this.paciente = await this.pacienteService.findById(this.planoIntervencao.idPaciente).toPromise();
      } else {
        const pic = await this.picService.findById(this.idPlanoIntervencao).toPromise();
        PICs = await this.picService.findByPaciente(pic.idPaciente).toPromise();
        this.paciente = await this.pacienteService.findById(pic.idPaciente).toPromise();
      }

      this.profissionaisDoPaciente = this.paciente.equipe;
      this.profissionalLogado = this.profissionaisDoPaciente.find(p => p.uid === currentUser.uid);
      if (this.profissionalLogado) {
        this.idProfissional = this.profissionalLogado.id;
      }
      
      //Carregando Profissionais
      const profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = profissionais;
      this.coletasdiarias?.forEach(coleta => {
        coleta.profissional = this.profissionalLogado;
        coleta.idProfissional = this.profissionalLogado.id;
      })
      
      if (PICs.length > 0) {
        this.PICs = PICs.filter(p => p.status !== false);
        this.pic = this.PICs[0];
        this.coletasdiariasPIC.forEach(coleta => {
          coleta.idProfissional = this.profissionalLogado.id;
          coleta.nomeProfissional = this.profissionalLogado.nome;
        })
      }
      
      if(this.planoIntervencao?.objetivos?.length > 0){
        await this.addColetaDiaria();
      }

      if(this.pic?.objetivos?.length > 0){
        await this.addColetaDiariaPIC();
      }
    
    } catch (error) {
      console.error("Erro ao carregar os dados:", error);
    } finally {
      this.loadingService.hide();
    }
  }

  async setObjetivosPorDominioCreate(sessaoId: number){
    this.loadingService.show()
    try {
      let dMap = new Map<string, any[]>();
      let objetivos: any[] = [];
  
      // await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      for(const [,objetivo] of this.coletasdiarias[sessaoId].objetivos.entries()){
  
        if (!objetivo.habilidades) {
          if(dMap.get(objetivo.dominio.id) != undefined){
            objetivos = dMap.get(objetivo.dominio.id);
            objetivos.push(objetivo);
            dMap.set(objetivo.id_dominio, objetivos)
          } else {
            dMap.set(objetivo.id_dominio, [objetivo])
          }
        } else {
          if(objetivo.marco.dominio == undefined && dMap.get("SD") != undefined) {
            objetivos = dMap.get("SD");
            objetivos.push(objetivo);
            dMap.set("SD", objetivos)
          } else if(objetivo.marco.dominio == undefined && dMap.get("SD") == undefined){
            dMap.set("SD", [objetivo])
          }
          
          if(objetivo.marco.dominio != undefined && dMap.get(objetivo.marco.dominio.id) != undefined){
            // console.log(objetivo)
            objetivos = dMap.get(objetivo.marco.dominio.id);
            objetivos.push(objetivo);
            dMap.set(objetivo.marco.id_dominio, objetivos)
          } else if(objetivo.marco.dominio != undefined && dMap.get(objetivo.marco.dominio.id) == undefined){
              dMap.set(objetivo.marco.id_dominio, [objetivo])
          }
        }
      }
      // })
      this.dominioMap.push(dMap);
    } catch (error) {
      console.log("Erro", error)
    } finally {
      this.loadingService.hide()
    }
    // console.log(dMap)
    // console.log(this.coletasdiarias[sessaoId])
    // console.log(this.dominioMap[this.dominioMap.length - 1])
  }

  async setObjetivosPorDominioEdit(sessaoId: number){
    let dMap = new Map<string, any[]>();
    let objetivos: any[] = [];

    await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    this.dominioMap.push(dMap);
  }

  checkDominioAtual(dominio: string): boolean {
    if(this.domAtual ==  dominio){
      return false;
    } else {
      this.domAtual = dominio;
      return true;
    }
  }

  async setObjetivos(sessaoId: number){
    this.loadingService.show()
    
    try {
      let objetivoNovo: any;
      let tipoSuporteNovo: TipoSuporte;
      let estimuloNovo: Estimulo;
      let etapaDefinida: boolean;
      let estimulos: string[]=[];
      let estimuloDefinido: boolean;
  
      // Recupero os objetivos do plano de intervenção atual
      this.planoIntervencao.objetivos.forEach(objetivo => {
        if (!objetivo.habilidades) {
          objetivoNovo = {...objetivo};
          etapaDefinida = false;
  
          if(objetivoNovo.status == "Adquirido") {
            //Se o objetivo foi adquirido, incluo o mesmo sem etapas
            objetivoNovo.etapa = []
            if(this.coletasdiarias[sessaoId].objetivos == undefined){
              this.coletasdiarias[sessaoId].objetivos = [];
            }
            this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
          } else {
            objetivoNovo.etapa.forEach(etapa => {
              if(!etapaDefinida){
                if(etapa.status == undefined || etapa.status != "Adquirida"){
                  objetivoNovo.etapa = []
                  objetivoNovo.etapa.push({...etapa})
                  objetivoNovo.etapa[0].periodo = []
                  objetivoNovo.etapa[0].periodo.push("", "", "", "")
                  etapaDefinida = true;
                } else {
                  objetivoNovo.etapa = [];
                }
              }
            })
            if(this.coletasdiarias[sessaoId].objetivos == undefined){
              this.coletasdiarias[sessaoId].objetivos = [];
            }
            this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
            // console.log(this.coletasdiarias[sessaoId])
          }
        } else {
          objetivoNovo = {...objetivo};
          estimuloDefinido = false;
    
          // Retiro todos os tipos de suporte do objetivo a ser trabalhado para que eu possa incluir apenas os
          // tipos de suporte que ainda precisam ser trabalhados.
          objetivoNovo.tiposSuporte = [];
    
          // Caso o Tipo de Coleta seja Naturalista, incluo na sessão apenas os tipos de suporte que estão em andamento.
          if(objetivoNovo.tipoColeta == "Naturalista"){
            if(objetivoNovo.status == "Adquirido") {
              //Se o objetivo foi adquirido, incluo o mesmo sem tipos de suporte
              if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }
              this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
            } else {
              // Se o objetivo não estiver Adquirido, incluo o tipo de suporte e seus estímulos que precisam ser trabalhado.
              estimulos = [];
              objetivo.tiposSuporte.forEach(tipoSuporte => {
                tipoSuporteNovo = {...tipoSuporte};
    
                if(tipoSuporteNovo.status == undefined || tipoSuporteNovo.status != "Adquirido"){
                  // objetivoNovo.tiposSuporte = []
                  tipoSuporteNovo.estimulos = [];
                  
                  tipoSuporte.estimulos.forEach(estimulo => {
                    estimuloNovo = {...estimulo};
                    estimuloNovo.status = "";
                    if(estimulo.status == undefined || estimulo.status != "Adquirido"){
                      if(estimulos.find(e => e == estimulo.id) == undefined){ //Estímulo ainda não foi adicionado para trabalhar
                        estimulos.push(estimulo.id)
                        estimuloNovo.periodo = []
                        //Adiciono o número de períodos de acordo com o número de oportunidades descritas no objetivo
                        for(let i = 1; i <= (objetivoNovo.oportunidadesEstimulo == undefined ? 3 : objetivoNovo.oportunidadesEstimulo); i++){
                          estimuloNovo.periodo.push("")
                        }
                        tipoSuporteNovo.estimulos.push({...estimuloNovo});
                      }
                    }
                  })
                  objetivoNovo.tiposSuporte.push({...tipoSuporteNovo});
                }
              })
              if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }
              this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
              // console.log(this.coletasdiarias[sessaoId])
            }
          }
        }
      })
  
      // console.log(this.coletasdiarias);
      //Incluo os objetivos em um domínio de Map para facilitar a listagem no HTML
      await this.setObjetivosPorDominioCreate(sessaoId);
    } catch (error) {
      console.log("Erro", error);
    } finally {
      this.loadingService.hide()
    }
  }

  async addTab(name: string, selectAfterAdding: boolean) {
    this.tabs.push(name);
    //console.log(this.tabs)
    //console.log(name)

    if (selectAfterAdding) {
      this.selected.setValue(this.tabs.length - 1);
    }
  }

  async addColetaDiaria(){
    this.loadingService.show()
    try {
      let cd = new ColetaDiariaVBMAPP();
      let dataInicio: Date;
      //console.log(this.coletasdiarias.length)
  
      //Incluindo os dados do paciente
      cd.idPaciente = this.planoIntervencao.idPaciente;
      cd.paciente = this.paciente;
  
      //Incluindo os dados do plano de intervenção
      cd.idPlanoIntervencao = this.idPlanoIntervencao;
  
      //Incluindo os dados do profissional
      if (this.coletasdiarias.length == 0) {
        if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
          if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
            cd.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            cd.idProfissional = cd.profissional.id;
            // this.idProfissional = cd.idProfissional;
          }
        }
      } else {
        cd.profissional = this.coletasdiarias[0]?.profissional;
        cd.idProfissional = this.coletasdiarias[0]?.profissional.id;
      }
  
      //Incluindo o nome da sessão
      cd.sessao = "Sessão " + (this.coletasdiarias.length + 1);
  
      //Aplicando a data e hora atual a coleta diária
      cd.data = new Date(new Date(this.data).getFullYear(), new Date(this.data).getMonth(), new Date(this.data).getDate(), 0,0,0,0);
      //console.log(cd.data)
      if(this.coletasdiarias.length > 0){
        dataInicio = new Date(this.data);
        dataInicio.setHours(parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(0,2)) + 1,
          parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(3,2)))
        // console.log(dataInicio)
        cd.horaInicio = ("0" + dataInicio.getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cd.horaTermino = ("0" + (dataInicio.getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      } else {
        cd.horaInicio = ("0" + new Date().getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cd.horaTermino = ("0" + (new Date().getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      }
  
      this.coletasdiarias.push(cd);
  
      //this.addTab("Sessão " + (this.coletasdiarias.length), true)
      await this.addTab(cd.sessao, true)
      await this.setObjetivos(this.coletasdiarias.length - 1);
    } catch (error) {
      console.log("Erro: " + error);
    } finally {
      this.loadingService.hide()
    }
  }

  async addColetaDiariaPIC(){
    if (this.pic?.id) {
      this.loadingService.show()
      let cdPIC = new SessaoColetaDiariaPIC();
      let dataInicio: Date;
  
      //Incluindo os dados do paciente
      cdPIC.idPaciente = this.pic.idPaciente;
  
      //Incluindo os dados do plano de intervenção
      cdPIC.idPlanoIntervencao = this.pic.id;
  
      //Incluindo os dados do profissional
  
      if (this.coletasdiariasPIC.length == 0) {
        if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
          if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
            let profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            cdPIC.idProfissional = profissional.id;
            cdPIC.nomeProfissional = profissional.nome;
          }
        }
      } else {
        cdPIC.idProfissional = this.coletasdiariasPIC[0]?.idProfissional;
        cdPIC.nomeProfissional = this.coletasdiariasPIC[0]?.nomeProfissional;
      }
  
      //Incluindo o nome da sessão
      cdPIC.sessao = "Sessão " + (this.coletasdiariasPIC.length + 1);
  
      //Aplicando a data e hora atual a coleta diária
      cdPIC.data = new Date(new Date(this.data).getFullYear(), new Date(this.data).getMonth(), new Date(this.data).getDate(), 0,0,0,0);
      //console.log(cdPIC.data)
      if(this.coletasdiariasPIC.length > 0){
        dataInicio = new Date(this.data);
        dataInicio.setHours(parseInt(this.coletasdiariasPIC[this.coletasdiariasPIC.length - 1].horaInicio.substr(0,2)) + 1,
          parseInt(this.coletasdiariasPIC[this.coletasdiariasPIC.length - 1].horaInicio.substr(3,2)))
        // console.log(dataInicio)
        cdPIC.horaInicio = ("0" + dataInicio.getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cdPIC.horaTermino = ("0" + (dataInicio.getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      } else {
        cdPIC.horaInicio = ("0" + new Date().getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cdPIC.horaTermino = ("0" + (new Date().getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      }
  
      // Incluindo os dados da coleta para cada objetivo no plano
      cdPIC.objetivosColeta = this.pic.objetivos?.map(objetivo => {
        let coletaObjetivo = new ColetaDiariaPIC();
        coletaObjetivo.idObjetivo = objetivo.id;
        if(objetivo.tipoColeta == 'Cronometragem' || objetivo.tipoColeta == 'Amostragem de Tempo'){
          coletaObjetivo.duracoes = [];
        } else {
          coletaObjetivo.qtdObservada = null;
        }
        // Adicionar outros dados, como qtdObservada e duracoes, se necessário
        return coletaObjetivo;
      });
  
      this.coletasdiariasPIC.push(cdPIC);
      if (!this.planoIntervencao?.id) {
        this.addTab(cdPIC.sessao, true)
      }
      this.loadingService.hide()
      // console.log(this.coletasdiariasPIC)
    }
  }

  // async generatePDF() {
  //   // Adicione um timeout para garantir que o DOM seja atualizado
  //   setTimeout(() => {
  //     const pdf = new jsPDF('l', 'pt', 'a4', true);
  //     pdf.html(this.el.nativeElement, {
  //       callback: (pdf) => {
  //         pdf.save('Avaliacao de Marcos.pdf');
  //         this.pdfGenerated.emit();
  //         this.loadingService.hide();
  //       },
  //       x: 0, // Alinhamento horizontal (ajuste conforme necessário)
  //       y: 10 // Margem superior (ajuste conforme necessário)
  //     });
  //   }, 1500); // Tempo de espera para garantir a renderização
  // };

}  