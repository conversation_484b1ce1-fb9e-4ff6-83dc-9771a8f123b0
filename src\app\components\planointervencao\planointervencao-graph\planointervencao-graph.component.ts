import { LoadingService } from 'src/app/shared/service/loading.service';
import { FirebaseUserModel } from "./../../template/auth/user-model";
import { Objetivo } from "./../../objetivo/objetivo-model";
import { ESDMChecklistGraph } from "./../../esdmchecklist/esdmchecklist-graph-model";
import { ActivatedRoute } from "@angular/router";
import { AuthService } from "./../../template/auth/auth.service";
import { PlanoIntervencaoService } from "./../planointervencao.service";
import { Paciente } from "./../../paciente/paciente-model";
import { PlanoIntervencao } from "./../planointervencao-model";
import { Component, OnInit, Input } from "@angular/core";
import { ChartDataSets } from "chart.js";

@Component({
  selector: "app-planointervencao-graph",
  templateUrl: "./planointervencao-graph.component.html",
  styleUrls: ["./planointervencao-graph.component.css"],
})
export class PlanointervencaoGraphComponent implements OnInit {
  public planointervencao: PlanoIntervencao = new PlanoIntervencao();

  //  public idPaciente: string;
  public idPlanoIntervencao: string;

  paciente: Paciente = new Paciente();

  public colors: any[];

  public adquiridas: any[] = [];
  public objetivos: any[] = [];

  public graph: ESDMChecklistGraph = new ESDMChecklistGraph();

  constructor(
    private planointervencaoService: PlanoIntervencaoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private loadingService: LoadingService
  )
  {}

  get user(): FirebaseUserModel {
    return this.authService.getUser();
  }

  ngOnInit(): void {
    this.loadingService.show();
    this.idPlanoIntervencao = this.route.snapshot.paramMap.get("id");

    // console.log(this.idPlanoIntervencao);
    this.planointervencaoService
      .findById(this.idPlanoIntervencao)
      .subscribe((plano) => {
        this.planointervencao = plano;

        // console.log(this.planointervencao.objetivos.length)

        for (const [, objetivo] of plano.objetivos.entries()) {
          // console.log(objetivo.id + " - " + this.countEtapas(objetivo) + " - " + this.countEtapasAdquiridas(objetivo));

          this.objetivos.push(objetivo.id);
          this.adquiridas.push(
            Math.round(this.percentualEtapasAdquiridas(objetivo) * 100)
          );

          // if(this.percentualEtapasNaoAdquiridas(objetivo) == 1){
          //   this.adquiridas.push();
          // } else {
          //   this.adquiridas.push(this.percentualEtapasAdquiridas(objetivo));
          // }

          // if(this.percentualEtapasAdquiridas(objetivo) == 1){
          //   this.naoAdquiridas.push();
          // } else {
          //   this.naoAdquiridas.push(this.percentualEtapasNaoAdquiridas(objetivo));
          // }
        }

        // console.log("1")

        //Setando as cores padrão de cada checklist (por ordem)
        this.colors = [
          {
            backgroundColor: "rgba(68, 114, 187, 0.8)",
            fill: true,
            borderColor: "rgb(68, 114, 187)",
            pointBackgroundColor: "rgb(68, 114, 187)",
            pointBorderColor: "#fff",
            pointHoverBackgroundColor: "#fff",
            pointHoverBorderColor: "rgb(68, 114, 187)",
          },
        ];

        this.graph.chartType = "bar";
        this.graph.options = {
          plugins: {
            datalabels: {
              display: false,
            },
          },
          scales: {
            xAxes: [
              {
                stacked: true,
              },
            ],
            yAxes: [
              {
                stacked: true,
                ticks: {
                  beginAtZero: true,
                  max: 100,
                },
              },
            ],
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItems) {
                return (
                  datasets[tooltipItems.datasetIndex].data[tooltipItems.index] +
                  "%"
                );
              },
            },
          },
        };
        let datasets: ChartDataSets[] = [];
        datasets.push({ label: "Adquiridas", data: this.adquiridas });
        this.graph.datasets = datasets;
        this.graph.labels = this.objetivos;

        this.loadingService.hide();
      });
    // console.log("2")
  }

  percentualEtapasAdquiridas(row: Objetivo) {
    if (row) {
      return this.countEtapasAdquiridas(row) / this.countEtapas(row);
    } else {
      return 0;
    }
  }

  percentualEtapasNaoAdquiridas(row: Objetivo) {
    if (row) {
      return (
        (this.countEtapas(row) - this.countEtapasAdquiridas(row)) /
        this.countEtapas(row)
      );
    } else {
      return 0;
    }
  }

  countEtapas(row: Objetivo) {
    //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
    if (row) {
      return row.etapa.length;
    } else {
      return 0;
    }
  }

  countEtapasAdquiridas(row: Objetivo) {
    if (row) {
      return row.etapa.filter((e) => e.status == "Adquirida").length;
    } else {
      return 0;
    }
  }
}
