import { Paciente } from './../paciente/paciente-model';
import { EventoAtendimento } from './eventoatendimento-model';

import { CalendarEvent/*, EventAction, EventColor*/ } from 'angular-calendar';
import { Profissional } from './../profissional/profissional-model';
import { TipoProcedimento } from './../tipoprocedimento/tipoprocedimento-model';
import { RRule, RRuleSet } from 'rrule';

export class Atendimento implements EventoAtendimento{
    
    /*
    color?: EventColor;
    actions?: EventAction[];
    allDay?: boolean;
    cssClass?: string;
    */
    id?: string | number;
    idOrigem?: string | number;
    start: Date;
    end?: Date;
    title: string;
    status: string; //Desmarcado pelo paciente, Não compareceu, Atendido, Marcado confirmado, Marcado não confirmado
    procedimento: TipoProcedimento; 
    idProcedimento: string;
    profissionais: Profissional[];
    idProfissionais: string[];
    paciente: Paciente;
    idPaciente: string;
    recorrente?: boolean;
    color?: any;
    //rrule?: RRule;
    
    rrule?: {
        freq: any;
        count: number;
        bymonth?: number;
        bymonthday?: number;
        byweekday?: any;
        dtstart: Date;
        until?: Date;
    };
    
}