import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { EMPTY, Observable } from 'rxjs';
import { NivelAvalicao } from './nivel-avaliacao-model';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})

export class NivelAvaliacaoService {

  nivelUrl = `${environment.API_URL}/nivel_avaliacao`;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(nivel: NivelAvalicao): Observable<NivelAvalicao>{
    return this.http.post<NivelAvalicao>(this.nivelUrl, nivel).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(nivel: NivelAvalicao): Observable<NivelAvalicao>{
    return this.http.put<NivelAvalicao>(this.nivelUrl + "/" + nivel.id, nivel).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<NivelAvalicao>{
    return this.http.get<NivelAvalicao>(this.nivelUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<NivelAvalicao[]>{
    return this.http.get<NivelAvalicao[]>(this.nivelUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  
  }

  findByTipoAvaliacao(idTipoAvaliacao: string): Observable<NivelAvalicao[]>{
    return this.http.get<NivelAvalicao[]>(this.nivelUrl + '/tipo_avaliacao/' + idTipoAvaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<NivelAvalicao>{
    return this.http.delete<NivelAvalicao>(this.nivelUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
