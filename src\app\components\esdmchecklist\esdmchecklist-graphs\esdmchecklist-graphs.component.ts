import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { CompetenciaService } from './../../competencia/competencia.service';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { NivelService } from './../../nivel/nivel.service';
import { ESDMChecklistGraph } from './../esdmchecklist-graph-model';
import { Paciente } from './../../paciente/paciente-model';
import { Observable } from 'rxjs';
import { ESDMChecklist } from './../esdmchecklist-model';
import { EsdmchecklistService } from './../esdmchecklist.service';
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { MatCheckboxChange, MatCheckbox } from '@angular/material/checkbox';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-esdmchecklist-graphs',
  templateUrl: './esdmchecklist-graphs.component.html',
  styleUrls: ['./esdmchecklist-graphs.component.css']
})
export class EsdmchecklistGraphsComponent implements OnInit {

  public esdmchecklists: ESDMChecklist[];
  public esdmchecklist: ESDMChecklist = new ESDMChecklist();
  public paciente: Paciente = new Paciente();
  public niveis: Nivel[];
  public nivel: Nivel = new Nivel();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>(); 
  public colors: any[];
  //public colorMap: Map<number, string> = new Map<number, string>();
  public avParcMap: Map<string, Map<string, boolean>> = new Map<string, Map<string, boolean>>();
  public avDominioParc;
  public dominioParc: boolean;
  
  public esdmGraph: ESDMChecklistGraph;
  public esdmGraphConsolidado: ESDMChecklistGraph;

  public graph: ESDMChecklistGraph = new ESDMChecklistGraph();

  public selectedChartType: string = 'radar';
  public showPercentage: boolean;
  public hasAccessRead: boolean;

  @Input()
  $pacienteSearch: Observable<Paciente>;

  @ViewChild('checklist0') private checklistBox0: MatCheckbox;

  constructor(private esdmchecklistService: EsdmchecklistService,
    private nivelService: NivelService,
    public authService: AuthService,
    private competenciaService: CompetenciaService,
    private loadingService: LoadingService) { }
  
  ngOnInit(): void {
    this.avParcMap.clear();

    //Setando as cores padrão de cada checklist (por ordem)
    this.colors = [
      { backgroundColor: "rgba(144, 238, 144, 0.60)",
        fill: true,
        borderColor: "rgb(144, 238, 144)",
        pointBackgroundColor: "rgb(144, 238, 144)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(144, 238, 144)" },
      { backgroundColor:"rgba(54, 163, 235, 0.50)",
        fill:true,
        borderColor:"rgb(54, 162, 235)",
        pointBackgroundColor:"rgb(54, 162, 235)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(54, 162, 235)" },
      { backgroundColor:"rgba(138,43,226,0.50)",
        fill:true,
        borderColor:"rgb(138,43,226)",
        pointBackgroundColor:"rgb(138,43,226)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(138,43,226)" },
      { backgroundColor:"rgba(218, 165, 32, 0.50)",
        fill:true,
        borderColor:"rgb(218, 165, 32)",
        pointBackgroundColor:"rgb(218, 165, 32)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(218, 165, 32)" }
    ];
    

    //Atribuindo o paciente vindo por parâmetro
    this.$pacienteSearch.subscribe(paciente => { 
      this.paciente = paciente;
      this.esdmchecklist.idPaciente = paciente.id;
      this.esdmchecklist.paciente = paciente;

      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','read')
      

      //Carregando Checklists do paciente
      this.esdmchecklistService.findByPaciente(paciente.id).subscribe(chklsts => {
        if(chklsts.length > 0){
          this.esdmchecklists = chklsts.filter(c=>c.status != false);

          //Atribuindo o primeiro (mais novo) checklist para visualização
          if(this.esdmchecklists.length > 0){
            this.esdmchecklist = this.esdmchecklists[0];
          }
        }
      }, (err) => console.error(err),
      () => {
        //Carregando Níveis
        this.nivelService.find().subscribe(niveis => {
          this.niveis = niveis;
          //this.niveis.push({ id: 'C', nome: 'Consolidado' })
          this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)

          //Carregando Domínios dos Níveis (TAB)
            niveis.forEach(nivel => {
            this.competenciaService.findDominiosByNivel(nivel.id).subscribe(dominios => {
              this.dominioMap.set(nivel.id,dominios)
            },
            (err) => console.error(err),
            () => {
              if((this.dominioMap.size == 4)){// Só calculo quando os 4 níveis foram carregados, caso contrário dá erro.
                this.graph.datasets = [];
                //this.nivel = { id: 'C', nome: 'Consolidado' }
                //this.calculateGraphNivel(new Date(this.esdmchecklists[0].data).toLocaleDateString('pt-BR'));
                if(this.esdmchecklists != undefined){
                  //this.calculateGraphConsolidado(new Date(this.esdmchecklists[0].data).toLocaleDateString('pt-BR'));
                  this.calculateGraphNivel(new Date(this.esdmchecklists[0].data).toLocaleDateString('pt-BR'), 'N1');
                }
              }
            })
          })
        })
      })
    });

    //Criando gráfico de exemplo
    /*
    this.graph.chartType = 'radar';
    let datasets: ChartDataSets[] = [];
    datasets.push({ label: 'Hoje', data: [90, 70, 80, 60, 90, 100, 90, 90, 80, 100]})
    datasets.push({ label: '3 meses', data: [70, 50, 60, 40, 70, 80, 70, 70, 60, 80]})
    this.graph.datasets = datasets;
    this.graph.labels = ['CRE', 'CEX', 'CSO', 'IMI', 'COG', 'JOG', 'MFI', 'MGR', 'COM', 'IPE'];
    */

  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

   // console.log(funcoes)

    return funcoes;
  }

  checked(index: number): boolean{
    if (index == 0){
      return true
    } else {
      return false
    }
  }

  doGraphCal(date: string, idNivel: string): number[]{
    let n, p, a, t;

    let ipeCalc = false;
    let csoCalc = false;
    let jogCalc = false;

    let data: number[] = [];

    this.dominioParc = false;
    let internalDominioParc: Map<string, boolean> = new Map<string, boolean>();
    

    //Busco a data selecionada nos checklists
    let index = this.esdmchecklists.map(function(e) { return new Date(e.data).toLocaleDateString('pt-BR'); }).indexOf(date)

    this.dominioMap.get(idNivel).forEach(dom => {
      if( (dom.id == 'IPE' || dom.id == 'IPA' || dom.id == 'IPV' || dom.id == 'IPH' || dom.id == 'IPT') && ipeCalc == false ) {
        ipeCalc = true;

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'A')).length

        t = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel == idNivel && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT'))).length;
        
        // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação 
        if(this.avParcMap.size == 0){
          internalDominioParc = new Map<string, boolean>();
          if(n+p+a != t){
            internalDominioParc.set(idNivel+'IPE', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('IPE*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'IPE', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('IPE');
          }
        } else {
          if(n+p+a != t) {
            internalDominioParc.set(idNivel+'IPE', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('IPE*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'IPE', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'IPE')) {
              this.graph.labels.push('IPE*');
              this.dominioParc = true;
            } else {
              this.graph.labels.push('IPE');
            }
          }
        }

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))
        //   + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'CSO' || dom.id == 'CSA' || dom.id == 'CSP' || dom.id == 'CAP') && csoCalc == false ) {
        csoCalc = true;

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'A')).length

        t = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel == idNivel && 
            (chklst.competencia.id_dominio == 'CSO' || 
            chklst.competencia.id_dominio == 'CSA' || 
            chklst.competencia.id_dominio == 'CSP' || 
            chklst.competencia.id_dominio == 'CAP'))).length;

        // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
        if(this.avParcMap.size == 0){
          internalDominioParc = new Map<string, boolean>();
          if(n+p+a != t){
            internalDominioParc.set(idNivel+'CSO', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('CSO*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'CSO', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('CSO');
          }
        } else {
          if(n+p+a != t) {
            internalDominioParc.set(idNivel+'CSO', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('CSO*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'CSO', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'CSO')) {
              this.graph.labels.push('CSO*');
              this.dominioParc = true;
            } else {
              this.graph.labels.push('CSO');
            }
          }
        }
        
        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) 
        //  + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'JOG' || dom.id == 'JOR' || dom.id == 'JOI') && jogCalc == false ) {
        jogCalc = true;

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'A')).length

        t = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI'))).length

        // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
        if(this.avParcMap.size == 0){
          internalDominioParc = new Map<string, boolean>();
          if(n+p+a != t){
            internalDominioParc.set(idNivel+'JOG', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('JOG*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'JOG', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('JOG');
          }
        } else {
          if(n+p+a != t) {
            internalDominioParc.set(idNivel+'JOG', true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push('JOG*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+'JOG', false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'JOG')) {
              this.graph.labels.push('JOG*');
              this.dominioParc = true;
            } else {
              this.graph.labels.push('JOG');
            }
          }
        }

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) 
        //  + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );

      } else if ( (dom.id != 'IPE' && dom.id != 'IPA' && dom.id != 'IPV' && dom.id != 'IPH' && dom.id != 'IPT') 
                  && (dom.id != 'CSO' && dom.id != 'CSA' && dom.id != 'CSP' && dom.id != 'CAP')
                  && (dom.id != 'JOG' && dom.id != 'JOR' && dom.id != 'JOI') ) { //Caso não seja um domínio a ser consolidado
  
        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && chklst.competencia.id_dominio == dom.id && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id && chklst.valor == 'A')).length

        t = this.esdmchecklists[index].checklist.filter(chklst =>
          (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id)).length

        // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
        if(this.avParcMap.size == 0){
          internalDominioParc = new Map<string, boolean>();
          if(n+p+a != t){
            internalDominioParc.set(idNivel + dom.id, true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push(dom.id + '*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel + dom.id, false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push(dom.id);
          }
        } else {
          if(n+p+a != t) {
            internalDominioParc.set(idNivel + dom.id, true);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            this.graph.labels.push(dom.id + '*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel + dom.id, false);
            this.avParcMap.set(this.esdmchecklists[index].id, internalDominioParc);
            if(this.haveAvaliacaoNivelDominioParcial(idNivel, dom.id)) {
              this.graph.labels.push(dom.id + '*');
              this.dominioParc = true;
            } else {
              this.graph.labels.push(dom.id);
            }
          }
        }
  
        //let x = this.esdmchecklists[index].checklist.filter(chklst => 
        //  (chklst.competencia.id_nivel==idNivel && chklst.competencia.id_dominio == dom.id && (chklst.valor == 'X' || chklst.valor == undefined))).length

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) + 
        //  ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      }
    })
    return data;
  }
  //CSO, JOG, IPE

  calculateGraphNivel(date: string, idNivel: string){
    this.loadingService.show();
    const chartType = this.selectedChartType as 'radar' | 'bar' | 'horizontalBar';
    this.graph.chartType = chartType;
    //this.graph.datasets = [];
    this.graph.labels = [];
    //let datasets: ChartDataSets[] = [];
    let data: number[] = [];

    //Busco a data selecionada nos checklists
    let index = this.esdmchecklists.map(function(e) { return new Date(e.data).toLocaleDateString('pt-BR'); }).indexOf(date)

    //Solicito o cálculo do nívei desejado
    data = this.doGraphCal(date, idNivel)
    if(this.isAvaliacaoParcial(this.esdmchecklists[index].id)){
      this.graph.datasets.push({ label:  new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR') + "*", data: data});
    } else {
      this.graph.datasets.push({ label:  new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR'), data: data});
    }

    this.organizeDatasets(chartType);
    
    //console.log("IndexOf:" + this.graph.datasets.map(function(e) { return e.label; }).indexOf(new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR')))
    this.graph.labels = [...this.graph.labels];
    this.graph.options = {
      scales: {
        yAxes: [{
          ticks: {
            beginAtZero: true,
            max: 100,
            stepSize: 10
          }
        }]
      },
      responsive: true,
      legend: {
        position: 'top',
      },
      tooltips: {
        enabled: true,
        mode: 'single',
        callbacks: {
          label: function (tooltipItems, data) {
            return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
          }
        }
      },
    };

    if (chartType === 'radar') {
      this.graph.options.scales = {} as any;
      this.graph.options.scale = {
        ticks: {
          min: 0,
          max: 100,
          stepSize: 10
        }
      } as any;
    }

    if (chartType === 'horizontalBar') {
      this.graph.options.scales = {
        xAxes: [{
          ticks: {
            beginAtZero: true,
            max: 100,
            stepSize: 10
          }
        }],
        yAxes: [{
          ticks: {
            beginAtZero: true
          }
        }]
      } as any;
      this.graph.options.plugins = {
        datalabels: {
          display: false
        }
      } as any;
      if (this.showPercentage) {
        this.graph.options.plugins = {
          datalabels: {
            display: true,
            align: 'center',
            anchor: 'center',
            formatter: (value, ctx) => {
              return value + '%'; 
            },
            color: '#000',
            font: {
              weight: 'bold',
              size: 7 // Reduzindo o tamanho da fonte para ficar dentro da barra
            }
          }
        } as any;
      } else {
        this.graph.options.plugins = {
          datalabels: {
            display: false
          }
        } as any;
      }
    }

    if(chartType === 'bar'){
      this.graph.options.plugins = {
        datalabels: {
          display: false
        }
      } as any;
      if (this.showPercentage) {
        this.graph.options.plugins = {
          datalabels: {
            display: true,
            align: 'center',
            anchor: 'center',
            formatter: (value, ctx) => {
              return value + '%'; 
            },
            color: '#000',
            font: {
              weight: 'bold',
              size: 7 // Reduzindo o tamanho da fonte para ficar dentro da barra
            }
          }
        } as any;
      } else {
        this.graph.options.plugins = {
          datalabels: {
            display: false
          }
        } as any;
      }
    }

    this.loadingService.hide();
  }

  calculateGraphConsolidado(date: string){
    /*
    Regra de Unificação
    N1
    IPE = IPA + IPV + IPH + IPT

    N2
    CSO = CSA + CSP
    JOG = JOR + JOI
    IPE = IPA + IPV + IPH + IPT

    N3
    CSO = CAP
    IPE = IPE + IPH + IPT

    Consolidando
    IPE = IPE + IPA + IPV + IPH + IPT
    CSO = CSO + CSA + CSP + CAP
    JOG = JOG + JOR + JOI
    */
    const chartType = this.selectedChartType as 'radar' | 'bar' | 'horizontalBar';
    this.graph.chartType = chartType;
    //this.graph.datasets = [];
    this.graph.labels = [];
    //let datasets: ChartDataSets[] = [];
    let data: number[] = [];

    let n, p, a;

    let ipeCalc = false;
    let csoCalc = false;
    let jogCalc = false;

    //Busco a data selecionada nos checklists
    let index = this.esdmchecklists.map(function(e) { return new Date(e.data).toLocaleDateString('pt-BR'); }).indexOf(date)

    //Inicio do cálculo
    /*this.dominioMap.get(this.nivel.id).forEach(dom => {
      if( (dom.id == 'IPE' || dom.id == 'IPA' || dom.id == 'IPV' || dom.id == 'IPH' || dom.id == 'IPT') && ipeCalc == false ) {
        ipeCalc = true;

        this.graph.labels.push('IPE'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'A')).length

        console.log("Domínio: " + dom.id + " (" + 
          (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))
           + ")")
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'CSO' || dom.id == 'CSA' || dom.id == 'CSP' || dom.id == 'CAP') && csoCalc == false ) {
        csoCalc = true;

        this.graph.labels.push('CSO'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
              (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'A')).length

        console.log("Domínio: " + dom.id + " (" + (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) + ")")
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'JOG' || dom.id == 'JOR' || dom.id == 'JOI') && jogCalc == false ) {
        jogCalc = true;

        this.graph.labels.push('JOG'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
              (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'A')).length

        console.log("Domínio: " + dom.id + " (" + (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) + ")")
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );

      } else if ( (dom.id != 'IPE' && dom.id != 'IPA' && dom.id != 'IPV' && dom.id != 'IPH' && dom.id != 'IPT') 
                  && (dom.id != 'CSO' && dom.id != 'CSA' && dom.id != 'CSP' && dom.id != 'CAP')
                  && (dom.id != 'JOG' && dom.id != 'JOR' && dom.id != 'JOI') ) { //Caso não seja um domínio a ser consolidado
        this.graph.labels.push(dom.id); //Incluindo o label do domínio
  
        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          ( //chklst.competencia.id_nivel==this.nivel.id && 
            chklst.competencia.id_dominio == dom.id && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            chklst.competencia.id_dominio == dom.id && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (//chklst.competencia.id_nivel==this.nivel.id && 
            chklst.competencia.id_dominio == dom.id && chklst.valor == 'A')).length
  
        //let x = this.esdmchecklists[index].checklist.filter(chklst => 
        //  (chklst.competencia.id_nivel==this.nivel.id && chklst.competencia.id_dominio == dom.id && (chklst.valor == 'X' || chklst.valor == undefined))).length

        console.log("Domínio: " + dom.id + " (" + (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) + ")")
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      }
      
    })
    */
    data = this.doGraphCal(date, 'N1')
    this.graph.datasets.push({ label:  new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR'), data: data});

    this.organizeDatasets(chartType);

    //pos = myArray.map(function(e) { return e.hello; }).indexOf('stevie');

    //console.log("IndexOf:" + this.graph.datasets.map(function(e) { return e.label; }).indexOf(new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR')))
    this.graph.labels = [...this.graph.labels];
    this.graph.options = {
      scales: {
        yAxes: [{
          ticks: {
            beginAtZero: true,
            max: 100,
            stepSize: 10
          }
        }]
      },
      responsive: true,
      legend: {
        position: 'top',
      },
      tooltips: {
        enabled: true,
        mode: 'single',
        callbacks: {
          label: function (tooltipItems, data) {
            return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
          }
        }
      },
    };

    if (chartType === 'horizontalBar') {
      this.graph.options.scales = {
        xAxes: [{
          ticks: {
            beginAtZero: true,
            max: 100,
            stepSize: 10
          }
        }],
        yAxes: [{
          ticks: {
            beginAtZero: true
          }
        }]
      } as any;
      this.graph.options.plugins = {
        datalabels: {
            display: false
        }
      } as any;
      if (this.showPercentage) {
        this.graph.options.plugins = {
          datalabels: {
            display: true,
            align: 'center',
            anchor: 'center',
            formatter: (value, ctx) => {
              return value + '%'; 
            },
            color: '#000',
            font: {
              weight: 'bold',
              size: 7 // Reduzindo o tamanho da fonte para ficar dentro da barra
            }
          }
        } as any;
      } else {
        this.graph.options.plugins = {
          datalabels: {
            display: false
          }
        } as any;
      }
    }

    if(chartType === 'bar'){
      this.graph.options.plugins = {
        datalabels: {
            display: false
        }
      } as any;
      if (this.showPercentage) {
        this.graph.options.plugins = {
          datalabels: {
            display: true,
            align: 'center',
            anchor: 'center',
            formatter: (value, ctx) => {
              return value + '%'; 
            },
            color: '#000',
            font: {
              weight: 'bold',
              size: 7 // Reduzindo o tamanho da fonte para ficar dentro da barra
            }
          }
        } as any;
      } else {
        this.graph.options.plugins = {
          datalabels: {
            display: false
          }
        } as any;
      }
    }
    
    //this.graph.datasets = datasets;
  }

  updateChart() {
    let selected: string[] = [];
    this.graph.datasets.forEach(dataset => {
      selected.push(dataset.label.replace("*", ""));
    })
    selected.forEach(label =>{
      this.removeChecklistFromGraph(label);
      if(this.nivel.id == 'C'){
        this.calculateGraphConsolidado(label);
      } else {
        this.calculateGraphNivel(label, this.nivel.id);
      }
    })
  }

  removeChecklistFromGraph(data: string){
    let datasets
    let dtAvaliacao;
    let i;
    let index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data);
    if(index == -1){
      index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data + "*");
    }
    
    i = this.esdmchecklists.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == data);
    this.avParcMap.delete(this.esdmchecklists[i].id);

    this.graph.datasets.splice(index, 1);
    datasets = [...this.graph.datasets];
    datasets.sort(function(a, b) {
      if( a.data < b.data) {
        return 1;
      } else {
        return -1;
      }
    });
    for(const[,dataset] of datasets.sort(function(a, b) {
      if( a.data < b.data) {
        return 1;
      } else {
        return -1;
      }
    }).entries()){ {
      dtAvaliacao = dataset.label;
      index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(dtAvaliacao);
      this.graph.datasets.splice(index, 1);
      let i = dtAvaliacao.replace("*", "");
      this.calculateGraphNivel(i, this.nivel.id);
    }
    }
    this.loadingService.hide();
  }

  isAvaliacaoParcial(idAvaliacao: string){
    let internalDominioParc = this.avParcMap.get(idAvaliacao);

    for (const value of internalDominioParc.values()) {
      if (value) {
        return true;
      }
    }
    return false;

  }

  haveAvaliacaoParcial() {
    for(const [idAvaliacao,v] of this.avParcMap.entries()){
      for (const value of this.avParcMap.get(idAvaliacao)) {
        if (value) {
          return true;
        }
      }
      return false;
    }

  }

  haveAvaliacaoNivelDominioParcial(idNivel: string, idDominio: string) {
    let internalDominioParc: Map<string, boolean> = new Map<string, boolean>();
    let parcial: boolean = false;
    
    for(const [idAvaliacao,v] of this.avParcMap.entries()){
      internalDominioParc = this.avParcMap.get(idAvaliacao);
      if (internalDominioParc.get(idNivel + idDominio)){
        parcial = true;
      }
    }
    return parcial;
  }

  setChecklist(event:MatCheckboxChange){
    this.loadingService.show();
    if(event.checked == false){
      this.removeChecklistFromGraph( event.source.name )
    } else {
      if(this.nivel.id == 'C'){
        this.calculateGraphConsolidado(event.source.name);
      } else {
        this.calculateGraphNivel(event.source.name, this.nivel.id);
      }
      //this.calculateGraphNivel( event.source.name );
    }
    this.loadingService.hide();
    //this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)
    //this.calculateGraphNivel();
  }

  setNivel(){
    // console.log("Nível: " + this.nivel.id)
    //Recuperando os checklists apresentados no gráfico
    let selected: string[] = [];
    this.graph.datasets.forEach(dataset => {
      selected.push(dataset.label.replace("*", ""));
    })
    selected.forEach(label =>{
      this.removeChecklistFromGraph(label);
      if(this.nivel.id == 'C'){
        this.calculateGraphConsolidado(label);
      } else {
        this.calculateGraphNivel(label, this.nivel.id);
      }
    })
    //this.calculateGraphNivel();
  }

  organizeDatasets(chartType: string) {
    if(chartType == 'horizontalBar'){
      this.graph.datasets.sort((a, b) => {
        const dateA = new Date(a.label.replace('*', '').trim());
        const dateB = new Date(b.label.replace('*', '').trim());
        
        return dateB.getTime() - dateA.getTime();
      });
    } else {
      this.graph.datasets.sort((a, b) => {
        const dateA = new Date(a.label.replace('*', '').trim());
        const dateB = new Date(b.label.replace('*', '').trim());
        
        return dateA.getTime() - dateB.getTime();
      });
    }
  }

  togglePercentage() {
    this.updateChart();
  }

}
