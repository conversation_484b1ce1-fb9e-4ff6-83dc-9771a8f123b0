import { Objetivo } from './../../objetivo/objetivo-model';
import { Estimulo } from './../../estimulo/estimulo-model';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { PlanointervencaovbmappService } from './../../planointervencaovbmapp/planointervencaovbmapp.service';
import { PICService } from './../../planopic/pic.service';
import { ColetadiariavbmappService } from './../coletadiariavbmapp.service';
import { ColetadiariaPICService } from './../coletadiariapic.service';
import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { ColetaDiariaVBMAPP } from './../coletadiariavbmapp-model';
import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from './../coletadiariapic-model';
import { PIC } from './../../planopic/pic-model';
import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Paciente } from './../../paciente/paciente-model';
import { Observable } from 'rxjs';
import { Component, OnInit, Input } from '@angular/core';
import { Etapa } from '../../etapa/etapa-model';
import { PlanoIntervencaoService } from '../../planointervencao/planointervencao.service';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { ColetadiariaService } from '../../coletadiaria/coletadiaria.service';
import { DatePipe } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ColetaDiaria } from '../../coletadiaria/coleta-diaria-model';

@Component({
  selector: 'app-coletadiariavbmapp-read',
  templateUrl: './coletadiariavbmapp-read.component.html',
  styleUrls: ['./coletadiariavbmapp-read.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ColetadiariavbmappReadComponent implements OnInit {

  @Input()
  $pacienteSearch: Observable<Paciente>;

  datasourceObjetivosPEI = new MatTableDataSource();

  datasourceObjetivosPIC = new MatTableDataSource();

  displayedColumnsObjs = ['index', 'expand', 'etapas', 'nomeObj']

  displayedColumnsObjsPIC = ['index', 'expand', 'compAlvoObj', 'defOperacionalObj', 'tipoColetaObj']

  // displayedColumnsObjs = ['index', 'expand', 'etapas', 'nomeObj', 'tipoColeta', 'dominio', 'tipoSuporte', 'estimulos']

  paciente: Paciente = new Paciente();

  public hideRows: Map<number, Map<string, boolean>>;

  public PEIs: any[];

  public PICs: PIC[];
  
  public pei: any;

  public pic: PIC;

  public coletasDiariasPIC: SessaoColetaDiariaPIC[];

  public planoSelecionado: string = 'PIC';

  public coletasDiarias: ColetaDiariaVBMAPP[];

  public intervaloForm: FormGroup;
  private previousStartDate: string | null = null;
  private previousEndDate: string | null = null;
  public minDate: Date;
  public maxDate: Date = new Date(); 

  //             tipoSuporte.id     data    TipoSuporte[]
  public etapaMap: Map<string, Map<string, TipoSuporte[]>> = new Map<string, Map<string, TipoSuporte[]>>();

  public isTableExpanded = false;

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;
  public hasAccessCreatePlano: boolean;
  
  shownHeaders: Set<number> = new Set<number>(); // Controle para cabeçalhos exibidos

  ultimaColetaVBMAPP: any | null = null;
  ultimaColetaESDM: any | null = null;
  ultimaColetaPIC: any | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private datePipe: DatePipe,
    public authService: AuthService,
    private loadingService: LoadingService,
    private PEIService: PlanointervencaovbmappService,
    private PEIESDMService: PlanoIntervencaoService,
    private PICService: PICService,
    private coletaDiariaService: ColetadiariavbmappService,
    private coletaDiariaESDMService: ColetadiariaService,
    private coletaDiariaPICService: ColetadiariaPICService
  ) {
    this.intervaloForm = this.fb.group({
      data: this.fb.group({
        start: [null, Validators.required],
        end:   [null, Validators.required]
      }, { validators: this.dateRangeValidator })
    });
  }

  async ngOnInit(): Promise<void> {
    //Atribuindo o paciente vindo por parâmetro
    this.$pacienteSearch.subscribe(async paciente => {
      this.paciente = paciente; 

      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','create');
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','update');
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','read');
      this.hasAccessCreatePlano = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Plano de Intervenção','create');
      
      //Recupero os planos de intervenção do paciente
      const planos = await this.PEIService.findResumoByPaciente(this.paciente.id).toPromise()
      const esdms = await this.PEIESDMService.findResumoByPaciente(this.paciente.id).toPromise()
      const planosPIC = await this.PICService.findResumoByPaciente(this.paciente.id).toPromise()
      
      this.PEIs = [
        ...planos.filter(p => p.status !== false && p.ativo !== false),
        ...esdms.filter(p => p.status !== false
      )];
          
      this.PEIs.sort((a, b) => {
        const dateA = new Date(a.data);
        const dateB = new Date(b.data);
        return dateB.getTime() - dateA.getTime();
      });

      this.PICs = planosPIC.filter(p => p.status !== false && p.ativo !== false);
          
      //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
      if(this.PEIs.length > 0) {
        this.planoSelecionado = 'PEI';
        const lastPlanointervencao = this.PEIs[0];
        // console.log(this.PEI)

        await this.findPlanoPEISelecionado(lastPlanointervencao.id);

        //Seto os objetivos do plano atual para serem visualizados
        this.datasourceObjetivosPEI.data = this.pei.objetivos;
      }
      
      //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
      if(this.PICs.length > 0) {
        this.pic = this.PICs[0];

        await this.findPlanoPICSelecionado(this.pic.id);

        //Seto os objetivos do plano atual para serem visualizados
        this.datasourceObjetivosPIC.data = this.pic.objetivos;
      }

      await this.findLastColeta();
    })
  }

  async findLastColeta(planoTipo?: 'PEI' | 'PIC'): Promise<void> {
    const datasValidas: number[] = [];

    // 1. Buscar coletas conforme o tipo de plano
    if (!planoTipo || planoTipo === 'PEI') {
      this.ultimaColetaVBMAPP = await this.coletaDiariaService.findLastByPlanoIntervencao(this.pei?.id).toPromise();
      this.ultimaColetaESDM = await this.coletaDiariaESDMService.findLastByPlanoIntervencao(this.pei?.id).toPromise();
    }

    if (!planoTipo || planoTipo === 'PIC') {
      this.ultimaColetaPIC = await this.coletaDiariaPICService.findLastByPIC(this.pic?.id).toPromise();
    }

    // 2. Define a data mínima com base na data de início do PEI ou PIC
    if (this.pei?.data) {
      datasValidas.push(new Date(this.pei.data).getTime());
    }
    if (this.pic?.data) {
      datasValidas.push(new Date(this.pic.data).getTime());
    }

    this.minDate = datasValidas.length > 0 ? new Date(Math.min(...datasValidas)) : new Date();

    // 3. Determinar a última data de coleta entre todas as fontes
    const datas = [
      new Date(this.ultimaColetaVBMAPP?.data || 0),
      new Date(this.ultimaColetaESDM?.data || 0),
      new Date(this.ultimaColetaPIC?.data || 0)
    ];

    const ultimaDataValida = datas.reduce((max, d) => d > max ? d : max, new Date(0));
    if (ultimaDataValida.getTime() === 0) return; // Nenhuma coleta encontrada

    // 4. Calcular intervalo de 30 dias retroativos
    const dataFinal = new Date(ultimaDataValida);
    const dataInicial = new Date(dataFinal);
    dataInicial.setDate(dataInicial.getDate() - 30);

    // 5. Ajustar se data inicial for menor que o início do plano
    if (dataInicial < this.minDate) {
      dataInicial.setTime(this.minDate.getTime());
    }

    // 6. Atualizar o formulário
    this.intervaloForm.get('data.start')?.setValue(dataInicial);
    this.intervaloForm.get('data.end')?.setValue(dataFinal);

    // 7. Formatar datas para API
    const dataInicialFormatada = this.datePipe.transform(dataInicial, 'yyyy-MM-dd')!;
    const dataFinalFormatada = this.datePipe.transform(dataFinal, 'yyyy-MM-dd')!;

    // 8. Buscar coletas dentro do intervalo
    if (this.pei?.id) {
      this.findColetasPeiPorPeriodo(this.pei.id, dataInicialFormatada, dataFinalFormatada);
    }
    if (this.pic?.id) {
      this.findColetasPicPorPeriodo(this.pic.id, dataInicialFormatada, dataFinalFormatada);
    }
  }

  async findColetasPeiPorPeriodo(idPlano: string, dataInicial: string, dataFinal: string): Promise<void> {
    // 1. Buscar coletas de ambos os tipos
    const [coletasVBMAPP, coletasESDM] = await Promise.all([
      this.coletaDiariaService.findResumoByPlanoIntervencaoPeriodo(idPlano, dataInicial, dataFinal).toPromise(),
      this.coletaDiariaESDMService.findResumoByPlanoIntervencaoPeriodo(idPlano, dataInicial, dataFinal).toPromise()
    ]);

    // 2. Concatenar as coletas se houver ESDM
    this.coletasDiarias = [
      ...coletasVBMAPP,
      ...(coletasESDM ?? []) as ColetaDiariaVBMAPP[]
    ];

    // 3. Ordenar coletas por data (desc) e sessão (desc)
    this.coletasDiarias.sort((a, b) => {
      const dataA = new Date(a.data).getTime();
      const dataB = new Date(b.data).getTime();

      if (dataA !== dataB) return dataB - dataA;
      return (b.sessao || '').localeCompare(a.sessao || '');
    });

    // 4. Atualizar mapas e dados auxiliares
    await this.setMapToogleColetas(this.pei.objetivos);
    await this.setMapEtapasporData(coletasESDM);
  }

  async findColetasPicPorPeriodo(idPlano: string, dataInicial: string, dataFinal: string): Promise<void> {
    // 1. Buscar coletas do PIC no intervalo
    const coletasPIC = await this.coletaDiariaPICService
      .findByPICPeriodo(idPlano, dataInicial, dataFinal)
      .toPromise();

    // 2. Garantir array válido e ordenar por data e sessão
    this.coletasDiariasPIC = (coletasPIC ?? []).sort((a, b) => {
      const dataA = new Date(a.data).getTime();
      const dataB = new Date(b.data).getTime();

      if (dataA !== dataB) return dataB - dataA;
      return (b.sessao || '').localeCompare(a.sessao || '');
    });
  }

  dateRangeValidator(group: FormGroup): { [key: string]: boolean } | null {
    const start: Date = group.get('start')?.value;
    const end: Date = group.get('end')?.value;

    if (start && end && start > end) {
      return { rangeInvalid: true };
    }
    return null;
  }

  onDateRangeOpened(): void {
    const start = this.intervaloForm.get('data.start')?.value;
    const end = this.intervaloForm.get('data.end')?.value;

    this.previousStartDate = start ? this.formatDate(start) : null;
    this.previousEndDate = end ? this.formatDate(end) : null;
  }

  onDateRangeSelected(): void {
    const start: Date | null = this.intervaloForm.get('data.start')?.value || null;
    let end: Date | null = this.intervaloForm.get('data.end')?.value || null;

    if (!start) return;

    // Se só foi selecionada a data inicial, define a final como hoje
    if (!end) {
      end = new Date();
      this.intervaloForm.get('data.end')?.setValue(end);
    }

    const startStr = this.formatDate(start);
    const endStr = this.formatDate(end!);

    // Verifica se houve mudança
    if (startStr === this.previousStartDate && endStr === this.previousEndDate) {
      return; // Não faz nada, pois não houve mudança
    }

    // Executa busca se houve alteração
    if (this.pei?.id) {
      this.findColetasPeiPorPeriodo(this.pei.id, startStr, endStr);
    }
    if (this.pic?.id) {
      this.findColetasPicPorPeriodo(this.pic.id, startStr, endStr);
    }
  }

  formatDate(date: Date): string {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  }

  async findPlanoPEISelecionado(idPlanoSelecionado: any){ 
    this.pei = await this.PEIService.findById(idPlanoSelecionado).toPromise();
    if (!this.pei) {
      this.pei = await this.PEIESDMService.findById(idPlanoSelecionado).toPromise();
    }
    let idx = this.PEIs?.findIndex(p => p.id === idPlanoSelecionado);
    this.PEIs[idx] = this.pei;
  }

  async findPlanoPICSelecionado(idPlanoPICSelecionado: any){
    this.pic = await this.PICService.findById(idPlanoPICSelecionado).toPromise();
    let idx = this.PICs?.findIndex(p => p.id === idPlanoPICSelecionado);
    this.PICs[idx] = this.pic;
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    // console.log(funcoes)


    return funcoes;
  }

  getEtapaMapKeys(etapa: TipoSuporte){
    return [etapa.id];
  }

  hasColetasEtapa(idEtapa: string): boolean{
    let m: Map<string, TipoSuporte[]>;
    let has = false;
    let keys: string[];
    // console.log("idEtapa: " + idEtapa);
    m = this.etapaMap.get(idEtapa);
    if(m == undefined){
      // console.log("m udefined");
      return false;
    } else {
      keys = Array.from(m.keys());
      keys.forEach(key => {
        m.get(key).forEach(etapa => {
          if(etapa.status != "" && etapa.status != undefined){
            has = true;
          }
        })
      })
      return has;
    }
  }

  async setMapToogleColetas(objetivos: any[]){
    //console.log(objetivos);
    let m: Map<string, boolean>;
    this.hideRows = new Map<number, Map<string, boolean>>();
    for(const [,objetivo] of (objetivos ? objetivos.entries() : [])){
      if (objetivo.estimulos) {
        for(const [,estimulo] of objetivo.estimulos.entries()){
          if(this.hideRows.get(this.pei.objetivos.indexOf(objetivo)) == undefined) { //Não achei o objetivo
            m = new Map<string, boolean>();
            m.set(estimulo.id, true);
            this.hideRows.set(this.pei.objetivos.indexOf(objetivo), m);
          } else {
            m = this.hideRows.get(this.pei.objetivos.indexOf(objetivo));
            m.set(estimulo.id, true);
          }
        }
      } else if (objetivo.etapa) {
        for (const [,etapa] of objetivo.etapa.entries()) {
          if(this.hideRows.get(this.pei.objetivos.indexOf(objetivo)) == undefined) { //Não achei o objetivo
            m = new Map<string, boolean>();
            m.set(etapa.id, true);
            this.hideRows.set(this.pei.objetivos.indexOf(objetivo), m);
          } else {
            m = this.hideRows.get(this.pei.objetivos.indexOf(objetivo));
            m.set(etapa.id, true);
          }
        }
      }
    }
    // console.log(this.hideRows)
  }

  async setMapEtapasporData(coletas: any[]){
    // console.log(coletas);
    let m: Map<string, any[]>;
    let data: string;
    let etapas: any[];
    this.etapaMap = new Map<string, Map<string, any[]>>();
    for(const [,coleta] of coletas.entries()) { //coletas.forEach(coleta => {
      for(const [, objetivo] of coleta.objetivos.entries()) {//coleta.objetivos.forEach(objetivo => {
        if (!objetivo.habilidades) {
          for(const [,etapa] of objetivo.etapa.entries()) {//objetivo.etapa.forEach(etapa => {
            //console.log(coleta.data)
            data = new Date(coleta.data).getDate() + "/" + (new Date(coleta.data).getMonth() + 1) + "/" + new Date(coleta.data).getFullYear();
            //console.log(data)
            //Verifico se já tenho a etapa cadastrada
            if(this.etapaMap.get(etapa.id) == undefined){
              //Caso não tenha a etapa cadastrada, faço o cadastro já com a data corrente
              m = new Map<string, any[]>();
              m.set(data, [etapa])
              this.etapaMap.set(etapa.id, m);
            } else {
              //Caso já tenha a etapa no Map, recupero o map da etapa e vejo se a data está cadastrada
              m = this.etapaMap.get(etapa.id);
              if(m.get(data) == undefined) {
                //Se a data não está cadastrada, faço o cadastro da mesma
                m.set(data, [etapa])
                this.etapaMap.set(etapa.id, m)
              } else {
                //Se a data está cadastrada, pego o array de datas e faço um push para o novo status
                etapas = m.get(data)
                etapas.push(etapa)
                this.etapaMap.set(etapa.id, m);
              }
            }
          }
        } else {
          for(const [,etapa] of objetivo.tiposSuporte.entries()) {//objetivo.etapa.forEach(etapa => {
            for(const [,estimulo] of etapa.estimulos.entries()) {//objetivo.etapa.forEach(etapa => {
  
              //Compatibilidade com a versão de coleta Naturalista anterior, quando a coleta era feita por Tipo de Suporte x Estímulo e só tinha um período
              if(estimulo.positivos == undefined && estimulo.independentes == undefined){
                estimulo.positivos = estimulo.status == "Adquirido" ? 1 : 0; //etapa.estimulos.filter(e => e.status == "Adquirido").length;
                estimulo.negativos = estimulo.status == "Não adquirido" ? 1 : 0;//etapa.estimulos.filter(e => e.status == "Não adquirido").length;
                estimulo.percentual = estimulo.positivos/(estimulo.negativos + estimulo.positivos);
              }
  
              //console.log(coleta.data)
              data = new Date(coleta.data).getDate() + "/" + (new Date(coleta.data).getMonth() + 1) + "/" + new Date(coleta.data).getFullYear();
  
              //Verifico se já tenho a etapa cadastrada
              if(this.etapaMap.get(objetivo.id + "" + etapa.id + "" + estimulo.id) == undefined){
                //Caso não tenha a etapa cadastrada, faço o cadastro já com a data corrente
                m = new Map<string, TipoSuporte[]>();
                m.set(data, [etapa])
                this.etapaMap.set(objetivo.id + "" + etapa.id + "" + estimulo.id, m);
              } else {
                //Caso já tenha a etapa no Map, recupero o map da etapa e vejo se a data está cadastrada
                m = this.etapaMap.get(objetivo.id + "" + etapa.id + "" + estimulo.id);
                if(m.get(data) == undefined) {
                  //Se a data não está cadastrada, faço o cadastro da mesma
                  m.set(data, [etapa])
                  this.etapaMap.set(objetivo.id + "" + etapa.id + "" + estimulo.id, m)
                } else {
                  //Se a data está cadastrada, pego o array de datas e faço um push para o novo status
                  etapas = m.get(data)
                  etapas.push(etapa)
                  this.etapaMap.set(objetivo.id + "" + etapa.id + "" + estimulo.id, m);
                }
              }
            }
          }
        }
      }
    }
    // console.log(this.etapaMap)
  }

  getColetasObjetivoEtapa(idObjetivo: string, idTipoSuporte: string, idEstimulo: string): ColetaDiariaVBMAPP[]{
    let coletas: ColetaDiariaVBMAPP[] = [];
    for(const [,coleta] of this.coletasDiarias.entries()) { //this.coletasDiarias.forEach(coleta => {
      for(const [, objetivo] of coleta.objetivos.entries()) { //coleta.objetivos.forEach(objetivo => {
        if(objetivo.id == idObjetivo){
          for(const [, etapa] of objetivo.tiposSuporte.entries()) { //objetivo.etapa.forEach(etapa => {
            if(etapa.id == idTipoSuporte){
              for(const [, estimulo] of etapa.estimulos.entries()) { //objetivo.etapa.forEach(etapa => {
                if(estimulo.id == idEstimulo){
                  coletas.push(coleta)
                }
              }
            }
          }
        }
      }
    }
    return coletas;
  }

  async setPIC(){
    await this.findPlanoPICSelecionado(this.pic.id)
    try {
      //Seto os objetivos do plano atual para serem visualizados
      this.datasourceObjetivosPIC.data = [...this.pic.objetivos];
  
      await this.findLastColeta("PIC");

    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async setPEI(){
    await this.findPlanoPEISelecionado(this.pei.id)
    try {
      this.datasourceObjetivosPEI.data = [...this.pei.objetivos];
      //Recupero as coletas do plano atual
      await this.findLastColeta("PEI");

    } catch (error) {
      console.log(error);
    }
  }

  toggleTableRow(row: any){
    row.isExpanded = !row.isExpanded;
  }

  percentualEtapas(row: any){
    if (row?.etapa) {
      return this.countEtapasAdquiridasESDM(row) / this.countEtapasESDM(row)
    } else {
      return this.countEtapasAdquiridas(row) / this.countEtapas(row)
    }
  }

  countEtapas(row: any){
    if (row?.etapa) {
      return row.etapa?.length
    } else {
      return row.tiposSuporte.length
    }
  }

  countEtapasAdquiridas(row: any){
    if (row?.etapa) {
      return row.etapa?.filter(e => (e?.status == 'Adquirida')).length
    } else {
      return row.tiposSuporte.filter(e => (e.status=='Adquirido')).length
    }
  }

  countEtapasESDM(row: any){
    if(row?.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.length
    }
  }

  countEtapasAdquiridasESDM(row: any){
    if(row?.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.filter(e => (e?.status == 'Adquirida')).length
    }
  }

  changeTipoSuporteEstimuloStatus(estimulo: Estimulo, idObjetivo: string, idTipoSuporte: string){
    let obj: ObjetivoVBMAPP;
    let tipoSuporte: TipoSuporte;
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreatePlano){
      let objAdq: boolean;
      if(this.hasAccessUpdate){

        if(estimulo.status == undefined || estimulo.status == 'Não adquirido'){
          estimulo.status = "Adquirido"
        } else {
          estimulo.status = "Não adquirido"
        }
        //obj = this.planointervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
        obj = this.pei.objetivos.find(objetivo => objetivo.id == idObjetivo);
        tipoSuporte = obj.tiposSuporte.find(tipo => tipo.id == idTipoSuporte);
        
        //Verifico se todos os estímulos ativos foram adquiridos e, caso postivo, coloco o tipo de suporte como adquirido
        if (tipoSuporte.estimulos.find(e => e.ativo == true && e.status != "Adquirido") == undefined){
        tipoSuporte.status = "Adquirido";
        }  else {
        tipoSuporte.status = "Não adquirido";
        }

        //Verifico se todos os tipos de suporte foram adquiridos e, caso postivo, coloco o objetivo como adquirido
        if (obj.tiposSuporte.find(t => t.status != "Adquirido") == undefined){
          obj.status = "Adquirido";
        }  else {
          obj.status = "Não adquirido";
        }
        this.save(); 
      }
    }
  }

  changeTipoSuporteStatus(tipoSuporte: TipoSuporte, idObjetivo: string){
  let obj: ObjetivoVBMAPP;
  //Verifico se o usuário possui autorização de alterar o status da etapa
  if(this.hasAccessCreatePlano){
    let objAdq: boolean;
    if(this.hasAccessUpdate){
        if(tipoSuporte.status == undefined || tipoSuporte.status == 'Não adquirido'){
        tipoSuporte.status = "Adquirido"
        } else {
        tipoSuporte.status = "Não adquirido"
        }

        //Verifico se todos os tipos de suporte foram adquiridos e, caso postivo, coloco o objetivo como adquirido
        obj = this.pei.objetivos.find(objetivo => objetivo.id == idObjetivo);
        if (obj.tiposSuporte.find(t => t.status != "Adquirido") == undefined){
          obj.status = "Adquirido";
        }  else {
          obj.status = "Não adquirido";
        }
        this.save(); 
      }
    }
  }

  changeEtapaStatus(etapa: TipoSuporte){
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreatePlano){
      let objAdq: boolean;
      if(this.hasAccessUpdate){
        if(etapa.status == undefined || etapa.status == 'Não adquirido'){
          etapa.status = "Adquirido"
        } else {
          etapa.status = "Não adquirido"
        }
        //Verifico se todas as etapas do objetivo foram adquiridas e coloco o objetivo como "Adquirido"
        objAdq = true;
        for(const [,eta] of this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).tiposSuporte.entries()) { 
          // console.log(eta.id + " - " + eta.status)
          if(eta.status != "Adquirido"){
            // console.log("False")
            objAdq = false;
          }
        }
        
        if(objAdq == true){
          this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Adquirido"
        } else {
          this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Não adquirido";
        }
        this.save();
      }

    }
  }

  changeEtapaStatusESDM(etapa: Etapa){
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Plano de Intervenção','create')){
      let objAdq: boolean;
      if(this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Coleta de Dados','update')){
        if(etapa.status == undefined || etapa.status == 'Não adquirida'){
          etapa.status = "Adquirida"
        } else {
          etapa.status = "Não adquirida"
        }
        //Verifico se todas as etapas do objetivo foram adquiridas e coloco o objetivo como "Adquirido"
        objAdq = true;
        for(const [,eta] of this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).etapa.entries()) { 
          // console.log(eta.id + " - " + eta.status)
          if(eta.status != "Adquirida"){
            // console.log("False")
            objAdq = false;
          }
        }
        
        if(objAdq == true){
          this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Adquirido"
        } else {
          this.pei.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0,6)).status = "Não adquirido";
        }
        this.save();
      }

    }
  }

  save(): void{
    if (this.pei.habilidades != undefined){
      if(this.pei.id == undefined){
        this.PEIService.create(this.pei).subscribe((id) => {
          this.pei.id = id;
          // console.log(id)
          //this.PEIService.showMessage('Plano de intervenção criado com sucesso!');
          //this.router.navigate(['/paciente/' + id]);
        });
      } else {
        this.PEIService.update(this.pei).subscribe((paciente) => {
          //this.PEIService.showMessage('Plano de intervenção alterado com sucesso!');
          //this.router.navigate(['/paciente/' + paciente.id]);
        });
      }
    } else {
      if(this.pei.id == undefined){
        this.PEIESDMService.create(this.pei).subscribe((id) => {
          this.pei.id = id;
          this.loadingService.hide();
        });
      } else {
        this.PEIESDMService.update(this.pei).subscribe((paciente) => {
          this.loadingService.hide();
        });
      }
    }
  }

  add(){
    const peiTemObjetivos = Array.isArray(this.pei?.objetivos) && this.pei.objetivos.length > 0;
    const picTemObjetivos = Array.isArray(this.pic?.objetivos) && this.pic.objetivos.length > 0;

    //this.router.navigate(['/PEI/create', {   
    if(peiTemObjetivos) { 
      this.router.navigate(['/coletadiaria_vbmapp/' + this.pei.id])
    } else if (picTemObjetivos){
      this.router.navigate(['/coletadiaria_vbmapp/' + this.pic.id])
    } else {
      this.PEIService.showMessage('Nenhum objetivo disponível para coleta!', true);
    }
  }

  estimuloAdquirido(idxObjetivo: number, tipoSuporte: TipoSuporte, estimulo: Estimulo): boolean{
    return this.pei.objetivos[idxObjetivo]
      .tiposSuporte.find(t => t.id == tipoSuporte.id)
      .estimulos.find(e => e.id == estimulo.id).status == "Adquirido"
  }

  idxColeta(coleta: ColetaDiariaVBMAPP, idxObjetivo: number, tipoSuporte: TipoSuporte, estimulo: Estimulo): number {

    return this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]
      .tiposSuporte.find(t => t.status != undefined)
      .estimulos.find(e => (e.status != undefined) && (e.status != "") && (e.id == estimulo.id) )
      ).indexOf(coleta)
  }

  lengthColetasValidas(coleta: ColetaDiariaVBMAPP, idxObjetivo: number, tipoSuporte: TipoSuporte, estimulo: Estimulo): number {

    return this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]
      .tiposSuporte.find(t => t.status != undefined)
      .estimulos.find(e => (e.status != undefined) && (e.status != "") && (e.id == estimulo.id) )
      ).length
  }

  printColetasObjetivo(idxObjetivo: number, estimulo: Estimulo){
    let countColetas: Map< number, Map<string, number>> = new Map< number, Map<string, number>>();
    let m: Map<string, number> = new Map<string, number>();
    let cont: number;
    for(const [idx, o] of this.pei.objetivos.entries()){
      for(const [,e] of o.estimulos.entries()){
        if(countColetas.get(idx) == undefined){
          m = new Map<string, number>();
          m.set(e.nome, 0)
        } else {
          m = countColetas.get(idx);
          m.set(e.nome, 0);
        }
        countColetas.set(idx, m);
      }
    }
    
    if(this.coletasDiarias == undefined){
      return null;
    } else {
      for(const [, coleta] of this.coletasDiarias.entries()){
        for(const [idx, objetivo] of coleta.objetivos.entries()) {
          for(const [, tipo] of objetivo.tiposSuporte.entries()){
            for(const [, e] of tipo.estimulos.entries()){
              if( (e.status != undefined) && (e.status != "") /* && (e.id == estimulo.id)*/ ){
                m = countColetas.get(idx);
                cont = m.get(e.nome);
                cont = cont + 1;
                m.set(e.nome, cont);
                countColetas.set(idx, m);
              }
            }
          }
        }
      }
    }

    for(const [idx, o] of this.pei.objetivos.entries()){
      for(const [,e] of o.estimulos.entries()){
        // console.log(idx + " - " + e.nome + " - "+ countColetas.get(idx).get(e.nome));
      }
    }
  }
  
  getColetasPorObjetivo(objetivo: any, estimulo: Estimulo): ColetaDiariaVBMAPP[]{
    let manter = false;
    // Pego as coletas do objetivo solicitado
    if(this.coletasDiarias == undefined){
      return undefined;
    } else {
      // console.log("Coletas com o objetivo: " + col.length)
      let idxObjetivo: number;
      let col = this.coletasDiarias.filter(c => c.objetivos[c.objetivos.findIndex( o => o.nome == objetivo.nome)]);
      
      if(estimulo != null){
        // let col = this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]); 
        for(let i=0; i < col.length; i++){
        // for(const [, coleta] of col.entries()){
          idxObjetivo = col[i].objetivos.findIndex( o => o.nome == objetivo.nome);
          manter = false;
          
          for(const [, tipo] of col[i].objetivos[idxObjetivo].tiposSuporte.entries()){
            if(tipo.estimulos.filter(e => (e.status != undefined) && (e.status != "")  && (e.id == estimulo.id)).length > 0){
              manter=true;
            }
            
          }
          if(!manter){
            col.splice(col.indexOf(col[i]), 1);
            i--;
          }
              
        }
      } else {
        
        for(let i=0; i < col.length; i++){
          
          idxObjetivo = col[i].objetivos.findIndex( o => o.nome == objetivo.nome)
          manter = false;
          for(const [, tipo] of col[i].objetivos[idxObjetivo]?.tiposSuporte?.entries()){
            if(tipo.estimulos.filter(e => (e.status != undefined) && (e.status != "")).length > 0){
              manter=true;
            }
          }
          // console.log(idxObjetivo + " - " + manter)
          if(!manter){
            col.splice(col.indexOf(col[i]), 1);
            i--;
          }
        }
      }
      // console.log("Total após rotina: ",  col)
      return col;
    }
  }

  getColetasPorObjetivoESDM(objetivo: any, etapa: Etapa): any[]{
    let manter = false;
    // Pego as coletas do objetivo solicitado 
    if(this.coletasDiarias == undefined){
      return undefined;
    } else {
      // console.log("Coletas com o objetivo: " + col.length)
      let idxObjetivo: number;
      let col = this.coletasDiarias.filter(c => c.objetivos[c.objetivos.findIndex( o => o.id == objetivo.id)]) as any;

      if(etapa != null){
        for(let i=0; i < col.length; i++){
          idxObjetivo = col[i].objetivos.findIndex( o => o.id == objetivo.id);
          manter = false;
          
          for(const [, e] of col[i].objetivos[idxObjetivo]?.etapa?.entries()){
            if((e.status != undefined) && (e.status != "")  && (e.id == etapa.id)){ 
              manter=true;
            }
          }
          if(!manter){
            col.splice(col.indexOf(col[i]), 1);
            i--;
          }
              
        }
      } else {
        for(let i=0; i < col.length; i++){
          idxObjetivo = col[i].objetivos.findIndex( o => o.id == objetivo.id)
          manter = false;
          for(const [, e] of col[i].objetivos[idxObjetivo]?.etapa?.entries()){
            if((e.status != undefined) && (e.status != "")){
              manter=true;
            }
          }
          if(!manter){
            col.splice(col.indexOf(col[i]), 1);
            i--;
          }
        }
      }
      // console.log("Total após rotina: ",  col)
      return col;
    }
  }

  getColetasObjetivo(idxObjetivo: number, estimulo: Estimulo = null): ColetaDiariaVBMAPP[]{
    let manter = false;
    // Pego as coletas do objetivo solicitado
    if(this.coletasDiarias == undefined){
      return undefined;
    } else {
      let col = this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]); 

      if(estimulo != null){
        // let col = this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]); 
        for(let i=0; i < col.length; i++){
        // for(const [, coleta] of col.entries()){
          manter = false;
          // for(const [,objetivo] of coleta.objetivos.entries()){
            // if(this.PEI.objetivos[idxObjetivo].id != objetivo.id){
              // console.log("Entrou")
              // coleta.objetivos.splice(idxObjetivo, 1);
            // } else {
              for(const [, tipo] of col[i].objetivos[idxObjetivo].tiposSuporte.entries()){
                if(tipo.estimulos.filter(e => (e.status != undefined) && (e.status != "")  && (e.id == estimulo.id)).length > 0){
                  manter=true;
                }
                // if(tipo.status == undefined){
                //   coleta.objetivos[idxObjetivo].tiposSuporte.splice(
                //     coleta.objetivos[idxObjetivo].tiposSuporte.indexOf(tipo), 1
                //   )
                // } 
                // else {
                //   for(const [, e] of tipo.estimulos.entries()){
                //     if( !((e.status != undefined) && (e.status != "")  && (e.id == estimulo.id)) ){
                //       tipo.estimulos.splice(tipo.estimulos.indexOf(e), 1);
                //     }
                //   }
                // }
              }
              if(!manter){
                col.splice(col.indexOf(col[i]), 1);
                i--;
              }
            // }
          // }
        }
      } else {
        for(let i=0; i < col.length; i++){
        // for(const [, coleta] of col.entries()){
          // console.log(col[i].id)
          // console.log(col[i].data)
          // console.log(col[i].objetivos[idxObjetivo].nome)
          // console.log(col[i].objetivos[idxObjetivo].habilidades)
          // console.log((col[i].objetivos[idxObjetivo] as any).etapa)
          manter = false;
          for(const [, tipo] of col[i].objetivos[idxObjetivo]?.tiposSuporte?.entries()){
            if(tipo.estimulos.filter(e => (e.status != undefined) && (e.status != "")).length > 0){
              manter=true;
            }
          }
          // console.log(idxObjetivo + " - " + manter)
          if(!manter){
            col.splice(col.indexOf(col[i]), 1);
            i--;
          }
        }
      }
      // console.log("Total após rotina: " + col.length)
      return col;
    }


    // if(this.coletasDiarias == undefined){
    //   return null;
    // } else {
    //   return this.coletasDiarias.filter(c => c.objetivos[idxObjetivo]
    //     .tiposSuporte.find(t => t.status != undefined)
    //     .estimulos.find(e => (e.status != undefined) && (e.status != "")  && (e.id == estimulo.id) )
    //     );
    // }
  }

  saveToPDF() {
    if (this.pei.objetivos.length > 0) {
      this.router.navigate([]).then(result => {
        window.open('coletadiaria/pdf/' + this.pei.id + "?hasFullView=true",'_blank');
      })
    } else {
      this.router.navigate([]).then(result => {
        window.open('coletadiaria/pdf/' + this.pic.id + "?hasFullView=true",'_blank');
      })
    }
  }

  getStateColetaRow(idObjetivo: number, idEstimulo: string): boolean{
    // console.log(idObjetivo)
    if(this.hideRows == undefined){
      return true;
    } else {
      return this.hideRows?.get(idObjetivo)?.get(idEstimulo);
    }
  }

  toogleColetaRows(idObjetivo: number, idEstimulo: string){
    // console.log(idObjetivo + " - " + idEstimulo)
    let toogle = !this.hideRows.get(idObjetivo).get(idEstimulo);
    this.hideRows.get(idObjetivo).set(idEstimulo, toogle);
  }

  isQtdOrDuracao(element: any): string {
    // Retorna 'qtd' se tiver quantidade, caso contrário 'duracao'
    return element.tipoColeta == 'Registro de Eventos' ? 'qtd' : 'duracao';
  }

  hasManualDuration(coleta: any): boolean {
    return coleta.duracoes.some((d: any) => d.manual);
  }

  hasIndependentes(coletas: any[]): boolean {
    return coletas.some(coleta =>
      coleta.objetivos?.some(obj =>
        obj.tiposSuporte?.some(ts =>
          ts.estimulos?.some(est => est.independentes != null && est.independentes !== 0)
        )
      )
    );
  }

  graphPEI(){
    if (this.pei?.idTipoAvaliacao != 5) {
      window.open('vbmapp_planointervencao/evolucao/' + this.pei.id + "?hasFullView=true",'_blank');
    } else {
      this.router.navigate([]).then(result => {
        window.open('planointervencao/evolucao/' + this.pei.id + "?hasFullView=true",'_blank');
      })
    }
  }
  
  graphPIC(){
    this.loadingService.reset();
    window.open('pic/evolucao/' + this.pic.id + "?hasFullView=true",'_blank');
  }
  
}
