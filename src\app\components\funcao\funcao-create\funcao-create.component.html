<mat-card> 
    <mat-card-title class="title">{{ funcao.id == undefined ? "Novo função" : funcao.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <mat-form-field style="width: 60%; padding: 10px">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="funcao.nome" name="nome" required>
                <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>  
            </mat-form-field>
        </div>
    </form>



    <mat-card class="mat-elevation-z0"  style="margin-top: 10px; width: 100%;">
        <mat-card-title class="subtitle">Permissões</mat-card-title>
        <!-- In<PERSON>cio da listagem de permissões -->
        <div class="mat-elevation-z0">
            <table>
                <tr>
                    <th></th>
                    <th>Visualizar</th>
                    <th>Incluir</th>
                    <th>Atualizar</th>
                    <th>Excluir</th>
                </tr>
                <ng-container *ngFor="let area of permissaoAreas">
                    <tr class="header">
                        <td colspan="5" class="header"><strong>{{ area.nome }}</strong></td>
                    </tr>
                    <ng-container *ngFor="let funcionalidade of area.funcionalidades">
                        <tr>
                            <td class="rowNome" style="width:40%">{{ funcionalidade.nome }}</td>
                            <td class="rowCRUD" style="width:15%">
                                <!-- READ -->
                                <mat-form-field  style="width: 55%;">
                                    <mat-select 
                                        [(ngModel)]="funcao.permission == undefined ? '' : funcao.permission[area.nome + '.' + funcionalidade.nome]['read']" 
                                        name="read">
                                        <mat-option value="X" >
                                            Sem acesso
                                        </mat-option>
                                        <mat-option value="P" >
                                            Parcial
                                        </mat-option>
                                        <mat-option value="T" >
                                            Total
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </td>
                            <td class="rowCRUD" style="width:15%">
                                <!-- CREATE -->
                                <mat-form-field  style="width: 55%;">
                                    <mat-select 
                                        [(ngModel)]="funcao.permission == undefined ? '' : funcao.permission[area.nome + '.' + funcionalidade.nome]['create']" 
                                        name="read">
                                        <mat-option value="X" >
                                            Sem acesso
                                        </mat-option>
                                        <mat-option value="P" >
                                            Parcial
                                        </mat-option>
                                        <mat-option value="T" >
                                            Total
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </td>
                            <td class="rowCRUD" style="width:15%">
                                <!-- UPDATE -->
                                <mat-form-field  style="width: 55%;">
                                    <mat-select 
                                        [(ngModel)]="funcao.permission == undefined ? '' : funcao.permission[area.nome + '.' + funcionalidade.nome]['update']" 
                                        name="read">
                                        <mat-option value="X" >
                                            Sem acesso
                                        </mat-option>
                                        <mat-option value="P" >
                                            Parcial
                                        </mat-option>
                                        <mat-option value="T" >
                                            Total
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </td>
                            <td class="rowCRUD" style="width:15%">
                                <!-- DELETE -->
                                <mat-form-field  style="width: 55%;">
                                    <mat-select 
                                        [(ngModel)]="funcao.permission == undefined ? '' : funcao.permission[area.nome + '.' + funcionalidade.nome]['delete']" 
                                        name="read">
                                        <mat-option value="X" >
                                            Sem acesso
                                        </mat-option>
                                        <mat-option value="P" >
                                            Parcial
                                        </mat-option>
                                        <mat-option value="T" >
                                            Total
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </td>
                        </tr>
                    </ng-container>
                </ng-container>
            </table>


            <!--table mat-table [dataSource]="datasource">  

                <ng-container matColumnDef="nome">
                    <th mat-header-cell *matHeaderCellDef>Nome</th>
                    <td mat-cell *matCellDef="let row" >{{row}}</td>
                </ng-container>

                <ng-container matColumnDef="read">
                    <th mat-header-cell *matHeaderCellDef>Visualizar</th>
                    <td mat-cell *matCellDef="let row" >
                        <mat-form-field  style="width: 55%;">
                            <mat-select 
                                [ngModel]="funcao.permission.get(row).get('read')"
                                (ngModelChange) = "funcao.permission.get(row).set('read',$event)"
                                name="read">
                                <mat-option value="x" >
                                    Sem acesso
                                </mat-option>
                                <mat-option value="p" >
                                    Parcial
                                </mat-option>
                                <mat-option value="t" >
                                    Total
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </td>
                </ng-container>

                <ng-container matColumnDef="create">
                    <th mat-header-cell *matHeaderCellDef>Incluir</th>
                    <td mat-cell *matCellDef="let row" >
                        <mat-form-field  style="width:55%;">
                            <mat-select 
                                [ngModel]="funcao.permission.get(row).get('create')"
                                (ngModelChange) = "funcao.permission.get(row).set('create',$event)"
                                name="read">
                                <mat-option value="x" >
                                    Sem acesso
                                </mat-option>
                                <mat-option value="p" >
                                    Parcial
                                </mat-option>
                                <mat-option value="t" >
                                    Total
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </td>
                </ng-container>

                <ng-container matColumnDef="update">
                    <th mat-header-cell *matHeaderCellDef>Alterar</th>
                    <td mat-cell *matCellDef="let row" >
                        <mat-form-field  style="width:55%;">
                            <mat-select 
                                [ngModel]="funcao.permission.get(row).get('update')"
                                (ngModelChange) = "funcao.permission.get(row).set('update',$event)"
                                name="read">
                                <mat-option value="x" >
                                    Sem acesso
                                </mat-option>
                                <mat-option value="p" >
                                    Parcial
                                </mat-option>
                                <mat-option value="t" >
                                    Total
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </td>
                </ng-container>

                <ng-container matColumnDef="delete">
                    <th mat-header-cell *matHeaderCellDef>Excluir</th>
                    <td mat-cell *matCellDef="let row" >
                        <mat-form-field style="width: 55%;">
                            <mat-select 
                                [ngModel]="funcao.permission.get(row).get('delete')"
                                (ngModelChange) = "funcao.permission.get(row).set('delete',$event)"
                                name="read">
                                <mat-option value="x" >
                                    Sem acesso
                                </mat-option>
                                <mat-option value="p" >
                                    Parcial
                                </mat-option>
                                <mat-option value="t" >
                                    Total
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </td>
                </ng-container>
          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table-->
          </div>        
        <!-- Fim da listagem de permissões -->

    <button mat-raised-button (click)="save()" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>