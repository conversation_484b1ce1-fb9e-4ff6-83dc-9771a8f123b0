import { Component, OnInit } from '@angular/core';
import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-estimulo-category-crud',
  templateUrl: './estimulo-category-crud.component.html',
  styleUrls: ['./estimulo-category-crud.component.css']
})
export class EstimuloCategoryCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Categorias de Estímulo',
        icon: 'library_books',
        routeUrl: '/estimulo/category'
      }
    }

  ngOnInit(): void {
  }

}
