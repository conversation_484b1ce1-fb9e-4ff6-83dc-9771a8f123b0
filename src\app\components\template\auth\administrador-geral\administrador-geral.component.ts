import { Router } from '@angular/router';
import { FirebaseUserModel } from './../user-model';
import { ProfissionalService } from './../../../profissional/profissional.service';
import { AuthService } from './../auth.service';
import { MatTableDataSource } from '@angular/material/table';
import { Profissional } from './../../../profissional/profissional-model';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-administrador-geral',
  templateUrl: './administrador-geral.component.html',
  styleUrls: ['./administrador-geral.component.css']
})
export class AdministradorGeralComponent implements OnInit {

  adminUsers: Profissional[] = [];
  users: FirebaseUserModel[] = [];
  user: FirebaseUserModel;

  datasourceAdmins = new MatTableDataSource();


  displayedColumns = ['nome', 'email', 'admin', 'superadmin', 'gsupervisor', 'organization', 'action']

  constructor(private authService: AuthService,
    //private pessoaService: PessoaService
    private router: Router,
    private profissionalService: ProfissionalService
    ) { }
//TODO: Incluir o nome da organização ao criar um novo usuário.
  async ngOnInit(): Promise<void> { 

    //Recupero todo os usuários
    await this.authService.listUsers().subscribe(async users => {
      for(let user of users) {
        this.user = new FirebaseUserModel();
        this.user.image = user.photoURL;
        this.user.name = user.displayName;
        this.user.provider = user.providerData[0].providerId;
        this.user.email = user.email;
        this.user.uid = user.uid;
        this.user.gsupervisor = user.customClaims ? user.customClaims.gsupervisor : false;
        this.user.admin = user.customClaims ? user.customClaims.admin : false;
        this.user.organizationId = user.customClaims ? user.customClaims.organization : "";
        this.user.superadmin = user.customClaims ? user.customClaims.superadmin : false;
        this.user.familia = user.customClaims ? user.customClaims.familia : false;
        this.user.profissional = user.customClaims ? user.customClaims.profissional : false;
        
        this.users.push(this.user);
      }

      this.datasourceAdmins.data = this.users.sort(function(a, b) {
        if( a.organizationId+a.name > b.organizationId+b.name) {
          return 1;
        }
        if (a.organizationId+a.name < b.organizationId+b.name) {
          return -1;
        }
        return 0;
      });
    })
  }

  edit(user){
    this.router.navigate(['/admin/users/'+user.uid]);
  }
  do(){

  }

}
