<mat-card>
    <mat-card-header>
        <mat-card-title>Status: {{statusAgenda.nome}}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field style="width: 50%;">
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="statusAgenda.nome" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field> 

                <mat-form-field style="width: 30%; margin-left: 15px;">
                    <mat-label>Autoriza Cobrança</mat-label>
                    <mat-select [(ngModel)]="statusAgenda.confirmaCobranca" name="confirmaCobranca">
                        <mat-option [value]="true">Sim</mat-option>
                        <mat-option [value]="true">Não</mat-option>
                    </mat-select>
                </mat-form-field> 

                <mat-form-field style="width: 5%; margin-left: 15px;">
                    <input type="color" matInput placeholder="Cor Agenda" 
                        [(ngModel)]="statusAgenda.cor" name="cor">
                </mat-form-field> 
                <div>
                    <mat-checkbox  placeholder="Status Inicial do atendimento" 
                        [(ngModel)]="statusAgenda.default" name="default">
                    </mat-checkbox>  
                    <mat-label>
                        Status inicial do atendimento
                    </mat-label>
                </div>
                              
            </form>
        </div>
    </mat-card-content>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>
