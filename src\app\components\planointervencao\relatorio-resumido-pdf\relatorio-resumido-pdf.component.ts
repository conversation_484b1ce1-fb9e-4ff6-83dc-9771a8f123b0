import { OrganizacaoService } from './../../organizacao/organizacao.service';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { DominioService } from './../../dominio/dominio.service';
import { Objetivo } from './../../objetivo/objetivo-model';
import { Dominio } from './../../dominio/dominio-model';
import { Router, ActivatedRoute } from '@angular/router';
import { PlanoIntervencao } from './../planointervencao-model';
import { PlanoIntervencaoService } from './../planointervencao.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import * as moment from 'moment';
// import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-plano-intervencao-resumido-pdf',
  templateUrl: './relatorio-resumido-pdf.component.html',
  styleUrls: ['./relatorio-resumido-pdf.component.css']
})
export class PlanoIntervencaoResumidoPdfComponent implements OnInit {

  public planointervencao: PlanoIntervencao = new PlanoIntervencao();
  public dominioMap: Map<string, Objetivo[]> = new Map<string, Objetivo[]>();
  public dominios: Dominio[];
  // public pdfGerado: boolean = false;
  public data: Date = new Date();
  public dia: string;
  public mes: string;
  public ano: string; 
  public assinatura: string; 
  public profissional: Profissional;

  @ViewChild("pdf") htmlData: ElementRef;

  constructor(private planointervencaoService: PlanoIntervencaoService,
    private profissionalService: ProfissionalService,
    public authService: AuthService,
    private dominioService: DominioService,
    private organizationService: OrganizacaoService,
    private router: Router,
    private route: ActivatedRoute) { }

  ngOnInit(): void {
    let idPlano = this.route.snapshot.paramMap.get("id");
    moment.locale('pt-br');
    this.dia = moment(this.data).format('DD');
    this.mes = moment(this.data).format('MMMM');
    this.ano = moment(this.data).format('YYYY');

      this.planointervencaoService.findById(idPlano).subscribe(plano => {
        this.planointervencao = plano;
        this.profissionalService.findById(plano.idProfissional).subscribe(p => {
          this.profissional = p;
          if(this.profissional.assinatura != undefined && this.profissional.assinatura != ""){
            this.assinatura = this.profissional.assinatura.replace(/\r\n|\r|\n/g," <br /> ")
            document.getElementById('assinatura').innerHTML = this.assinatura;
          } else {
            this.organizationService.findById(this.authService.user.organizationId).subscribe(o => {
              this.assinatura =   this.profissional.nome + "<br />" +
                                  o.nome
              document.getElementById('assinatura').innerHTML = this.assinatura;
            })
          }
        })
        //console.log(this.planointervencao)
        this.setObjetivosPorDominio();
      })

      this.dominioService.find().subscribe(doms => {
        this.dominios = doms;
      })
  }

  get user(): FirebaseUserModel{
    //console.log(this.headerService.user.name);
    //console.log(this.headerService.getUser())
    
    return this.authService.getUser();
    //return this.headerService.getUser();


    /*console.log(this.userService.getCurrentUser())
    this.userService.getCurrentUser()
      .then( (user) => {
        return user;
      }, (error) => {
        console.log('Usuário não está logado.')
        return null;
      });
      return null;*/
  }

  setObjetivosPorDominio(){
    
    let objetivos: Objetivo[] = [];
    this.planointervencao.objetivos.forEach(objetivo => {
      //objetivos = [];
      if(this.dominioMap.get(objetivo.dominio.id) != undefined){
        objetivos = this.dominioMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        this.dominioMap.set(objetivo.id_dominio, objetivos)
      } else {
        this.dominioMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    
  }

}
