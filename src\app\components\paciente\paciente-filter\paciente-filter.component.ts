import { PacienteService } from './../paciente.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { Profissional, ProfissionalSimple } from './../../profissional/profissional-model';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-paciente-filter',
  templateUrl: './paciente-filter.component.html',
  styleUrls: ['./paciente-filter.component.css']
})
export class PacienteFilterComponent implements OnInit {

  ativo: boolean = true;
  profissional: any;
  nome: string;

  profissionais: any[];

  tiposIntervencao: string;

  constructor(private route: ActivatedRoute,
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    private router: Router) { }

  ngOnInit(): void {
    this.profissionalService.getResumoProfissional().subscribe(data => {
      this.profissionais = data;
    });
  }

  cleanFilter(){
    this.ativo = true;
    this.profissional =  undefined;
    this.nome = "";
    this.tiposIntervencao =  undefined;
    this.setFilter();
  }

  filterPaciente(obj, ativo: boolean, profissional: Profissional, nome: string, tiposIntervencao: string){
    // console.log(tiposIntervencao);
    // console.log(obj.tiposIntervencao)
    //console.log(obj.equipe.filter(prof => prof.nome == profissional).length)
    if(   ( obj.ativo == ativo )
      &&  ( (profissional == undefined) || (obj.equipe?.filter(prof => prof.id == profissional).length > 0) )
      &&  ( (nome == "") || (nome == undefined) || (obj.nome.toLowerCase().includes(nome.toLowerCase())) )
      &&  ( (tiposIntervencao == undefined) || (tiposIntervencao.length == 0) 
            || ( obj.tiposIntervencao != undefined && (obj.tiposIntervencao as string[]).some( (interv) => interv == tiposIntervencao ) ) 
          )
      ){
      //console.log('true');
      return true;
    } else {
      //console.log('false');
      return false;
    }
  }

  setFilter(){
    var selfRef = this;
    this.pacienteService.find().subscribe(listas => {
      this.pacienteService.pacientes.next(listas);


      this.pacienteService.pacientes.next(this.pacienteService.pacientes.value.filter(function(x){
        return selfRef.filterPaciente(x, selfRef.ativo, selfRef.profissional, selfRef.nome, selfRef.tiposIntervencao);
      }));

      //console.log(this.listas);
    })
    //this.pacienteService.listas.value.filter(this.filterCandidatoByTurno).bind(this);
    
    //console.log
    
    //filter(this.filterCandidatoByTurno);
  }

}
