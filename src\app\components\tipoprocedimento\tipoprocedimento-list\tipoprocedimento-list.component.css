table {
    width: 100%
}

.edit {
    margin-right: 10px;
}
.edit > i {
    color: #d9cd26;
    cursor: pointer;
}

.delete > i {
    color: #e35e6b;
    cursor: pointer;
}

.mat-column-nome {
    flex: 0 0 40% !important;
    width: 40% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

.mat-column-minutos {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
  
.mat-column-valor {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .mat-column-permiteAlterValor {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

.mat-column-action {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }