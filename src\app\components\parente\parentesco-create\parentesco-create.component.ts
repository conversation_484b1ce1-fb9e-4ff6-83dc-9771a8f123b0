import { MatSelectChange } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { ParentescoService } from './../parentesco.service';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { Parentesco } from './../parentesco-model';
import { Component, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-parentesco-create',
  templateUrl: './parentesco-create.component.html',
  styleUrls: ['./parentesco-create.component.css']
})
export class ParentescoCreateComponent implements OnInit {

  parentesco: Parentesco = new Parentesco();

  //datasource: MatTableDataSource<string>;
  datasource: string[];

  @ViewChild(NgForm) form;

  //Form Controls
  nome = new FormControl('', [Validators.required]);

  constructor(private parentescoService: ParentescoService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let idParentesco = this.route.snapshot.paramMap.get('id');  

    if(idParentesco == undefined) { //Create
      this.parentesco = new Parentesco();
      this.parentesco.ativo = true;
    } else {  //Edit
        await this.parentescoService.findById(idParentesco).subscribe(async parentesco => {
          this.parentesco = parentesco;
        })
    }
    

  }

  save(){
    //console.log("Saving...")
    if(this.form.valid){
      if(this.parentesco.id == undefined){
        this.parentescoService.create(this.parentesco).subscribe((id) => {
          this.parentescoService.showMessage('Parentesco criado com sucesso!');
          this.router.navigate(['/parentesco']);
        });
      } else {
        this.parentescoService.update(this.parentesco).subscribe((parentesco) => {
          this.parentescoService.showMessage('Parentesco alterado com sucesso!');
          this.router.navigate(['/parentesco']);
        });
      }
    } else {
      this.parentescoService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/parentesco']);
  }

}
