import { MatSelectChange } from '@angular/material/select';
import { ObjetivoComportamental } from '../objetivo-comportamental-model';
import { ObjetivoPICService } from './../objetivopic.service';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-objetivopic-filter',
  templateUrl: './objetivopic-filter.component.html',
  styleUrls: ['./objetivopic-filter.component.css']
})
export class ObjetivopicFilterComponent implements OnInit {
  @Input() objetivosDoPlano: any[] = []; // Recebe os objetivos do plano
  
  public allObjetivos: ObjetivoComportamental[];
  public meta = '';
  public compAlvo = '';
  public defOp = '';
  public tiposColeta: string[] = ['Amostragem de Tempo', 'Registro de Eventos', 'Cronometragem'];
  public tipoColeta: string | null = null;

  constructor(
    private objetivoPICService: ObjetivoPICService,
  ) { }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Verifica se os objetivos do plano mudaram
    if (changes['objetivosDoPlano'] && this.objetivosDoPlano && this.allObjetivos) {
      this.allObjetivos = [...this.allObjetivos.filter(obj => this.objetivosDoPlano?.find(o => o.id == obj.id) == undefined)];
    }
  }

  private loadInitialData(): void {
    // Carregar tanto os objetivos VBMAPP quanto ESDM, e outros se aplicável
    this.objetivoPICService.find().subscribe(listas => {
          
      this.allObjetivos = [...listas];
      this.allObjetivos = this.allObjetivos.filter(obj => this.objetivosDoPlano?.find(o => o.id == obj.id) == undefined);
    });
  }

  setTipoColeta(event: MatSelectChange): void {
    // Verifica o tipo de avaliação selecionado
    if (event.value === 'Amostragem de Tempo') {
      this.tipoColeta = 'Amostragem de Tempo';
    } else if (event.value === 'Registro de Eventos') {
      this.tipoColeta = 'Registro de Eventos';
    } else if (event.value === 'Cronometragem') {
      this.tipoColeta = 'Cronometragem';
    } else {
      this.tipoColeta = null;
    }
    this.setFilter();
  }

  cleanFilter(): void {
    this.resetFilters();
    this.setFilter();
  }

  private resetFilters() {
    this.meta = '';
    this.compAlvo = '';
    this.defOp = '';
    this.tipoColeta = null;
  }

  setFilter(): void {
    if (!this.allObjetivos) return;
    // Filtra os objetivos com base nos critérios definidos
    const filteredList = this.allObjetivos.filter(obj => this.filterObjetivo(obj) && !this.objetivosDoPlano.includes(obj.id));
    this.objetivoPICService.objetivosPIC.next(filteredList);
  }
  
  private filterObjetivo(obj: any): boolean {
    const hasValidMeta = !this.meta || obj.meta.toLowerCase().includes(this.meta.toLowerCase());

    const hasValidCompAlvo = !this.compAlvo || obj.comportamentoAlvo.toLowerCase().includes(this.compAlvo.toLowerCase());

    const hasValidDefOp = !this.defOp || obj.definicaoOperacional.toLowerCase().includes(this.defOp.toLowerCase());

    const hasValidTipoColeta = !this.tipoColeta || obj.tipoColeta == this.tipoColeta;
  
    return hasValidMeta && hasValidCompAlvo && hasValidDefOp && hasValidTipoColeta;
  }  

}
