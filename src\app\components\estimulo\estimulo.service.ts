import { Estimulo } from './estimulo-model';
import { map, catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';


@Injectable({
  providedIn: 'root'
})
export class EstimuloService {

  estimuloUrl = `${environment.API_URL}/estimulo`;

  public $estimulo: Observable<Estimulo[]>;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false): void {
    this.snackbar.open(msg, 'X', {
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any> {
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }


  create(estimulo: Estimulo): Observable<string> {
    // console.log(estimulo);
    return this.http.post<string>(this.estimuloUrl, estimulo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }

  update(estimulo: Estimulo): Observable<Estimulo> {
    return this.http.put<Estimulo>(this.estimuloUrl + "/" + estimulo.id, estimulo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }

  findById(id: string): Observable<Estimulo> {
    return this.http.get<Estimulo>(this.estimuloUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }

  find(): Observable<Estimulo[]> {
    return this.http.get<Estimulo[]>(this.estimuloUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }
}
