.md-mini-fab-right {
    top: 10px !important;
    right: 20px !important;
    left: auto !important;
    bottom: auto !important;
    position: absolute !important;
}

.mat-column-tipo {
    flex: 0 0 30% !important;
    width: 30% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-data {
    flex: 0 0 20% !important;
    width: 20% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-profissional {
    flex: 0 0 35% !important;
    width: 35% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-action {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

table {
    width: 100%
}

.tdlinked{
    display: block;
    text-decoration: none;
    cursor: pointer;
}

.center {
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}

.overlay{
    height:100vh;
    width:100%;
    background-color:rgba(0, 0, 0, 0.286);
    z-index:    10;
    top:        0; 
    left:       0; 
    position:   fixed;
}
