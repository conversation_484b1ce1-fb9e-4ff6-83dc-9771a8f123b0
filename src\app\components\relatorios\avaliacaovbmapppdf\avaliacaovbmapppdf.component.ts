import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Router, ActivatedRoute } from '@angular/router';
import jsPDF from 'jspdf';
import { DominioVBMAPP } from '../../dominiovbmapp/dominiovbmapp-model';
import { MarcovbmappService } from '../../marcovbmapp/marcovbmapp.service';
import { Nivel } from '../../nivel/nivel-model';
import { NivelService } from '../../nivel/nivel.service';
import { Paciente } from '../../paciente/paciente-model';
import { PacienteService } from '../../paciente/paciente.service';
import { Profissional } from '../../profissional/profissional-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { AuthService } from '../../template/auth/auth.service';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { VBMAPPMilestonesAssessmentItem } from '../../vbmappmsassmt/vbmapp-milestones-item-model';
import { VBMAPPMilestonesAssessment } from '../../vbmappmsassmt/vbmappmsassmt-model';
import { VBMAPPMsAssmtService } from '../../vbmappmsassmt/vbmappmsassmt.service';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-avaliacaovbmapppdf',
  templateUrl: './avaliacaovbmapppdf.component.html',
  styleUrls: ['./avaliacaovbmapppdf.component.css']
})
export class AvaliacaovbmapppdfComponent implements OnInit {

  public vbmappmsassmts: VBMAPPMilestonesAssessment[];

  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  public niveis: Nivel[];
  public dominios: DominioVBMAPP[];
  public dominiosTab: DominioVBMAPP[] = [];
  public vbmappMsAssmtItemView: VBMAPPMilestonesAssessmentItem[] = [];

  public vbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public nivel: Nivel = new Nivel();
  public dominio: DominioVBMAPP = new DominioVBMAPP();
  public dominioMap: Map<string, DominioVBMAPP[]> = new Map<string, DominioVBMAPP[]>(); 

  idOrganizacao: any;

  dataSource: MatTableDataSource<VBMAPPMilestonesAssessmentItem>;

  @Input()
  idAvaliacao: string;

  @Output() pdfGenerated: EventEmitter<void> = new EventEmitter<void>();

  @ViewChild('vbmappMarcos', {static: false})
  el!: ElementRef;
  vbmapInfosPDF: VBMAPPMilestonesAssessmentItem[];

  constructor(private vbmappMsAssmtService: VBMAPPMsAssmtService,
    private nivelService: NivelService,
    private pacienteService: PacienteService,
    private marcoVBMAPPService: MarcovbmappService,
    public authService: AuthService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private loadingService: LoadingService) { }

    async ngOnInit(): Promise<void> {
      try {
        const av = this.vbmappMsAssmtService.findById(this.idAvaliacao).toPromise();
        this.vbmappmsassmt = await av;
        this.paciente = await this.pacienteService.findById(this.vbmappmsassmt.idPaciente).toPromise();
        this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
  
        // Carregar Paciente
        this.vbmappmsassmt.idPaciente = this.paciente.id;
        this.vbmappmsassmt.paciente = this.paciente;
  
        this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt =>
          (assmt.marco.id_nivel == 'N1' && assmt.marco.id_dominio == 'Mando')
        );
  
        // Carregar Níveis
        const niveis = await this.nivelService.find().toPromise();
        this.niveis = niveis;
        this.niveis.pop();
        this.nivel = this.niveis.find(n => n.id == 'N1');
  
        // Carregar Domínios dos Níveis (TAB)
        await Promise.all(niveis.map(async (nivel) => {
          const dominios = await this.marcoVBMAPPService.findDominiosByNivel(nivel.id).toPromise();
          this.dominioMap.set(nivel.id, dominios);
          // Carregar Domínios para Combo (Default: N1-Mando)
          if (nivel.id === 'N1') {
            this.dominios = dominios;
            this.dominio = this.dominios.find(dom => dom.id == 'Mando');
          }
        }));
  
        // Carregar Profissionais
        this.profissionais = await this.profissionalService.find().toPromise();
  
        // Após carregar todos os dados, gerar o PDF
        this.generatePDF();
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
      } finally {
        this.loadingService.hide();
      }
    }

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];  
    this.filterAssessment();
  }

  filterAssessment(){
    this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
      (assmt.marco.id_nivel==this.nivel.id 
        && assmt.marco.id_dominio == this.dominio.id))
  }

  setAssessment(){
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'Mando');
    this.filterAssessment()
  }

  async generatePDF() {
    // Adicione um timeout para garantir que o DOM seja atualizado
    setTimeout(() => {
      const pdf = new jsPDF('l', 'pt', 'a4', true);
      pdf.html(this.el.nativeElement, {
        callback: (pdf) => {
          pdf.save('Avaliacao de Marcos.pdf');
          this.pdfGenerated.emit();
          this.loadingService.hide();
        },
        x: 0, // Alinhamento horizontal (ajuste conforme necessário)
        y: 10 // Margem superior (ajuste conforme necessário)
      });
    }, 1500); // Tempo de espera para garantir a renderização
  };

  getVbmappInfosPDF(id_nivel, id_dominio){
    this.vbmapInfosPDF = this.vbmappmsassmt.assessment.filter(assmt => (assmt.marco.id_nivel==id_nivel && assmt.marco.id_dominio == id_dominio));
    
    return this.vbmapInfosPDF
  }

  getStyleByNivelIdForGlossario(nivelId: string): any {
    const styles = {
      'N1': { height: '1140px' },
      'N2': { height: '1770px' },
    };

    return styles[nivelId] || {};
  }

  getStyleForAvaliacao(vbmapp: any): any {
    const styles = {
      // Formatação PDF VBMAPP
      'VP-MTS-2': { marginBottom: '45px' },
      'VP-MTS-4': { marginBottom: '45px' },
      'VP-MTS-7': { marginBottom: '30px' },
      'Ecóico-7': { marginBottom: '45px' },
      'Ecóico-10': { marginBottom: '45px' },
      'VP-MTS-11': { marginBottom: '30px' },
      'VP-MTS-14': { marginBottom: '45px' },
      'Ecóico-11': { marginBottom: '20px' },
      'Escrita-11': { marginBottom: '30px' },
      'Ling-11': { marginBottom: '50px' },
      'Ling-14': { marginBottom: '50px' },
    };

    return styles[vbmapp] || { marginBottom: '1px' };
  }
  
}