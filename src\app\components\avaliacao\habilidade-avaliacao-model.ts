import { DominioAvaliacao } from './dominio-avaliacao-model';
import { DominioRespostaHabilidadeAvaliacao } from './dominio-resposta-habilidade-avaliacao-model';
import { NivelAvalicao } from './nivel-avaliacao-model';
// import { DominioAvaliacao } from './dominio-avaliacao-model';
// import { NivelAvalicao } from './nivel-avaliacao-model';


export class HabilidadeAvaliacao{
    id?: string;
    nome: string;
    ordem: number;
    idNivelAvaliacao: string;
    nivel: NivelAvalicao;
    idDominioAvaliacao: string;
    dominio: DominioAvaliacao;
    sigla: string;
    idTipoAvaliacao: string;
    dominioResposta: DominioRespostaHabilidadeAvaliacao[];
}