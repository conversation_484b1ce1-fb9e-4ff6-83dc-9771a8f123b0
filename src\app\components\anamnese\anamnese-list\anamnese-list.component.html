<div >
    <table mat-table [dataSource]="anamneses" class="mat-elevation-z8" > 

        <!-- Nome Column -->
        <ng-container matColumnDef="descricao"  >
            <th mat-header-cell *matHeaderCellDef >Descrição</th>
            <td mat-cell *matCellDef="let row"  >{{row.descricao}}</td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="edit(row)" class="edit" style="cursor: pointer;">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row)" class="delete" style="cursor: pointer;">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  