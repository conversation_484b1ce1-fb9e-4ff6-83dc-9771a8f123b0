<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row wrap" style="display: flex;"> 
            <div style="display: flex; width: 70%;" fxLayout="row wrap">
                <mat-form-field  style="width: 20%; padding: 20px;" *ngIf="planointervencao.id != undefined">
                    <mat-label>Plano de Intervenção</mat-label>
                    <mat-select placeholder="Plano de Intervenção" 
                        [(ngModel)]="planointervencao"
                        name="planointervencao"
                        (selectionChange) = "setObjetivosPlano()">
                        <mat-option *ngFor="let plano of planosintervencao" [value]="plano" >
                            {{plano.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div style="display: flex; width: 40%;" fxLayout="row wrap">
                <ng-container *ngIf="planointervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="saveToPDF()"
                        matTooltip="Imprimir Completo"
                        *ngIf="hasAccessRead">
                        <!--mat-icon>picture_as_pdf</mat-icon-->
                        <mat-icon>print</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="planointervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="resumoSaveToPDF()"
                        matTooltip="Imprimir Resumido"
                        *ngIf="hasAccessRead">
                        <mat-icon>summarize</mat-icon>
                    </button>
                    <!-- Resumo -->
                </ng-container>
                <ng-container *ngIf="planointervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="graficoEvolucao()"
                        matTooltip="Gráfico de Evolução"
                        *ngIf="hasAccessRead">
                        <mat-icon>leaderboard</mat-icon>
                    </button>
                    <!-- Resumo -->
                </ng-container>
                <ng-container *ngIf="planointervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="edit()"
                        matTooltip="Editar"
                        *ngIf="hasAccessUpdate">
                        <mat-icon>edit</mat-icon>
                    </button>
                </ng-container>
                <button mat-mini-fab color="primary" style="margin: 10px" (click)="add()"
                    matTooltip="Incluir Novo"
                    *ngIf="hasAccessCreate">
                    <mat-icon>add</mat-icon>
                </button>
                <ng-container *ngIf="planointervencao.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="delete(planointervencao.id)"
                        matTooltip="Excluir"
                        *ngIf="hasAccessDelete">
                        <!--mat-icon>picture_as_pdf</mat-icon-->
                        <mat-icon>delete</mat-icon>
                    </button>
                </ng-container>
            </div>
        </div>
        <div class="mat-elevation-z0" style="padding-bottom: 30px;" 
            *ngIf="hasAccessRead">
            <!--strong>Objetivos do Plano de Intervenção</strong-->
            <!--a mat-raised-button href="javascript:void()" (click)="toggleTableRows()" color="primary">Toggle Rows</a-->
            <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows *ngIf="datasourceObjetivos.data != null"> 
                <!-- Index Column --> 
                <ng-container matColumnDef="index">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">{{ planointervencao.objetivos.indexOf(row) + 1 }}</td>
                </ng-container>

                <!-- Expand Column --> 
                <ng-container matColumnDef="expand">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">
                        <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                            <mat-icon>{{ (row != null && row.isExpanded) ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                        </a>
                    </td>
                </ng-container>

                <!-- etapasSum Column --> 
                <ng-container matColumnDef="etapas">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row">
                        <div [ngClass]="percentualEtapas(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEtapas(row) < 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        <div class="label label-ok" style="width: 20%;" *ngIf="percentualEtapas(row) == 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        
                    </td>
                </ng-container>

                <!-- Id Column --> 
                <ng-container matColumnDef="idNome">
                    <!--th mat-header-cell *matHeaderCellDef ></th-->
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':(row != null && row.isExpanded) ? 600 : normal}">{{row.id}} - {{row.nome}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <!--ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
                </ng-container--> 

                <!-- Action Column -->
                <ng-container matColumnDef="action" fxFlex="30">
                    <!--th mat-header-cell *matHeaderCellDef fxFlex="30"></th-->
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a style="padding-left: 10px; color: gray; cursor: pointer;" (click)="deleteObjetivo(planointervencao.objetivos.indexOf(row))">x</a>
                    </td>
                </ng-container> 

                <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjs.length">
              
                      <div class="row student-element-detail" [@detailExpand]="(element != null && element.isExpanded) ? 'expanded' : 'collapsed'">
                        <mat-list>
                          <div style="padding: 0px 0px 10px 0px;">{{element.descricao_plano}}</div>
                          <mat-list-item *ngFor="let etapa of element.etapa">
                            <div matline *ngIf="etapa.status=='Adquirida'" 
                                style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                <div style="width: 5%;">
                                    <a style="cursor: pointer; color: green;"  (click)="changeEtapaStatus(etapa)">
                                        <mat-icon>
                                            check
                                        </mat-icon>
                                    </a>
                                </div>
                                <div style="width: 90%; text-align: left;">
                                    <small>{{etapa.id}} - {{etapa.nome}}</small>
                                </div>
                            </div>
                            <div matline *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" 
                                style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">   
                                <div style="width: 5%;">
                                    <a style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatus(etapa)">
                                        <mat-icon>
                                            miscellaneous_services
                                        </mat-icon>
                                    </a>
                                </div>
                                <div style="width: 90%; text-align: left;">
                                    <small>{{etapa.id}} - {{etapa.nome}}</small>
                                </div>
                            </div>
                          </mat-list-item>
                        </mat-list>
                      </div>
              
                    </td>
                  </ng-container>
          
              <!--tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr-->
              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
              <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="student-detail-row"></tr>
            </table>
        </div> 
    </mat-card-content>
</mat-card>
<div *ngIf="planosintervencao?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Nenhum plano de intervenção criado!</h1>
</div>