<mat-card style="width: 50%; margin: auto;">
  <mat-card-header>
    <mat-card-title>Perfi<PERSON> de Usuário</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div style="display: flex; flex-direction: column; width: 100%; text-align: center;">
      <div *ngIf='user.provider == "password"' style="width: 100%;">
        <form [formGroup]="profileForm" style="display: flex; flex-direction: column;">
            <mat-form-field >
              <mat-label>Nome</mat-label>
              <input class="input" matInput placeholder="Nome" 
                  formControlName="nome"  name="nome" required>
            </mat-form-field>
            <mat-form-field >
              <mat-label>E-mail</mat-label>
              <input class="input" matInput placeholder="E-mail" type="email"
                  formControlName="email"  name="email">
            </mat-form-field>
            <mat-form-field >
              <mat-label>Telefone</mat-label>
              <input class="input" matInput placeholder="Telefone"
                  formControlName="telefone"  name="telefone" required>
            </mat-form-field>
        </form>
        <mat-list>
          <div class="funcoes">Funções</div>
          <ng-container *ngIf="!user.familia">
            <mat-list-item *ngFor="let funcao of profissional.funcao" class="v-middle">
              <mat-icon style="padding-right: 5px;">star</mat-icon> {{ funcao.nome }}
            </mat-list-item>
          </ng-container>
          <ng-container *ngIf="user.familia">
            <mat-list-item class="v-middle">
              <mat-icon style="padding-right: 5px;">star</mat-icon> {{ parente.parentesco }}
            </mat-list-item>
          </ng-container>
        </mat-list>
        <!--
          nome: string;
          email: string;
          telefone: string;
          funcao: Funcao[];
        -->
        <button mat-flat-button	color="primary" (click)="save(profileForm.value)">Salvar</button>
        <br><br>
        <a (click)="redefinePassword()" style="cursor: pointer">Redefinir senha</a>
      </div>
    </div>
  </mat-card-content>
</mat-card>

  
  <!--h1 class="header">Profile</h1>
  <div class="row">
    <div class="col-md-4 col-md-offset-4">
      <div class="card" style="width: 20rem;"-->
        
        <!--img class="card-img-top image" [src]="user.image" *ngIf="user.image" alt="User image"-->
        <!--img class="card-img-top image" src="https://via.placeholder.com/400x300" *ngIf="!user.image" alt="User image"-->
        
        <!--div *ngIf='user.provider != "password"'class="card-body">
          <h4 class="card-title">{{user.name}}</h4>
          <p class="card-text">This is an example of Social Authentication using Firebase in an Angular 7 web app. </p>
        </div>
        <div *ngIf='user.provider == "password"'class="card-body">
          <form [formGroup]="profileForm">
            <div class="form-group">
              <label>Nome do usuário</label>
              <input type="text" formControlName="name" class="form-control" required>
            </div>
          </form>
          <button mat-flat-button	color="primary" (click)="save(profileForm.value)">Salvar</button>
          <br><br>
          <a (click)="redefinePassword()" style="cursor: pointer">Redefinir senha</a>
        </div>
      </div>
    </div>
  </div-->