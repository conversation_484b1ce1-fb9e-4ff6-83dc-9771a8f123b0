import { EMPTY } from 'rxjs';
import { ListaesperaService } from './../listaespera.service';
//import { ListaEsperaReadComponent } from './../lista-espera-read/lista-espera-read.component';
import { TerapeutaService } from './../../terapeuta/terapeuta.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Terapeuta } from './../../terapeuta/terapeuta-model';
import { ListaEspera } from './../listaespera-model';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-lista-espera-filter',
  templateUrl: './lista-espera-filter.component.html',
  styleUrls: ['./lista-espera-filter.component.css']
})
export class ListaEsperaFilterComponent implements OnInit {

  preferenciaTurno: string;
  status: string;
  terapeuta: Terapeuta;

  terapeutas: Terapeuta[];

  constructor(private route: ActivatedRoute,
    private terapeutaService: TerapeutaService,
    private listaEsperaService: ListaesperaService,
    //private listaEsperaRead: ListaEsperaReadComponent,
    private router: Router) { }

  ngOnInit(): void {

    this.terapeutaService.find().subscribe(terapeutas => {
      this.terapeutas = terapeutas;
    });



  }

  cleanFilter(){
    this.status = undefined;
    this.preferenciaTurno = undefined;
    this.terapeuta =  undefined;
    this.setFilter();
  }

  filterCandidato(obj, turno, status, terapeuta){
    //console.log(status);
    //console.log(obj.status)
    if(   ( (turno == '') || (turno == undefined) ||(obj.preferenciaTurno == turno) ) 
      &&  ( (status == '') || (status == undefined) || (obj.status == status) )
      &&  ( (terapeuta == '') || (terapeuta == undefined) || (obj.terapeuta == terapeuta) )
      ){
      //console.log('true');
      return true;
    } else {
      //console.log('false');
      return false;
    }
  }

  setFilter(){
    //console.log("Filtrando por turno!");
    var selfRef = this;
    this.listaEsperaService.find().subscribe(listas => {
      this.listaEsperaService.listas.next(listas);


      this.listaEsperaService.listas.next(this.listaEsperaService.listas.value.filter(function(x){
        return selfRef.filterCandidato(x, selfRef.preferenciaTurno,
          selfRef.status, selfRef.terapeuta);
      }));

      //console.log(this.listas);
    })
    //this.listaEsperaService.listas.value.filter(this.filterCandidatoByTurno).bind(this);
    
    //console.log
    
    //filter(this.filterCandidatoByTurno);
  }

}
