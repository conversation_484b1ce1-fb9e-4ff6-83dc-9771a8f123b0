table.dominio {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

table.etapa {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

th.tipo<PERSON>uporte{
  width: 40%;
}

td.dominio {
  width: 100%;
  background-color: silver;
  font-family: Helvetica;
  font-size: 1.4em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
}

td.objetivo {
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
  width: 100%;
}

td.objetivoAdquirido {
  background-color: aquamarine;
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  padding: 10px 10px 5px 5px;
  border: 1px solid #ddd;
  width: 100%;
}

td.etapa {
  width: 30%;
  font-size: 1em;
  font-weight: normal;
  padding: 10px 10px 5px 5px;
}

tr.dominio {
  background-color: lightgray;
  width: 100%;
}

p.dominio {
  font-family: Helvetica;
  font-size: 1.2em;
  font-weight: bold;
  line-height: normal;
}

p.objetivo {
  font-family: Helvetica;
  font-size: 1em;
  font-weight: bold;
  line-height: normal;
}

p.etapa {
  font-family: Helvetica;
  font-size: 0.7em;
  font-weight: normal;
  line-height: normal;
  page-break-inside: avoid;
}

div.subtitulo{
  display: flex; 
  flex-direction: row;
  width: 90%; 
  padding: 20px 0px 20px 0px;
}

.container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start;
  
}

.item {
  flex: 1 1 33%;
  max-width: 33%;
  min-width: 200px;

}

@media screen and (max-width: 768px) {
  .item {
    flex: 1 1 100%;
    max-width: 100%;
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.cabecalho-pdf {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px;
  margin-right: 20px;
}
  
.cabecalho-img-pdf {
  text-align: left
}
  
.cabecalho-dados-pdf {
  text-align: right !important;
  font-size: 10px;
  font-weight: bold;
  margin-inline-end: 10px;
}

.counter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px;
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  position: relative;
}

.truncatable {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.registro-lista {
  padding-top: 10px;
}

.registro-conteudo {
  height: 150px; 
  overflow-y: auto; 
  padding: 10px; 
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.avoid-page-break {
  page-break-inside: avoid;
  break-inside: avoid;
}
