import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { VBMAPPMilestonesAssessment } from './vbmappmsassmt-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class VBMAPPMsAssmtService {

  vbmappmsassmtUrl = `${environment.API_URL}/vbmappmsassmt`;
  
  public vbmappmsassmts: BehaviorSubject<VBMAPPMilestonesAssessment[]> = 
    new BehaviorSubject<VBMAPPMilestonesAssessment[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(vbmappmsassmt: VBMAPPMilestonesAssessment): Observable<string>{
    // console.log(vbmappmsassmt);
    return this.http.post<VBMAPPMilestonesAssessment>(this.vbmappmsassmtUrl, vbmappmsassmt).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(vbmappmsassmt: VBMAPPMilestonesAssessment): Observable<VBMAPPMilestonesAssessment>{
    // console.log(vbmappmsassmt);
    return this.http.put<VBMAPPMilestonesAssessment>(this.vbmappmsassmtUrl + "/" + vbmappmsassmt.id, vbmappmsassmt).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<VBMAPPMilestonesAssessment>{
    return this.http.get<VBMAPPMilestonesAssessment>(this.vbmappmsassmtUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByPaciente(idPaciente: string): Observable<VBMAPPMilestonesAssessment[]>{
    return this.http.get<VBMAPPMilestonesAssessment[]>(this.vbmappmsassmtUrl + "/paciente/" + idPaciente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  } 

  findResumoByPaciente(idPaciente: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.vbmappmsassmtUrl}/paciente/${idPaciente}/resumo`).pipe(
      map(obj => obj),
      catchError(() => EMPTY)
    );
  }

  findLastByPacienteData(idPaciente: string, data: string): Observable<VBMAPPMilestonesAssessment[]>{
    return this.http.get<VBMAPPMilestonesAssessment[]>(this.vbmappmsassmtUrl + "/paciente/" + idPaciente + "/data/" +data).pipe(
      map(objs => objs),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<VBMAPPMilestonesAssessment[]>{
    return this.http.get<VBMAPPMilestonesAssessment[]>(this.vbmappmsassmtUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<VBMAPPMilestonesAssessment>{
    return this.http.delete<VBMAPPMilestonesAssessment>(this.vbmappmsassmtUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
