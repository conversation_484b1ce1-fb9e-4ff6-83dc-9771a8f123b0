<mwl-demo-utils-calendar-header 
  [(view)]="view" 
  [(viewDate)]="viewDate"
  (viewChange)="viewChanged()"
  >
</mwl-demo-utils-calendar-header>

<!--div class="alert alert-info">
  <div [ngSwitch]="view">
    <span *ngSwitchCase="'month'"
      >Clique em um mês para mudar a visão para aquele mês.
      </span
    >
    <span *ngSwitchCase="'week'"
      >Clique em um dia para mudar a visão para aquele dia.
      </span
    >
    <span *ngSwitchCase="'day'">
        !--There is no other view to navigate to.--
    </span>
  </div>
</div-->

<div [ngSwitch]="view" class="scroll-container" #scrollContainer>
  <mwl-calendar-month-view
    *ngSwitchCase="'month'"
    [viewDate]="viewDate"
    [events]="events"
    (dayClicked)="changeDay($event.day.date)"
    (eventClicked)="eventClicked($event)"
    (beforeViewRender)="updateCalendarEvents($event)"
  >
  </mwl-calendar-month-view>
  <ng-template
    #weekViewHourSegmentTemplate
    let-segment="segment"
    let-locale="locale"
    let-segmentHeight="segmentHeight"
    let-isTimeLabel="isTimeLabel"
  > 
    <div
      #segmentElement
      class="cal-hour-segment"
      [style.height.px]="segmentHeight"
      [class.cal-hour-start]="segment.isStart"
      [class.cal-after-hour-start]="!segment.isStart"
      [ngClass]="segment.cssClass"
    >
      <div class="cal-time" *ngIf="isTimeLabel">
        {{ segment.date | calendarDate:'weekViewHour':locale }}
      </div>
    </div>
  </ng-template>
  <mwl-calendar-week-view
    *ngSwitchCase="'week'"
    [viewDate]="viewDate"
    [events]="events"
    [hourSegmentTemplate]="weekViewHourSegmentTemplate"
    [weekStartsOn]="weekStartsOn"
    (dayHeaderClicked)="changeDay($event.day.date)"
    (eventClicked)="eventClicked($event)"
    (hourSegmentClicked)="hourClickedDate($event.date)"
    (beforeViewRender)="updateCalendarEvents($event)"
  >
  </mwl-calendar-week-view>
  <mwl-calendar-day-view
    *ngSwitchCase="'day'"
    [viewDate]="viewDate"
    [events]="events"
    (eventClicked)="eventClicked($event)"
    (hourSegmentClicked)="hourClickedDate($event.date)"
    (beforeViewRender)="updateCalendarEvents($event)"
  >
  </mwl-calendar-day-view>
</div>
