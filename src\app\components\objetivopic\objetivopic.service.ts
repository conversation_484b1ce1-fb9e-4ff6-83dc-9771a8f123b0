import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ObjetivoComportamental } from './objetivo-comportamental-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ObjetivoPICService {
 
  objetivolUrl = `${environment.API_URL}/objetivo_comportamental`;
  
  public objetivosPIC: BehaviorSubject<ObjetivoComportamental[]> = 
    new BehaviorSubject<ObjetivoComportamental[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }
  
    create(objetivo: ObjetivoComportamental): Observable<string>{
      //console.log(objetivo);
      return this.http.post<string>(this.objetivolUrl, objetivo).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(objetivo: ObjetivoComportamental): Observable<ObjetivoComportamental>{
      return this.http.put<ObjetivoComportamental>(this.objetivolUrl + "/" + objetivo.id, objetivo).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findById(id: string): Observable<ObjetivoComportamental>{
      return this.http.get<ObjetivoComportamental>(this.objetivolUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findByNivelDominio(id_nivel: string, id_dominio: string): Observable<ObjetivoComportamental>{
      return this.http.get<ObjetivoComportamental>(this.objetivolUrl + '/' + id_nivel + 'nivel/'
          + id_dominio + '/dominio').pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<ObjetivoComportamental[]>{
      return this.http.get<ObjetivoComportamental[]>(this.objetivolUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    delete(id: string): Observable<ObjetivoComportamental>{
      return this.http.delete<ObjetivoComportamental>(this.objetivolUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
}
