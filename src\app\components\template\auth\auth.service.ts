import { AreaFuncionalidades } from './../../funcao/area-funcionalidades-model';
import { FuncaoJSON } from './../../funcao/funcaojson-model';
import { ProfissionalJSON } from './../../profissional/profissionaljson-model';
import { Funcao } from './../../funcao/funcao-model';
import { HeaderService } from './../header/header.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FuncaoService } from './../../funcao/funcao.service';
import { Profissional } from './../../profissional/profissional-model';
import { map, catchError } from 'rxjs/operators';
import { Observable, EMPTY } from 'rxjs';
import { FirebaseUserModel } from './user-model';
import { environment } from './../../../../environments/environment';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Injectable } from "@angular/core";
import { AngularFireAuth } from '@angular/fire/auth';
import * as firebase from 'firebase/app';
import { auth } from 'firebase';
import { JsonConvert, OperationMode, ValueCheckingMode } from 'json2typescript';



@Injectable({
  providedIn: 'root'
})
export class AuthService {

  authorizationUrl = `${environment.API_URL}/authorization`;

  user: FirebaseUserModel = new FirebaseUserModel();
  addingUser: number = 0;

  constructor(
    private snackbar: MatSnackBar,
    public afAuth: AngularFireAuth,
    private http: HttpClient,
    private funcaoService: FuncaoService,
    private profissionalService: ProfissionalService
    //private headerService: HeaderService
    //private route: ActivatedRoute
  ){}

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  getToken(): Promise<string> {
    return firebase.auth().currentUser.getIdToken();

    // return new Promise((resolve, reject) => {
    //   firebase.auth().onAuthStateChanged( user => {
    //     if (user) {
    //       user.getIdToken(true).then(idToken => {
    //         //console.log(idToken)
    //         resolve(idToken);
    //       });
    //     }
    //   });
    // })
  }

  refreshToken(): Promise<string> {
    return firebase.auth().currentUser.getIdToken(true);

    // return new Promise((resolve, reject) => {
    //   firebase.auth().onAuthStateChanged( user => {
    //     if (user) {
    //       user.getIdToken(true).then(idToken => {
    //         //console.log(idToken)
    //         resolve(idToken);
    //       });
    //     }
    //   });
    // })
  }

  /* 
    this.router.post('/organization/:email',this.authorizationController.setOrganizacao)
  */

 setOrganizacao(user: Profissional,organizacaoId: string): Observable<FirebaseUserModel>{
   //console.log(user.email)
   //console.log(organizacaoId)
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/organization/' + user.email,{ organizacaoId }).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }

 listOrganizationAdminUsers(organizacaoId: string): Observable<any[]>{
    return this.http.get<FirebaseUserModel[]>(this.authorizationUrl + '/listAdmins/' + organizacaoId).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

 listOrganizationUsers(organizacaoId: string): Observable<any[]>{
    return this.http.get<FirebaseUserModel[]>(this.authorizationUrl + '/listUsers/' + organizacaoId).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

 grantSuperAdminAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/superadmin/' + user.email,user).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  revokeSuperAdminAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.delete<FirebaseUserModel>(this.authorizationUrl + '/superadmin/' + user.email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

 grantOrganizationAdminAccess(user: Profissional,organizacaoId: string): Observable<FirebaseUserModel>{
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/orgadmin/' + user.email,{ organizacaoId }).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  revokeOrganizationAdminAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.delete<FirebaseUserModel>(this.authorizationUrl + '/orgadmin/' + user.email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  listAdmins(): Observable<any[]>{
    return this.http.get<FirebaseUserModel[]>(this.authorizationUrl + '/listAdmins').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  listUsers(){
    return this.http.get<any[]>(this.authorizationUrl + '/listUsers').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findUserByUID(uid: string): Observable<FirebaseUserModel>{
    return this.http.get<FirebaseUserModel>(this.authorizationUrl + '/user/'+uid).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  updateUser(user: FirebaseUserModel): Observable<FirebaseUserModel>{
    return this.http.put<FirebaseUserModel>(this.authorizationUrl + '/user/'+user.uid,user).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  updateUserOrganization(user: FirebaseUserModel): Observable<FirebaseUserModel>{
    return this.http.put<FirebaseUserModel>(this.authorizationUrl + '/user/'+user.uid+'/organization/'+user.organizationId,user).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  grantAdminAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/admin/' + user.email,user).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }

  revokeAdminAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.delete<FirebaseUserModel>(this.authorizationUrl + '/admin/' + user.email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  grantGeneralSupervisorAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/gsupervisor/' + user.email,user).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }

  revokeGeneralSupervisorAccess(user: Profissional): Observable<FirebaseUserModel>{
    return this.http.delete<FirebaseUserModel>(this.authorizationUrl + '/gsupervisor/' + user.email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
/*
  setFuncao(user: Profissional,funcoes: string[]): Observable<FirebaseUserModel>{
    return this.http.post<FirebaseUserModel>(this.authorizationUrl + '/funcao/' + user.email,funcoes).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }
*/
 
   /*
   doFacebookLogin(){
     return new Promise<any>((resolve, reject) => {
       let provider = new firebase.auth.FacebookAuthProvider();
       this.afAuth.auth
       .signInWithPopup(provider)
       .then(res => {
         resolve(res);
       }, err => {
         console.log(err);
         reject(err);
       })
     })
   }*/
 
   /*
   doTwitterLogin(){
     return new Promise<any>((resolve, reject) => {
       let provider = new firebase.auth.TwitterAuthProvider();
       this.afAuth.auth
       .signInWithPopup(provider)
       .then(res => {
         resolve(res);
       }, err => {
         console.log(err);
         reject(err);
       })
     })
   }
   */
 
   doGoogleLogin(){
     return new Promise<any>((resolve, reject) => {
       //let provider = new firebase.auth.GoogleAuthProvider();
       let provider = new auth.GoogleAuthProvider();

       provider.addScope('profile');
       provider.addScope('email');
       //this.afAuth.auth
       this.afAuth 
       .signInWithPopup(provider)
       .then(res => {
         resolve(res);
       }, err => {
         console.log(err);
         reject(err);
       })
     })
   }
 
   doRegister(value){
     return new Promise<any>((resolve, reject) => {
       firebase.auth().createUserWithEmailAndPassword(value.email, value.password)
       .then(res => {
         resolve(res);
       }, err => reject(err))
     })
   }

   getUser(): any{
    //console.log(this.user)
    return this.user;
  }

   setUser(user: any){
    let u: FirebaseUserModel = new FirebaseUserModel();
    u.image = user.photoURL;
    u.name = user.displayName;
    u.provider = user.providerData[0].providerId;
    u.email = user.email;
    u.uid = user.uid;
    //console.log(user)
    user.getIdTokenResult().then ((idTokenResult) => {
      //console.log(idTokenResult.claims.familia)
      u.admin = idTokenResult.claims.admin? true: false;
      u.gsupervisor = idTokenResult.claims.gsupervisor? true: false;
      u.funcao = idTokenResult.claims.funcao;
      //u.organizationId = idTokenResult.claims.organizationId;
      u.organizationId = idTokenResult.claims.organization;
      u.superadmin = idTokenResult.claims.superadmin;
      u.familia = idTokenResult.claims.familia;
      u.profissional = idTokenResult.claims.profissional;
      //console.log("admin: " + u.admin);
    })

    //u.permission = this._user.value.permission;
    this.user = u
    //this.user = user;
    //console.log(this.user)
  }

  /*
  setUserPermission(permission: Map<string, Map< string, Map<string,string> >>){
    console.log(permission)
    this.user.permission = permission;
    this.setPermission(this.user)
  }
  */

  verifyDetailedAccess(funcoes: string[], funcionalidade: string, nivel: string): boolean{
    return false;
  }

  verifySimpleAccess(funcoes: string[], funcionalidade: string, nivel: string): boolean{
    // console.log("chamou verifySimpleAccess");
    let access = false;

    let suprevisor = false;

    for (let i = 0; i < funcoes.length; i++) {
      if (funcoes[i] == 'Suprevisor(a)') {
        suprevisor = true;
        break;
      }
    }
    // console.log(funcoes);
    //console.log(funcionalidade);
    //console.log(nivel);
    
    // console.log( (this.getUser() as FirebaseUserModel))
    
    let user: ProfissionalJSON = (JSON.parse(localStorage.getItem("user")) as ProfissionalJSON);
    // console.log(user)
    // console.log(user)

    //Verifico se o usuário é administrador ou supervisor geral
    if( (this.getUser() as FirebaseUserModel).admin || (this.getUser() as FirebaseUserModel).gsupervisor ){
      return true;
    }
    
    //Verifico se é familiar e se está dentre as funções fixas que ele pode fazer
    if( (this.getUser() as FirebaseUserModel).familia /*&& user == null*/){
      // console.log("1")
      if( (funcionalidade.startsWith("Paciente") && nivel == "read")
      || (funcionalidade == "Paciente.Registro de notas" && nivel == "create") ){
        return true;
      } else {
        return false;
      }
    }
    // console.log("2")
    
    if (user == null) { // Usuário não está logado
      let email = (this.getUser() as FirebaseUserModel).email;
      if (user == null && firebase.auth().currentUser != null && this.addingUser == 0) {
        this.addingUser = 1;

        // Recupera as funções do usuário
        this.profissionalService.findByEmail(email).subscribe(profissional => {
          if (profissional[0] != undefined) {
            profissional[0].funcao.forEach(funcao => {
              funcoes.push(funcao.nome);
              this.funcaoService.findById(funcao.id).subscribe(f => {
                profissional[0].funcao[profissional[0].funcao.indexOf(funcao)] = f;
                localStorage.setItem("user", JSON.stringify(profissional[0]));
                window.location.reload();
              });
            });
          }
        }, error => {
          console.error("Erro ao buscar profissional:", error);
          this.addingUser = 0;
        });
      }
      return false;
    }
    
    if(funcoes.length > 0){
      if(funcoes[0] == "*"){  
        //Verifico se ele possui a permissão em qualquer uma de suas funções      
        // console.log(user.funcao)
        user.funcao.forEach(funcao => {
          if(funcao.permission[funcionalidade] && funcao.permission[funcionalidade][nivel] != "X"){
            access = true;
          }
        })
      } else if (suprevisor){
        // console.log('Funcoes ' + funcoes)
        // console.log(user.funcao);
        // console.log(funcionalidade);
        //Verifico se ele possui permissão nas funções especificadas
        funcoes.forEach(funcao => {
          if((user.funcao.find(f => (f.nome == 'Supervisor(a)') && funcao == 'Suprevisor(a)') != undefined) && (user.funcao.find(f => (f.nome == 'Supervisor(a)') && funcao == 'Suprevisor(a)').permission[funcionalidade][nivel] != "X") ){
            access = true;
          } else if((user.funcao.find(f => f.nome == funcao) != undefined) && (user.funcao.find(f => f.nome == funcao).permission[funcionalidade][nivel] != "X") ){
            access = true;
          }
        })
      } else {
        funcoes.forEach(funcao => {
          if((user.funcao.find(f => f.nome == funcao) != undefined) && (user.funcao.find(f => f.nome == funcao).permission[funcionalidade][nivel] != "X") ){
            access = true;
          }
        })
      }
    }
    return access;
  }
 
  doLogin(value){
    //let permission: Map<string, Map< string, Map<string,string> >>;
    //let permission: [key: string, value: [key: string, value: string]];
    let permis: [string, [string, string][]];
    let perm: [string, string][];
    let t = this;
    let funcoes: string[] = [];
    
    let p: ProfissionalJSON;
    let func: FuncaoJSON;

    return new Promise<any>((resolve, reject) => {
      this.addingUser = 1;
      localStorage.removeItem("user");
       firebase.auth().signInWithEmailAndPassword(value.email, value.password)
       .then(res => {
        var user = firebase.auth().onAuthStateChanged(function(user){
          if (user) {
            // console.log(user)
            //t.headerService.setUser(user);
            t.setUser(user);
            resolve(user);
          } else {
            reject('No user logged in');
          }
        })
        //  console.log(value.email)
        //Inicializo as permissões
        //permission = new Map<string, Map< string, Map<string,string> >>();
        //Recupero as funções do usuário
        this.profissionalService.findByEmail(value.email).subscribe(profissional => {
          if(profissional[0] != undefined){
            
            (profissional[0] as Profissional).funcao.forEach(funcao => {
            //for(const [,funcao] of (profissional[0] as Profissional).funcao.entries()) { //(profissional[0] as Profissional).funcao.forEach(funcao => {
              funcoes.push(funcao.nome);

              this.funcaoService.findById(funcao.id).subscribe(f => {
                (profissional[0] as Profissional).funcao[(profissional[0] as Profissional).funcao.indexOf(funcao)] = f;
                //console.log("Setando usuário")
                localStorage.setItem("user", JSON.stringify( profissional[0] ))
                this.addingUser = 0;
              })
            })
            //console.log(profissional[0]);
            

            //Seto as funções atualizadas do usuário logado
            /*
            this.setFuncao(profissional[0], funcoes).subscribe(p =>{
              this.user = p;
            })
            */
            //console.log("Setando usuário")
            //localStorage.setItem("user", JSON.stringify( profissional[0] ))
            //console.log(localStorage.getItem("user"))

            //console.log(this.user);
          }
        })
        resolve(res);
      }, err => reject(err) )
    })
   }

  resetPassword(email){
    return new Promise<any>((resolve, reject) => {
      firebase.auth().sendPasswordResetEmail(email)
      .then(res => {
        resolve(res);
      }, err => {
        console.log(err)
        reject(err)
      })
    })
  }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 5000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }
 
   doLogout(){
     return new Promise<void>((resolve, reject) => {
       if(firebase.auth().currentUser){
         //this.afAuth.auth.signOut();
         this.afAuth.signOut();
         resolve();
       }
       else{
         reject();
       }
     });
   }
}
