<!-- <app-coleta-diaria-pdf [idPlanoIntervencao]="idPlanoIntervencao"></app-coleta-diaria-pdf> -->

<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <!-- INÍCIO DADOS BÁSICOS DA COLETA -->
        <div fxLayout="column" fxLayoutAlign="space-between stretch"> 
            <form #ngForm>
                <mat-form-field  class="paciente-field">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" 
                        [(ngModel)]="paciente.id"
                        name="paciente" disabled required>
                        <mat-option [value]="paciente.id" >
                            {{paciente.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  class="profissional-field">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="idProfissional"
                        name="profissionalFC" required
                        (selectionChange) = "setProfissional($event)">
                        <mat-option></mat-option>
                        <ng-container *ngFor="let profissional of profissionaisDoPaciente">
                            <mat-option [value]="profissional.id">
                                {{profissional.nome}}
                            </mat-option>
                        </ng-container>
                    </mat-select>
                    <mat-error *ngIf="profissionalFC.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  class="data-field" [matTooltip]="coletasdiarias[0]?.id ? 'Sessão já inciada!' : ''">
                    <mat-label>Data</mat-label>
                    <input 
                        matInput 
                        placeholder="Data"
                        class="input" 
                        [max] = "today"
                        [(ngModel)]="data" 
                        name="data"
                        (dateChange)="setColetasPorData()"
                        [matDatepicker]="picker"
                        [disabled]="coletasdiarias[0]?.id"
                        required
                        >
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="dataFC.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field  class="plano-intervencao-field" *ngIf="planoIntervencao?.id">
                    <mat-label>PEI</mat-label>
                    <mat-select placeholder="PEI" 
                        [(ngModel)]="planoIntervencao.id" disabled
                        name="planoPEI" >
                        <mat-option [value]="planoIntervencao.id" >
                            {{planoIntervencao.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  class="plano-intervencao-field" *ngIf="pic?.id">
                    <mat-label>PIC</mat-label>
                    <mat-select placeholder="PIC" 
                        [(ngModel)]="pic.id" disabled
                        name="planoPIC">
                        <mat-option [value]="pic.id" >
                            {{pic.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <button mat-mini-fab matTooltip="Imprimir Coleta" color="primary" class="back-arrow" style="margin-left: 30px;" (click)="saveToPDF()">
                    <mat-icon>printer</mat-icon>
                </button>

                <button mat-mini-fab matTooltip="Sair da edição" color="primary" class="back-arrow" style="margin-left: 30px;" (click)="exitEdit()">
                    <mat-icon>arrow_back</mat-icon>
                </button>
            </form>
        </div>
        <!-- FIM DADOS BÁSICOS DA COLETA -->

        <!-- INÍCIO DAS SESSÕES -->
        <div class="mat-elevation-z0">
            <button mat-button
                    color="primary"
                    (click)="addColetaDiaria(); addColetaDiariaPIC();"
                    class="new-session v-middle">
                    <mat-icon>add</mat-icon>
                    Nova sessão
            </button>
            <mat-tab-group [selectedIndex]="selected.value"
               (selectedIndexChange)="selected.setValue($event)">
                <mat-tab *ngFor="let tab of tabs; let idxSessao = index" [label]="tab">
                    <!-- INÍCIO DE SESSÃO -->
                    <div style="margin-top: 10px; display: flex; flex-direction: row; flex-wrap: wrap;">
                        <mat-form-field  class="hora-inicio-field">
                            <mat-label>Hora Início</mat-label>
                            <mat-select placeholder="Hora Início" 
                                [(ngModel)]="coletasdiarias[idxSessao].horaInicio"
                                (ngModelChange)="onHoraInicioChange($event, idxSessao)"
                                name="horainicio" >
                                <ng-container *ngFor="let hour of hours">
                                    <mat-option [value]="hour">
                                        {{hour}}
                                    </mat-option>
                                </ng-container>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field  class="hora-fim-field">
                            <mat-label>Hora Término</mat-label>
                            <mat-select placeholder="Hora Término" 
                                [(ngModel)]="coletasdiarias[idxSessao].horaTermino"
                                (ngModelChange)="onHoraTerminoChange($event, idxSessao)"
                                name="horatermino" >
                                <ng-container *ngFor="let hour of hours">
                                    <mat-option [value]="hour">
                                        {{hour}}
                                    </mat-option>
                                </ng-container>
                            </mat-select>
                        </mat-form-field>
                        <button mat-button color="primary" (click)="startSession(idxSessao)"
                            class="begin-session v-middle"
                            *ngIf="(coletasdiarias[idxSessao]?.id == undefined) && (coletasdiariasPIC[idxSessao]?.id == undefined)">
                            <mat-icon>timer</mat-icon>
                            Iniciar sessão
                        </button>
                    </div>
                    <div *ngIf="!planoIntervencao && !pic" style="align-items: center; text-align: center; margin-top: 30px;">
                        <h1>Nenhum plano cadastrado para a data selecionada!</h1>
                    </div>
                    <mat-tab-group style="width: 100%;"
                        dynamicHeight>
                        <mat-tab style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;" [label]="TittlePEI">
                            <div *ngIf="planoIntervencao?.objetivos?.length > 0"  style="display: flex; flex-direction: row; flex-wrap: wrap;">
                                <table class="dominio">
                                    <!-- DOMÍNIOS, OBJETIVOS E ETAPAS -->
                                    <!-- DOMÍNIOS -->
                                    <ng-container *ngFor="let idDominio of dominioMap[idxSessao]?.keys()">
                                        <tr>
                                            <td class="dominio">
                                                {{ idDominio == "SD" ? "" : dominioMap[idxSessao].get(idDominio)[0].marco.dominio.nome.toUpperCase() }}
                                            </td>
                                        </tr>
                                        <!-- OBJETIVOS -->
                                        <div *ngFor="let objetivo of dominioMap[idxSessao].get(idDominio)">
                                            <tr *ngIf="objetivo.habilidades">
                                                <td [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
                                                    {{ objetivo.nome }}
                                                    <ng-container *ngIf="objetivo.status == 'Adquirido'">
                                                        <table class="etapa">
                                                            <tr>
                                                                <td class="etapa v-middle">
                                                                    <mat-icon>construction</mat-icon>
                                                                    Objetivo em manutenção
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </ng-container>
                                                    <!-- ETAPAS -->
                                                    <ng-container *ngIf="objetivo.tipoColeta == 'Naturalista'">
                                                        <div *ngFor="let etapa of objetivo.tiposSuporte; let i=index">
                                                            <ng-container *ngFor="let estimulo of etapa.estimulos">
                                                                <ng-container *ngIf="estimulo.ativo">
                                                                    <table class="etapa">
                                                                        <tr *ngIf="objetivo.habilidades">
                                                                            <!-- Nome do estímulo -->
                                                                            <td class="etapa">
                                                                                {{ estimulo?.nome }} ({{ coletasdiarias[idxSessao]?.objetivos
                                                                                    [coletasdiarias[idxSessao]?.objetivos.indexOf(objetivo)]
                                                                                    ?.tiposSuporte[coletasdiarias[idxSessao].objetivos
                                                                                            [coletasdiarias[idxSessao]?.objetivos.indexOf(objetivo)]
                                                                                            .tiposSuporte.indexOf(etapa)]?.sigla }})
                                                                            </td>

                                                                            <!-- Todos os controles numa única célula -->
                                                                            <td class="resps-row">
                                                                                <!-- Positivos -->
                                                                                <div class="resp-container" [matTooltip]="!coletasdiarias[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : 'Com ajuda'" *ngIf="coletasdiarias[idxSessao]?.objetivos
                                                                                    [coletasdiarias[idxSessao]?.objetivos?.indexOf(objetivo)]
                                                                                    ?.tiposSuporte[coletasdiarias[idxSessao]?.objetivos
                                                                                            [coletasdiarias[idxSessao]?.objetivos?.indexOf(objetivo)]
                                                                                            ?.tiposSuporte?.indexOf(etapa)]?.sigla != 'IND'">
                                                                                    <button mat-icon-button color="primary"
                                                                                            (click)="setRespostaPeriodo(idxSessao, objetivo, etapa, estimulo, 'positivos')"
                                                                                            [disabled]="!coletasdiarias[idxSessao]?.id">
                                                                                        <mat-icon>thumb_up</mat-icon>
                                                                                    </button>
                                                                                    <mat-form-field appearance="outline" class="contador-input">
                                                                                        <input matInput
                                                                                            type="number"
                                                                                            [formControl]="controles[idxSessao][objetivo.id + '_' + etapa.id + '_' + estimulo.id]?.get('positivos')"
                                                                                            (click)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'positivos')"
                                                                                            (blur)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'positivos')"
                                                                                            min="0">
                                                                                    </mat-form-field>
                                                                                </div>
                                                                                <!-- Negativos -->
                                                                                <div class="resp-container" [matTooltip]="!coletasdiarias[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : 'Erro'">
                                                                                    <button mat-icon-button color="primary"
                                                                                            (click)="setRespostaPeriodo(idxSessao, objetivo, etapa, estimulo, 'negativos')"
                                                                                            [disabled]="!coletasdiarias[idxSessao]?.id">
                                                                                        <mat-icon>thumb_down</mat-icon>
                                                                                    </button>
                                                                                    <mat-form-field appearance="outline" class="contador-input">
                                                                                        <input matInput
                                                                                            type="number"
                                                                                            [formControl]="controles[idxSessao][objetivo.id + '_' + etapa.id + '_' + estimulo.id]?.get('negativos')"
                                                                                            (click)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'negativos')"
                                                                                            (blur)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'negativos')"
                                                                                            min="0">
                                                                                    </mat-form-field>
                                                                                </div>
                                                                                <!-- Independentes -->
                                                                                <div class="resp-container" [matTooltip]="!coletasdiarias[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : 'Independente'">
                                                                                    <button mat-icon-button color="primary"
                                                                                            (click)="setRespostaPeriodo(idxSessao, objetivo, etapa, estimulo, 'independentes')"
                                                                                            [disabled]="!coletasdiarias[idxSessao]?.id"
                                                                                            style="text-decoration: none; cursor: pointer;">
                                                                                        <mat-icon>add_circle</mat-icon>
                                                                                    </button>
                                                                                    <mat-form-field appearance="outline" class="contador-input">
                                                                                        <input matInput
                                                                                            type="number"
                                                                                            [formControl]="controles[idxSessao][objetivo.id + '_' + etapa.id + '_' + estimulo.id]?.get('independentes')"
                                                                                            (click)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'independentes')"
                                                                                            (blur)="onControlTouched(idxSessao, objetivo, etapa, estimulo, 'independentes')"
                                                                                            min="0">
                                                                                    </mat-form-field>
                                                                                </div>
                                                                                <div class="limite-box">
                                                                                    Total: {{ getTotalRespostas(estimulo) }} / {{ objetivo.oportunidadesEstimulo }}
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </ng-container>
                                                            </ng-container>
                                                        </div>
                                                    </ng-container>
                                                </td>
                                            </tr>
                                            <tr *ngIf="!objetivo.habilidades">
                                                <td [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
                                                    {{ objetivo.id }} - {{ objetivo.nome }}
                                                    <ng-container *ngIf="objetivo.status == 'Adquirido'">
                                                        <table class="etapa">
                                                            <tr>
                                                                <td class="etapa v-middle">
                                                                    <mat-icon>construction</mat-icon>
                                                                    Objetivo em manutenção
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </ng-container>
                                                    <div *ngFor="let etapa of objetivo.etapa; let i=index">
                                                        <table class="etapa">
                                                            <tr>
                                                                <th></th>
                                                                <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                .etapa[coletasdiarias[idxSessao].objetivos
                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                    .etapa.indexOf(etapa)].periodo; let idx = index">
                                                                    <th class="periodo">P{{idx+1}}</th>
                                                                </ng-container>
                                                            </tr>
                                                            <tr>
                                                                <td class="etapa">{{ coletasdiarias[idxSessao].objetivos
                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                        .etapa[coletasdiarias[idxSessao].objetivos
                                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                .etapa.indexOf(etapa)].id.slice(-3) }} - 
                                                                    {{ coletasdiarias[idxSessao].objetivos
                                                                            [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                            .etapa[coletasdiarias[idxSessao].objetivos
                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                    .etapa.indexOf(etapa)].nome }}
                                                                </td>
                                                                <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                .etapa[coletasdiarias[idxSessao].objetivos
                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                    .etapa.indexOf(etapa)].periodo; let idxP = index">
                                                                    <td style="text-align: center;" [matTooltip]="!coletasdiarias[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : ''">
                                                                        <a style="text-decoration: none; cursor: pointer;" (click)="setRespostaPeriodoESDM(idxSessao, idxP, objetivo, etapa)"
                                                                        [ngClass]="{'disabled': !coletasdiarias[idxSessao]?.id}">
                                                                            <mat-icon>{{ getPeriodoIcon(periodo) }}</mat-icon>
                                                                        </a>
                                                                    </td>
                                                                </ng-container>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </div>
                                    </ng-container>
                                </table>
                            </div>
                            <div *ngIf="!planoIntervencao?.objetivos || planoIntervencao?.objetivos?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                                <h1>Nenhum objetivo cadastrado no PEI!</h1>
                            </div>
                        </mat-tab>
                        <mat-tab style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;" [label]="TittlePIC">
                            <div *ngIf="pic?.objetivos?.length > 0" class="container">
                                <ng-container>
                                    <div *ngFor="let objetivoPIC of pic.objetivos" class="item" [matTooltip]="!coletasdiariasPIC[idxSessao]?.id ? 'Sessão não inciada! Favor iniciar a sessão.' : ''"> 
                                    <!-- Define o tamanho máximo que cada componente pode ocupar -->
                            
                                        <!-- Registro de Eventos -->
                                        <div *ngIf="objetivoPIC?.tipoColeta == 'Registro de Eventos'"
                                        [ngClass]="{'disabled': !coletasdiariasPIC[idxSessao]?.id}">
                                            <app-count 
                                                [objetivoPIC]="objetivoPIC" 
                                                [coletasdiariasPIC]="coletasdiariasPIC"
                                                [idxSessao]="idxSessao"
                                                (coletaDiariaPICChange)="onColetaDiariaPICChange($event)">
                                            </app-count>
                                        </div>

                                        <!-- Cronometragem ou Amostragem de Tempo -->
                                        <div *ngIf="objetivoPIC?.tipoColeta == 'Cronometragem' || objetivoPIC?.tipoColeta == 'Amostragem de Tempo'"
                                        [ngClass]="{'disabled': !coletasdiariasPIC[idxSessao]?.id}">
                                            <app-timer 
                                                [objetivoPIC]="objetivoPIC" 
                                                [coletasdiariasPIC]="coletasdiariasPIC"
                                                [idxSessao]="idxSessao"
                                                (coletaDiariaPICChange)="onColetaDiariaPICChange($event)">
                                            </app-timer>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                            <div *ngIf="!pic?.objetivos || pic?.objetivos?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                                <h1>Nenhum objetivo cadastrado no PIC!</h1>
                            </div>
                        </mat-tab>                          
                    </mat-tab-group>
                    <!-- FIM DE SESSÃO -->
                </mat-tab>
            </mat-tab-group>
        </div>
        <!-- FIM DAS SESSÕES -->  
        <div style="width: 100%; display: table; margin: 0%; margin-top: 10px;">
            <div style="display: table-row-group;">
                <div style="display:table-row;">
                    <div style="display: table-cell;">
                        SFT – Suporte físico total
                    </div>
                    <div style="display: table-cell; text-align: right; margin: 0%;">
                        SFP – Suporte físico parcial
                    </div>
                </div>
                <div style="display:table-row;">
                    <div style="display: table-cell;">
                        SV – Suporte verbal
                    </div>
                    <div style="display: table-cell; text-align: right; margin: 0%;">
                        SG – Suporte gestual
                    </div>
                </div>
                <div style="display:table-row;">
                    <div style="display: table-cell;">
                        SP – Suporte posicional
                    </div>
                    <div style="display: table-cell; text-align: right; margin: 0%;">
                        &nbsp;
                    </div>
                </div>
            </div>
        </div>  
    </mat-card-content>
</mat-card> 

<!-- Todos os controles numa única célula -->
<!-- <td class="resps-row">
    <div class="resp-container">
        <button mat-icon-button color="primary"
            (click)="setRespostaPeriodoESDM(idxSessao, objetivo, etapa, 'positivos')"
            [disabled]="!coletasdiarias[idxSessao]?.id">
            <mat-icon>thumb_up</mat-icon>
        </button>
        <mat-form-field appearance="outline" class="contador-input">
            <input matInput type="number"
                [formControl]="controles[idxSessao][etapa.id].get('positivos')"
                (blur)="onControlTouchedESDM(idxSessao, objetivo, etapa, 'positivos')"
                min="0">
        </mat-form-field>
    </div>
    <div class="resp-container">
        <button mat-icon-button color="primary"
                (click)="setRespostaPeriodoESDM(idxSessao, objetivo, etapa, 'negativos')"
                [disabled]="!coletasdiarias[idxSessao]?.id">
            <mat-icon>thumb_down</mat-icon>
        </button>
        <mat-form-field appearance="outline" class="contador-input">
            <input matInput type="number"
                [formControl]="controles[idxSessao][etapa.id].get('negativos')"
                (blur)="onControlTouchedESDM(idxSessao, objetivo, etapa, 'negativos')"
                min="0">
        </mat-form-field>
    </div>
    <div class="resp-container">
        <button mat-icon-button color="primary"
                (click)="setRespostaPeriodoESDM(idxSessao, objetivo, etapa, 'independentes')"
                [disabled]="!coletasdiarias[idxSessao]?.id">
            <mat-icon>add_circle</mat-icon>
        </button>
        <mat-form-field appearance="outline" class="contador-input">
            <input matInput type="number"
                [formControl]="controles[idxSessao][etapa.id].get('independentes')"
                (blur)="onControlTouchedESDM(idxSessao, objetivo, etapa, 'independentes')"
                min="0">
        </mat-form-field>
    </div>
</td> -->