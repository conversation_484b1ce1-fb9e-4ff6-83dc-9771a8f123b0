import { environment } from './../../../environments/environment';
import { Nivel } from './nivel-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class NivelService {

  nivelUrl = `${environment.API_URL}/nivel`;
  
  //public $niveis: BehaviorSubject<Nivel[]> = 
  //  new BehaviorSubject<Nivel[]>([]);

  public $niveis: Observable<Nivel[]>;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  /*
  create(nivel: Nivel): Observable<Nivel>{
    console.log(nivel);
    return this.http.post<Nivel>(this.nivelUrl, nivel).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  findById(id: string): Observable<Nivel>{
    //Carregos os níveis, caso necessário
    if(this.$niveis == null){
      this.find();
    }
    return this.$niveis.pipe(
      map(ns => {
        ns.find(nivel => nivel.id == id);
      }),
      catchError(e => this.errorHandler(e) )
    )
    /*
    return this.http.get<Nivel>(this.nivelUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
    */
  }

  find(): Observable<Nivel[]>{
    if(this.$niveis == null){
      //Carrego os níveis pela primeira vez
      //console.log('Carreguei os níveis...');
      this.$niveis = this.http.get<Nivel[]>(this.nivelUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
    return this.$niveis;
    /*
    return this.http.get<Nivel[]>(this.nivelUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
    */
  }

  /*
  delete(id: string): Observable<Nivel>{
    return this.http.delete<Nivel>(this.nivelUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */
}