import { Paciente } from './../../components/paciente/paciente-model';
import { PacienteService } from './../../components/paciente/paciente.service';
import { ChartType, ChartOptions, ChartDataSets } from 'chart.js';
import { MultiDataSet, Label, PluginServiceGlobalRegistrationAndOptions } from 'ng2-charts';
import { HeaderService } from './../../components/template/header/header.service';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import * as pluginDataLabels from 'chartjs-plugin-datalabels'; 
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {

  public pieChartLabelsChklst: Label[];
  public pieChartLabelsColeta: Label[];
  public pieChartLabelsPlano: Label[];
  
  public pieChartPercentualChklst: Label[];
  public pieChartPercentualColeta: Label[];
  public pieChartPercentualPlano: Label[];
  
  public pieChartDataChklst = [0,0,0];//: MultiDataSet;
  public pieChartDataColeta = [0,0,0];//: MultiDataSet;
  public pieChartDataPlano = [0,0,0];//: MultiDataSet;
  
  public pieChartType: ChartType = 'pie';
  
  public pieChartPluginsChklst: PluginServiceGlobalRegistrationAndOptions[];
  public pieChartPluginsColeta: PluginServiceGlobalRegistrationAndOptions[];
  public pieChartPluginsPlano: PluginServiceGlobalRegistrationAndOptions[];

  public pieChartOptionsChklst: ChartOptions = {
    responsive: true,
    legend: {
      position: 'top',
      display: false
    },
    title: {
      display: true,
      fontSize: 18,
      fontFamily: 'Helvetica',
      text: ['Avaliação']
    },
    plugins: {
      datalabels: {
        labels:{
          name: {
            align: 'center',
            font: {size: 16},
            formatter: function(value, ctx) {
              var perc = Number(ctx.chart.data.datasets[0].data[ctx.dataIndex])  / (
                Number(ctx.chart.data.datasets[0].data[0]) + Number(ctx.chart.data.datasets[0].data[1])
                 + Number(ctx.chart.data.datasets[0].data[2])
              )
              return Number(perc * 100).toFixed(0) + "%";
            }
          },
          value: {
            align: 'bottom',
            /*backgroundColor: function(ctx) {
              var value = ctx.dataset.data[ctx.dataIndex];
              return value > 50 ? 'white' : null;
            },*/
            formatter: function(value, ctx) {
              return value;
            },
            padding: 4
          }
        }
      },
    }
  };
  public pieChartOptionsColeta: ChartOptions = {
    responsive: true,
    legend: {
      position: 'top',
      display: false
    },
    title: {
      display: true,
      fontSize: 18,
      fontFamily: 'Helvetica',
      text: ['Coleta Diária']
    },
    plugins: {
      datalabels: {
        labels:{
          name: {
            align: 'center',
            font: {size: 16},
            formatter: function(value, ctx) {
              var perc = Number(ctx.chart.data.datasets[0].data[ctx.dataIndex])  / (
                Number(ctx.chart.data.datasets[0].data[0]) + Number(ctx.chart.data.datasets[0].data[1])
                 + Number(ctx.chart.data.datasets[0].data[2])
              )
              return Number(perc * 100).toFixed(0) + "%";
            }
          },
          value: {
            align: 'bottom',
            /*backgroundColor: function(ctx) {
              var value = ctx.dataset.data[ctx.dataIndex];
              return value > 50 ? 'white' : null;
            },*/
            formatter: function(value, ctx) {
              return value;
            },
            padding: 4
          }
        }
      },
    }
  };

  public pieChartOptionsPlano: ChartOptions = {
    responsive: true,
    legend: {
      position: 'top',
      display: false
    },
    title: {
      display: true,
      fontSize: 18,
      fontFamily: 'Helvetica',
      text: ['Plano de Intervenção']
    },
    plugins: {
      datalabels: {
        labels:{
          name: {
            align: 'center',
            font: {size: 16},
            formatter: function(value, ctx) {
              var perc = Number(ctx.chart.data.datasets[0].data[ctx.dataIndex])  / (
                Number(ctx.chart.data.datasets[0].data[0]) + Number(ctx.chart.data.datasets[0].data[1])
                 + Number(ctx.chart.data.datasets[0].data[2])
              )
              return Number(perc * 100).toFixed(0) + "%";
            }
          },
          value: {
            align: 'bottom',
            /*backgroundColor: function(ctx) {
              var value = ctx.dataset.data[ctx.dataIndex];
              return value > 50 ? 'white' : null;
            },*/
            formatter: function(value, ctx) {
              return value;
            },
            padding: 4
          }
        }
      },
    }
  };

  public pieChartPlugins = [pluginDataLabels];

  public pacientes: Paciente[];
  

  constructor(private headerService: HeaderService,
    private pacienteService: PacienteService,
    private loadingService: LoadingService) {
    headerService.headerData = {
      title: 'Início',
      icon: 'home',
      routeUrl: ''
    }
   }

   public colors: any[];
   public kpiChklst: number;
   public numChklstDesatualizado: number;
   public numChklstAtencao: number;
   public numChklstAtualizado: number;
   
   public kpiColeta: number;
   public numColetaDesatualizado: number;
   public numColetaAtencao: number;
   public numColetaAtualizado: number;
   
   public kpiPlano: number;
   public numPlanoDesatualizado: number;
   public numPlanoAtencao: number;
   public numPlanoAtualizado: number;

   public totPacientes = 0;

  ngOnInit(): void {
    this.loadingService.show();


    this.pacienteService.find().subscribe(pacientes => {
      this.pacienteService.pacientes.next(pacientes.filter(p => p.ativo == true));
      this.totPacientes = pacientes.filter(p => p.ativo == true).length;
      this.pacienteService.pacientes.next(pacientes.filter(p => p.ativo == true));
      this.numChklstDesatualizado = pacientes.filter(p => p.ativo == true && p.kpiChkLst == 'red').length;
      this.numChklstAtencao = pacientes.filter(p => p.ativo == true && p.kpiChkLst == 'orange').length;
      this.numChklstAtualizado = pacientes.filter(p => p.ativo == true && p.kpiChkLst == 'green').length;
      this.kpiChklst = this.numChklstAtualizado / pacientes.filter(p => p.ativo == true).length;
      this.pieChartDataChklst = 
        [this.numChklstDesatualizado, this.numChklstAtencao, this.numChklstAtualizado];
      this.pieChartPercentualChklst = [  (this.numChklstDesatualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%", 
                                (this.numChklstAtencao/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%",
                                (this.numChklstAtualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%"];
      this.pieChartLabelsChklst = [  "Desatualizado", 
                                "Atenção",
                                "Atualizado"];

      this.numColetaDesatualizado = pacientes.filter(p => p.ativo == true && p.kpiColeta == 'red').length;
      this.numColetaAtencao = pacientes.filter(p => p.ativo == true && p.kpiColeta == 'orange').length;
      this.numColetaAtualizado = pacientes.filter(p => p.ativo == true && p.kpiColeta == 'green').length;
      this.kpiColeta = this.numColetaAtualizado / pacientes.filter(p => p.ativo == true).length;
      this.pieChartDataColeta = 
        [this.numColetaDesatualizado, this.numColetaAtencao, this.numColetaAtualizado];
      this.pieChartPercentualColeta = [  (this.numColetaDesatualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%", 
                                  (this.numColetaAtencao/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%",
                                  (this.numColetaAtualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%"];

      this.numPlanoDesatualizado = pacientes.filter(p => p.ativo == true && p.kpiPlInterv == 'red').length;
      this.numPlanoAtencao = pacientes.filter(p => p.ativo == true && p.kpiPlInterv == 'orange').length;
      this.numPlanoAtualizado = pacientes.filter(p => p.ativo == true && p.kpiPlInterv == 'green').length;
      this.kpiPlano = this.numPlanoAtualizado / pacientes.filter(p => p.ativo == true).length;
      this.pieChartDataPlano = 
        [this.numPlanoDesatualizado, this.numPlanoAtencao, this.numPlanoAtualizado];
      this.pieChartPercentualPlano = [  (this.numPlanoDesatualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%", 
                                  (this.numPlanoAtencao/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%",
                                  (this.numPlanoAtualizado/pacientes.filter(p => p.ativo == true).length * 100).toFixed(0)+ "%"];
      this.loadingService.hide();

    })

    //this.numChklstDesatualizado = 14;
    //this.numChklstAtencao = 11;
    //this.numChklstAtualizado = 53;
    //this.kpiChklst = this.numChklstAtualizado / 78;
    //this.pieChartDataChklst = 
    //  [this.numChklstDesatualizado, this.numChklstAtencao, this.numChklstAtualizado];
    //this.pieChartPercentualChklst = [  (this.numChklstDesatualizado/78 * 100).toFixed(0)+ "%", 
    //                            (this.numChklstAtencao/78 * 100).toFixed(0)+ "%",
    //                            (this.numChklstAtualizado/78 * 100).toFixed(0)+ "%"];
    //this.pieChartLabelsChklst = [  "Desatualizado", 
    //                            "Atenção",
    //                            "Atualizado"];
    
    
    //this.numColetaDesatualizado = 14;
    //this.numColetaAtencao = 21;
    //this.numColetaAtualizado = 43;
    //this.kpiColeta = this.numColetaAtualizado / 78;
    //this.pieChartDataColeta = 
    //  [this.numColetaDesatualizado, this.numColetaAtencao, this.numColetaAtualizado];
    //this.pieChartPercentualColeta = [  (this.numColetaDesatualizado/78 * 100).toFixed(0)+ "%", 
    //                              (this.numColetaAtencao/78 * 100).toFixed(0)+ "%",
    //                              (this.numColetaAtualizado/78 * 100).toFixed(0)+ "%"];
    this.pieChartLabelsColeta = [  "Desatualizado", 
                                  "Atenção",
                                  "Atualizado"];

    //this.numPlanoDesatualizado = 22;
    //this.numPlanoAtencao = 21;
    //this.numPlanoAtualizado = 35;
    //this.kpiPlano = this.numPlanoAtualizado / 78;
    //this.pieChartDataPlano = 
    //  [this.numPlanoDesatualizado, this.numPlanoAtencao, this.numPlanoAtualizado];
    //this.pieChartPercentualPlano = [  (this.numPlanoDesatualizado/78 * 100).toFixed(0)+ "%", 
    //                              (this.numPlanoAtencao/78 * 100).toFixed(0)+ "%",
    //                              (this.numPlanoAtualizado/78 * 100).toFixed(0)+ "%"];
    this.pieChartLabelsPlano = [  "Desatualizado", 
                                  "Atenção",
                                  "Atualizado"];
    
    
    this.colors = [
      { backgroundColor:["rgba(240, 80, 80, 0.5)", "rgba(255, 144, 43, 0.5)", "rgba(39, 194, 76, 0.5)"],
        fill:false,
        borderColor:["rgb(240, 80, 80)","rgb(255, 144, 43)", "rgb(39, 194, 76)"],
      }
    ];
        
  }

  

}
