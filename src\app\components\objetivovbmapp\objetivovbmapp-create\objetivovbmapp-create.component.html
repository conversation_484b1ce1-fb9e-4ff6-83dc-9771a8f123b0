<mat-card [style.overflow]="isModal ? 'auto' : ''" [style.height.vh]=" isModal ? '80' : ''">
    <mat-card-header>
        <mat-card-title>{{ objetivo == undefined || objetivo.nome == undefined ? "Novo Objetivo" : objetivo.nome }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <div style="display: flex; flex-direction: row;  justify-content: space-between;">
            <form #ngForm> 
                <div style="display:flex; flex-direction: column; width: 55%;">
                    <label class="labelInput">Programa</label>
                    <mat-form-field appearance="outline">
                        <input class="input" matInput placeholder="" 
                            [(ngModel)]="objetivo.nome" name="programa" 
                            [disabled]="!hasAccessUpdate"
                            required>
                        <mat-error *ngIf="nomeFC.invalid">Programa é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>

                <div style="padding-left: 10px; display:flex; flex-direction: column; width: 20%;">
                    <!-- <label class="labelInput">Marco</label>
                    <mat-form-field appearance="outline">
                        <input type="text"
                            placeholder="" 
                            matInput
                            [(ngModel)]="objetivo.id_marco"
                            name="Marco"
                            [formControl]="marcoFC"
                            [matAutocomplete]="autoGroup">
                        <mat-icon matSuffix>keyboard_arrow_down</mat-icon>
                        <mat-autocomplete #autoGroup="matAutocomplete" (optionSelected) = "setMarco()">
                            <mat-optgroup  *ngFor="let nivel of marcoGroupOptions | async">
                                <strong>{{nivel.descricao}}</strong>
                                <mat-option  *ngFor="let marco of nivel.marcos" [value]="marco.id" >
                                    {{marco.id}}
                                    <mat-icon [matTooltip]="marco.nome" style="float: right;">list</mat-icon>
                                </mat-option>
                            </mat-optgroup>
                        </mat-autocomplete>
                    </mat-form-field> -->
                </div>  
                
                <div style="padding-left: 10px; display:flex; flex-direction: column; width: 20%; align-self: flex-end;">
                    <label class="labelInput">Tipo de Coleta</label>
                    <mat-form-field appearance="outline">
                        <mat-select
                            [(ngModel)]="objetivo.tipoColeta"
                            [disabled]="!hasAccessUpdate"
                            name="tipoColeta" required>
                            <mat-option value="Naturalista" >
                                Naturalista
                            </mat-option>
                            <!-- <mat-option value="DTT" >
                                DTT (Discrete Trial Training)
                            </mat-option> -->
                        </mat-select>
                        <mat-error *ngIf="tipoColetaFC.invalid">Tipo de Coleta é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>
                <br>
                <div style="padding-top: 10px; display:flex; flex-direction: column; width: 100%;">
                <!-- <div style="display:flex; flex-direction: column; width: 100% !important;"> -->
                    <label class="labelInput">Habilidades</label>
                    <div class="group">
                        <div style="padding-left: 10px; display:flex; flex-direction: row; width: 100%;">
                            <div style="padding-left: 10px; display:flex; flex-direction: column; width: 30%;">
                                <mat-form-field >

                                    <mat-select placeholder="Tipo de Avaliação" 
                                    [(ngModel)]="tipoAvaliacao" appearance="outline"
                                    name="tipoAvaliacao" (selectionChange) = "setTipoAvaliacao($event)">
                                    <mat-option *ngFor="let tipoAvaliacao of tiposAvaliacao" [value]="tipoAvaliacao">
                                        {{tipoAvaliacao.nome}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            </div>

                            <div style="padding-left: 10px; display:flex; flex-direction: column; width: 15%;">
                                <mat-form-field>
                                    <mat-select placeholder="Nível" 
                                        [(ngModel)]="nivel"
                                        name="nivel" (selectionChange) = "setNivel($event)">
                                        <mat-option *ngFor="let nivel of niveis" [value]="nivel">
                                            {{nivel.nome}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div style="padding-left: 10px; display:flex; flex-direction: column; width: 30%;">
                                <mat-form-field>
                                    <mat-select placeholder="Domínio" 
                                        [(ngModel)] = "dominio"
                                        name="dominio" (selectionChange) = "setDominio()">
                                            <mat-option *ngFor="let dom of dominios" [value]="dom">
                                                {{dom.nome}}
                                            </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>

                            <div style="padding-left: 10px; display:flex; flex-direction: column; width: 20%;">
                                <mat-form-field>
                                    <input type="text"
                                        placeholder="Habilidade" 
                                        matInput
                                        [(ngModel)]="siglaHabilidade"
                                        name="Habilidade"
                                        [formControl]="marcoFC"
                                        [matAutocomplete]="auto">
                                    <mat-icon matSuffix>keyboard_arrow_down</mat-icon>
                                    <mat-autocomplete #auto="matAutocomplete" (optionSelected) = "setHabilidade()">
                                        <mat-option *ngFor="let hab of habilidades" [value]="hab.sigla" (click)="addHabilidade($event)">
                                            {{ getNameHabilidade(hab) }}
                                            <mat-icon [matTooltip]="hab.nome" style="float: right;">list</mat-icon>
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                            </div>
                        </div>
                        
                        <div style="padding-left: 10px; padding-top: 10px; display:flex; flex-direction: column; width: 100%;">
                            <mat-divider style="padding-bottom: 10px;" > </mat-divider>
                            <mat-chip-list style="padding-left: 10px; padding-top: 10px;"> 
                                <mat-chip *ngFor="let hab of objetivo.habilidades" [value]="estimulo"
                                    [removable]="hasAccessUpdate">
                                    {{ getTipoAvaliacao(hab.idTipoAvaliacao) }} - {{ getNameHabilidade(hab) }}
                                    <mat-icon matChipRemove (click)="removeHabilidade(hab)"
                                        *ngIf="hasAccessUpdate">cancel</mat-icon>
                                </mat-chip>
                            </mat-chip-list>
                        </div>
                    </div>
                </div>
                <br>
                <div style="padding-top: 10px; display:flex; flex-direction: column; width: 100%;">
                    <label class="labelInput">Meta</label>
                    <mat-form-field appearance="outline">
                        <textarea class="textarea" matInput 
                            [disabled]="!hasAccessUpdate"
                            [(ngModel)]="objetivo.descricao" name="meta" required
                            rows="3"></textarea>
                        <mat-error *ngIf="descricaoFC.invalid">Meta é obrigatória.</mat-error>  
                    </mat-form-field>
                </div>

                <div style="display:flex; flex-direction: column; width: 100%;">
                    <label class="labelInput">SD</label>
                    <mat-form-field appearance="outline">
                        <input class="input" matInput 
                            [disabled]="!hasAccessUpdate"
                            [(ngModel)]="objetivo.sd" name="sd" 
                            [formControl]="sdFC" [errorStateMatcher]="matcher" required>
                        <mat-error *ngIf="sdFC.invalid">SD é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>
 
                <div style="width: 100%; ">
                    <label class="labelInput">Critérios de Sucesso</label><br>
                    <mat-form-field style="width: 5%;" appearance="outline">
                        <input matInput  style="text-align: center;"
                            [(ngModel)]="objetivo.percOportunidadesSucesso" name="percOportunidadesSucesso" 
                             required disabled>
                    </mat-form-field>
                    &nbsp; % das oportunidades, 
                    <mat-form-field style="width: 5%;" appearance="outline">
                        <input matInput disabled
                            [(ngModel)]="objetivo.diasSucesso" name="diasSucesso" required style="text-align: center;"
                              >
                    </mat-form-field>
                    dias consecutivos de acertos. <!-- e com 2 ou mais pessoas diferentes e 2 ou mais ambientes diferentes. -->

                </div>
                    <!--
                            <input class="input" matInput placeholder="Critério de Sucesso" 
                                [(ngModel)]="objetivo.criterioSucesso" name="criterioSucesso" required>
                            percOportunidadesSucesso: number;
                            diasSucesso: number;
                            pessoasSucesso: number;
                            ambientesSucesso: number;
                            80 % das oportunidades, 3 dias consecutivos de acertos e com 2 ou mais pessoas diferentes e 2 ou mais ambientes diferentes.
                        -->

                    <!--mat-error *ngIf="criterioSucessoFC.invalid">Critério de Sucesso é obrigatório.</mat-error-->  


                <mat-card style="width: 100%; margin-top: 10px; " class="mat-elevation-z4">
                    <mat-card-header>
                        <mat-card-title>
                            Estímulos<br>
                        </mat-card-title>
                        <mat-card-subtitle>
                            <small style="font-size: xx-small;">
                                ({{ objetivo.estimulos.length}} selecionados)
                            </small>
                        </mat-card-subtitle>
                    </mat-card-header>
                    <div style="display:flex; flex-direction: row; width: 100%;">
                        <div style="display:flex; flex-direction: column; width: 70%;">
                            <label class="labelInput">Materiais</label>
                            <mat-form-field appearance="outline">
                                <textarea class="textarea" matInput  
                                    [(ngModel)]="objetivo.materiais" name="materiais" 
                                    [formControl]="materiaisFC" [errorStateMatcher]="matcher" required
                                    rows="3"></textarea>
                                <mat-error *ngIf="materiaisFC.invalid">Materiais é obrigatória.</mat-error>  
                            </mat-form-field>
                        </div>
        
                        <div style="display:flex; flex-direction: column; width: 30%; padding-left: 10px;">
                            <label class="labelInput">Nº de Oportunidades/Estímulo</label>
                            <mat-form-field appearance="outline">
                                <input matInput type="number" min="1"
                                    [(ngModel)]="objetivo.oportunidadesEstimulo" name="oportunidadesEstimulo" 
                                    [formControl]="oportunidadesEstimuloFC" [errorStateMatcher]="matcher" required
                                    >
                                <mat-error *ngIf="oportunidadesEstimuloFC.invalid">Número de Oportunidades por Estímulo é obrigatória.</mat-error>  
                            </mat-form-field>
                        </div>

                    </div>
                    
                    <div style="display:flex; flex-direction: column; width: 100%;">
                        <label class="labelInput">Estímulos Futuros</label>
                        <mat-form-field appearance="outline">
                            <textarea class="textarea" matInput  
                                [disabled]="!hasAccessUpdate"
                                [(ngModel)]="objetivo.estimulosFuturos" name="estimulosFuturos" 
                                rows="3"></textarea> 
                        </mat-form-field>
                    </div>

                    <!-- Estímulos Selecionados-->
                    <mat-card-content>
                        <!--mat-form-field style="width: 100%; padding-left: 10px;" floatLabel="never"-->
                            <!--mat-label>Estímulos selecionados</mat-label-->
                            <mat-chip-list>
                                <mat-chip *ngFor="let estimulo of objetivo.estimulos" [value]="estimulo"
                                    [removable]="hasAccessUpdate">
                                    {{estimulo.nome}}
                                    <mat-icon matChipRemove (click)="removeEstimulo(estimulo)"
                                        *ngIf="hasAccessUpdate">cancel</mat-icon>
                                    <mat-icon matChipRemove *ngIf="estimulo.ativo && idVbmappPlan != undefined && hasAccessUpdate"
                                        (click)="toogleEstimulo(estimulo)"
                                        matTooltip="Desabilitar coleta">sensors</mat-icon>
                                    <mat-icon matChipRemove *ngIf="!estimulo.ativo && idVbmappPlan != undefined && hasAccessUpdate" 
                                        (click)="toogleEstimulo(estimulo)"
                                        matTooltip="Habilitar coleta">sensors_off</mat-icon>
                                </mat-chip>
                            </mat-chip-list>
                        <!--/mat-form-field-->
                    </mat-card-content>

                    <!-- Selecionar Estímulos-->
                    <mat-card-footer style="padding: 15px; padding-top: 30px; background-color: lightgray;"
                        *ngIf="hasAccessUpdate">
                        <div class="v-middle" style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
                            <div class="v-middle" style="width: 190px; margin-top: 30px; text-align: left;">
                                Selecione novos estímulos:
                            </div>
                            <div class="v-middle" style="width: 30%;">
                                <mat-form-field>
                                    <mat-label>Categoria</mat-label>
                                    <input type="text"
                                        placeholder="Categoria" 
                                        matInput
                                        [(ngModel)]="categoriaEstimulo"
                                        name="categoria"
                                        [formControl]="categEstimuloFC"
                                        [matAutocomplete]="auto">
                                        <mat-icon matSuffix>keyboard_arrow_down</mat-icon>
                                    <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn"
                                        (optionSelected) = "setEstimulos()">
                                        <mat-option *ngFor="let categoria of filteredCategsEstimulo | async" [value]="categoria">
                                            {{categoria.nome}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                                <mat-icon matSuffix matTooltip="Criar nova categoria" class="create-categoria" (click)="addCategoria($event)">add</mat-icon>
                                <!-- <mat-select placeholder="Categoria" 
                                    [(ngModel)]="idCategoriaEstimulo"
                                    name="categoria" (selectionChange) = "setEstimulos($event)">
                                    <mat-option *ngFor="let categoria of categoriasEstimulo" [value]="categoria.id">
                                        {{categoria.nome}}
                                    </mat-option>
                                </mat-select> -->
                            </div>
                            <div class="v-middle" style="width: 50%;"></div>
                            <div class="v-middle" style="width: 100%; padding-left: 10px; padding-top: 10px;">
                                <mat-chip-list #chipListEstimulo style="width: 100%">
                                    <mat-chip *ngFor="let estimulo of filteredEstimulos" [value]="estimulo"
                                        (click)="addEstimulo(estimulo)">
                                        {{estimulo.nome}}
                                    </mat-chip>
                                    <input placeholder="Novo estímulo..."
                                        [matChipInputFor]="chipListEstimulo"
                                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                                        [matChipInputAddOnBlur]="addOnBlur"
                                        (matChipInputTokenEnd)="addNewEstimulo($event)"
                                        style="padding-left: 5px;">
                                </mat-chip-list>
                            </div>
                        </div>
                    </mat-card-footer>
                </mat-card>

                <div style="display:flex; flex-direction: column; width: 100%; margin-top: 20px;">
                    <label class="labelInput">Procedimento</label>
                    <mat-form-field appearance="outline">
                        <textarea class="textarea" matInput 
                            [(ngModel)]="objetivo.procedimento" name="procedimento" 
                            [formControl]="procedimentoFC" [errorStateMatcher]="matcher" required
                            rows="3"></textarea>
                        <mat-error *ngIf="procedimentoFC.invalid">Procedimento é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>

                <div style="display:flex; flex-direction: column; width: 100%; margin-top: 20px;">
                    <label class="labelInput">Correção de Erros</label>
                    <mat-form-field appearance="outline">
                        <textarea class="textarea" matInput  
                            [disabled]="!hasAccessUpdate"
                            [(ngModel)]="objetivo.correcaoErros" name="correcaoErros" 
                            [errorStateMatcher]="matcher" required
                            rows="3"></textarea>
                        <mat-error *ngIf="corErrosValidator.invalid">Correção de Erros é obrigatória.</mat-error>  
                    </mat-form-field>
                </div>
                
                <section *ngIf="hasAccessUpdate">
                    <mat-checkbox [(ngModel)] = "precadastro"
                        style="float: right; padding: 10px;" *ngIf="noExistInList"
                        name="precadastro">
                        Salvar no pré-cadastro de objetivos para que seja utilizado em outros Planos de Intervenção
                    </mat-checkbox>
                </section>

                <section *ngIf="hasAccessUpdate">
                    <mat-checkbox [(ngModel)] = "updatePrecadastro"
                        style="float: right; padding: 10px;" *ngIf="ExistInListEdit"
                        name="updatePrecadastro">
                        Atualizar no pré-cadastro de objetivos
                    </mat-checkbox>
                </section>

                <!-- Tipos de Suporte -->
                <mat-card style="width: 100%; padding-bottom: 20px;" class="mat-elevation-z4">
                    <mat-card-header>
                        <mat-card-title>Tipos de Suporte (Ordenação)</mat-card-title>
                    </mat-card-header>
                    <mat-card-content>
                        <div class="mat-elevation-z4">
                            <table mat-table [dataSource]="objetivo.tiposSuporte"> 
                                <!-- Sigla Column --> 
                                <ng-container matColumnDef="sigla">
                                    <th mat-header-cell *matHeaderCellDef >Id</th>
                                    <td mat-cell *matCellDef="let row">
                                        {{ row.sigla }}
                                    </td>
                                </ng-container>
                        
                                <!-- Nome Column -->
                                <ng-container matColumnDef="nome" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        {{ row.nome }}
                                    </td>
                                </ng-container> 
                
                                <!-- Action Column -->
                                <ng-container matColumnDef="action">
                                    <th mat-header-cell *matHeaderCellDef>Ações</th>
                                    <td mat-cell *matCellDef="let row">
                                        <!--a (click)="edit(row.id)" class="edit"
                                            *ngIf="hasAccessUpdate">
                                            <i class="material-icons">
                                                edit
                                            </i>
                                        </a-->
                                        <a (click)="up(row.id)" class="edit"
                                            [ngStyle]="objetivo.tiposSuporte.indexOf(row) == 0 && {'visibility': 'hidden'}"
                                            *ngIf="hasAccessUpdate">
                                            <i class="material-icons">
                                                expand_less
                                            </i>
                                        </a>
                                        <a (click)="down(row.id)" class="edit"
                                            [ngStyle]="objetivo.tiposSuporte.indexOf(row) == (objetivo.tiposSuporte.length - 1) && {'visibility': 'hidden'}"
                                            *ngIf="hasAccessUpdate">
                                            <i class="material-icons">
                                                expand_more
                                            </i>
                                        </a>
                                        <a (click)="delete(row.id)" class="delete"
                                            *ngIf="hasAccessDelete">
                                            <i class="material-icons">
                                                delete
                                            </i>
                                        </a>
                                    </td>
                                </ng-container>
                        
                                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                            </table>
                            <div style="display:flex; flex-direction: row; width: 100%; margin-top: 20px;">
                                <mat-select placeholder="Tipo de Suporte" 
                                    *ngIf="hasAccessUpdate"
                                    [(ngModel)]="tipoSuporte"
                                    name="tipoSuporte" style="width: 20%; padding: 10px;">
                                    <mat-option *ngFor="let tipo of tiposSuporte" [value]="tipo" (click)="addTipoSuporte()">
                                        {{tipo.nome}}
                                    </mat-option>
                                </mat-select>
                            </div>
                        </div>        
                    </mat-card-content>
                </mat-card>
            </form>
        </div>
    </mat-card-content>
    <mat-card-actions *ngIf="!data">
        <!--button mat-raised-button color="primary" (click)="save(true)"
            *ngIf="hasAccessUpdate">
            Salvar e Ir para Próximo
        </button-->
        <button mat-raised-button color="primary" (click)="save(false)"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()">
            Cancelar
        </button>
    </mat-card-actions>
    <mat-card-actions *ngIf="data">
        <button mat-raised-button color="primary" (click)="addObjetivoPersonalizado()">
            Salvar
        </button>
        <button mat-raised-button mat-dialog-close>
            Cancelar
        </button>
    </mat-card-actions>
</mat-card>