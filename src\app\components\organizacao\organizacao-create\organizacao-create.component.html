<mat-card *ngIf="authService.getUser().admin">
    <mat-card-header>
        <mat-card-title>{{organizacao.nome}}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field style="width: 43%; padding-right: 15px;">
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="organizacao.nome" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 43%; padding-right: 15px;">
                    <input class="input" matInput placeholder="Razão Social" 
                        [(ngModel)]="organizacao.razaoSocial" name="razaoSocial" required>
                    <mat-error *ngIf="razaoSocialFC.invalid">Razão Social é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 20%; padding-right: 15px;">
                    <input class="input" matInput placeholder="CNPJ" 
                        [(ngModel)]="organizacao.cnpj" name="cnpj" required>
                    <mat-error *ngIf="cnpjFC.invalid">CNPJ é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 30%; padding-right: 15px;">
                    <input class="input" matInput placeholder="E-mail" 
                        [(ngModel)]="organizacao.email" name="email" required>
                    <mat-error *ngIf="emailFC.invalid">E-mail é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 20%; padding-right: 15px;">
                    <input class="input" matInput placeholder="Telefone" 
                        [(ngModel)]="organizacao.telefone" name="telefone">
                </mat-form-field>
            </form>

        </div>
        <h3><strong>Horários de Atendimento</strong></h3>
        <table>
            <tr>
                <th>Dia da Semana</th>
                <th>Horário de Início</th>
                <th>Horário de Término</th>
            </tr>
            <tr style="text-align: center;">
                <td>Domingo</td>
                <td><input type="time" [(ngModel)]="domingo.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="domingo.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Segunda</td>
                <td><input type="time" [(ngModel)]="segunda.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="segunda.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Terça</td>
                <td><input type="time" [(ngModel)]="terca.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="terca.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Quarta</td>
                <td><input type="time" [(ngModel)]="quarta.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="quarta.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Quinta</td>
                <td><input type="time" [(ngModel)]="quinta.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="quinta.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Sexta</td>
                <td><input type="time" [(ngModel)]="sexta.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="sexta.horaTermino"></td>
            </tr>
            <tr style="text-align: center;">
                <td>Sábado</td>
                <td><input type="time" [(ngModel)]="sabado.horaInicio" ></td>
                <td><input type="time" [(ngModel)]="sabado.horaTermino"></td>
            </tr>
        </table>
    </mat-card-content>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>