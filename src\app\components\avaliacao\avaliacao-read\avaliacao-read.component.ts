import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Paciente } from '../../paciente/paciente-model';
import { TipoAvaliacao } from '../tipo-avaliacao-model';
import { Avaliacao } from '../avaliacao-model';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../template/auth/auth.service';
import { AvaliacaoService } from '../avaliacao.service';
import { TipoAvaliacaoService } from '../tipo-avaliacao.service';
import { HabilidadeAvaliacaoService } from '../habilidade-avaliacao.service';
import { NivelAvalicao } from '../nivel-avaliacao-model';
import { DominioAvaliacao } from '../dominio-avaliacao-model';
import { RespostaHabilidadeAvaliacao } from '../resposta-habilidade-avaliacao-model';
import { HabilidadeAvaliacao } from '../habilidade-avaliacao-model';
import { DominioRespostaHabilidadeAvaliacao } from '../dominio-resposta-habilidade-avaliacao-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { DominioAvaliacaoService } from '../dominio-avaliacao.service';
import { NivelAvaliacaoService } from '../nivel-avaliacao.service';
import { Profissional } from '../../profissional/profissional-model';
import { PacienteService } from '../../paciente/paciente.service';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-avaliacao-read',
  templateUrl: './avaliacao-read.component.html',
  styleUrls: ['./avaliacao-read.component.css']
})
export class AvaliacaoReadComponent implements OnInit {

  public tipoAvaliacao: TipoAvaliacao;
  public paciente: Paciente = new Paciente();
  public avaliacao: Avaliacao = new Avaliacao();
  public idAvaliacao: string;
  public profissional: Profissional = new Profissional();
  
  public nivel: NivelAvalicao = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();
  public niveis: NivelAvalicao[] = [];
  public dominios: DominioAvaliacao[] = [];

  public respostasHabilidadesView: RespostaHabilidadeAvaliacao[] = [];
  public habilidadesView: HabilidadeAvaliacao[] = [];
  public habilidades: HabilidadeAvaliacao[] = [];
  public domoniosResposta: DominioRespostaHabilidadeAvaliacao[] = [];
  public avaliacoes: Avaliacao[] = [];

  public viewAvaliacaoPDF: boolean;
  public viewVbmappPDF: boolean;
  public displayedColumnsMsAssmt = ['id', 'nome', 'status']
  public displayedColumns: string[] = ['id', 'nome']

  public hasAccessRead: boolean;
  public hasAccessReadVBMAPP: boolean;
  
  viewPDF: boolean = false;
  idOrganizacao: any;
  
  @ViewChild('avaliacaoPDF', { static: false })
  el: ElementRef;
  todasHabilidadesView: HabilidadeAvaliacao[];

  constructor(private habilidadeService: HabilidadeAvaliacaoService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private avaliacaoService: AvaliacaoService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    private pacienteService: PacienteService,
    private profissionalService: ProfissionalService,
    private habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    public authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.loadingService.show();
    this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
    this.idAvaliacao = this.route.snapshot.paramMap.get('idAvaliacao');

    this.avaliacaoService.findById(this.idAvaliacao).subscribe(av => {
      this.avaliacao = av;

      this.pacienteService.findById(av.idPaciente).subscribe(p => {
        this.paciente = p;
        this.hasAccessReadVBMAPP = this.authService.verifySimpleAccess(this.getFuncoesUsuario(p), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read');
        this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(p), 'Paciente.Cadastro de Plano de Intervenção','read');
      })

      this.tipoAvaliacaoService.findById(av.idTipoAvaliacao).subscribe(tipo => {
        this.tipoAvaliacao = tipo;
        //Carrego os níveis do tipo de avaliação
        this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao).subscribe(async n => {
          this.niveis = n;
          this.nivel = n[0];

          //Recupero as habilidades do Tipo de Avaliação selecionado
          (await this.habilidadeAvaliacaoService.findByTipoAvaliacao(this.tipoAvaliacao.id)).subscribe(habs => {
            this.habilidades = habs;
            this.setDominios();
            this.loadingService.hide();
          })
          
          this.profissionalService.findById(av.idProfissional).subscribe(p => {
            this.profissional = p;
          })
        })
      })
    })
  }

  setDominios(){
    this.habilidadeAvaliacaoService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];

      this.filterHabilidades();
    })
  }

  filterHabilidades(){    
    // console.log("filterHabilidades")
    this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel.id, this.dominio.id).subscribe(habilidades => {
      // console.log(habilidades.length)
      this.respostasHabilidadesView = this.avaliacao.respostasHabilidades.filter(resposta => {
        return habilidades.filter(h => "" + h.idDominioAvaliacao === "" + this.dominio.id
          && "" + h.idNivelAvaliacao === "" + this.nivel.id
          && "" + resposta.idHabilidadeAvaliacao === "" + h.id).length > 0
      })
      // console.log(this.respostasHabilidadesView.length)

      this.habilidadesView = this.habilidades.filter(habilidade => {
        return "" + habilidade.idDominioAvaliacao === "" + this.dominio.id
        && "" + habilidade.idNivelAvaliacao === "" + this.nivel.id
      })

      // console.log(this.habilidadesView.length)

      //Carrego os domínios máximos
      for(const [, h] of habilidades.entries()) {
        for(const [, d] of h.dominioResposta.entries()) {
          if(this.domoniosResposta.filter( resp =>  resp.sigla === d.sigla).length == 0){
            this.displayedColumns.push("resp-" + d.sigla)
            this.domoniosResposta.push(d);
          }
        }
      }
      // console.log(this.domoniosResposta)

      this.domoniosResposta.sort(function(a, b) {
        if( a.valor < b.valor) {
          return 1;
        } else {
          return -1;
        }
      })
      // this.habilidadesView = habilidades;

      this.habilidadesView.sort(function(a, b) {
        if( a.ordem > b.ordem) {
          return 1;
        } else {
          return -1;
        }
      })
      this.todasHabilidadesView = this.habilidadesView
    })
  }

  getDominioResposta(valor: string){
    let dr = this.domoniosResposta.find(d => "" + d.valor == valor);

    if (dr == undefined){
      dr = this.domoniosResposta.find(d => d.valor == undefined);
    }
    return dr;
  }

  findIndexHabilidade(idHabilidade: number){
    return this.avaliacao?.respostasHabilidades?.findIndex( resp => parseFloat(resp.idHabilidadeAvaliacao) == idHabilidade);
  }

  getRespostaHabilidade(idHabilidade: any){
    return this.avaliacao?.respostasHabilidades[this.findIndexHabilidade(idHabilidade)]
  }

  getClassRespostaHabilidade(idHabilidade: any){
    const habilidadeIndex = this.todasHabilidadesView?.findIndex(habilidade => habilidade.id == idHabilidade);
    const habilidade = (this.todasHabilidadesView[habilidadeIndex] as any);
    const maxValor = habilidade.dominioResposta
      .map(dom => parseFloat(dom.valor))
      .filter(v => !isNaN(v))
      .sort((a, b) => b - a)[0];

    const valorResposta = this.getRespostaHabilidade(idHabilidade)?.valor;
    
    // console.log(this.getRespostaHabilidade(idHabilidade))
    if (this.tipoAvaliacao.id == '6' ) {
      // Avalição Socially Savvy
      switch (valorResposta){
        case '0':
          return "label label-N";
        case '1':
          return "label label-P";
        case '2':
          return "label label-P3";
        case '3':
          return "label label-A";
        default:
          return "label label-X";
      }
    } else if (this.tipoAvaliacao.id == "2" || this.tipoAvaliacao.id == "7") {
      // Avaliação ABLLS-R e AFLS
      if (maxValor == 2) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-P";
          case '2':
            return "label label-A";
          default:
            return "label label-X";
        }
      } else if (maxValor == 3) {
        switch (valorResposta){
          case '0':
            return "label label-ABLLS-N";
          case '1':
            return "label label-ABLLS-P";
          case '2':
            return "label label-ABLLS-P3";
          case '3':
            return "label label-ABLLS-A";
          default:
            return "label label-ABLLS-X";
        }
      } else if (maxValor == 4) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-P1";
          case '2':
            return "label label-P";
          case '3':
            return "label label-P3";
          case '4':
            return "label label-A";
          default:
            return "label label-X";
        }
      } else if (maxValor == 1) {
        switch (valorResposta) {
          case '0':
            return "label label-N";
          case '1':
            return "label label-A";
          default:
            return "label label-X";
        }
      }
    } else {
      switch (valorResposta){
        case '0':
          return "label label-N";
        case '0.5':
          return "label label-P";
        case '1':
          return "label label-A";
        default:
          return "label label-X";
      }
    }
  }

  getClassRespostaHabilidadePDF(habilidade: any){
    const maxValor = habilidade.dominioResposta
      .map(dom => parseInt(dom.valor, 10))
      .filter(v => !isNaN(v))
      .sort((a, b) => b - a)[0];

    const valorResposta = this.getRespostaHabilidade(habilidade.id).valor;
    
    // console.log(this.getRespostaHabilidade(idHabilidade))
    if (this.tipoAvaliacao.nome != 'ABLLS-R') {
      switch (valorResposta){
        case '0':
          return "label label-N-pdf";
        case '0.5':
          return "label label-P-pdf";
        case '1':
          return "label label-A-pdf";
        default:
          return "label label-X-pdf";
      }
    } else {
      if (maxValor == 2) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-P-pdf";
          case '2':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      } else if (maxValor == 4) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-P1-pdf";
          case '2':
            return "label label-ABLLS-P-pdf";
          case '3':
            return "label label-ABLLS-P3-pdf";
          case '4':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      } else if (maxValor == 1) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      }
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  exitEdit(){
    this.router.navigate(['/paciente/' + this.paciente.id, {
      tab:"avaliacao"
    }]);
  }

  generatePDF() {
    this.loadingService.show();
    if(this.avaliacao.idTipoAvaliacao == '3'){
      this.viewVbmappPDF = true;  
    } else {
      this.viewAvaliacaoPDF = true;
    }
  }

  onPDFGenerated() {
    this.viewAvaliacaoPDF = false;
    this.viewVbmappPDF = false;
  }

}
