import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { Profissional } from './../../profissional/profissional-model';
import { Chart, ChartDataSets, ChartPoint } from 'chart.js';
import { ColetaDiariaVBMAPP } from './../../coletadiariavbmapp/coletadiariavbmapp-model';
import { ColetadiariavbmappService } from './../../coletadiariavbmapp/coletadiariavbmapp.service';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { Objetivo } from '../../objetivo/objetivo-model';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from './../../template/auth/auth.service';
import { PacienteService } from './../../paciente/paciente.service';
import { PlanointervencaovbmappService } from './../planointervencaovbmapp.service';
import { ESDMChecklistGraph } from './../../esdmchecklist/esdmchecklist-graph-model';
import { Paciente } from './../../paciente/paciente-model';
import { PlanoIntervencaoVBMAPP } from './../planointervencaovbmapp-model';
import { Component, OnInit } from '@angular/core';
import moment from 'moment';
import { FormControl } from '@angular/forms';                                                                                                                                                          
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-planointervencaovbmapp-graph',
  templateUrl: './planointervencaovbmapp-graph.component.html',
  styleUrls: ['./planointervencaovbmapp-graph.component.css']
})
export class PlanointervencaovbmappGraphComponent implements OnInit {

  public planoIntervencaoVBMAPP: any = new PlanoIntervencaoVBMAPP();
  public idPlanoIntervencao: string;
  public coletas: ColetaDiariaVBMAPP[];

  public paciente: Paciente = new Paciente();

  public objetivos: any[] = [];
  public objetivosESDM: any[] = [];
  public idObjetivo: string;

  public colors: any[] = [
    { //Rosa
      backgroundColor:"rgba(255, 99, 132, 0)",
      fill:true,
      borderColor:"rgb(255, 99, 132)",
      pointBackgroundColor:"rgb(255, 99, 132)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(255, 99, 132)" },
    { //Azul Claro
      backgroundColor:"rgba(54, 162, 235, 0.0)",
      fill:true,
      borderColor:"rgb(54, 162, 235)",
      pointBackgroundColor:"rgb(54, 162, 235)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(54, 162, 235)" },
    { //Lilás
      backgroundColor:"rgba(138,43,226,0.0)",
      fill:true,
      borderColor:"rgb(138,43,226)",
      pointBackgroundColor:"rgb(138,43,226)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(138,43,226)" },
    { //Ouro
      backgroundColor:"rgba(218, 165, 32,0.0)",
      fill:true,
      borderColor:"rgb(218, 165, 32)",
      pointBackgroundColor:"rgb(218, 165, 32)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(218, 165, 32)" },
    { //Vermelho
      backgroundColor:"rgba(255, 0, 0,0.0)",
      fill:true,
      borderColor:"rgb(255, 0, 0)",
      pointBackgroundColor:"rgb(255, 0, 0)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(255, 0, 0)" },
    { //Verde
      backgroundColor:"rgba(0, 255, 0,0.0)",
      fill:true,
      borderColor:"rgb(0, 255, 0)",
      pointBackgroundColor:"rgb(0, 255, 0)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(0, 255, 0)" },
    { //Laranja
      backgroundColor:"rgba(255, 110, 0,0.0)",
      fill:true,
      borderColor:"rgb(255, 153, 0)",
      pointBackgroundColor:"rgb(255, 110, 0)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(255, 110, 0)" },
    { //Azul Escuro
      backgroundColor:"rgba(0, 0, 255,0.0)",
      fill:true,
      borderColor:"rgb(0, 0, 255)",
      pointBackgroundColor:"rgb(0, 0, 255)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(0, 0, 255)" },
    { //Amarelo
      backgroundColor:"rgba(255, 255, 0,0.0)",
      fill:true,
      borderColor:"rgb(255, 221, 0)",
      pointBackgroundColor:"rgb(255, 255, 0)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(255, 255, 0)" },
    { //Cinza
      backgroundColor:"rgba(179, 179, 179,0.0)",
      fill:true,
      borderColor:"rgb(179, 179, 179)",
      pointBackgroundColor:"rgb(179, 179, 179)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(179, 179, 179)" },
    { //Preto
      backgroundColor:"rgba(0, 0, 0,0.0)",
      fill:true,
      borderColor:"rgb(0, 0, 0)",
      pointBackgroundColor:"rgb(0, 0, 0)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(0, 0, 0)" },
    { //Roxo
      backgroundColor:"rgba(115, 0, 153,0.0)",
      fill:true,
      borderColor:"rgb(115, 0, 153)",
      pointBackgroundColor:"rgb(115, 0, 153)",
      pointBorderColor:"#fff",
      pointHoverBackgroundColor:"#fff",
      pointHoverBorderColor:"rgb(115, 0, 153)" }
  ];

  public mapDatasColetas: string[] = [];
  public mapEstimulos: Map<string, number[]> = new Map<string, number[]>();
  public faixaSuporte: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();
  public tipoSuporteData: Map<string, Map<string, string>> = new Map<string, Map<string, string>>();

  public mapDatasColetasESDM: string[] = [];
  public mapEtapasESDM: Map<string, number[]> = new Map<string, number[]>();

  public percentuais: number[];

  public graphESDM: ESDMChecklistGraph = new ESDMChecklistGraph();
  public graphs: { [key: string]: any } = {};
  public graphsSimples: { [key: string]: any } = {};
  public graphsOportunidades: { [key: string]: any } = {};
  public graphObjetivos: ESDMChecklistGraph = new ESDMChecklistGraph();
  public graphsESDMColeta: { [key: string]: any } = {};
  public graphsESDMObtencao: { [key: string]: any } = {};
  public adquiridas: any[] = [];
  public selected = new FormControl(0);
  public aba: string;
  public hasAccessRead: boolean;

  public mapPositivos: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();
  public mapNegativos: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();
  public mapIndependentes: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();

  public mapPositivosESDM: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();
  public mapNegativosESDM: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();

  public mapPercentualEtapasESDM: Map<string, Map<string, number>> = new Map<string, Map<string, number>>();
  public labelsObjetivosESDM: string[] = [];

  constructor(
    private planointervencaoService: PlanointervencaovbmappService,
    private coletaDiariaVbmappService: ColetadiariavbmappService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    private route: ActivatedRoute,) { 
      // 1) Definindo e registrando o plugin uma única vez, no topo do arquivo
      const multiArbitraryLine = {
        id: 'multiArbitraryLine',
        beforeDatasetsDraw(chart: any) {
          const opts = chart.config.options.plugins.multiArbitraryLine as {
            xPositions: number[];
            yPositions: number[];
            etapaLabels: string[];
          };
          if (!opts || !opts.xPositions?.length || !opts.etapaLabels?.length) return;

          const xScale = chart.scales['x-axis-0'];
          const yScale = chart.scales['y-axis-0'];
          if (!xScale || !yScale) return;

          const { ctx, chartArea: { top, left, right } } = chart;
          ctx.save();
          ctx.strokeStyle = 'gray';
          ctx.lineWidth = 2;
          ctx.fillStyle = 'gray';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'top';

          // Pixels das linhas (incluindo posição 0 e transições)
          const lineXPixAll = opts.xPositions.map(xVal => xScale.getPixelForValue(xVal));
          // Transições são skip primeiro elemento
          const lineXPix = lineXPixAll.slice(1);

          // Desenha linhas tracejadas para cada transição
          ctx.setLineDash([6, 6]);
          lineXPix.forEach((xPix, i) => {
            const yPos = yScale.getPixelForValue(opts.yPositions[i + 1] ?? yScale.min);
            ctx.beginPath();
            ctx.moveTo(xPix, yPos);
            ctx.lineTo(xPix, top);
            ctx.stroke();
          });
          ctx.setLineDash([]);

          // Boundary pixels: from left edge to right edge through transitions
          const boundaries = [left, ...lineXPix, right];
          // Midpoints for labels
          const fullLabelX = boundaries.slice(0, -1).map((b, idx) => (b + boundaries[idx + 1]) / 2);

          // Desenha os labels, um para cada etapa
          fullLabelX.forEach((xPix, idx) => {
            const label = opts.etapaLabels[idx] || '';
            ctx.fillText(label, xPix, top + 10);
          });

          ctx.restore();
        }
      };
    Chart.pluginService.register(multiArbitraryLine);
  }

  async ngOnInit(): Promise<void> {
    this.idPlanoIntervencao = this.route.snapshot.paramMap.get('id');
    try {
      const plano = await this.planointervencaoService.findById(this.idPlanoIntervencao).toPromise();
      this.planoIntervencaoVBMAPP = plano;
      
      const paciente = await this.pacienteService.findById(plano.idPaciente).toPromise();
      this.paciente = paciente;
      
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','read')

      // Preenchendo os arrays objetivos e objetivosESDM (separando os objetivos VBMAPP e ESDM)
      await this.planoIntervencaoVBMAPP.objetivos.forEach(objetivo => {
        if (!objetivo.habilidades) {
          this.objetivosESDM.push(objetivo);
          this.adquiridas.push(Math.round(this.percentualEtapasAdquiridas(objetivo) * 100));
        } else {
          this.objetivos.push(objetivo); // Preenche apenas os objetivos VBMAPP
        }
      });
      
      // Verifique se existem objetivos VBMAPP
      if (this.objetivos.length > 0) {
        this.selected.setValue(0); // Mostra a aba VBMAPP
      } else if (this.objetivosESDM.length > 0) {
        this.selected.setValue(1); // Pula para a aba ESDM
      } else {
        this.selected.setValue(null); // Nenhuma aba disponível
      }
  
      // Carregando as coletas do plano de intervenção
      const coletas = await this.coletaDiariaVbmappService.findAllByPlanoIntervencao(this.idPlanoIntervencao).toPromise()
      this.coletas = coletas;
      // console.log(this.coletas);

      for (let i = 0; i < this.objetivos.length; i++) {
        const objetivo = this.objetivos[i];
        // Gera um ID temporário se não houver 
        if (!objetivo.id) {
          objetivo.id = `temp-${i}`;
        }
        await this.setColetasObjetivo(objetivo);
      }

      await this.setGraphObjetivos();
      
      if(this.objetivos.length == 0) {
        this.selected.setValue(1);
      }

      if(this.objetivosESDM?.length > 0) {
        for (let objetivo of this.objetivosESDM) {
          await this.setColetasObjetivoESDM(objetivo);
        }
        await this.renderGraphESDM();
      }
    } catch (error) {
      console.log(error);
    }
  }

  async setColetasObjetivo(objetivo: ObjetivoVBMAPP) {
    let objs: ObjetivoVBMAPP[] = [];
    let obj: ObjetivoVBMAPP;
    let objPlano: ObjetivoVBMAPP;
    let estimulo: any;
    // data       estimulo valor
    this.mapPositivos = new Map<string, Map<string, number>>();
    this.mapNegativos = new Map<string, Map<string, number>>();
    this.mapIndependentes = new Map<string, Map<string, number>>();
    let num: number;
    
    this.faixaSuporte = new Map<string, Map<string, number>>();
    this.tipoSuporteData = new Map<string, Map<string, string>>();

    let perc: number[] = [];

    //Zero os dados do gráfico
    this.mapDatasColetas = [];
    this.mapEstimulos = new Map<string, number[]>();

    for(const [, coleta] of this.coletas?.entries()){
      //Recupero o objetivo
      obj = coleta.objetivos.find(o => o.nome == objetivo.nome);
      objPlano = this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome == objetivo.nome);

      //Verifico se foi retornado o objetivo
      if(obj != undefined){
        //Armazeno as datas em que houveram coletas no objetivo
        if (this.mapDatasColetas.indexOf(moment(coleta.data).format("DD/MM/YYYY")) == -1) {
          this.mapDatasColetas.push(
            moment(coleta.data).format("DD/MM/YYYY")
          );
          this.mapPositivos.set(moment(coleta.data).format("DD/MM/YYYY"), new Map<string, number>());
          this.mapNegativos.set(moment(coleta.data).format("DD/MM/YYYY"), new Map<string, number>());
          this.mapIndependentes.set(moment(coleta.data).format("DD/MM/YYYY"), new Map<string, number>());
          this.faixaSuporte.set(moment(coleta.data).format("DD/MM/YYYY"), new Map<string, number>());
          this.tipoSuporteData.set(moment(coleta.data).format("DD/MM/YYYY"), new Map<string, string>());
        }
        // console.log(coleta)
        for(const [t, tpSuporte] of objPlano.tiposSuporte?.entries()){
          for(const [, est] of tpSuporte.estimulos.filter(e => e.ativo === true)?.entries()){
            estimulo = undefined;
            //Recupero o estimulo do tipo de suporte no objetivo da coleta
            if(obj.tiposSuporte.find(ts => ts.id == tpSuporte.id) != undefined
              && obj?.tiposSuporte?.find(ts => ts.id == tpSuporte.id)?.estimulos != undefined
              && obj.tiposSuporte.find(ts => ts.id == tpSuporte.id).estimulos.length > 0){
                estimulo = obj?.tiposSuporte?.find(ts => ts.id == tpSuporte.id)?.estimulos.find(e => e.id == est.id);
              }
            
            if(estimulo != undefined){
              //Armazeno os limites máximos de cada tipo de suporte em uma data
              this.faixaSuporte?.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, (this.planoIntervencaoVBMAPP.objetivos
                .find(o => o.nome == obj.nome).tiposSuporte.findIndex(tp => tp.id == tpSuporte.id) + 1)/this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome == obj.nome).tiposSuporte.length);
              
              //Armazeno as siglas de cada tipo de suporte em uma data
              this.tipoSuporteData?.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, tpSuporte.sigla);
  
              //Armzeno um map com todos os estímulos existentes
              this.mapEstimulos.set(estimulo.nome, []);
  
              //Verifico se o estímulo já está no Map. 
              if(this.mapPositivos?.get(moment(coleta.data).format("DD/MM/YYYY"))?.get(estimulo.nome) == undefined){
                if(estimulo.positivos != null){ //Tratar estrutura de dados legada
                  this.mapPositivos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.positivos);
                  this.mapNegativos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.negativos);
                  this.mapIndependentes.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.independentes || 0);
                } else {
                  this.mapPositivos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.status == "Adquirido" ? 1 : 0);
                  this.mapNegativos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.status != "Adquirido" ? 1 : 0);
                  this.mapIndependentes.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, estimulo.status != "Adquirido" ? 1 : 0);
                }
              } else {
                num = this.mapPositivos.get(moment(coleta.data).format("DD/MM/YYYY"))?.get(estimulo.nome);
                if(estimulo.positivos != null){ //Tratar estrutura de dados legada
                  num += estimulo.positivos;
                } else {
                  num += estimulo.status == "Adquirido" ? 1 : 0;
                }
                this.mapPositivos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, num);
  
                num = this.mapNegativos.get(moment(coleta.data).format("DD/MM/YYYY"))?.get(estimulo.nome);
                if(estimulo.negativos != null){ //Tratar estrutura de dados legada
                  num += estimulo.negativos; 
                } else {
                  estimulo.status != "Adquirido" ? 1 : 0
                }
                this.mapNegativos.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, num);

                num = this.mapIndependentes.get(moment(coleta.data).format("DD/MM/YYYY"))?.get(estimulo.nome);
                if(estimulo.independentes != null){ //Tratar estrutura de dados legada
                  num += estimulo.independentes;
                } else {
                  if (estimulo.positivos != null) {
                    num += 0;
                  } else {
                    num += estimulo.status != "Adquirido" ? 1 : 0;
                  }
                }
                this.mapIndependentes.get(moment(coleta.data).format("DD/MM/YYYY"))?.set(estimulo.nome, num);
              }
            }
          }
        }
      }
    }

    // console.log(this.mapPositivos);
    // console.log(this.mapNegativos);

    for (const [i, data] of this.mapDatasColetas?.entries()) {
      this.mapEstimulos.forEach((value: Array<number | null>, key: string) => {
        const faixa = Number(this.faixaSuporte.get(data)?.get(key) ?? 0);
        const neg = Number(this.mapNegativos.get(data)?.get(key) ?? 0);
        const pos = Number(this.mapPositivos.get(data)?.get(key) ?? 0);
        const ind = Number(this.mapIndependentes.get(data)?.get(key) ?? 0);

        const total = neg + pos + ind;
        if (total === 0) {
          value.push(null);
          this.mapEstimulos.set(key, value);
          return;
        }

        const proporcao = (pos + ind) / total;

        // Passo proporcional à própria faixa 
        const passoProporcional = faixa - (1 / objetivo.tiposSuporte.length);
        const intervalo = Math.max(0, faixa - passoProporcional);
        
        // cálculo final (mantive a sua fórmula, só deixei mais legível)
        const num = (faixa - intervalo) + (intervalo * proporcao);
        
        console.log(obj.nome);
        console.log(data);
        console.log('Faixa ' + faixa);
        console.log('Tipos de suporte ' + objetivo.tiposSuporte.length);
        console.log(1 / objetivo.tiposSuporte.length)
        console.log('Passo Proporcional ' + passoProporcional);
        console.log('Intervalo ' + intervalo);
        console.log('Resultado ' + num);   
        
        
        value.push(Math.round(num * 100));
        this.mapEstimulos.set(key, value);
      });
    }

    await this.setGraphOportunidades(objetivo);
    await this.setGraphVisaoColeta(objetivo);
    await this.setGraph(objetivo);
  }

  async setGraphOportunidades(objetivo: ObjetivoVBMAPP) {
    this.excluiDatasSemColeta();

    const dateLabels = [...this.mapDatasColetas];
    const labels = dateLabels.map((_, i) => (i + 1).toString());

    // 1) Monta rawMap por estímulo para detalhamento
    const rawMap = new Map<string, { positivos: number; negativos: number; independentes: number; }[]>();
    const objPlano = this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome === objetivo.nome)!;
    objPlano.tiposSuporte.forEach(tp =>
      tp.estimulos.filter(e => e.ativo).forEach(e =>
        rawMap.set(e.nome, dateLabels.map(() => ({ positivos: 0, negativos: 0, independentes: 0 })))
      )
    );

    // 2) Preenche rawMap com valores por data e estímulo
    dateLabels.forEach((dataFmt, idx) => {
      const coletasNoDia = Array.from(this.coletas.values()).filter(c =>
        moment(c.data).format('DD/MM/YYYY') === dataFmt &&
        c.objetivos.some(o => o.nome === objetivo.nome)
      );

      coletasNoDia.forEach(coleta => {
        const objColeta = coleta.objetivos.find(o => o.nome === objetivo.nome)!;
        objColeta.tiposSuporte.forEach(ts =>
          ts.estimulos.forEach(est => {
            if (!est.ativo) return;
            const rec = rawMap.get(est.nome)![idx];
            rec.positivos += est.positivos ?? (est.status === 'Adquirido' ? 1 : 0);
            rec.negativos += est.negativos ?? 0;
            rec.independentes += est.independentes ?? 0;
          })
        );
      });
    });

    // 3) Agrega por data: somatório geral de positivos, negativos e total
    const positivosTotais = dateLabels.map((_, idx) =>
      Array.from(rawMap.values()).reduce((sum, arr) => sum + arr[idx].positivos, 0)
    );
    const negativosTotais = dateLabels.map((_, idx) =>
      Array.from(rawMap.values()).reduce((sum, arr) => sum + arr[idx].negativos, 0)
    );
    const independentesTotais = dateLabels.map((_, idx) =>
      Array.from(rawMap.values()).reduce((sum, arr) => sum + arr[idx].independentes, 0)
    );
    const maiorValor = dateLabels.map((_, idx) => {
      const p = positivosTotais[idx] || 0;
      const n = negativosTotais[idx] || 0;
      const i = independentesTotais[idx] || 0;
      return Math.max(p, n, i);
    });

    // 4) Cria apenas três datasets: Positivos, Negativos e Total
    const datasets: Chart.ChartDataSets[] = [
      {
        label: 'Com ajuda',
        data: positivosTotais.map((y, i) => ({ y, date: dateLabels[i] })),
        borderColor: '#4CAF50',
        backgroundColor: 'rgba(108, 173, 111, 0.3)',
        pointBackgroundColor: '#4CAF50',
        fill: false,
      },
      {
        label: 'Erro',
        data: negativosTotais.map((y, i) => ({ y, date: dateLabels[i] })),
        borderColor: '#F44336',
        backgroundColor: 'rgba(240, 116, 107, 0.3)',
        pointBackgroundColor: '#F44336',
        fill: false,
      },
      {
        label: 'Independente',
        data: independentesTotais.map((y, i) => ({ y, date: dateLabels[i] })),
        borderColor: '#2196F3',
        backgroundColor: 'rgba(33, 150, 243, 0.3)',
        pointBackgroundColor: '#2196F3',
        fill: false,
      },
    ];

    // 5) Configura opções com tooltip detalhado por estímulo
    const graph = new ESDMChecklistGraph();
    graph.labels    = labels;
    graph.datasets  = datasets;
    graph.chartType = 'line';
    graph.options   = {
      responsive: true,
      spanGaps: false,
      legend: {
        display: true,
        labels: {
          generateLabels: (chart: any) => {
            const tipos = ['Com ajuda', 'Erro', 'Independente'];
            const coresBorda = ['#66BB6A', '#E57373', '#2196F3'];
            const coresFundo = ['rgba(76, 175, 80, 0.3)', 'rgba(244, 67, 54, 0.3)', 'rgba(33, 150, 243, 0.3)'];

            return tipos.map((tipo, idx) => {
              const meta = chart.getDatasetMeta(idx);
              const hidden = meta.hidden === true;

              return {
                text: tipo,
                fillStyle: coresFundo[idx],
                strokeStyle: coresBorda[idx],
                lineWidth: 3,
                index: idx,
                hidden,
                fontStyle: hidden ? 'line-through' : 'normal'
              } as Chart.ChartLegendLabelItem;
            });
          }
        },
        onClick(this: any, _, legendItem: any) {
          const chart = this.chart as Chart;
          const idx = legendItem.index;
          chart.getDatasetMeta(idx).hidden = !chart.getDatasetMeta(idx).hidden;
          chart.update();
        }
      },
      plugins: {
        datalabels: {
          display: false
        }
      },
      title: {
        display: true,
        text: [
          `Objetivo: ${this.abreviarTexto(objetivo.nome, 90)}`,
          'Total de Oportunidades por Dia'
        ],
        fontSize: 16
      },
      scales: {
        yAxes: [{ ticks: { beginAtZero: true, stepSize: 1, max: Math.max(...maiorValor) + 2 } }]
      },
      tooltips: {
        callbacks: {
          title: items => {
            const pt = items[0];
            const ds = datasets[pt.datasetIndex!].data as any[];
            return `Data: ${ds[pt.index!].date}`;
          },
          label: (toolTipItem, data) => {
            const idx = toolTipItem.index!;
            const dsIndex = toolTipItem.datasetIndex!;
            const dsLabel = data.datasets![dsIndex].label;

            const lines: string[] = [];
            // somente exibe o tipo relevante
            rawMap.forEach((arr, nomeEst) => {
              if (dsLabel === 'Com ajuda') {
                lines.push(`${nomeEst}: ${arr[idx].positivos}(CA)`);
              } else if (dsLabel === 'Erro') {
                lines.push(`${nomeEst}: ${arr[idx].negativos}(E)`);
              } else if (dsLabel === 'Independente') {
                lines.push(`${nomeEst}: ${arr[idx].independentes}(I)`);
              }
            });
            // adiciona o total ou valor da linha no fim
            lines.push(`${dsLabel}: ${toolTipItem.yLabel}`);
            return lines;
          },
        }
      }
    };

    this.graphsOportunidades[objetivo.id] = graph;
  }

  async setGraphVisaoColeta(objetivo: ObjetivoVBMAPP) {
    this.excluiDatasSemColeta();

    const dateLabels = [...this.mapDatasColetas];
    const labels = dateLabels.map((_, idx) => (idx + 1).toString());

    const rawMap = new Map<string, { positivos: (number | null)[], negativos: (number | null)[], independentes: (number | null)[] }>();

    const objPlano = this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome === objetivo.nome);
    objPlano.tiposSuporte.forEach(tp =>
      tp.estimulos
        .filter(e => e.ativo)
        .forEach(e => rawMap.set(e.nome, { positivos: [], negativos: [], independentes: [] }))
    );

    dateLabels.forEach(dataFmt => {
      const coletasNoDia = Array.from(this.coletas.values())
        .filter(c =>
          moment(c.data).format("DD/MM/YYYY") === dataFmt
          && c.objetivos.some(o => o.nome === objetivo.nome)
        );

      rawMap.forEach((dadosEst, nomeEst) => {
        let encontrou = false;
        let positivos = 0;
        let negativos = 0;
        let independentes = 0;

        coletasNoDia.forEach(coleta => {
          const objColeta = coleta.objetivos.find(o => o.nome === objetivo.nome);
          objColeta.tiposSuporte.forEach(ts => {
            ts.estimulos.forEach(est => {
              if (est.nome === nomeEst) {
                encontrou = true;
                if (est.positivos != null) {
                  positivos += est?.positivos;
                } else if (est.status === "Adquirido") {
                  positivos += 1;
                }

                if (est.negativos != null) {
                  negativos += est.negativos;
                }

                if (est.independentes != null) {
                  independentes += est.independentes;
                }
              }
            });
          });
        });

        if (!encontrou) {
          dadosEst.positivos.push(null);
          dadosEst.negativos.push(null);
          dadosEst.independentes.push(null);
        } else {
          if (positivos === 0 && negativos === 0 && independentes === 0) {
            dadosEst.positivos.push(null);
            dadosEst.negativos.push(null);
            dadosEst.independentes.push(null);
          } else {
            dadosEst.positivos.push(positivos);
            dadosEst.negativos.push(negativos);
            dadosEst.independentes.push(independentes);
          }
        }
      });
    });

    const datasets: ChartDataSets[] = [];
    rawMap.forEach((valores, nomeEst) => {
      const mediaPorData: ChartPoint[] = [];

      for (let i = 0; i < valores.positivos.length; i++) {
        const p = valores.positivos[i];
        const n = valores.negativos[i];
        const ind = valores.independentes[i];

        let positivos = p;
        if (ind != null) {
          if (positivos == null) {
            positivos = 0;
          }
          positivos += ind;
        }

        if (positivos == null && n == null && ind == null) {
          mediaPorData.push({ y: null, date: dateLabels[i], detalhes: null } as any);
        } else {
          let media = 0;

          if (positivos && positivos !== 0) {
            media =+ (positivos / (positivos + (n ?? 0)) * 100).toFixed(1);
          } else {
            media = 0;
          }

          mediaPorData.push({
            y: media,
            date: dateLabels[i],
            detalhes: { positivos: p ?? 0, negativos: n ?? 0, independentes: ind ?? 0 }
          } as any);
        }
      }

      datasets.push({
        label: nomeEst,
        data: mediaPorData,
        fill: false,
      });
    });

    const graphSimples = new ESDMChecklistGraph();
    graphSimples.labels    = labels;
    graphSimples.datasets  = datasets;
    graphSimples.chartType = 'line';
    graphSimples.options   = {
      responsive: true,
      spanGaps: false,
      layout: {
        padding: { top: 10, left: 10, right: 10, bottom: 10 },
      },
      plugins: {
        datalabels: { display: false },
        legend: { position: 'top' },
      },
      title: {
        display: true,
        text: [
          `Objetivo: ${this.abreviarTexto(objetivo.nome, 90)}`,
          'Gráfico de Porcentagem de Acertos Diários'
        ],
        fontSize: 16,
      },
      scales: {
        yAxes: [{
          ticks: {
            stepSize: 10,
            max: 100,
            beginAtZero: true,
            callback: function(value) {
              return value + '%';
            }
          }
        }]
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems, data) {
            const point: any = data.datasets[tooltipItems[0].datasetIndex].data[tooltipItems[0].index];
            return `Data: ${point.date}`;
          },
          label: function (tooltipItem, data) {
            const ds = data.datasets[tooltipItem.datasetIndex];
            const point: any = ds.data[tooltipItem.index];
            const media = tooltipItem.yLabel;
            const positivos = point.detalhes?.positivos ?? 0;
            const negativos = point.detalhes?.negativos ?? 0;
            const independentes = point.detalhes?.independentes ?? 0;
            return `${ds.label}: Média = ${media?.toString().replace('.', ',')}% (CA: ${positivos}, E: ${negativos}, I: ${independentes})`;
          }
        }
      }
    };

    this.graphsSimples[objetivo.id] = graphSimples;
  }

  async setGraph(objetivo: ObjetivoVBMAPP){
    await this.excluiDatasSemColeta();
    let datasets: ChartDataSets[] = [];
    let m = this.mapDatasColetas;
    let o = objetivo;
    let tpSuporte = this.tipoSuporteData;
    const graph = new ESDMChecklistGraph();

    const dateLabels = [...this.mapDatasColetas];
    const labels = dateLabels.map((_, idx) => (idx + 1).toString());

    this.mapEstimulos.forEach((value: number[], key: string) => {
      datasets.push({
        label: key, 
        data: value.map((v, i) => ({
          y: v,
          date: dateLabels[i]
        })) as ChartPoint[],
        fill: false,
      })
    });

    graph.datasets = datasets;
    graph.labels = labels;
    //Setando as cores padrão de cada estímulo
    graph.chartType = 'line';
    graph.options = {
      responsive: true,
      layout: {
        padding: { 
          top: 10,
          left: 10,
          right: 10,
          bottom: 10
        },
      },
      plugins: {
        datalabels: {
          display: false,
        },
        legend: {
          position: 'top',
        }
      },
      scales: {
        yAxes: [{
          ticks: {
            beginAtZero: true,
            stepSize: 10,
            max: 100,
            callback: function(value) {
              return value + '%';
            }
          }
        }]
      },
      title: {
        display: true,
        text: [
          `Objetivo: ${this.abreviarTexto(objetivo.nome, 90)}`,
          'Gráfico de Obtenção do Objetivo'
        ],
        fontSize: 16,
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems, data) {
            const ds = data.datasets[tooltipItems[0].datasetIndex] as any;
            const point = ds.data[tooltipItems[0].index];
            return `Data: ${point.date}`;
          },
          label: function (tooltipItem, data) {
            const ds = data.datasets[tooltipItem.datasetIndex];
            const label = ds.label || '';
            const value = tooltipItem.yLabel;
            return ` ${label}: ${value}%` + ' (' + tpSuporte.get(m[tooltipItem.index]).get(o.estimulos[tooltipItem.datasetIndex].nome) + ')';
          }
        }
      }
    };

    this.graphs[objetivo.id] = graph;
    this.graphs[objetivo.id].meta = {
      faixaSuporte: this.faixaSuporte,
      tipoSuporteData: this.tipoSuporteData,
      mapDatasColetas: this.mapDatasColetas,
      mapEstimulos: this.mapEstimulos
    }
    
  }

  async setGraphObjetivos() {
    const labels: string[] = [];
    const valores: number[] = [];
  
    // 1) percorre todos os objetivos e calcula o percentual
    for (const objetivo of this.objetivos) {
      let totalEstimulos = 0;
      let estimulosAdquiridos = 0;
  
      for (const tipoSuporte of objetivo.tiposSuporte || []) {
        for (const estimulo of tipoSuporte.estimulos || []) {
          totalEstimulos++;
          if (estimulo.status === 'Adquirido') {
            estimulosAdquiridos++;
          }
        }
      }
  
      const percentual = totalEstimulos > 0
        ? Math.round((estimulosAdquiridos / totalEstimulos) * 100)
        : 0;
  
      labels.push(objetivo.nome);
      valores.push(percentual);
    }
  
    // 2) monta o único dataset com todos os percentuais
    const datasets: ChartDataSets[] = [{
      label: 'Percentual de Estímulos Adquiridos',
      data: valores,
      backgroundColor:'rgba(39, 188, 207, 0.66)',
      borderColor: 'rgba(0, 188, 212, 0.3)',
      hoverBackgroundColor: 'rgba(0, 187, 212, 0.79)',
    }];
  
    // 3) preenche o gráfico
    const graph = new ESDMChecklistGraph();
    graph.labels    = labels;
    graph.datasets  = datasets;
    graph.chartType = 'bar';
    graph.options = {
      responsive: true,
      layout: {
        padding: {
          top: 10,
          left: 10,
          right: 10,
          bottom: 10
        },
      },
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Percentual de Obtenção dos Objetivos',
        fontSize: 16,
      },
      plugins: {
        datalabels: {
          display: false,
        },
      },
      scales: {
        xAxes: [{
          ticks: {
            autoSkip: false,
            // Abrevia apenas na exibição do eixo:
            callback: (label: string) => {
              const max = 30; // ajuste máximo de caracteres
              return label.length > max
                ? label.substr(0, max - 1) + '…'
                : label;
            }
          }
        }],
        yAxes: [{
          ticks: {
            beginAtZero: true,
            stepSize: 10,
            max: 100,
            callback: function(value) {
              return value + '%';
            }
          }
        }]
      },
      tooltips: {
        callbacks: {
          // Ao passar o mouse, exibe o label completo:
          title: (tooltipItems, data) => {
            const idx = tooltipItems[0].index;
            return data.labels[idx] as string;
          },
          label: function (tooltipItem, data) {
            const ds = data.datasets[tooltipItem.datasetIndex];
            const label = ds.label || '';
            const value = tooltipItem.yLabel;
            return ` ${label}: ${value}%`;
          }
        }
      }
    };

    this.graphObjetivos = graph;
  } 

  async excluiDatasSemColeta(){
    let exclui: boolean = true;
    let newMapDatasColetas = [...this.mapDatasColetas];
    let newMapEstimulos: Map<string, number[]> = new Map<string, number[]>();
    let nums: number[] = [];

    //Duplicando o Map
    for(const [, estimulo] of Array.from(this.mapEstimulos.keys())?.entries()){
      newMapEstimulos.set(estimulo, [...this.mapEstimulos.get(estimulo)]);
    }

    for(const [i, data] of this.mapDatasColetas?.entries()){
      exclui = true;
      for(const [, estimulo] of Array.from(this.mapEstimulos.keys())?.entries()){
        if(this.mapEstimulos.get(estimulo)[i] != null){
          exclui = false;
        }
      }

      if(exclui){
        for(const [, estimulo] of Array.from(this.mapEstimulos.keys())?.entries()){
          nums = newMapEstimulos.get(estimulo);
          nums.splice(newMapDatasColetas.findIndex(d => d == data),1);
          newMapEstimulos.set(estimulo, nums);
        }
        newMapDatasColetas.splice(newMapDatasColetas.findIndex(d => d == data), 1);
      }
    }

    this.mapEstimulos = newMapEstimulos;
    this.mapDatasColetas = newMapDatasColetas;
  } 

  async setColetasObjetivoESDM(objetivo: any) {
    // mapas e arrays para dados do gráfico
    this.mapDatasColetasESDM = [];
    this.mapPositivosESDM = new Map<string, Map<string, number>>();
    this.mapNegativosESDM = new Map<string, Map<string, number>>();
    this.mapPercentualEtapasESDM = new Map<string, Map<string, number>>();
    this.mapEtapasESDM = new Map<string, number[]>();

    // percorre cada registro de coleta
    for (const [, coleta] of this.coletas?.entries()) {
      const dataKey = moment(coleta.data).format('DD/MM/YYYY');
      
      // console.log(coleta);

      // encontra o objetivo ESDM na coleta
      const obj = coleta.objetivos.find(o => o.nome === objetivo.nome) as any;
      if (!obj) continue;

      // console.log(obj);

      // inicializa estruturas para a data
      if (!this.mapDatasColetasESDM.includes(dataKey)) {
        this.mapDatasColetasESDM.push(dataKey);
        this.mapPositivosESDM.set(dataKey, new Map());
        this.mapNegativosESDM.set(dataKey, new Map());
        this.mapPercentualEtapasESDM.set(dataKey, new Map());
      }

      // itera sobre cada estímulo ativo
      for (const etp of obj.etapa.filter(e => e.ativo !== false)) {
        // inicializa série de dados para o estímulo
        if (!this.mapEtapasESDM.has(etp.id)) {
          this.mapEtapasESDM.set(etp.id, []);
        }

        // calcula positivos/negativos
        const pos = etp.positivos ?? (etp.status === 'Adquirido' ? 1 : 0);
        const neg = etp.negativos ?? (etp.status !== 'Adquirido' ? 1 : 0);

        // acumula valores no dia
        const mPos = this.mapPositivosESDM.get(dataKey);
        const mNeg = this.mapNegativosESDM.get(dataKey);
        mPos.set(etp.id, (mPos.get(etp.id) ?? 0) + pos);
        mNeg.set(etp.id, (mNeg.get(etp.id) ?? 0) + neg);

        // ✅ Salva percentual da etapa, se existir
        const percentuais = this.mapPercentualEtapasESDM.get(dataKey);
        const etpPercentual = etp?.percentual * 100;
        if (etp.positivos == 0 && etp.negativos != 0) {
          percentuais.set(etp.id, etpPercentual);
        } else {
          percentuais.set(etp.id, etpPercentual != 0 ? etpPercentual : null);
        }
      }
    }

    // console.log(this.mapPercentualEtapasESDM);

    for (const date of this.mapDatasColetasESDM) {
      this.mapEtapasESDM.forEach((serie, nome) => {
        const pos = this.mapPositivosESDM.get(date).get(nome) ?? 0;
        const neg = this.mapNegativosESDM.get(date).get(nome) ?? 0;
        const total = pos + neg;

        const pct = total === 0
          ? null
          : Math.round((pos / total) * 100);

        serie.push(pct);
      });
    }

    // desenha gráficos específicos para ESDM
    await this.setGraphESDMColeta(objetivo);
    await this.setGraphESDMObtencao(objetivo);
  }

  async setGraphESDMColeta(objetivo: any) {
    const dateLabels = [...this.mapDatasColetasESDM];
    const labels = dateLabels.map((_, idx) => (idx + 1).toString());

    // Armazena arrays separados para positivos e negativos
    const rawMap = new Map<string, { positivos: (number | null)[], negativos: (number | null)[] }>();

    // Inicializar rawMap com todos os estímulos ativos do plano
    const objPlano = this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome === objetivo.nome);
    objPlano.etapa.forEach(etp =>
      rawMap.set(etp.id, { positivos: [], negativos: [] })
    );

    // Para cada data, buscar nas coletas daquele dia o total bruto de cada estímulo
    dateLabels.forEach(dataFmt => {
      // para cada etapa, soma positivos e negativos nas coletas do dia
      rawMap.forEach((dadosEst, nomeEst) => {
        let encontrou = false;
        let positivos = 0;
        let negativos = 0;

        if(this.mapPositivosESDM.has(dataFmt) || this.mapNegativosESDM.has(dataFmt)) {
          encontrou = true;
          positivos = this.mapPositivosESDM.get(dataFmt)?.get(nomeEst) ?? 0;
          negativos = this.mapNegativosESDM.get(dataFmt)?.get(nomeEst) ?? 0;
        }

        // Se não encontrou o estímulo na coleta do dia, empilha null.
        if (!encontrou) {
          dadosEst.positivos.push(null);
          dadosEst.negativos.push(null);
        } else {
          // Se encontrou, mas ambos os contadores são 0, empilha null.
          if (positivos === 0 && negativos === 0) {
            dadosEst.positivos.push(null);
            dadosEst.negativos.push(null);
          } else {
            // Se encontrou e pelo menos um contador é > 0, empilha os valores (incluindo 0).
            dadosEst.positivos.push(positivos);
            dadosEst.negativos.push(negativos);
          }
        }
      });
    });

    const datasets: ChartDataSets[] = [];

    // 1) Defina as duas cores fixas lá em cima:
    const positivoColor = '#4CAF50';
    const positivoBack  = 'rgba(108, 173, 111, 0.3)';
    const negativoColor = '#F44336';
    const negativoBack  = 'rgba(240, 116, 107, 0.3)';

    // 2) Dentro do rawMap.forEach, monte os datasets assim:
    rawMap.forEach((valores, nomeEst) => {
      const allNullPositive = valores.positivos.every(v => v === null);
      const allNullNegative = valores.negativos.every(v => v === null);

      const label = `${nomeEst} (Positivos)`;

      if (!allNullPositive) {
        datasets.push({
          label: label,
          data: valores.positivos.map((v, i) => ({
            y: v,
            date: dateLabels[i]
          })) as ChartPoint[],
          borderColor: positivoColor,
          backgroundColor: positivoBack,
          pointBackgroundColor: positivoColor,
          fill: false,
          lineTension: 0
        });
      }

      if (!allNullNegative) {
        datasets.push({
          label: `${nomeEst} (Negativos)`,
          data: valores.negativos.map((v, i) => ({
            y: v,
            date: dateLabels[i]
          })) as ChartPoint[],
          borderColor: negativoColor,
          backgroundColor: negativoBack,
          pointBackgroundColor: negativoColor,
          fill: false,
          lineTension: 0
        });
      }
    });

    // --- calcula xPositions, yPositions e labels de etapas corretamente ---
    const etapas = objPlano.etapa;
    // Detecta transições de etapa por data
    const localEtapaIndices = new Map<string, number[]>();
    dateLabels.forEach((dateKey, idx) => {
      const percentMap = this.mapPercentualEtapasESDM.get(dateKey)!;
      for (const etp of etapas) {
        if (percentMap.get(etp.id) != null) {
          if (!localEtapaIndices.has(etp.id)) localEtapaIndices.set(etp.id, []);
          localEtapaIndices.get(etp.id)!.push(idx);
          break;
        }
      }
    });

    const dateToEtapa: (string|null)[] = dateLabels.map((_, idx) => {
      for (const [etpId, indices] of localEtapaIndices.entries()) {
        if (indices.includes(idx)) return etpId;
      }
      return null;
    });

    // Monta xPositions para cada transição, iniciando com 0 para primeira etapa
    const xPositions: number[] = [0];
    const etapaLabels: string[] = [etapas[0].id];
    for (let i = 1; i < dateToEtapa.length; i++) {
      if (dateToEtapa[i] !== dateToEtapa[i - 1] && dateToEtapa[i] != null && dateToEtapa[i - 1] != null) {
        xPositions.push(i - 0.5);
        const etpId = dateToEtapa[i]!;
        const etpObj = etapas.find(e => e.id === etpId)!;
        etapaLabels.push(etpObj?.id);
      }
    }
    // Para última etapa, caso queira reforçar, não duplica se já adicionada
    if (etapaLabels.length < xPositions.length + 1) {
      etapaLabels.push(etapas[etapas.length - 1].id);
    }
    const yPositions = xPositions.map(() => 0);(_=>0);

    // CALCULA o maior valor positivo
    let maxVal = 0;
    datasets.forEach(ds => {
      ds.data.forEach((point: any) => {
        if (point && point.y != null && point.y > maxVal) {
          maxVal = point.y;
        }
      });
    });
    const yAxisMax = maxVal + 2;

    // Configura e grava o gráfico simples
    const graphSimples = new ESDMChecklistGraph();
    graphSimples.labels    = labels;
    graphSimples.datasets  = datasets;
    graphSimples.chartType = 'line';
    graphSimples.options   = {
      responsive: true,
      spanGaps: false,
      // → Legend customizada
      legend: {
        display: true,
        labels: {
          generateLabels: (chart) => {
            const datasets = chart.data.datasets;
            const labels = [] as any[];

            ['Positivos', 'Negativos'].forEach((tipo, index) => {
              const dsIndex = datasets.findIndex(ds => ds.label?.includes(tipo));
              if (dsIndex === -1) return;

              const meta = chart.getDatasetMeta(dsIndex);
              const hidden = meta.hidden === true;

              labels.push({
                text: tipo,
                fillStyle: index === 0
                  ? 'rgba(76, 175, 80, 0.3)'
                  : 'rgba(244, 67, 54, 0.3)',
                strokeStyle: index === 0
                  ? '#66BB6A'
                  : '#E57373', 
                lineWidth: 3,
                index: index,
                hidden,
                fontStyle: hidden ? 'line-through' : 'normal',
              });
            });

            return labels;
          },
        },
        onClick: function (this: any, e: MouseEvent, legendItem: any) {
          const chart = this.chart as Chart;
          const tipo = legendItem.index === 0 ? 'Positivos' : 'Negativos';

          chart.data.datasets?.forEach((ds, idx) => {
            if ((ds.label as string).includes(tipo)) {
              const meta = chart.getDatasetMeta(idx);
              meta.hidden = !meta.hidden;
            }
          });

          chart.update();
        }
      },
      layout: {
        padding: {
          top: 10,
          left: 10,
          right: 10,
          bottom: 10
        },
      },
      plugins: {
        datalabels: { display: false },
        // Passando posições onde as linhas tracejadas devem aparecer
        multiArbitraryLine: { xPositions, yPositions, etapaLabels }
      },
      title: {
        display: true,
        text: [
          `Objetivo: ${this.abreviarTexto(objetivo.nome, 90)}`,
          'Gráfico de Visão de Coleta (Positivos e Negativos) por dia'
        ],
        fontSize: 16,
      },
      scales: {
        yAxes: [{
          ticks: {
            stepSize: 1,
            max: yAxisMax,
            beginAtZero: true
          }
        }],
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems, data) {
            const ds = data.datasets[tooltipItems[0].datasetIndex] as any;
            const point = ds.data[tooltipItems[0].index];
            return `Data: ${point.date}`;
          },
          label: function (tooltipItem, data) {
            const ds = data.datasets[tooltipItem.datasetIndex];
            const label = ds.label || '';
            const value = tooltipItem.yLabel;
            return ` ${label}: ${value}`;
          }
        }
      }
    };

    this.graphsESDMColeta[objetivo.id] = graphSimples;
  }

  async setGraphESDMObtencao(objetivo: any) {
    // labels são as datas de coleta
    const dateLabels = [...this.mapDatasColetasESDM];

    const labels = dateLabels.map((_, idx) => (idx + 1).toString());

    // rawMap agora armazena somente o array de percentuais por etapa
    const rawMap = new Map<string, { percentual: (number | null)[] }>();

    // inicializa rawMap com todas as etapas do plano
    const objPlano = this.planoIntervencaoVBMAPP.objetivos.find(o => o.nome === objetivo.nome);
    objPlano.etapa.forEach(etp => rawMap.set(etp.id, { percentual: [] }));

    // populate rawMap...
    dateLabels.forEach(dataFmt => {
      rawMap.forEach((dadosEst, nomeEst) => {
        let encontrou = false;
        let positivos = 0;
        let negativos = 0;
        let percentual = 0;

        if (this.mapPercentualEtapasESDM.has(dataFmt)) {
          encontrou = true;
          positivos = this.mapPositivosESDM.get(dataFmt)?.get(nomeEst) ?? 0;
          negativos = this.mapNegativosESDM.get(dataFmt)?.get(nomeEst) ?? 0;
          percentual = this.mapPercentualEtapasESDM.get(dataFmt)?.get(nomeEst) ?? 0;
        }

        if (!encontrou || (percentual === 0 && positivos === 0 && negativos === 0)) {
          dadosEst.percentual.push(null);
        } else {
          dadosEst.percentual.push(percentual);
        }
      });
    });

    const datasets: ChartDataSets[] = [];

    // cores e construção de datasets...
    const positivoColor = 'rgba(179, 106, 17, 0.3)';
    const positivoBack  = 'rgba(174, 142, 104, 0.3)';

    rawMap.forEach((valores, nomeEst) => {
      const allNull = valores.percentual.every(v => v === null);
      if (!allNull) {
        datasets.push({
          label: nomeEst,
          data: valores.percentual.map((v, i) => ({
            y: v,
            date: dateLabels[i]
          })) as ChartPoint[],
          borderColor: positivoColor,
          backgroundColor: positivoBack,
          pointBackgroundColor: positivoColor,
          fill: false,
          lineTension: 0
        }); 
      }
    });
    
    // --- calcula xPositions, yPositions e labels de etapas corretamente ---
    const etapas = objPlano.etapa;
    // Detecta transições de etapa por data
    const localEtapaIndices = new Map<string, number[]>();
    dateLabels.forEach((dateKey, idx) => {
      const percentMap = this.mapPercentualEtapasESDM.get(dateKey)!;
      for (const etp of etapas) {
        if (percentMap.get(etp.id) != null) {
          if (!localEtapaIndices.has(etp.id)) localEtapaIndices.set(etp.id, []);
          localEtapaIndices.get(etp.id)!.push(idx);
          break;
        }
      }
    });
    const dateToEtapa: (string|null)[] = dateLabels.map((_, idx) => {
      for (const [etpId, indices] of localEtapaIndices.entries()) {
        if (indices.includes(idx)) return etpId;
      }
      return null;
    });

    // Monta xPositions para cada transição, iniciando com 0 para primeira etapa
    const xPositions: number[] = [0];
    const etapaLabels: string[] = [etapas[0].id];
    for (let i = 1; i < dateToEtapa.length; i++) {
      if (dateToEtapa[i] !== dateToEtapa[i - 1] && dateToEtapa[i] != null && dateToEtapa[i - 1] != null) {
        xPositions.push(i - 0.5);
        const etpId = dateToEtapa[i]!;
        const etpObj = etapas.find(e => e.id === etpId)!;
        etapaLabels.push(etpObj.id);
      }
    }
    // Para última etapa, caso queira reforçar, não duplica se já adicionada
    if (etapaLabels.length < xPositions.length + 1) {
      etapaLabels.push(etapas[etapas.length - 1].id);
    }
    const yPositions = xPositions.map(() => 0);(_=>0);

    const graphSimples = new ESDMChecklistGraph();
    graphSimples.labels = labels;
    graphSimples.datasets = datasets;
    graphSimples.chartType = 'line';
    graphSimples.options = {
      responsive: true,
      spanGaps: false,
      layout: { padding: { top: 10, left: 10, right: 10, bottom: 10 } },
      title: {
        display: true,
        text: [
          `Objetivo: ${this.abreviarTexto(objetivo.nome, 90)}`,
          'Gráfico de Porcentagem de Acertos'
        ],
        fontSize: 16,
      },
      legend: {
        display: false
      },
      tooltips: {
        callbacks: {
          title: function (tooltipItems, data) {
            const ds = data.datasets[tooltipItems[0].datasetIndex] as any;
            const point = ds.data[tooltipItems[0].index];
            return `Data: ${point.date}`;
          },
          label: function (tooltipItem, data) {
            const ds = data.datasets[tooltipItem.datasetIndex];
            const label = ds.label || '';
            const value = tooltipItem.yLabel;
            return ` ${label}: ${value}%`;
          }
        },
      },
      scales: { 
        yAxes: [{ 
          ticks: { 
            stepSize: 10, 
            max: 110, 
            beginAtZero: true,
            callback: function(value, index, values) {
              // values[0] é o tick mais alto (105), escondemos ele
              if (index === 0) {
                return '';
              }
              return value + '%';
            } 
          },
        }] 
      },
      plugins: {
        datalabels: { display: false },
        legend: { position: 'top' },
        // Passando posições onde as linhas tracejadas devem aparecer
        multiArbitraryLine: { xPositions, yPositions, etapaLabels }
      }
    };

    this.graphsESDMObtencao[objetivo.id] = graphSimples;
  }

  async renderGraphESDM(): Promise<void> {
    // Populando os datasets e labels para o gráfico ESDM
    this.labelsObjetivosESDM = [];
    const datasets: ChartDataSets[] = [{ 
      label: "Adquiridas", 
      data: this.adquiridas, 
      backgroundColor: 'rgba(39, 188, 207, 0.66)',
      borderColor: 'rgba(0, 188, 212, 0.3)',
      hoverBackgroundColor: 'rgba(0, 187, 212, 0.79)', 
    }];

    this.objetivosESDM.forEach((obj) => {
      this.labelsObjetivosESDM.push(obj.id);
    });
    
    const graph = new ESDMChecklistGraph();
    graph.datasets = datasets;
    graph.labels = this.labelsObjetivosESDM;
    graph.chartType = "bar";
    graph.options = {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Percentual de Obtenção dos Objetivos',
        fontSize: 16,
      },
      plugins: {
        datalabels: {
          display: false,
        },
      },
      scales: {
        xAxes: [
          {
            stacked: true,
          },
        ],
        yAxes: [
          {
            stacked: true,
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10,
              callback: function(value) {
                return value + '%';
              }
            },
          },
        ],
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItems) {
            return (
              datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + "%"
            );
          },
        },
      },
    };

    this.graphESDM = graph;
  }  

  abreviarTexto(texto: string, limite: number): string {
    return texto.length > limite ? texto.slice(0, limite - 3) + '...' : texto;
  }

  getLimiteTipoSuporte(tpSuporte: TipoSuporte, objetivo: any) {
    return (objetivo.tiposSuporte.findIndex(tp => tp.id == tpSuporte.id) + 1) / objetivo.tiposSuporte.length * 100
  }

  getEstimulos(objetivoId: string): string[] {
    const map = this.graphs[objetivoId]?.meta?.mapEstimulos;
    return map ? Array.from(map.keys()) : [];
  }
  
  percentualEtapasAdquiridas(row: Objetivo) {
    if (row) {
      return this.countEtapasAdquiridas(row) / this.countEtapas(row);
    } else {
      return 0;
    }
  }

  countEtapas(row: Objetivo) {
    if(row.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.length
    }
  }
  
  countEtapasAdquiridas(row: Objetivo) {
    if(row.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.filter(e => (e?.status == 'Adquirida')).length
    }
  }

  onTabChange(event) {
    // Verifica se há objetivos VBMAPP ou ESDM e ajusta o selected.value
    if (this.objetivos.length > 0 && event === 0) {
      this.selected.setValue(0); // Aba VBMAPP
    } else if (this.objetivosESDM.length > 0 && event === 1) {
      this.selected.setValue(1); // Aba ESDM
    }
  }  
  
  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    // console.log(funcoes)


    return funcoes;
  }

  get user(): FirebaseUserModel{
    return this.authService.getUser();
  }

  setAba(){
   
    if(this.aba=="objABA"){
      this.selected.setValue(0);
    }

    if(this.aba=="objESDM"){
      this.selected.setValue(1);
    }   
    
  }
  
}
