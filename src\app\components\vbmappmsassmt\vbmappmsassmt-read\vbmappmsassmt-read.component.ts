import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { MatTableDataSource } from '@angular/material/table';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter } from 'angular-calendar';
import { VBMAPPMsAssmtService } from './../vbmappmsassmt.service';
import { VBMAPPMilestonesAssessmentItem } from './../vbmapp-milestones-item-model';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { Nivel } from './../../nivel/nivel-model';
import { Profissional } from './../../profissional/profissional-model';
import { VBMAPPMilestonesAssessment } from './../vbmappmsassmt-model';
import { Paciente } from './../../paciente/paciente-model';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from './../../profissional/profissional.service';
import { AuthService } from './../../template/auth/auth.service';
import { NivelService } from './../../nivel/nivel.service';
import { MarcovbmappService } from './../../marcovbmapp/marcovbmapp.service';
import { Component, OnInit, Input, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { PacienteService } from '../../paciente/paciente.service';
import { FirebaseUserModel } from '../../template/auth/user-model';
import jsPDF from 'jspdf';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-vbmappmsassmt-read',
  templateUrl: './vbmappmsassmt-read.component.html',
  styleUrls: ['./vbmappmsassmt-read.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class VbmappmsassmtReadComponent implements OnInit {

  public vbmappmsassmts: VBMAPPMilestonesAssessment[];

  public paciente: Paciente = new Paciente();
  public profissionais: Profissional[];
  public niveis: Nivel[];
  public dominios: DominioVBMAPP[];
  public dominiosTab: DominioVBMAPP[] = [];
  public vbmappMsAssmtItemView: VBMAPPMilestonesAssessmentItem[] = [];

  public vbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public nivel: Nivel = new Nivel();
  public dominio: DominioVBMAPP = new DominioVBMAPP();
  public dominioMap: Map<string, DominioVBMAPP[]> = new Map<string, DominioVBMAPP[]>(); 

  public hasAccessRead: boolean = false;
  
  public viewVbmappPDF: boolean;
  idOrganizacao: any;

  //public pontos: number;

  dataSource: MatTableDataSource<VBMAPPMilestonesAssessmentItem>;

  @ViewChild('vbmappMarcos', {static: false})
  el!: ElementRef;
  vbmapInfosPDF: VBMAPPMilestonesAssessmentItem[];

  @Input()
  //$pacienteSearch: Observable<Paciente>;

  @Input()
  // $idMsAssmtSearch: string;

  displayedColumns = ['id', 'nome', 'status']

  constructor(private vbmappMsAssmtService: VBMAPPMsAssmtService,
    private nivelService: NivelService,
    private pacienteService: PacienteService,
    //private dominioVBMAPPService: DominioVBMAPPService,
    private marcoVBMAPPService: MarcovbmappService,
    public authService: AuthService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService) { }

  ngOnInit(): void { 
        let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
        let idMsAssmt = this.route.snapshot.paramMap.get('idMsAssmt');
        this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;

        //Atribuindo o paciente vindo por parâmetro
        this.loadingService.show();
        // this.$pacienteSearch.subscribe(paciente => { 
        this.pacienteService.findById(idPaciente).subscribe(paciente => {
          this.paciente = paciente;

          this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')

          this.vbmappmsassmt.idPaciente = paciente.id;
          this.vbmappmsassmt.paciente = paciente;
          
    
          //Carregando Checklist
          this.vbmappMsAssmtService.findByPaciente(paciente.id).subscribe(chklsts => {
            if(chklsts.length > 0){
              //Atribuo a lista de checklists
              this.vbmappmsassmts = chklsts.filter(chklst => chklst.status != false);
              
              //Atribuindo o primeiro checklist para visualização
              // console.log(this.$idMsAssmtSearch);
              if(this.vbmappmsassmts.length > 0){
                if(idMsAssmt == undefined){
                  this.vbmappmsassmt = this.vbmappmsassmts[0];
                } else {
                  this.vbmappmsassmt = this.vbmappmsassmts.find(plano => plano.id == idMsAssmt);
                }
              }

              //this.vbmappmsassmt = chklsts[0];


    
              //Atribuindo o checklist para visualização ao datasource
              //this.dataSource = new MatTableDataSource(this.esdmchecklist.checklist);
    
    
              //Atribuindo a primeira visão de competências (N1-CRE)
            this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
              (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
            }
            this.loadingService.hide();
          })
          
        });    
        
    
        //Carregando Níveis
        this.nivelService.find().subscribe(niveis => {
          this.niveis = niveis;
          this.niveis.pop();
          this.nivel = this.niveis.find(n => n.id == 'N1');
    
          //Carregando Domínios dos Níveis (TAB)
          niveis.forEach(nivel => {
            this.marcoVBMAPPService.findDominiosByNivel(nivel.id).subscribe(dominios => {
              this.dominioMap.set(nivel.id,dominios)
    
              //Carregando Domínios para Combo (Default: N1-Mando)
              this.dominios = this.dominioMap.get('N1');
              if(this.dominios){
                this.dominio = this.dominios.find(dom => dom.id == 'Mando');
              }
            })
          })
    
        })
    
        //Carregando Profissionais
        this.profissionalService.find().subscribe(profissionais => {
          this.profissionais = profissionais;
        })
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];//this.dominios.find(dom => dom.id == 'CRE');  
    this.filterAssessment();

    /*
    this.competenciaService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];
      this.filterChecklist();
    })
    */
  }

  filterAssessment(){
    this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
      (assmt.marco.id_nivel==this.nivel.id 
        && assmt.marco.id_dominio == this.dominio.id))
  }

  delete(): void{
    //console.log("1")
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    //console.log("2")
    dialogRef.afterClosed().subscribe(result => {
      //console.log("3")
      if(result){
        //console.log("4")
        this.loadingService.show();
        this.vbmappmsassmt.status = false;
        this.vbmappMsAssmtService.update(this.vbmappmsassmt).subscribe(
          () => {
            //console.log("5")
            this.vbmappMsAssmtService.showMessage('Avaliação de Marcos excluída!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            //console.log("6")
            this.router.navigate(['/paciente/'+this.paciente.id, {   
              tab: 'msassmt'
            }])  
            //console.log("7")
            this.loadingService.hide();
          }

          //Carregando Checklist
          // this.vbmappMsAssmtService.findByPaciente(this.paciente.id).subscribe(chklsts => {
            // if(chklsts.length > 0){
              //Atribuo a lista de checklists
              // this.vbmappmsassmts = chklsts.filter(chklst => chklst.status != false);
              
              //Atribuindo o primeiro checklist para visualização
              // if(this.vbmappmsassmts.length > 0){
                // this.vbmappmsassmt = this.vbmappmsassmts[0];
                //Atribuindo a primeira visão de competências (N1-CRE)
                  // this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
                    // (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
              // } 
              
            // this.router.navigate(['/paciente/'+this.paciente.id, {   
              // tab: 'msassmt'
            // }])  
            // this.loadingService.hide();
          //  } 
          
          // });
        ); 
      }
    });
  }

  do() {

  }

  edit(){
    //console.log(this.vbmappmsassmt.id);
    this.router.navigate(['/vbmappmsassmt/create', {  idPaciente: this.vbmappmsassmt.idPaciente,
                                                      idMsAssmt: this.vbmappmsassmt.id
                                                   }])
  }

  new(){
    //console.log(this.esdmchecklist.paciente.id);
    this.router.navigate(['/vbmappmsassmt/create', {  idPaciente: this.vbmappmsassmt.idPaciente}])
  }

  setAssessment(){
    //Carrega novo chechklist no default N1-CRE
    //this.nivelField.value=   //nativeElement.children[0].selected=true;
    //this.dominioField.nativeElement.children[0].selected=true;
    //this.nivel.id = 'N1';
    //this.dominio.id = 'CRE'
    this.nivel = this.niveis.find(n => n.id == 'N1');
    this.dominio = this.dominios.find(dom => dom.id == 'Mando');
    this.filterAssessment()
  }

  exitEdit(){
    this.router.navigate(['/paciente/' + this.paciente.id, {
      tab:"avaliacao"
    }]);
  }

  generatePDF() {
    this.loadingService.show();
    this.viewVbmappPDF = true;
  }

  onPDFGenerated() {
    this.viewVbmappPDF = false;
  }

}
