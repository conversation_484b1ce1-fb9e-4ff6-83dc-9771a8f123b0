<app-loading></app-loading>
<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="column" fxLayoutAlign="space-between stretch"> 
            <form #ngForm>
                <mat-form-field  style="width: 15%; padding: 20px;">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" 
                        [(ngModel)]="planointervencao.idPaciente"
                        name="paciente" disabled required>
                        <!--mat-option *ngFor="let paciente of pacientes" [value]="paciente.id" >
                            {{paciente.nome}}
                        </mat-option-->
                        <mat-option [value]="planointervencao.idPaciente" >
                            {{paciente?.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field  style="width: 15%;  padding-left: 20px;">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data" 
                        [(ngModel)]="planointervencao.data" name="data"
                        (dateChange)="setMsAssmtPorData()"
                        [matDatepicker]="picker" required>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="data.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <!-- <mat-form-field  style="width: 15%;  padding-left: 20px;">
                    <mat-label>Avaliação de Marcos</mat-label>
                    <mat-select placeholder="Avaliação de Marcos" 
                        [(ngModel)]="vbmappmsassmt" disabled
                        name="msassmt" (selectionChange) = "setMsAssmt()">
                        <mat-option *ngFor="let vbmappmsassmt of vbmappmsassmts" [value]="vbmappmsassmt" >
                            {{vbmappmsassmt.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field> -->

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="planointervencao.idProfissional"
                        name="profissionalFC" required
                        (selectionChange) = "setProfissional($event)">
                        <mat-option></mat-option>
                        <ng-container *ngFor="let profissional of profissionaisDoPaciente">
                            <mat-option [value]="profissional.id">
                                {{profissional.nome}}
                            </mat-option>
                        </ng-container>
                    </mat-select>
                    <mat-error *ngIf="profissionalFC.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Status</mat-label>
                    <mat-select placeholder="Status" 
                        [(ngModel)]="planointervencao.ativo"
                        name="ativo" required
                        (selectionChange) = "changeStatus($event)">
                        <mat-option [value]="false">
                            Rascunho 
                        </mat-option>
                        <mat-option [value]="true">
                            PEI liberado para coleta 
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="ativo.invalid">Status é obrigatório.</mat-error>  
                </mat-form-field>

                <button mat-mini-fab alt="Sair da edição" color="primary" style="margin: 15px;" (click)="exitEdit()">
                    <mat-icon>arrow_back</mat-icon>
                </button>
                <mat-form-field  style="visibility: hidden;">
                    <input class="input" matInput placeholder="Paciente" 
                        [(ngModel)]="planointervencao.idPaciente" name="paciente">
                </mat-form-field>
                <div style="display: flex; flex-direction: row; width: 100%;">
                    <div style="padding: 20px; width: 90%;">
                        <strong *ngIf="avaliacoesPlano.length > 0">Avaliações</strong><br>
                        <mat-chip-list>
                            <mat-chip *ngFor="let av of avaliacoesPlano" [value]="av"
                                [removable]="hasAccessCreate"
                                >
                                {{ getNomeTipoAvaliacao(av) }} - {{ av.data | date: 'dd/MM/yyyy'}} 
                                <mat-icon matChipRemove (click)="deleteAvaliacao(av)">
                                    cancel
                                </mat-icon>   
                            </mat-chip>
                        </mat-chip-list>
                    </div>
                </div>

                <mat-tab-group style="width: 100%;"
                    [selectedIndex]="selected.value"
					(selectedIndexChange)="selected.setValue($event)"
                    dynamicHeight>  

                    <mat-tab style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
                        <ng-template mat-tab-label>
                            <!-- <mat-icon class="example-tab-icon">thumb_up</mat-icon> -->
                            <span [matBadge]="planointervencao.habilidades ? planointervencao.habilidades.length : 0" matBadgeOverlap="false">Habilidades</span>
                        </ng-template>
                        <div style="padding: 10px;">
                        <!-- <div style="position: relative;z-index: 50; width: 100%;"> -->
                            <mat-chip-list>
                                <mat-chip *ngFor="let habilidade of planointervencao.habilidades" [value]="habilidade"
                                    [matTooltip]="habilidade?.nome"
                                    [removable]="hasAccessCreate"
                                    >
                                    <ng-container *ngIf="habilidade != undefined && !habilidade.etapa">
                                        {{ getNomeTipoAvaliacao(habilidade) }} - {{habilidade.sigla}}
                                    </ng-container>

                                    <ng-container *ngIf="habilidade != undefined && habilidade.etapa">
                                        [ESDM] - {{ habilidade?.id_nivel }} - {{ habilidade.dominio?.nome }} - {{ habilidade?.id.substr(4) }}
                                    </ng-container>
                                        
                                    <mat-icon matChipRemove (click)="deleteHabilidade(habilidade)"
                                        *ngIf="hasAccessCreate">
                                        cancel</mat-icon>
                                </mat-chip>
                            </mat-chip-list>
                        </div>
                        <div style="width: 100%; padding-top: 20px;"
                            *ngIf="hasAccessCreate">
                            
                            <strong>Avaliação</strong><br>
                            <small>Adicione as habilidades a serem trabalhadas neste plano selecionando-as abaixo.</small>
                        </div>
                        <div style="width: 100%; padding-top: 20px; display: flex; flex-direction: row; flex-wrap: wrap;"
                            *ngIf="hasAccessCreate">

                            <mat-form-field  style="width: 15%;">
                                <mat-label>Tipo de Avaliação</mat-label>
                                <mat-select placeholder="Tipo de Avaliação" 
                                    [(ngModel)]="tipoAvaliacao"
                                    name="tipoAvaliacao" (selectionChange) = "setAvaliacoes()" [disabled]="tipoAvaliacaoDisabled">
                                    <mat-option *ngFor="let tpAv of tipoAvaliacoesFiltered" [value]="tpAv">
                                        {{tpAv.nome}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field  style="width: 15%; padding-left: 10px;">
                                <mat-label>Avaliações</mat-label>
                                <mat-select placeholder="Avaliações" 
                                    [(ngModel)]="avaliacao"
                                    name="avaliacao" (selectionChange) = "setNiveis()" [disabled]="avaliacaoDisabled">
                                    <mat-option *ngFor="let av of avaliacoesFiltered" [value]="av" >
                                        {{ av.data | date: 'dd/MM/yyyy'}} 

                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field  style="width: 15%; padding-left: 10px;">
                                <mat-label>Nível</mat-label>
                                <mat-select placeholder="Nivel" 
                                    [(ngModel)]="nivel"
                                    name="nivel" (selectionChange) = "setDominios()" [disabled]="nivelDisabled">
                                    <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                                        {{nivel.nome}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <mat-form-field  style="width: 15%; padding-left: 10px;">
                                <mat-label>Domínio</mat-label>
                                <mat-select placeholder="Dominio" 
                                    [(ngModel)]="dominio"
                                    name="dominio" (selectionChange) = "filterHabilidades()" [disabled]="dominioDisabled">
                                    <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                                        {{dominio.nome}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>

                            <p style="width: 10%; padding-left: 10px; max-width: 10%; display: flex; align-items: center;">
                                <mat-checkbox #habilidadesInconsistentes (change)="sugestedMilestones($event)">
                                    Apenas habilidades obervadas como não consistentes.
                                </mat-checkbox>
                            </p>
                        </div>

                        <div class="mat-elevation-z0"
                            *ngIf="isAvaliacaoMarcoOrESDM() && isVbmappOrESDM=='VBMAPP' && hasAccessCreate">
                            <strong>Avaliação de Marco - {{ vbmappmsassmt?.data | date: 'dd/MM/yyyy' }}</strong><br>
                            <small>Adicione os marcos a serem trabalhados neste plano selecionando as competências abaixo.</small>
                            <table mat-table [dataSource]="vbmappMsAssmtItemView"> 
                                <!-- Id Column --> 
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef >Id</th>
                                    <td mat-cell *matCellDef="let row">{{row.marco.id}}</td>
                                </ng-container>
                        
                                <!-- Nome Column -->
                                <ng-container matColumnDef="nome" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.marco.nome}}</td>
                                </ng-container> 
                
                                <!-- Status Column -->
                                <ng-container matColumnDef="status" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <div class="label label-N" *ngIf="row.valor == 'N'"><small>Difícil obter</small></div>
                                        <div class="label label-P" *ngIf="row.valor == 'P'"><small>Mais ou menos</small></div>
                                        <div class="label label-A" *ngIf="row.valor == 'A'"><small>Consistente</small></div>
                                        <div class="label label-X" *ngIf="row.valor == 'X' || row.valor == undefined" disabled><small>Não observado</small></div>

                                    </td>
                                </ng-container> 
                          
                              <tr mat-header-row *matHeaderRowDef="displayedColumnsMsAssmt"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumnsMsAssmt;" 
                                [ngClass]="[msIsSelected(row.marco) ? 'msDisabled' : 'msEnabled']" 
                                (click)="addMarco(row.marco.id)"></tr>
                            </table>
                        </div>   

                        <div class="mat-elevation-z0"
                            *ngIf="isAvaliacaoMarcoOrESDM() && isVbmappOrESDM=='ESDM' && authService.verifySimpleAccess(getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create')">
                            <strong>ESDM Checklist - {{ esdmchecklist?.data | date: 'dd/MM/yyyy' }}</strong><br>
                            <small>Adicione os marcos a serem trabalhados neste plano selecionando as competências abaixo.</small>
                            <table mat-table [dataSource]="chklstcompView"> 
                                <!-- Id Column --> 
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef >Id</th>
                                    <td mat-cell *matCellDef="let row">{{row.competencia.id}}</td>
                                </ng-container>
                        
                                <!-- Nome Column -->
                                <ng-container matColumnDef="nome" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.competencia.nome}}</td>
                                </ng-container> 
                
                                <!-- Status Column -->
                                <ng-container matColumnDef="status" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <div class="label label-N" *ngIf="row.valor == 'N'"><small>Difícil obter</small></div>
                                        <div class="label label-P" *ngIf="row.valor == 'P'"><small>Mais ou menos</small></div>
                                        <div class="label label-A" *ngIf="row.valor == 'A'"><small>Consistente</small></div>
                                        <div class="label label-X" *ngIf="row.valor == 'X' || row.valor == undefined" disabled><small>Não observado</small></div>
                                    </td>
                                </ng-container> 
                          
                              <tr mat-header-row *matHeaderRowDef="displayedColumnsMsAssmt"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumnsMsAssmt;" 
                                [ngClass]="[msIsSelected(row.competencia) ? 'msDisabled' : 'msEnabled']" 
                                (click)="addCompetencia(row.competencia.id)"></tr>
                            </table>
                        </div>    
                        
                        <div class="mat-elevation-z0"
                            *ngIf="avaliacao != undefined && !isAvaliacaoMarcoOrESDM() && authService.verifySimpleAccess(getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create')">
                            <strong>{{ tipoAvaliacao?.nome }} - {{ avaliacao?.data | date: 'dd/MM/yyyy' }}</strong><br>
                            <small>Adicione as habilidades a serem trabalhados neste plano selecionando as competências abaixo.</small>
                            <table mat-table [dataSource]="habilidadesView"> 
                                <!-- Id Column --> 
                                <ng-container matColumnDef="id">
                                    <th mat-header-cell *matHeaderCellDef >Id</th>
                                    <td mat-cell *matCellDef="let row">{{row.sigla}}</td>
                                </ng-container>
                        
                                <!-- Nome Column -->
                                <ng-container matColumnDef="nome" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
                                </ng-container>

                                <!-- Status Column -->
                                <ng-container matColumnDef="status" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <div [ngClass] = "[getClassRespostaHabilidade(row.id)]"><small>{{ getDominioResposta(getRespostaHabilidade(row.id).valor)?.nome }}</small></div>
                                    </td>
                                </ng-container> 
                          
                              <tr mat-header-row *matHeaderRowDef="displayedColumnsMsAssmt"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumnsMsAssmt;" 
                                [ngClass]="[habilidadeIsSelected(row) ? 'msDisabled' : 'msEnabled']" 
                                (click)="addHabilidade(row.sigla)"></tr>
                            </table>
                        </div>

                    </mat-tab>

                    <!-- OBJETIVOS -->
                    <mat-tab style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
                        <ng-template mat-tab-label>
                            <!-- <mat-icon class="example-tab-icon">thumb_up</mat-icon> -->
                            <span [matBadge]="planointervencao.objetivos ? planointervencao.objetivos.length : 0" matBadgeOverlap="false">Objetivos</span>
                        </ng-template>

                        <div style="padding: 10px;" hidden>
                        <!-- <div style="position: relative;z-index: 50; width: 100%;"> -->
                            <mat-chip-list>
                                <mat-chip *ngFor="let objetivo of planointervencao.objetivos" [value]="objetivo"
                                    removable="true">
                                    {{objetivo.nome}} 
                                    <mat-icon matChipRemove (click)="deleteObjetivo(objetivo)">cancel</mat-icon>
                                </mat-chip>
                            </mat-chip-list>
                        </div>

                        <!-- OBJETIVOS SELECIONADOS -->
                        <div class="mat-elevation-z0" style="padding-bottom: 30px; width: 100%;">
                            <!--strong>Objetivos do Plano de Intervenção</strong-->
                            <!--a mat-raised-button href="javascript:void()" (click)="toggleTableRows()" color="primary">Toggle Rows</a-->
                            <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows style="width: 100%;"> 
                                <!-- displayedColumnsObjs = ['nomeObj', 'dominio', 'tipoSuporte', 'descricao', 'estimulos', 'action'] -->
                                <!-- Nome Column --> 
                                <ng-container matColumnDef="nomeObj">
                                    <th mat-header-cell *matHeaderCellDef >Nome</th>
                                    <td mat-cell *matCellDef="let row">
                                        {{ row.nome }}
                                    </td>
                                </ng-container>

                                <!-- Dominio Column 
                                <ng-container matColumnDef="dominio">
                                    <th mat-header-cell *matHeaderCellDef >Domínio</th>
                                    <td mat-cell *matCellDef="let row">{{row.dominio ? row.dominio.nome : null}}</td>
                                </ng-container> --> 

                                <!-- Marco Column --> 
                                <!-- <ng-container matColumnDef="marco">
                                    <th mat-header-cell *matHeaderCellDef >Marco</th>
                                    <td mat-cell *matCellDef="let row">{{row.marco ? row.marco.id : null}}</td>
                                </ng-container> -->
                                
                                <!-- Habilidades Column -->
                                <ng-container matColumnDef="habilidades">
                                    <th mat-header-cell *matHeaderCellDef>Habilidades</th>
                                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">
                                        <mat-chip-list style="padding: 10px;" *ngIf="row.habilidades">
                                            <mat-chip *ngFor="let habilidade of row.habilidades" [value]="habilidade"
                                                removable="false">
                                                {{ getNameHabilidade(habilidade) }}
                                            </mat-chip>
                                        </mat-chip-list>

                                        <mat-chip-list style="padding: 10px;" *ngIf="!row.habilidades">
                                            <mat-chip [value]="habilidade"
                                                removable="false">
                                                {{ getNomeTipoDominio(row.dominio, row.nivel, row.id) }}
                                            </mat-chip>
                                        </mat-chip-list>
                                    </td>
                                </ng-container>
                                
                                <!-- Estímulos Column -->
                                <ng-container matColumnDef="estimulos" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Estímulos</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30" style="padding: 10px 0 10px 0;">
                                        <mat-chip-list>
                                            <mat-chip *ngFor="let estimulo of row.estimulos" [value]="estimulo"
                                                removable="false"
                                                [disabled]="!estimulo.ativo">
                                                <small>{{estimulo.nome}}</small>
                                            </mat-chip>
                                            <mat-chip *ngIf="!row.estimulos" [value]="estimulo" [matTooltip]="'Objetivo não aplicável'">
                                                N/A
                                            </mat-chip>
                                        </mat-chip-list>
                                    </td>
                                </ng-container> 
                
                                <!-- Descricao Column -->
                                <ng-container matColumnDef="descricao" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Descrição</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.descricao}}</td>
                                </ng-container> 

                                <!-- Tipo Column -->
                                <ng-container matColumnDef="tipoSuporte" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Tipo de Suporte</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                    <mat-chip-list role="list" style="padding: 5px;" class="mat-chip-list-stacked">
                                        <mat-chip role="listitem" *ngFor="let tipoSuporte of row.tiposSuporte" 
                                        [value]="tipoSuporte" class="custom-chip">
                                        {{tipoSuporte.sigla}}
                                        </mat-chip>
                                        <mat-chip role="listitem" *ngIf="!row.tiposSuporte" 
                                            [value]="tipoSuporte" class="custom-chip" [matTooltip]="'Objetivo não aplicável'">
                                            N/A
                                        </mat-chip>
                                    </mat-chip-list>
                                    </td>
                                </ng-container>
                                
                                <!-- TipoColeta Column --> 
                                <ng-container matColumnDef="tipoColeta">
                                    <th mat-header-cell *matHeaderCellDef >Tipo de Coleta</th>
                                    <td mat-cell *matCellDef="let row">{{row.tipoColeta || "Naturalista"}}</td>
                                </ng-container>
                
                                <!-- Action Column -->
                                <ng-container matColumnDef="action" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <a (click)="editObjetivo(row)" class="edit"
                                            *ngIf="hasAccessRead
                                                   && !hasAccessUpdate">
                                            <i class="material-icons">
                                                remove_red_eye
                                            </i>
                                        </a>
                                        <a (click)="editObjetivo(row)" class="edit"
                                            *ngIf="hasAccessUpdate && !row.idTipoAvaliacao">
                                            <i class="material-icons">
                                                edit
                                            </i>
                                        </a>
                                        <a (click)="editarObjetivoESDM(planointervencao.objetivos.indexOf(row))" class="edit"
                                            *ngIf="hasAccessUpdate && row.idTipoAvaliacao">
                                            <i class="material-icons">
                                                edit
                                            </i>
                                        </a>
                                        <a (click)="deleteObjetivo(row)" class="delete"
                                            *ngIf="hasAccessDelete && !row.idTipoAvaliacao">
                                            <i class="material-icons">
                                                delete
                                            </i>
                                        </a>
                                        <a (click)="deleteObjetivoESDM(row)" class="delete"
                                            *ngIf="hasAccessDelete && row.idTipoAvaliacao">
                                            <i class="material-icons">
                                                delete
                                            </i>
                                        </a>
                                    </td>
                                </ng-container> 
                          
                              <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;" style="padding-top: 10px;"
                                [ngClass]="[row.status == 'Adquirido' ? 'objetivoAdquirido' : '']"></tr>
                            </table>
                        </div> 
                        <!-- OBJETIVOS LIST  -->
                        <div class="mat-elevation-z0"
                            *ngIf="hasAccessCreate">
                            <strong>Biblioteca de Objetivos</strong><br>
                            <small>Adicione os objetivos a serem trabalhados neste plano selecionando-os abaixo.</small>
                            <button mat-mini-fab matTooltip="Adicionar Novo Objetivo" color="primary" 
                                *ngIf="hasAccessCreateObjetivo"
                                style="margin: 15px; float: right;" (click)="newObjetivo()">
                                <mat-icon>add</mat-icon>
                            </button>
                            <!-- Filtro de Objetivos -->
                            <app-objetivovbmapp-filter [objetivosDoPlano]="listaDeObjetivosDoPlano" [mostrarObjetivosESDM]="true"></app-objetivovbmapp-filter>
                            
                            <table mat-table [dataSource]="objetivos" fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
                                <!-- Nome Column --> 
                                <ng-container matColumnDef="nomeObjSelList" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
                                </ng-container>

                                <!-- Habilidades Column -->
                                <ng-container matColumnDef="habilidadeObjSelList" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Habilidades</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <mat-chip-list *ngIf="row.habilidades" role="list" style="padding: 5px;">
                                            <mat-chip role="listitem" *ngFor="let habilidade of row.habilidades" [value]="habilidade">
                                                {{ getNameHabilidade(habilidade) }}
                                            </mat-chip>
                                        </mat-chip-list>
                                        <mat-chip-list role="list" style="padding: 5px;" *ngIf="!row.habilidades">
                                            <mat-chip role="listitem" [value]="habilidade">
                                                {{ getNomeTipoDominio(row.dominio, row.nivel, row.id) }}
                                            </mat-chip>
                                        </mat-chip-list>
                                    </td>
                                </ng-container> 

                                <!-- Estimulos Column -->
                                <ng-container matColumnDef="estimuloObjSelList" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Estímulos</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <mat-chip-list role="list" style="padding: 5px;">
                                            <mat-chip role="listitem" *ngFor="let estimulo of row.estimulos" [value]="estimulo">
                                                {{estimulo.nome}}
                                            </mat-chip>
                                            <mat-chip *ngIf="!row.estimulos" [value]="estimulo" [matTooltip]="'Objetivo não aplicável'" role="listitem">
                                                N/A
                                            </mat-chip>
                                        </mat-chip-list>
                                    </td>
                                </ng-container> 

                                <!-- Tipo Suporte Column -->
                                <ng-container matColumnDef="tipoSuporteObjSelList" fxFlex="30">
                                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Tipo de Suporte</th>
                                    <td mat-cell *matCellDef="let row" fxFlex="30">
                                        <mat-chip-list role="list" style="padding: 5px;" *ngIf="row.tiposSuporte" >
                                            <mat-chip role="listitem" *ngFor="let tipoSuporte of row.tiposSuporte" [value]="tipoSuporte">
                                                {{tipoSuporte.sigla}}
                                            </mat-chip>
                                        </mat-chip-list>
                                        <mat-chip-list role="list" style="padding: 5px;" *ngIf="!row.tiposSuporte" >
                                            <mat-chip role="listitem" [value]="tipoSuporte" [matTooltip]="'Objetivo não aplicável'">
                                                N/A
                                            </mat-chip>
                                        </mat-chip-list>
                                    </td>
                                </ng-container> 
                          
                              <tr mat-header-row *matHeaderRowDef="displayedColumnsObjsSelList"></tr>
                              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjsSelList;" 
                                class="msEnabled"
                                (click)="addObjetivo(row.id)"></tr>
                            </table>
                        </div> 
                    </mat-tab>
                </mat-tab-group>
            </form>  
        </div>
    </mat-card-content>
</mat-card>