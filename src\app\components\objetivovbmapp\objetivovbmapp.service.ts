import { DominioVBMAPP } from './../dominiovbmapp/dominiovbmapp-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ObjetivoVBMAPP } from './objetivovbmapp-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ObjetivoVBMAPPService {
 
  objetivolUrl = `${environment.API_URL}/objetivo_vbmapp`;
  
  public objetivos: BehaviorSubject<any[]> = 
    new BehaviorSubject<ObjetivoVBMAPP[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }
  
    create(objetivo: ObjetivoVBMAPP): Observable<string>{
      //console.log(objetivo);
      return this.http.post<string>(this.objetivolUrl, objetivo).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(objetivo: ObjetivoVBMAPP): Observable<ObjetivoVBMAPP>{
      return this.http.put<ObjetivoVBMAPP>(this.objetivolUrl + "/" + objetivo.id, objetivo).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findById(id: string): Observable<ObjetivoVBMAPP>{
      return this.http.get<ObjetivoVBMAPP>(this.objetivolUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findDominiosByNivel(id_nivel: string): Observable<DominioVBMAPP[]>{
      return this.http.get<DominioVBMAPP[]>(this.objetivolUrl + '/' + id_nivel + '/dominio').pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findByNivelDominio(id_nivel: string, id_dominio: string): Observable<ObjetivoVBMAPP>{
      return this.http.get<ObjetivoVBMAPP>(this.objetivolUrl + '/' + id_nivel + 'nivel/'
          + id_dominio + '/dominio').pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<ObjetivoVBMAPP[]>{
      return this.http.get<ObjetivoVBMAPP[]>(this.objetivolUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    delete(id: string): Observable<ObjetivoVBMAPP>{
      return this.http.delete<ObjetivoVBMAPP>(this.objetivolUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
}
