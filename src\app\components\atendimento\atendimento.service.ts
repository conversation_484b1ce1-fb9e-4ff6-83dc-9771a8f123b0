import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CalendarEventImp } from './calendarEventImp-model';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AtendimentoService {

  eventoatendimentoUrl = `${environment.API_URL}/eventoatendimento`;
  
  public funcoes: BehaviorSubject<CalendarEventImp[]> = 
    new BehaviorSubject<CalendarEventImp[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(eventoatendimento: CalendarEventImp): Observable<CalendarEventImp>{
    // console.log(eventoatendimento);
    return this.http.post<CalendarEventImp>(this.eventoatendimentoUrl, eventoatendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(eventoatendimento: CalendarEventImp): Observable<CalendarEventImp>{
    // console.log(eventoatendimento);
    return this.http.put<CalendarEventImp>(this.eventoatendimentoUrl + "/" + eventoatendimento.id, eventoatendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<CalendarEventImp>{
    return this.http.get<CalendarEventImp>(this.eventoatendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByProfissional(idProfissional: string): Observable<CalendarEventImp[]>{
    return this.http.get<CalendarEventImp[]>(this.eventoatendimentoUrl + '/profissional/' + idProfissional).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByProfissionalDate(idProfissional: string, data: string): Observable<CalendarEventImp[]>{
    return this.http.get<CalendarEventImp[]>(this.eventoatendimentoUrl + '/profissional/' + idProfissional + "/data/" + data).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<CalendarEventImp[]>{
    return this.http.get<CalendarEventImp[]>(this.eventoatendimentoUrl).pipe(
      map(obj => obj)
    );
  }

  delete(id: string): Observable<CalendarEventImp>{
    return this.http.delete<CalendarEventImp>(this.eventoatendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
