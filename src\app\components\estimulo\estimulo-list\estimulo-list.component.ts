import { Estimulo } from './../estimulo-model';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { EstimuloService } from './../estimulo.service';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-estimulo-list',
  templateUrl: './estimulo-list.component.html',
  styleUrls: ['./estimulo-list.component.css']
})
export class EstimuloListComponent implements OnInit {

  public estimulos: Estimulo[];

  displayedColumns = ['categoria', 'nome', 'action']

  constructor(private estimuloService: EstimuloService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {
    this.estimuloService.find().subscribe(estimulos => {
      this.estimulos = estimulos.filter((p) => p.ativo);
    })
  }

  edit(estimulo: Estimulo) {
    this.router.navigate(['/estimulo/' + estimulo.id]);
  }

  delete(estimulo: Estimulo): void {
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        estimulo.ativo = false;
        this.estimuloService.update(estimulo).subscribe((p) => {
          this.estimuloService.showMessage('Estímulo inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/estimulo']);
        });
      }
    });
  }
}
