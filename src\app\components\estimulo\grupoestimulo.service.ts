import { GrupoEstimulo } from './grupoestimulo-model';
import { map, catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';


@Injectable({
  providedIn: 'root'
})
export class GrupoEstimuloService {
  [x: string]: any;

  grupoEstimuloUrl = `${environment.API_URL}/grupoestimulo`;

  public $grupoEstimulo: Observable<GrupoEstimulo[]>;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  
  create(grupoEstimulo: GrupoEstimulo): Observable<string>{
    // console.log(grupoEstimulo);
    return this.http.post<GrupoEstimulo>(this.grupoEstimuloUrl, grupoEstimulo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  
  update(GrupoEstimulo: GrupoEstimulo): Observable<GrupoEstimulo>{
    return this.http.put<GrupoEstimulo>(this.grupoEstimuloUrl + "/" + GrupoEstimulo.id, GrupoEstimulo).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<GrupoEstimulo>{
    return this.http.get<GrupoEstimulo>(this.grupoEstimuloUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<GrupoEstimulo[]>{
    return this.http.get<GrupoEstimulo[]>(this.grupoEstimuloUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
