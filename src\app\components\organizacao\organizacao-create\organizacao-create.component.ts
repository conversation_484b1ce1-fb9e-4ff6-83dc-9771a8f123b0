import { FirebaseUserModel } from './../../template/auth/user-model';
import { Profissional } from './../../profissional/profissional-model';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from './../../template/auth/auth.service';
import { OrganizacaoService } from './../organizacao.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { Organizacao } from './../organizacao-model';
import { Component, OnInit, ViewChild } from '@angular/core';
import { RegistroDia } from '../../../shared/model/registro-dia'
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';


@Component({
  selector: 'app-organizacao-create',
  templateUrl: './organizacao-create.component.html',
  styleUrls: ['./organizacao-create.component.css']
})

export class OrganizacaoCreateComponent implements OnInit {
  organizacao: Organizacao = new Organizacao();

  domingo= new RegistroDia(1);
  segunda= new RegistroDia(2);
  terca= new RegistroDia(3);
  quarta= new RegistroDia(4);
  quinta= new RegistroDia(5);
  sexta= new RegistroDia(6);
  sabado= new RegistroDia(7);

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  razaoSocialFC = new FormControl('', [Validators.required]);
  cnpjFC = new FormControl('', [Validators.required]);
  emailFC = new FormControl('', [Validators.required]);

  hasAccessUpdate: boolean = false;

  constructor(private organizacaoService: OrganizacaoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    //Pego o id da organização do usuário corrente
    let idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId

    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Organizacao.Cadastro de organizacao','update');

    if(idOrganizacao == undefined){ //Create
      this.organizacao = new Organizacao();
      this.organizacao.ativo = true;
    } else { //Update
      //Recupero a organização
      this.organizacaoService.findById(idOrganizacao).subscribe( org => {
        this.organizacao = org;
        this.buildHorarioAtendimento('LOAD');
      })
    }
  }

   async save(){
    let valid: boolean = true;
    //console.log(this.organizacao)
    if(this.form.valid){
      await this.buildHorarioAtendimento('SAVE');
      if(this.organizacao.id == undefined){
        this.organizacaoService.create(this.organizacao).subscribe((id) => {
          this.organizacaoService.showMessage('Organização criada com sucesso!');
          //this.router.navigate(['/organizacao/update/' + this.organizacao.id]);
        });
      } else {
        this.organizacaoService.update(this.organizacao).subscribe((funcao) => {
          this.organizacaoService.showMessage('Organização alterada com sucesso!');
          //this.router.navigate(['/organizacao/update/' + this.organizacao.id]);
        });
      }
      
    } else {
      this.organizacaoService.showMessage('Existem campos inválidos no formulário!',true);  
    }
  }

  cancel() {
    this.router.navigate(['/']);    
  }

  buildHorarioAtendimento(tipoBuild: string){
    if(tipoBuild == 'LOAD'){

      this.organizacao.horarios.forEach(registro => {
        switch(registro.diaSemana){
          case 1 : 
          this.domingo = registro;
          break;

          case 2 : 
          this.segunda = registro;
          break;

          case 3 : 
          this.terca = registro;
          break;

          case 4 : 
          this.quarta = registro;
          break;

          case 5 : 
          this.quinta = registro;
          break;

          case 6 : 
          this.sexta = registro;
          break;

          case 7 : 
          this.sabado = registro;
          break;

        }
      })

    }else{
      var horarios: RegistroDia[] = [];
      horarios.push(this.domingo);
      horarios.push(this.segunda);
      horarios.push(this.terca);
      horarios.push(this.quarta);
      horarios.push(this.quinta);
      horarios.push(this.sexta);
      horarios.push(this.sabado);
      this.organizacao.horarios = horarios;
    }
    
  }

}
