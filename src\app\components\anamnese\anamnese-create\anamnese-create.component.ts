import { LoadingService } from './../../../shared/service/loading.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Anamnese, Campo, Pergunta, Valor } from '../anamnese-model';
import { Grupo } from '../anamnese-model';
import { AnamneseService } from '../anamnese.service'

@Component({
  selector: 'app-anamnese-create',
  templateUrl: './anamnese-create.component.html',
  styleUrls: ['./anamnese-create.component.css']
})
export class AnamneseCreateComponent implements OnInit {


  anamnese: Anamnese = new Anamnese();
  grupoList: Grupo[] = [];
  tiposCampo = [
    {value: 'TEXT', descricao:'Texto'},
    {value: 'NUMBER', descricao:'Numérico'},
    {value: 'SELECAO', descricao:'Seleção'},
  ]

  loginUser: boolean;

  constructor(private anamneseService: AnamneseService,
    private router: Router,
    private route: ActivatedRoute,
    private LoadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.LoadingService.show();
    let idAnamnese = this.route.snapshot.paramMap.get('id');

    try {
      this.anamnese.grupos = this.grupoList;
      if(idAnamnese){
        const data = await this.anamneseService.findById(idAnamnese).toPromise();
        // console.log(data);
        this.anamnese = data;
        this.grupoList = data.grupos;
      }
    } catch (error) {
      console.log(error);
      this.LoadingService.hide();
    } finally {
      this.LoadingService.hide();
    }
  }

  addGroup(){    
    this.grupoList.push(new Grupo);
  }

  addPergunta(index: number){

    if(this.grupoList[index].perguntas){
      this.grupoList[index].perguntas.push(new Pergunta());
    }else{
      this.grupoList[index].perguntas = [];
      this.grupoList[index].perguntas.push(new Pergunta());
    }

    // console.log(this.anamnese)
  }

  removerPergunta(indexGrupo: number,indexPergunta: number){
    
    this.grupoList[indexGrupo].perguntas.splice(indexPergunta,1);
  }

  save(): void{

      if(this.anamnese.id == undefined){
        this.anamneseService.create(this.anamnese).subscribe((id) => {
          this.anamneseService.showMessage('Anamnese criado com sucesso!');
          this.router.navigate(['/anamnese']);
        });
      } else {
        this.anamneseService.update(this.anamnese).subscribe((anamnese) => {
          this.anamneseService.showMessage('Anamnese alterado com sucesso!');
          this.router.navigate(['/anamnese']);
        });
      }    
    
  }

  onChangeTipoCampo(tipo: string, indexGrupo: number,indexPergunta: number){
    if(tipo != 'SELECAO'){
      this.anamnese.grupos[indexGrupo].perguntas[indexPergunta].campo.valores = [];
    }else{
      this.anamnese.grupos[indexGrupo].perguntas[indexPergunta].campo.valores.push(new Valor);
    }
  }

  addOpcao(indexGrupo: number,indexPergunta: number){
      this.anamnese.grupos[indexGrupo].perguntas[indexPergunta].campo.valores.push(new Valor);
  }

  cancel(){
    this.router.navigate(['/anamnese']);
  }

  imprime(){
    // console.log(this.anamnese);
  }

}
