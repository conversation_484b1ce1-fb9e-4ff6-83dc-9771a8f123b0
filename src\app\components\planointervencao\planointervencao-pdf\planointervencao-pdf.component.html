<!--div #pdf style="display: flex; flex-direction: column; width: 580px;"--> 
<div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 90%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">Plano de Intervenção - Objetivos</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle">Paciente: {{ planointervencao.paciente.nome }}</p>
        </div>
        <div style="text-align: center; width: 33%;">
            <p class="subtitle">Profissional: {{ planointervencao.profissional.nome }}</p>
        </div>
        <div style="text-align: right; width: 33%;">
            <p class="subtitle">Data: {{ planointervencao.data | date: 'dd/MM/yyyy' }}</p>
        </div>
    </div>
    
    <!-- DOMÍNIOS, OBJETIVOS E ETAPAS -->
    <!--div style="display: flex; flex-direction: column; flex-wrap: wrap; padding: 20px;"
        *ngFor="let dominio of dominios"-->
    <ng-container *ngFor="let dominio of dominios">
        <div *ngIf="dominioMap.get(dominio.id) != undefined">
            <p class="dominio">{{ dominio.nome }}</p>
            <div *ngFor="let objetivo of dominioMap.get(dominio.id)">
                <!--p [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">{{ objetivo.id }} - {{ objetivo.nome }}</p-->
                <p class="objetivo">
                    {{ objetivo.id }} - {{ objetivo.nome }}
                    <ng-container *ngIf="objetivo.status == 'Adquirido'">
                        <i><br>(Objetivo em manutenção)</i>  
                    </ng-container>
                </p>
                <p class="desc_objetivo">{{ objetivo.descricao_plano }}</p>
                <div *ngFor="let etapa of objetivo.etapa; let i=index">
                    <p [ngClass]="[etapa.status == 'Adquirida' ? 'etapa' : '', 
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] == undefined ? 'etapa_atual' : '',
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] != undefined && objetivo.etapa[i-1].status == 'Adquirida' ? 'etapa_atual' : '',
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] != undefined && objetivo.etapa[i-1].status != 'Adquirida' ? 'etapa' : ''
                                    ]">
                        Etapa {{ i+1 }} - {{ etapa.id }} - {{ etapa.nome }}
                    </p>
                </div>
            </div>
        </div>
    </ng-container>
    <!--/div-->
</div>
<!--button (click)=do()>Teste</button-->