import { TipoSuporteService } from './../../tiposuporte/tiposuporte.service';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { MatSelectChange } from '@angular/material/select';
import { DominioAvaliacao } from '../../avaliacao/dominio-avaliacao-model';
import { DominioVBMAPP } from '../../dominiovbmapp/dominiovbmapp-model';
import { DominioVBMAPPService } from '../../dominiovbmapp/dominiovbmapp.service';
import { HabilidadeAvaliacao } from '../../avaliacao/habilidade-avaliacao-model';
import { HabilidadeAvaliacaoService } from '../../avaliacao/habilidade-avaliacao.service';
import { NivelAvalicao } from '../../avaliacao/nivel-avaliacao-model';
import { NivelAvaliacaoService } from '../../avaliacao/nivel-avaliacao.service';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { ObjetivoVBMAPPService } from './../objetivovbmapp.service';
import { TipoSuporte } from '../../tiposuporte/tiposuporte-model';
import { ObjetivoService } from '../../objetivo/objetivo.service';
import { NivelService } from '../../nivel/nivel.service';
import { DominioService } from '../../dominio/dominio.service';
import { ESDMChkLstCompetencia } from '../../esdmchecklist/esdmchklst-competencia-model';
import { CompetenciaService } from '../../competencia/competencia.service';
import { ESDMChecklist } from '../../esdmchecklist/esdmchecklist-model';

@Component({
  selector: 'app-objetivovbmapp-filter',
  templateUrl: './objetivovbmapp-filter.component.html',
  styleUrls: ['./objetivovbmapp-filter.component.css']
})
export class ObjetivovbmappFilterComponent implements OnInit {
  @Input() objetivosDoPlano: any[] = []; // Recebe os objetivos do plano
  @Input() mostrarObjetivosESDM: boolean = false; // Variavel para tratar a exibição dos objetivos ESDM
  
  public allObjetivos = [];
  public nome = '';
  public tiposAvaliacao: TipoAvaliacao[] = [];
  public tipoAvaliacao: TipoAvaliacao | null = null;
  public niveis: any[] = [];
  public nivel: any | null = null;
  public dominios: any[] = [];
  public dominio: any | null = null;
  public habilidades: any[] = [];
  public habilidade: any | null = null;
  public siglaHabilidade = '';
  public tiposSuporte: string[] = [];
  public suportes: TipoSuporte[];

  public esdmchecklist: ESDMChecklist = new ESDMChecklist();

  constructor(
    private objetivoVBMAPPService: ObjetivoVBMAPPService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private nivelAvaliacaoService: NivelAvaliacaoService,
    private nivelService: NivelService,
    private habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    private tipoSuporteService: TipoSuporteService,
    private objetivoESDMService: ObjetivoService,
    private competenciaService: CompetenciaService,
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Verifica se os objetivos do plano mudaram
    if (changes['objetivosDoPlano'] && this.objetivosDoPlano) {
      this.allObjetivos = [...this.allObjetivos.filter(obj => this.objetivosDoPlano?.find(o => o.id == obj.id) == undefined)];
    }
  }

  private loadInitialData(): void {
    // Carregar todos os tipos de avaliação (ativos)
    this.tipoAvaliacaoService.find().subscribe(tipos => {
      this.tiposAvaliacao = tipos
        .filter(ta => ta.ativo)
        .sort((a, b) => {
          const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
          const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
          return nomeA.localeCompare(nomeB);
        });
    });

    // Carregar tipos de suporte
    this.tipoSuporteService.find().subscribe(async (tps) => {
      this.suportes = tps;
    });

    // Carregar tanto os objetivos VBMAPP quanto ESDM, e outros se aplicável
    this.objetivoVBMAPPService.find().subscribe(listasVBMAPP => {
      if(this.mostrarObjetivosESDM) {
        this.objetivoESDMService.find().subscribe(listasESDM => {
          let listaESDMComp = []
          for(let objetivo of listasESDM){
            objetivo.sigla = objetivo.id;
            objetivo.idTipoAvaliacao = "5";
            listaESDMComp.push(objetivo)
          }
          this.allObjetivos = [...listaESDMComp];
        })
      }
      this.allObjetivos = [...listasVBMAPP];
      this.allObjetivos = this.allObjetivos.filter(obj => this.objetivosDoPlano?.find(o => o.id == obj.id) == undefined);
    });

    this.esdmchecklist.checklist = [];
    this.competenciaService.find().subscribe(competencias => {
      let chklst: ESDMChkLstCompetencia;
      for(let competencia of competencias) {
        chklst = new ESDMChkLstCompetencia();
        chklst.competencia = competencia;
        this.esdmchecklist.checklist.push(chklst);
      }
    })
  }

  setTipoAvaliacao(event: MatSelectChange): void {
    this.resetFilters(['nivel', 'dominio', 'habilidade', 'siglaHabilidade', 'habilidades', 'dominios']);
    
    // Verifica o tipo de avaliação selecionado
    if (this.tipoAvaliacao) {
      // Se o tipo de avaliação não for o VB-MAPP (por exemplo, ESDM ou outro)
      if (this.tipoAvaliacao.id == "5") {
        // Filtrar objetivos pelo serviço objetivoESDMService, ou outro serviço específico
        this.nivelService.find().subscribe(niveis => {
          this.niveis = niveis;
          this.setFilter();
        });
      } else {
        // Para outros tipos de avaliação, usa o serviço nível de avaliação comum
        this.nivelAvaliacaoService.findByTipoAvaliacao(this.tipoAvaliacao.id).subscribe(niveis => {
          this.niveis = niveis;
          this.setFilter();
        });
      }
    }
  }

  setNivel(event: MatSelectChange): void {
    if (!this.tipoAvaliacao) return;
    this.resetFilters(['dominio', 'habilidade', 'siglaHabilidade', 'habilidades']);
    if (this.tipoAvaliacao.id == "5") {
      this.objetivoESDMService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
        this.dominios = dominios;
        this.setFilter();
      });
    } else {
      this.habilidadeAvaliacaoService.findDominiosByNivel(this.nivel!.id).subscribe(dominios => {
        this.dominios = dominios;
        this.setFilter();
      });
    }
  }

  setDominio(): void {
    if (!this.nivel) return;
    this.resetFilters(['habilidade', 'siglaHabilidade']);
    if (this.tipoAvaliacao.id == "5") {
      this.habilidades = this.esdmchecklist.checklist.filter(h => h.competencia.id_dominio == this.dominio!.id);
      this.setFilter();
    } else {
      this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel.id, this.dominio!.id).subscribe(habilidades => {
        this.habilidades = habilidades;
        this.setFilter();
      });
    }
  }

  cleanFilter(): void {
    this.resetFilters([
      'tipoAvaliacao', 'nivel', 'dominio', 'habilidade', 'nome', 'tiposSuporte'
    ]);
    this.setFilter();
  }

  private resetFilters(fields: string[]): void {
    fields.forEach(field => {
      (this as any)[field] = Array.isArray((this as any)[field]) ? [] : null;
    });
    this.nome = '';
    this.tiposSuporte = [];
  }

  setFilter(): void {
    const filteredList = this.allObjetivos.filter(obj => this.filterObjetivo(obj) && !this.objetivosDoPlano.includes(obj.id));
    this.objetivoVBMAPPService.objetivos.next(filteredList);
  }
  
  private filterObjetivo(obj: any): boolean {
    const hasValidTipoAvalicao = !this.tipoAvaliacao || obj.habilidades?.some(h => h.idTipoAvaliacao == this.tipoAvaliacao?.id) || obj.idTipoAvaliacao == this.tipoAvaliacao.id
  
    const hasValidNivel = !this.nivel || obj.habilidades?.some(h => h?.idNivelAvaliacao === this.nivel.id || h?.id_nivel == this.nivel.id) || obj.id_nivel == this.nivel.id;
  
    const hasValidDominio = !this.dominio || obj.habilidades?.some(h => h.dominio?.id === this.dominio.id || h.idDominioAvaliacao === this.dominio?.id) || obj.id_dominio === this.dominio.id;
  
    const hasValidHabilidade = !this.habilidade || obj.habilidades?.some(h => h.id === this.habilidade.id || h.id === this.habilidade?.competencia?.id) || obj?.id === this.habilidade?.competencia?.id;
  
    const hasValidTiposSuporte = !this.tiposSuporte.length || 
      this.tiposSuporte.some(suporte => obj.tiposSuporte?.some(s => s.nome.toLowerCase() === suporte.toLowerCase()));
  
    const hasValidNome = !this.nome || obj.nome.toLowerCase().includes(this.nome.toLowerCase());
  
    return hasValidTipoAvalicao && hasValidNivel && hasValidDominio && hasValidHabilidade && hasValidTiposSuporte && hasValidNome;
  }  
}
