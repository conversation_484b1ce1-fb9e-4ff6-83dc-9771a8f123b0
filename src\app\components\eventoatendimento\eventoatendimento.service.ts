import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EventoAtendimento } from './eventoatendimento-model';
import { BehaviorSubject, EMPTY, Observable } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class EventoatendimentoService {

  eventoatendimentoUrl = `${environment.API_URL}/eventoatendimento`;
  
  public funcoes: BehaviorSubject<EventoAtendimento[]> = 
    new BehaviorSubject<EventoAtendimento[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(eventoatendimento: EventoAtendimento): Observable<EventoAtendimento>{
    // console.log(eventoatendimento);
    return this.http.post<EventoAtendimento>(this.eventoatendimentoUrl, eventoatendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(eventoatendimento: EventoAtendimento): Observable<EventoAtendimento>{
    // console.log(eventoatendimento);
    return this.http.put<EventoAtendimento>(this.eventoatendimentoUrl + "/" + eventoatendimento.id, eventoatendimento).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<EventoAtendimento>{
    return this.http.get<EventoAtendimento>(this.eventoatendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByProfissional(idProfissional: string): Observable<EventoAtendimento[]>{
    return this.http.get<EventoAtendimento[]>(this.eventoatendimentoUrl + '/profissional/' + idProfissional).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByProfissionalDate(idProfissional: string, data: string): Observable<EventoAtendimento[]>{
    return this.http.get<EventoAtendimento[]>(this.eventoatendimentoUrl + '/profissional/' + idProfissional + "/data/" + data).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<EventoAtendimento[]>{
    return this.http.get<EventoAtendimento[]>(this.eventoatendimentoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<EventoAtendimento>{
    return this.http.delete<EventoAtendimento>(this.eventoatendimentoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
