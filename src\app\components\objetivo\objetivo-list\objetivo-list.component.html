<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="column" fxLayoutAlign="space-between stretch" fxLayoutGap="10">                         
            <mat-form-field  style="width: 10%; padding: 20px;">
                <mat-label>Nível</mat-label>
                <mat-select placeholder="Nivel" 
                    [(ngModel)]="nivel"
                    name="nivel" (selectionChange) = "setDominios()">
                    <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                        {{nivel.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%; padding: 20px;">
                <mat-label>Domínio</mat-label>
                <mat-select placeholder="Dominio" 
                    [(ngModel)]="dominio"
                    name="dominio" (selectionChange) = "filterObjetivo()">
                    <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                        {{dominio.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        
        <div class="mat-elevation-z0"
            *ngIf="hasAccessRead">
            <table mat-table [dataSource]="objetivosView"> 
                <!-- Id Column --> 
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef >Id</th>
                    <td mat-cell *matCellDef="let row">{{row.id}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        {{row.nome}}<br><br>
                        {{row.descricao}}
                    </td>
                </ng-container> 

                <!-- Etapas Column -->
                <!--ng-container matColumnDef="etapas" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Etapas</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <ng-container *ngFor="let etapa of row.etapa">
                            {{ etapa.id }} - {{ etapa.nome }}<br>
                        </ng-container>
                    </td>
                </ng-container--> 

                <!-- Action Column -->
                <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef>Ações</th>
                    <td mat-cell *matCellDef="let row">
                        <a (click)="edit(row.id)" class="edit"
                            *ngIf="hasAccessUpdate">
                            <i class="material-icons">
                                edit
                            </i>
                        </a>
                        <!--a (click)="delete(row.id)" class="delete"
                            *ngIf="hasAccessDelete">
                            <i class="material-icons">
                                delete
                            </i>
                        </a-->
                    </td>
                </ng-container>
          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>        
    </mat-card-content>
</mat-card>