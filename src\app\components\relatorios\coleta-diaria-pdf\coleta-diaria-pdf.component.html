<div>
    <mat-card class="mat-elevation-z0">
        <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <!-- INÍCIO DADOS BÁSICOS DA COLETA -->
            <div class="cabecalho-pdf">
                <img class="cabecalho-img-pdf" src="assets/img/{{ idOrganizacao }}.png" width="100px">
                <div class="cabecalho-dados-pdf">
                    Paciente: {{paciente.nome}}<br>
                    <ng-container *ngIf="profissionalLogado">Profissional: {{profissionalLogado?.nome}} <br></ng-container>
                    Data da Coleta: ____/____/____ <br>
                    <ng-container *ngIf="planoIntervencao?.objetivos?.length > 0">Data PEI: {{planoIntervencao?.data | date: 'dd/MM/yyyy'}} <br></ng-container>
                    <ng-container *ngIf="pic?.objetivos?.length > 0"> Data PIC: {{pic?.data | date: 'dd/MM/yyyy'}} <br></ng-container>
                </div>
            </div>
            <!-- FIM DADOS BÁSICOS DA COLETA -->

            <!-- INÍCIO DAS SESSÕES -->
            <div class="mat-elevation-z0">
                <div>
                    <div *ngFor="let tab of tabs; let idxSessao = index" [label]="tab">
                        <!-- INÍCIO DE SESSÃO -->
                        <div style="width: 100%;"
                            dynamicHeight>
                            <div style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
                                <div *ngIf="planoIntervencao?.objetivos?.length > 0" style="display: flex; flex-direction: row; flex-wrap: wrap;">
                                    <h2>Objetivos PEI</h2>
                                    <table class="dominio">
                                        <!-- DOMÍNIOS, OBJETIVOS E ETAPAS -->
                                        <!-- DOMÍNIOS -->
                                        <ng-container *ngFor="let idDominio of dominioMap[idxSessao]?.keys()">
                                            <tr>
                                                <td class="dominio">
                                                    {{ idDominio == "SD" ? "" : dominioMap[idxSessao].get(idDominio)[0].marco.dominio.nome.toUpperCase() }}
                                                </td>
                                            </tr>
                                            <!-- OBJETIVOS -->
                                            <div *ngFor="let objetivo of dominioMap[idxSessao].get(idDominio)">
                                                <tr *ngIf="objetivo.habilidades" class="avoid-page-break">
                                                    <td [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
                                                        {{ objetivo.nome }}
                                                        <ng-container *ngIf="objetivo.status == 'Adquirido'">
                                                            <table class="etapa">
                                                                <tr>
                                                                    <td class="etapa v-middle">
                                                                        <mat-icon>construction</mat-icon>
                                                                        Objetivo em manutenção
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </ng-container>
                                                        <!-- ETAPAS -->
                                                        <ng-container *ngIf="objetivo.tipoColeta == 'Naturalista'">
                                                            <div *ngFor="let etapa of objetivo.tiposSuporte; let i=index">
                                                                <ng-container *ngFor="let estimulo of etapa.estimulos">
                                                                    <ng-container *ngIf="estimulo.ativo">
                                                                        <table class="etapa">
                                                                            <tr>
                                                                                <td class="etapa">
                                                                                    {{estimulo.nome}} ({{ coletasdiarias[idxSessao].objetivos
                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                        .tiposSuporte[coletasdiarias[idxSessao].objetivos
                                                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                .tiposSuporte.indexOf(etapa)].sigla }})
                                                                                </td>
                                                                                <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                    .tiposSuporte[coletasdiarias[idxSessao].objetivos
                                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                        .tiposSuporte.indexOf(etapa)]
                                                                                                            .estimulos[coletasdiarias[idxSessao].objetivos[coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                                .tiposSuporte[coletasdiarias[idxSessao].objetivos
                                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                                        .tiposSuporte.indexOf(etapa)]
                                                                                                                            .estimulos.indexOf(estimulo)]
                                                                                                                                .periodo; let idxP = index"> 
                                                                                    <td style="text-align: center;">
                                                                                        <a style="text-decoration: none;" (click)="setRespostaPeriodo(idxSessao, idxP, objetivo, etapa, estimulo)">
                                                                                            <mat-icon style="font-size: 32px;">check_box_outline_blank</mat-icon>
                                                                                        </a>
                                                                                    </td>
                                                                                </ng-container>
                                                                            </tr>
                                                                        </table>
                                                                    </ng-container>
                                                                </ng-container>
                                                            </div>
                                                        </ng-container>
                                                    </td>
                                                </tr>
                                                <tr *ngIf="!objetivo.habilidades" class="avoid-page-break">
                                                    <td [ngClass]="objetivo.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
                                                        {{ objetivo.id }} - {{ objetivo.nome }}
                                                        <ng-container *ngIf="objetivo.status == 'Adquirido'">
                                                            <table class="etapa">
                                                                <tr>
                                                                    <td class="etapa v-middle">
                                                                        <mat-icon>construction</mat-icon>
                                                                        Objetivo em manutenção
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </ng-container>
                                                        <div *ngFor="let etapa of objetivo.etapa; let i=index">
                                                            <table class="etapa">
                                                                <tr>
                                                                    <th></th>
                                                                    <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                    .etapa[coletasdiarias[idxSessao].objetivos
                                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                        .etapa.indexOf(etapa)].periodo; let idx = index">
                                                                        <th class="periodo">P{{idx+1}}</th>
                                                                    </ng-container>
                                                                </tr>
                                                                <tr>
                                                                    <td class="etapa">{{ coletasdiarias[idxSessao].objetivos
                                                                            [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                            .etapa[coletasdiarias[idxSessao].objetivos
                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                    .etapa.indexOf(etapa)].id.slice(-3) }} - 
                                                                        {{ coletasdiarias[idxSessao].objetivos
                                                                                [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                .etapa[coletasdiarias[idxSessao].objetivos
                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                        .etapa.indexOf(etapa)].nome }}
                                                                    </td>
                                                                    <ng-container *ngFor="let periodo of coletasdiarias[idxSessao].objetivos
                                                                                                    [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                    .etapa[coletasdiarias[idxSessao].objetivos
                                                                                                        [coletasdiarias[idxSessao].objetivos.indexOf(objetivo)]
                                                                                                        .etapa.indexOf(etapa)].periodo; let idxP = index">
                                                                        <td style="text-align: center;">
                                                                            <a style="text-decoration: none;" (click)="setRespostaPeriodoESDM(idxSessao, idxP, objetivo, etapa)">
                                                                                <mat-icon style="font-size: 32px;">check_box_outline_blank</mat-icon>
                                                                            </a>
                                                                        </td>
                                                                    </ng-container>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </div>
                                        </ng-container>
                                    </table>
                                </div>
                                <h2 *ngIf="pic?.objetivos?.length > 0" style="padding-top: 20px;">Objetivos PIC</h2>
                                <div class="container" *ngIf="pic?.objetivos?.length > 0">
                                    <ng-container>
                                        <div *ngFor="let objetivoPIC of pic.objetivos" class="item"> 
                                        <!-- Define o tamanho máximo que cada componente pode ocupar -->
                                
                                            <!-- Registro de Eventos -->
                                            <div *ngIf="objetivoPIC?.tipoColeta == 'Registro de Eventos'" class="avoid-page-break">
                                                <div class="counter-container">
                                                    <!-- Descrição -->
                                                    <div class="truncatable">
                                                        <b>Coportamento Alvo: </b> {{objetivoPIC?.comportamentoAlvo}} <br>
                                                        <b>Definição Operacional: </b> {{objetivoPIC?.definicaoOperacional}} <br>
                                                        <b>Meta: </b> {{objetivoPIC?.meta}} <br>
                                                    </div>

                                                    <!-- Lista para adicionar registros -->
                                                    <div class="registro-lista">
                                                        <h3><b>Registro de Eventos</b></h3>
                                                        <div class="registro-conteudo">
                                                            <!-- Conteúdo da div -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Cronometragem ou Amostragem de Tempo -->
                                            <div *ngIf="objetivoPIC?.tipoColeta == 'Cronometragem' || objetivoPIC?.tipoColeta == 'Amostragem de Tempo'" class="avoid-page-break">
                                                <div class="counter-container">
                                                    <!-- Descrição -->
                                                    <div class="truncatable">
                                                        <b>Coportamento Alvo: </b> {{objetivoPIC?.comportamentoAlvo}} <br>
                                                        <b>Definição Operacional: </b> {{objetivoPIC?.definicaoOperacional}} <br>
                                                        <b>Meta: </b> {{objetivoPIC?.meta}} <br>
                                                        <ng-container *ngIf="objetivoPIC?.tipoAmostragem">
                                                            <b>Tipo de coleta: </b> {{objetivoPIC?.tipoAmostragem}} <br>
                                                            <b>Intervalo: </b> {{objetivoPIC?.intervalo}} {{objetivoPIC?.medidaIntervalo}} <br>
                                                        </ng-container>
                                                    </div>

                                                    <!-- Lista para adicionar registros de tempo -->
                                                    <div class="registro-lista">
                                                        <h3><b>Registros de Tempo</b></h3>
                                                        <ul>
                                                            <li *ngFor="let index of [].constructor(7); let i = index">
                                                                <span>{{i + 1}}. _____h_____m_____s</span>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>                          
                        </div>
                        <!-- FIM DE SESSÃO -->
                    </div>
                </div>
            </div>
            <!-- FIM DAS SESSÕES -->  
            <!-- <div style="width: 100%; display: table; margin: 0%; margin-top: 10px;">
                <div style="display: table-row-group;">
                    <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SFT – Suporte físico total
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            SFP – Suporte físico parcial
                        </div>
                    </div>
                    <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SV – Suporte verbal
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            SG – Suporte gestual
                        </div>
                    </div>
                    <div style="display:table-row;">
                        <div style="display: table-cell;">
                            SP – Suporte posicional
                        </div>
                        <div style="display: table-cell; text-align: right; margin: 0%;">
                            &nbsp;
                        </div>
                    </div>
                </div>
            </div> -->
        </mat-card-content>
    </mat-card>
</div>