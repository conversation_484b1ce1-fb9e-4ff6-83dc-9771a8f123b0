<div fxLayout="row wrap" style="width: 100%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"
    *ngIf="hasAccessRead && avaliacoes?.length > 0"> 
    <div fxLayout="row wrap" style="width: 25%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"> 
        <div fxLayout="column" style="width: 60%; height: fit-content; background-color: rgb(240, 240, 240); margin: 20px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.3);">
            
            <div style="padding-top: 10px; text-align: center;">
                <b>Tipo de Gráfico</b>
            </div>
            <div fxFlex="100" style="width: 100%; text-align: center;">
                <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select id="chartTypeSelect" [(ngModel)]="selectedChartType" (selectionChange)="updateChart()">
                        <mat-option value="horizontalBar">Barra Horizontal</mat-option>
                        <mat-option value="bar">Barra Vertical</mat-option>
                        <mat-option value="radar">Radar</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            
            <div style="text-align: center;">
                <h3><b>Filtros</b></h3>
            </div>

            <div style="text-align: center;">
                <strong>Tipo de Avaliação</strong>
            </div>
            
            <div fxFlex="100" style="width: 100%; text-align: center">
                <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select
                        [(ngModel)]="idTipoAVal"
                        name="tipoAvaliacao" (selectionChange) = "filterAvaliacoes($event)">
                        <mat-option *ngFor="let tipo of tiposAvaliacoesView" [value]="tipo.id" [selected]="tipoAvaliacao?.id === tipo.id">
                            {{tipo.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div style="text-align: center;">
                <section style="width: 100%;" fxFlex="100">
                    <div style="text-align: center;">
                        <strong>Avaliações</strong>
                    </div>
                    <ul style="padding-left: 10px;">
                        <li *ngFor="let msassmt of avaliacoesFiltered; let i=index">
                            <mat-checkbox placeholder="Avaliação" 
                                id="{{ i }}"
                                [checked] = "checked(i)"
                                name="{{msassmt.data  | date: 'dd/MM/yyyy'}}" (change) = "setMsAssessment($event)">
                                {{msassmt.data | date: 'dd/MM/yyyy'}} <br>
                            </mat-checkbox>
                        </li>
                    </ul>
                </section>
            </div>
            <div style="text-align: center;">
                <strong>Nível</strong>
            </div>
            <div fxFlex="100" style="width: 100%; text-align: center;" *ngIf="isAvaliacaoMarcoOrESDM()">
                <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select
                        [(ngModel)]="nivel"
                        name="nivel" (selectionChange) = "setNivel()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxFlex="100" style="width: 100%; text-align: center;" *ngIf="!isAvaliacaoMarcoOrESDM()">
                 <mat-form-field style="margin-left: 5px; width: 80%;">
                    <mat-select
                        [(ngModel)]="nivelAvaliacao"
                        name="nivel" (selectionChange) = "setNivel()">
                        <mat-option *ngFor="let nivel of niveisAvaliacao" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxLayout="row" fxLayoutAlign="start center" style="width: 100%; text-align: center; padding-bottom: 15px;" *ngIf="selectedChartType != 'radar'">
                <label style="cursor: pointer; text-decoration: none;">
                  <input type="checkbox" [(ngModel)]="showPercentage" (change)="togglePercentage()">
                    Mostrar porcentagem
                </label>
            </div>
            <div fxLayout="row" fxLayoutAlign="start center" style="width: 100%; text-align: center; padding-bottom: 15px;">
                <label style="cursor: pointer; text-decoration: none;">
                  <input type="checkbox" [(ngModel)]="showLegenda">
                    Mostrar Legenda
                </label>
            </div>
            <div fxLayout="row" fxLayoutAlign="start center" style="width: 100%; text-align: center; padding-bottom: 15px;">
                <a (click)="downloadChart()" style="cursor: pointer; text-decoration: none;">
                    <i class="material-icons" style="margin-right: 8px;">
                        print
                    </i>
                    <span style="font-weight: bold;">Baixar Gráfico</span>
                </a>
            </div>
        </div>
    </div>
    <div fxLayout="row wrap" style="width: 85%; display: flex;" fxLayoutAlign="end center" fxLayoutGap="10"> 
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 60%;">
            <!-- Canvas do Gráfico -->
            <canvas baseChart #meuCanvas
                [datasets]="graph.datasets" 
                [labels]="graph.labels" 
                [options]="graph.options"
                legend="true" 
                [chartType]="graph.chartType"
                [colors]="colors" height="250"
                style="display: flex;">
            </canvas>
    
            <div *ngIf="showLegenda" #legendContainer>
                <span *ngIf="dominioParc" style="color: red; font-weight: bold; font-size: 12px;">* Domínio possui habilidades não observadas na avaliação</span>
        
                <!-- Contêiner da Legenda -->
                <div style="text-align: center; margin-top: 20px; padding: 10px; border: 1px solid #ffffff; border-radius: 5px;">
                    <p style="font-weight: bold; text-decoration: underline; margin-bottom: 10px;">Legenda</p>
                    <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px;">
                        <div *ngFor="let dominio of dominios" style="flex: 1 1 calc(33.33% - 10px); text-align: left;">
                            <span style="font-weight: bold; color: #333;">{{ tipoAvaliacao.id == 3 || tipoAvaliacao.id == 5 ? dominio.id : dominio.sigla }}</span> - 
                            <span style="color: #555;">{{ dominio.nome }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div *ngIf="avaliacoes?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Aguardando a criação da primeira avaliação!</h1>
</div>
