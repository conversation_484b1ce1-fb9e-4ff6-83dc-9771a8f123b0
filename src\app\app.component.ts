import localePt from '@angular/common/locales/pt';
import { LOCALE_ID } from '@angular/core';
import { registerLocaleData } from '@angular/common';
import { EsdmchecklistService } from './components/esdmchecklist/esdmchecklist.service';
import { PlanoIntervencaoService } from './components/planointervencao/planointervencao.service';
import { PacienteService } from './components/paciente/paciente.service';
import { UserService } from './components/template/auth/user.service';
import { ActivatedRoute } from '@angular/router';
import { Component } from '@angular/core';
import schedule from 'node-schedule'
import * as moment from 'moment';
import { format } from 'date-fns';


@Component({
  selector: 'app-root',
  templateUrl: "app-component.html"
})
export class AppComponent {
  title = 'CapaciTEAutismo';
  hasFullView = false;

  constructor(private activatedRoute: ActivatedRoute,
    private userService: UserService,
    private pacienteService: PacienteService,
    private esdmchecklistService: EsdmchecklistService,
    private planoIntervencaoService: PlanoIntervencaoService) {}

  ngOnInit() {
    registerLocaleData(localePt);
    this.setHasFullView();
    /*
    schedule.scheduleJob('0 0 * * *', () => { 
    //schedule.scheduleJob('0 * * * * *', () => { 
      //console.log("Schedule test")
      this.pacienteService.find().subscribe(pacientes => {
        pacientes.forEach(paciente => {
      //this.pacienteService.findById("0").subscribe(paciente => {
          this.planoIntervencaoService.findLastByPaciente(paciente.id).subscribe(plano => {
            if(plano.length == 1){
              console.log(paciente.nome + " - " + plano[0].data)
              //Atualizo os status das etapas e objetivos no Plano de Intervenção
              //Atualizo o kpiColeta
              this.planoIntervencaoService.setStatusEtapaPlano(plano[0]);
            }
          })
        })
      })
    })*/
    
    //this.userService.getCurrentUser()
  }

  private setHasFullView() {
    this.activatedRoute.queryParams.subscribe(params => {
      this.hasFullView = params["hasFullView"] || false;
    });
  }
}
