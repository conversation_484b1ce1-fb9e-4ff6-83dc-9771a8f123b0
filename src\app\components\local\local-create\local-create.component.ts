import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, NgForm, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../template/auth/auth.service';
import { LocalAtendimento } from '../local-atendimento-model';
import { LocalAtendimentoService } from '../local-atendimento.service';

@Component({
  selector: 'app-local-create',
  templateUrl: './local-create.component.html',
  styleUrls: ['./local-create.component.css']
})
export class LocalCreateComponent implements OnInit {

  localAtendimento: LocalAtendimento = new LocalAtendimento();
  locaisAtendimento: LocalAtendimento[] = [];

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  permiteChoqueHorario = new FormControl('', [Validators.required]);

  hasAccessUpdate: boolean = false;

  constructor(private localAtendimentoService: LocalAtendimentoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let localAtendimento = this.route.snapshot.paramMap.get('id'); 
    
    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Local Atendimento.Cadastro de locais','update');

    if(localAtendimento == undefined) { //Create
      this.localAtendimento = new LocalAtendimento();
    } else {  //Edit
        await this.localAtendimentoService.findById(localAtendimento).subscribe(async localAtendimento => {
          this.localAtendimento = localAtendimento; 
        })
    }

  }
  /*
  setTwoNumberDecimal(event) {
    
    this.value = parseFloat(this.value).toFixed(2);
  }*/

  save(){
    let valid: boolean = true;
    if(this.form.valid){  
      this.localAtendimento.isAtivo = true;
      if(this.localAtendimento.id == undefined){
        this.localAtendimentoService.create(this.localAtendimento).subscribe((id) => {
          this.localAtendimentoService.showMessage('Local criado com sucesso!');
          this.router.navigate(['/local-atendimento']);
        });
      } else {
        this.localAtendimentoService.update(this.localAtendimento).subscribe((funcao) => {
          this.localAtendimentoService.showMessage('Local alterado com sucesso!');
          this.router.navigate(['/local-atendimento']);
        });
      }
        
    } else {
      this.localAtendimentoService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/local-atendimento']);
  }

}
