<div fxLayout="row wrap" style="width: 100%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"
    *ngIf="hasAccessRead"> 
    <div fxLayout="row wrap" style="width: 20%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10"> 
        <div fxLayout="column" style="width: 80%; height: fit-content; background-color: lightgray; margin: 20px;">
            <div style="text-align: center;">
                <h3><b>Filtros</b></h3>
            </div>
            <div>
                <section style="width: 100%;" fxFlex="100">
                    <strong style="padding: 5px;">Avaliações de Marcos</strong>
                    <ul>
                        <li *ngFor="let msassmt of vbmappmsassmts | slice:0:4; let i=index">
                            <mat-checkbox placeholder="Avaliação de Marcos" 
                                id="msassmt-{{ i }}"
                                [checked] = "checked(i)"
                                name="{{msassmt.data  | date: 'dd/MM/yyyy'}}" (change) = "setMsAssessment($event)">
                                {{msassmt.data | date: 'dd/MM/yyyy'}} <br>
                            </mat-checkbox>
                        </li>
                    </ul>
                </section>
            </div>
            <strong style="padding-left: 5px;">Nível</strong>
            <div fxFlex="100" style="width: 100%;">
                <mat-form-field style="margin-left: 5px; width: 60%;">
                    <mat-select
                        [(ngModel)]="nivel"
                        name="nivel" (selectionChange) = "setNivel()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
    </div>
    <div fxLayout="row wrap" style="width: 80%; display: flex;" fxLayoutAlign="end center" fxLayoutGap="10"> 
        <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 60%;">
            <canvas baseChart 
                [datasets]="graph.datasets" 
                [labels]="graph.labels" 
                [options]="graph.options"
                legend="true" 
                [chartType]="graph.chartType"
                [colors]="colors" height="250"
                style="display: flex;">
            </canvas>
        </div> 
    </div>
</div>
<!--div fxLayout="row wrap" style="width: 100%; display: flex;" fxLayoutAlign="space-between stretch" fxLayoutGap="10">   
    <div fxLayout="row wrap" style="display: flex; width: 20%;">
        <section style="width: 100%;" fxFlex="100">
            <ul>
                <li *ngFor="let checklist of vbmappmsassmts | slice:0:4; let i=index">
                    <mat-checkbox placeholder="Checklist" 
                        id="checklist-{{ i }}"
                        [checked] = "checked(i)"
                        name="{{checklist.data  | date: 'dd/MM/yyyy'}}" (change) = "setChecklist($event)">
                        {{checklist.data | date: 'dd/MM/yyyy'}} <br>
                    </mat-checkbox>
                </li>
            </ul>
        </section>
        <mat-form-field  style="padding: 20px;">
            <mat-label>Nível</mat-label>
            <mat-select placeholder="Nível" 
                [(ngModel)]="nivel"
                name="nivel" (selectionChange) = "setNivel()">
                <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                    {{nivel.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="chartjs-container" *ngIf="graph.datasets && graph.datasets.length > 0" style="width: 60%;">
        <canvas baseChart 
            [datasets]="graph.datasets" 
            [labels]="graph.labels" 
            [options]="graph.options"
            legend="true" 
            [chartType]="graph.chartType"
            [colors]="colors" height="200">
        </canvas>
    </div> 
</div-->