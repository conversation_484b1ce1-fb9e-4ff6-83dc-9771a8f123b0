import { <PERSON>rente } from './../parente/parente-model';
import { Profissional } from './../profissional/profissional-model';
import { Endereco } from './../../shared/model/endereco-model';
import { Anamnese } from '../anamnese/anamnese-model';
export class Paciente{
    id?: string;
    nome: string;
    cpf: string;
    telefone: string;
    email: string;
    dataNascimento: Date;
    sexo: string;
    equipe: Profissional[];
    parentes: Parente[];
    ativo: boolean;
    roles: Map<string, string>;
    kpiChkLst: string;
    kpiPlInterv: string;
    kpiColeta: string;
    tiposIntervencao: string[];
    endereco: Endereco;
    anamnese: Anamnese[]

    constructor() {
        this.endereco = new Endereco()
    }
}