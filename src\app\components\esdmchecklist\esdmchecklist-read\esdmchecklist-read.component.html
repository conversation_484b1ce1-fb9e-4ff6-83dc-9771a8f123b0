<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div style="width: 100%; text-align: right;">
            <button mat-mini-fab alt="Sair da edi<PERSON>" color="primary" style="margin: 15px;" (click)="exitEdit()">
                <mat-icon>arrow_back</mat-icon>
            </button>
            <ng-container *ngIf="esdmchecklists != undefined">
                <button mat-mini-fab color="primary" style="margin: 7px;" (click)="generatePDF()">
                    <mat-icon>printer</mat-icon>
                </button>
            </ng-container>
            <ng-container
            *ngIf="esdmchecklists != undefined && esdmchecklists.length > 0 && hasAccessUpdate">
                <button mat-mini-fab color="primary" style="margin: 7px;" (click)="edit()">
                    <mat-icon>edit</mat-icon>
                </button>
            </ng-container>
            <button mat-mini-fab color="primary" style="margin: 7px;" (click)="new()"
                *ngIf="hasAccessCreate">
                <mat-icon>add</mat-icon>
            </button>
            <ng-container
                *ngIf="esdmchecklists != undefined && esdmchecklists.length > 0 && hasAccessDelete">
                <button mat-mini-fab color="primary" style="margin: 7px;" (click)="delete()">
                    <mat-icon>delete</mat-icon>
                </button>
            </ng-container>
            <!-- <app-esdmchecklist-avaliacao *ngIf="viewAvaliacao" [paciente]="paciente" [esdmchecklist]="esdmchecklist" id="esdmchecklistAvaliacao" #esdmchecklistAvaliacao></app-esdmchecklist-avaliacao> -->
        </div>
        <div fxLayout="column" fxLayoutAlign="space-between stretch" fxLayoutGap="10" *ngIf="esdmchecklists?.length > 0">   
            <mat-form-field  style="width: 15%;  padding: 20px;">
                <mat-label>Checklist</mat-label>
                <mat-select placeholder="Checklist" 
                    [(ngModel)]="esdmchecklist"
                    name="checklist" (selectionChange) = "do()" disabled>
                    <mat-option *ngFor="let checklist of esdmchecklists" [value]="checklist" >
                        {{checklist.data | date: 'dd/MM/yyyy'}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%; padding: 20px;">
                <mat-label>Profissional</mat-label>
                <mat-select placeholder="Profissional" 
                    [(ngModel)]="esdmchecklist.idProfissional"
                    name="profissional" (selectionChange) = "do()"
                    disabled>
                    <mat-option *ngFor="let profissional of profissionais" [value]="profissional.id" >
                        {{profissional.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
                        
            <mat-form-field  style="width: 10%; padding: 20px;">
                <mat-label>Nível</mat-label>
                <mat-select placeholder="Nivel" 
                    [(ngModel)]="nivel"
                    name="nivel" (selectionChange) = "setDominios()">
                    <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                        {{nivel.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%; padding: 20px;">
                <mat-label>Domínio</mat-label>
                <mat-select placeholder="Dominio" 
                    [(ngModel)]="dominio"
                    name="dominio" (selectionChange) = "filterChecklist()">
                    <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                        {{dominio.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        
        <div class="mat-elevation-z0"
            *ngIf="hasAccessRead && esdmchecklists?.length > 0">
            <table mat-table [dataSource]="chklstcompView"> 
                <!-- Id Column --> 
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef >Id</th>
                    <td mat-cell *matCellDef="let row">{{row.competencia.id}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.competencia.nome}}</td>
                </ng-container> 

                <!-- Status Column -->
                <ng-container matColumnDef="status" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <div class="label label-N" *ngIf="row.valor == 'N'"><small>Difícil obter</small></div>
                        <div class="label label-P" *ngIf="row.valor == 'P'"><small>Mais ou menos</small></div>
                        <div class="label label-A" *ngIf="row.valor == 'A'"><small>Consistente</small></div>
                        <div class="label label-X" *ngIf="row.valor == 'X' || row.valor == undefined" disabled><small>Não observado</small></div>
                    </td>
                </ng-container> 
          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>
        <app-esdmchecklistpdf *ngIf="viewPDF" [esdmcheklistPdf]="esdmchecklist" [pacienteSearch]="paciente" (pdfGenerated)="onPDFGenerated()"></app-esdmchecklistpdf>
    </mat-card-content>
</mat-card>
<div *ngIf="esdmchecklists?.length == 0 || esdmchecklists == undefined" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Nenhum checklist criado!</h1>
</div>

<!--mat-tab-group mat-align-tabs="start">
    <mat-tab *ngFor="let nivel of niveis" [label]="nivel.nome" (click)="tabChange(nivel.id)" (selectedTabChange)="tabChanged($event)">
        <mat-tab-group>
            <mat-tab *ngFor="let dominio of dominioMap | filterdominio : nivel.id" [label]="dominio.id" (selectedTabChange)="tabChanged($event)">
                <table mat-table [dataSource]="chklstcompView"> 

                    <ng-container matColumnDef="id" fxFlex="70">
                        <th mat-header-cell *matHeaderCellDef fxFlex="70">Id</th>
                        <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.competencia.id}}</td>
                    </ng-container>
            
                    <ng-container matColumnDef="nome" fxFlex="30">
                        <th mat-header-cell *matHeaderCellDef fxFlex="30">Data de Nascimento</th>
                        <td mat-cell *matCellDef="let row" fxFlex="30">{{row.competencia.nome }}</td>
                    </ng-container>
              
                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
            </mat-tab>
        </mat-tab-group>
    </mat-tab>
</mat-tab-group-->
