import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ParentescoService } from './../parentesco.service';
import { Parentesco } from './../parentesco-model';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-parentesco-list',
  templateUrl: './parentesco-list.component.html',
  styleUrls: ['./parentesco-list.component.css']
})
export class ParentescoListComponent implements OnInit {

  public parentescos: Parentesco[];

  displayedColumns = ['nome', 'action']

  constructor(private parentescoService: ParentescoService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {
    this.parentescoService.find().subscribe(parentescos => {
      this.parentescos = parentescos;
    })
  }

  edit(parentesco: Parentesco){
    this.router.navigate(['/parentesco/' + parentesco.id]);
  }

  delete(parentesco: Parentesco): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        parentesco.ativo = false;
        this.parentescoService.update(parentesco).subscribe((p) => {
          this.parentescoService.showMessage('Parentesco inativado com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/parentesco']);
        });
      }
    });
  }

}
