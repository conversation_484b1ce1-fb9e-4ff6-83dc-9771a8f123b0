import { PIC } from './pic-model';
import { Injectable } from '@angular/core';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PICService {
  picUrl = `${environment.API_URL}/pic`;
  //                  etapa.id      data    status[]
  public etapaMap: Map<string, Map<string, string[]>>;
  
  public pic: BehaviorSubject<PIC[]> = new BehaviorSubject<PIC[]>([]);

  constructor(
    private snackbar: MatSnackBar,
    private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }
  
    create(pic: PIC): Observable<string>{
      //console.log(pic);
      return this.http.post<PIC>(this.picUrl, pic).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(pic: PIC): Observable<PIC>{
      // console.log(pic);
      return this.http.put<PIC>(this.picUrl + "/" + pic.id, pic).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findById(id: string): Observable<PIC>{
      return this.http.get<PIC>(this.picUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    findByPaciente(idPaciente: string): Observable<PIC[]>{
      return this.http.get<PIC[]>(this.picUrl + "/paciente/" + idPaciente).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findResumoByPaciente(idPaciente: string): Observable<any[]> {
      return this.http.get<any[]>(`${this.picUrl}/paciente/${idPaciente}/resumo`).pipe(
        map(obj => obj),
        catchError(() => EMPTY)
      );
    }
  
    findLastByPaciente(idPaciente: string): Observable<PIC[]>{
      return this.http.get<PIC[]>(this.picUrl + "/paciente/" + idPaciente + "/last").pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<PIC[]>{
      return this.http.get<PIC[]>(this.picUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    delete(id: string): Observable<PIC>{
      return this.http.delete<PIC>(this.picUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  }
  