<mat-card class="home mat-elevation-z3">
    <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap; 
        border-style: groove; border-width: 1px; border-radius: 10px;
        justify-content: center; margin: 0%;"> 
        <div style="width: 100%; display: flex; flex-direction: column; flex-wrap: wrap; 
            border-style: none; border-width: 1px; border-radius: 10px;
            justify-content: center; margin: 0%; text-align: center;">
            <h2>Indicadores de Pacientes
                <h4>(Total: {{ totPacientes }} pacientes)</h4>
            </h2>
        </div>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap; 
            border-style: none; border-width: 1px; border-radius: 10px;
            justify-content: center; margin: 0%;">
            <div class="chartjs-container" class="divCanvas">
                <canvas baseChart 
                    [data]="pieChartDataChklst"
                    [labels]="pieChartLabelsChklst" 
                    [colors]="colors"
                    [chartType]="pieChartType"
                    [plugins]="pieChartPlugins"
                    [options]="pieChartOptionsChklst"
                    legend="false" 
                    height="200"
                    style="display: flex; "
                    >
                </canvas>
            </div> 
            
            <div class="chartjs-container" class="divCanvas">
                <canvas baseChart 
                    [data]="pieChartDataPlano"
                    [labels]="pieChartLabelsPlano" 
                    [colors]="colors"
                    [chartType]="pieChartType"
                    [plugins]="pieChartPlugins"
                    [options]="pieChartOptionsPlano"
                    legend="false" 
                    height="200"
                    style="display: flex; "
                    >
                </canvas>
            </div> 
    
            <div class="chartjs-container" class="divCanvas">
                <canvas baseChart 
                    [data]="pieChartDataColeta"
                    [labels]="pieChartLabelsColeta" 
                    [colors]="colors"
                    [chartType]="pieChartType"
                    [plugins]="pieChartPlugins"
                    [options]="pieChartOptionsColeta"
                    legend="false" 
                    height="200"
                    style="display: flex; "
                    >
                </canvas>
            </div> 
        </div>
    </div>
</mat-card>
