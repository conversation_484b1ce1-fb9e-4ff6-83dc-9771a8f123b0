<mat-card>
    <mat-card-header>
        <mat-card-title>
            <ng-container *ngIf="!eventoAtendimento.id">
                Novo atendimento
            </ng-container>
            <ng-container *ngIf="eventoAtendimento.id">
                {{eventoAtendimento.meta.paciente.nome}} ({{eventoAtendimento.meta.procedimento.nome}})
            </ng-container>
        </mat-card-title>
        
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-card style="margin-bottom: 20px;">
                    <mat-card-header style="display: block;">
                        <label>Informações do Paciente</label>
                    </mat-card-header>
                    <mat-card-content>
                        <mat-form-field  style="width: 45%; padding-left: 10px;">
                            <input type="text"
                                placeholder="Paciente" 
                                matInput
                                [(ngModel)]="eventoAtendimento.meta.paciente"
                                name="Paciente"
                                [formControl]="pacienteFC" 
                                [matAutocomplete]="auto" required>
                                <mat-icon matSuffix>keyboard_arrow_down</mat-icon>
                            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                                <mat-option *ngFor="let paciente of filteredPacientes | async" [value]="paciente" >
                                    {{paciente.nome}}
                                </mat-option>
                            </mat-autocomplete>
                            <mat-label>Paciente</mat-label>
                            <mat-error *ngIf="pacienteFC.invalid">Paciente é obrigatório.</mat-error>  
                        </mat-form-field> 
        
                        <mat-form-field  style="width: 20%; padding-left: 10px;">
                            <mat-label>Procedimento</mat-label>
                            <mat-select placeholder="Procedimento" 
                                [(ngModel)]="procedimentoSelect"
                                (selectionChange)="adicionaHoraFinal()"
                                name="Procedimento" required>
                                <mat-option *ngFor="let proc of tiposProcedimentos" [value]="proc.id" >
                                    {{proc.nome}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="tipoProcedimento.invalid">Tipo de Procedimento é obrigatório.</mat-error>  
                        </mat-form-field>
                    </mat-card-content>
                </mat-card>
                <mat-card>
                    <mat-card-header style="display: block">
                        <label>Informações do Agendamento</label>
                    </mat-card-header>
                    <mat-card-content>
                        <div style="display: flex;">                        
                            <mat-form-field style="width: 2%; margin-left: 10px; margin-right: 20px;">
                                <input class="input" type="color" matInput placeholder="Cor" 
                                [(ngModel)]="eventoAtendimento.color.primary" name="color">
                            </mat-form-field>
                            <mat-form-field style="width: 15%; margin-left: 10px; margin-top: 10px;">
                                <input class="input" matInput placeholder="Data" 
                                [(ngModel)]="eventoAtendimento.start" name="dataInicio"
                                [matDatepicker]="picker" required>
                                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                                <mat-datepicker #picker></mat-datepicker>
                                <mat-error *ngIf="start">Data é obrigatória.</mat-error>  
                            </mat-form-field>
            
                            <mat-form-field style="width: 15%; margin-left: 10px; margin-top: 10px;">
                                <mat-label>Hora Ínicio</mat-label>
                                <input matInput [ngxTimepicker]="horaI"
                                        [(ngModel)]="horaInicio"
                                        [format]="24"
                                        placeholder="Hora"
                                        [formControl]="timeInicioFC" required
                                        (ngModelChange)="adicionaHoraFinal()">
                                        <mat-error *ngIf="timeInicioFC.invalid">Hora é obrigatória.</mat-error>  
                            </mat-form-field>
                            <ngx-material-timepicker #horaI></ngx-material-timepicker>
            
                            <mat-form-field style="width: 15%; margin-left: 10px; margin-top: 10px;">
                                <mat-label>Hora Término</mat-label>
                                <input matInput [ngxTimepicker]="horaT"
                                        [(ngModel)]="horaTermino"
                                        [format]="24"
                                        placeholder="Hora"
                                        [formControl]="timeTerminoFC" required>
                                        <mat-error *ngIf="timeTerminoFC.invalid">Hora é obrigatória.</mat-error>  
                            </mat-form-field>
                            <ngx-material-timepicker #horaT></ngx-material-timepicker> 
                            <div class="mat-form-field-infix ng-tns-c126-2" style="margin-left: 10px; width: 15px;">
                                <a matTooltip="Timeline Agendamentos" mat-button color="primary" (click)="visualizarChoqueHorario()" style="size: 20px;">
                                    <mat-icon>today</mat-icon>
                                </a>      
                            </div>                       
                        </div>  
                         <!--mat-label>Repetir</mat-label-->
                        <mat-checkbox placeholder="Repetir" 
                            *ngIf="!edicao"
                            [(ngModel)]="eventoAtendimento.meta.recorrente"
                            [formControl]="recorrenteFC"
                            (change)="checkRecorrente()"
                            style="width: 100%; padding-left: 10px;"
                            name="Repetir">
                                Repetir
                        </mat-checkbox>

                        <mat-form-field  style="width: 20%; padding-left: 10px;" *ngIf="!edicao">
                            <mat-label>Periodicidade:</mat-label>
                            <mat-select 
                                [(ngModel)]="freq"
                                name="frequencia" [disabled] = "!eventoAtendimento.meta.recorrente">
                                <mat-option [value] ="daily" >Diariamente</mat-option>
                                <mat-option [value] ="weekly" >Semanalmente</mat-option>
                                <mat-option [value] ="monthly" >Mensalmente</mat-option>
                                <!--mat-option [value]="Bimestralmente" >Bimestralmente</mat-option>
                                <mat-option value="Marcado não confirmado" >Marcado não confirmado</mat-option-->
                            </mat-select>
                        </mat-form-field>

                        <mat-form-field style="width: 15%; margin-left: 10px;" *ngIf="!edicao">
                            <mat-label>Nº de Repetições</mat-label>
                            <input matInput 
                                    [disabled]="true"
                                    type="number"
                                    [(ngModel)]="qtdRepeticao"
                                    placeholder="Nº de Repetições"
                                    [formControl]="qtdRepeticaoFC" >
                        </mat-form-field>

                        <mat-form-field  style="width: 20%; padding-left: 10px;">
                            <mat-label>Status</mat-label>
                            <mat-select placeholder="Status" 
                                [(ngModel)]="statusSelect"
                                name="status" required>
                                <mat-option *ngFor="let status of statusAgendaList" [value]="status.id" >
                                    {{status.nome}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="tipoProcedimento.invalid">Tipo de Procedimento é obrigatório.</mat-error>  
                        </mat-form-field>

                        <mat-form-field  style="width: 20%; padding-left: 10px;">
                            <mat-label>Local</mat-label>
                            <mat-select placeholder="Local" 
                                [(ngModel)]="localSelect"
                                name="Local" required>
                                <mat-option *ngFor="let loc of locais" [value]="loc.id" >
                                    {{loc.nome}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="tipoProcedimento.invalid">Tipo de Procedimento é obrigatório.</mat-error>  
                        </mat-form-field>
                    </mat-card-content>
                </mat-card>
                
                <mat-card style="margin-top: 20px;" >
                    <mat-card-header style="display: block;">
                        Informações da Equipe
                    </mat-card-header>
                    <mat-card-content>
                        <!-- Lista de Profissionais -->
                        <mat-card class="mat-elevation-z0"  style="margin-top: 10px; width: 100%;">
                            <mat-card-title class="subtitle">Profissional(is)</mat-card-title>
                            <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
                                <mat-card fxFlex="31" *ngFor="let profissional of atendimento.profissionais; index as id"
                                    class="mat-elevation-z0" style="margin: 10px; width: 27%;"
                                    fxFlexAlign="stretch" fxLayoutGap="20px">
                                        <mat-card-header> 
                                            <div mat-card-avatar style="margin: 10px;">
                                                <div *ngIf="profissional.sexo=='M'; then menino else menina"></div>
                                                <ng-template #menino><img src="/assets/img/menino_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                                                <ng-template #menina><img src="/assets/img/menina_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                                            </div>
                                            <mat-card-title class="mat-card-title">
                                                <small style="text-align: left;" _ngcontent-c9>
                                                    {{profissional.nome}}
                                                    <a style="padding-left: 10px; color: gray; cursor: pointer;"
                                                        (click)="deleteProfissional(atendimento.profissionais.indexOf(profissional))"
                                                        *ngIf="hasAccessUpdate">
                                                        <mat-icon>person_remove</mat-icon>
                                                    </a>
                                                </small>
                                            </mat-card-title>
                                        </mat-card-header>
                                    </mat-card>
                                </div>
                                <!-- Fim da lista de profissionais-->
                                <!-- Início da adição de profissionais-->
                                <mat-form-field  style="width: 40%;" >
                                    <mat-label>Profissional</mat-label>
                                    <mat-select placeholder="Profissional" 
                                        [disabled]="!permiteOutrosProfissionais()"
                                        [(ngModel)]="profissional"
                                        name="profissional" >
                                        <mat-option *ngFor="let profissional of profissionais" [value]="profissional" >
                                            {{profissional.nome}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <a (click)="addProfissional()" class="add" style="cursor: pointer;"
                                    
                                    *ngIf="permiteOutrosProfissionais()">
                                    <i class="material-icons">
                                        add
                                    </i>
                                </a>
                            </mat-card> 
                    <!-- Fim da adição de profissionais-->
                    </mat-card-content>
                </mat-card>
                
            </form>
        </div>
    </mat-card-content>
    <mat-card-actions>
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
        <button mat-raised-button color="danger" (click)="delete(eventoAtendimento)"
            *ngIf="hasAccessUpdate && eventoAtendimento.id">
            Excluir
        </button>
      </mat-card-actions>
      <hr>
</mat-card>