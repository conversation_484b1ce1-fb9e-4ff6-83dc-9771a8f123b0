<div style="width: 100%;">
    <div style="text-align: center">
        <img src="assets/img/{{ idOrganizacao }}.png" alt="" width="180px">
        <br>
        <h1><b>{{anamnese.descricao}}</b></h1>
    </div>
    
    <h3 style=" background-color:darkgray; margin: 20px;"><b><PERSON><PERSON></b></h3>
    <div style="margin: 20px; display: flex;" >
        
        <br>
        <div style="width: 100%;">
            <b>Nome: </b> 
            <span>{{paciente.nome}}</span>
        </div>
        <div style="width: 100%;">
            <b>Data de Nascimento: </b> 
            <span>{{paciente.dataNascimento | date}}</span>
        </div>
        <div style="width: 100%;">
            <b>Sexo: </b> 
            <span>{{getSexo(paciente.sexo)}}</span>
        </div>
        <div style="width: 100%;">
            
        </div>
    </div>
    <div style="margin: 20px; " *ngFor="let grupo of anamnese.grupos; index as indexGrupo">
        <h3  style=" background-color:darkgray"><b>{{grupo.descricao}}</b></h3>
        <div style="width: 100%;" *ngFor="let pergunta of grupo.perguntas; index as indexPergunta">
            <div style="width: 100%;">
                <b>{{pergunta.descricao}}</b> 
                <br>
                <span *ngIf="pergunta.campo.tipo != 'SELECAO'">{{pergunta.resposta}}</span>
                <span *ngIf="pergunta.campo.tipo == 'SELECAO'">{{retornaDescricaoPegunta(pergunta.campo.valores,pergunta.resposta)}}</span>
                
            </div>
         </div>
    </div>

</div>
