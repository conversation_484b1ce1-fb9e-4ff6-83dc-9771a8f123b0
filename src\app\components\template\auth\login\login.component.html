<mat-card style="width: fit-content; margin: auto;" class="mat-elevation-z4">
  <div style="text-align: center;">
    <img src="assets/img/Logo-Beni.png" style="max-height: 150px;">
    <!--img src="assets/img/{{ user.organizationId }}.png" style="max-height: 150px;"-->
  </div>
  <mat-card-title>
    <h1 style="text-align: center; padding-bottom: 20px;">Login</h1>
  </mat-card-title>
  
  <!--div class="container">
    <div class="row">
      <div class="col-md-6 col-md-offset-3"-->
  <div style="display: flex; flex-direction: row; width: 100%; text-align: center;">
        <form [formGroup]="loginForm" style="width: 100%; text-align: center;">
          <div>
            <mat-form-field style="width: 100%;">
              <mat-label>E-mail</mat-label>
              <input type="email" class="input" matInput placeholder="E-mail" 
                formControlName="email" name="email" email="true" required>
            </mat-form-field>
          </div>
          <div>
            <mat-form-field style="width: 100%;">
              <mat-label>Senha</mat-label>
              <input type="password" class="input" matInput placeholder="Senha" 
                formControlName="password" name="password" required>
            </mat-form-field>
          </div>
          <div>
            <label style="color: red; font-weight: bold;">{{errorMessage}}</label>
          </div>
          <button mat-flat-button type="submit" (ng-click)="tryLogin(loginForm.value)" (pan)="tryLogin(loginForm.value)" (click)="tryLogin(loginForm.value)" mat-flat-button color="primary" style="margin-top: 20px;">
            Login
          </button>
          <br><br>
          <a (click)="resetPassword(loginForm.value)" style="text-align: center; cursor: pointer;">Esqueci minha senha!</a>
        </form>
  </div>
</mat-card>
    <!--/div>
  </div-->
  <!--div class="row">
    <div class="col-md-4 col-md-offset-4">
      <button type="button" class="btn btn-primary btn-block" (click)="tryFacebookLogin()">Login with Facebook</button>
    </div>
  </div-->
  <!--div class="row">
    <div class="col-md-4 col-md-offset-4">
      <button type="button" class="btn btn-danger btn-block" (click)="tryGoogleLogin()">Login with Google</button>
    </div>
  </div-->
  <!--div class="row">
    <div class="col-md-4 col-md-offset-4">
      <button type="button" class="btn btn-info btn-block" (click)="tryTwitterLogin()">Login with Twitter</button>
    </div>
  </div>
  <div class="row">
    <div class="col-md-4 col-md-offset-4">
      <p>No account yet? <a href="/register">Create an account</a></p>
    </div>
  </div-->
<!--/div-->