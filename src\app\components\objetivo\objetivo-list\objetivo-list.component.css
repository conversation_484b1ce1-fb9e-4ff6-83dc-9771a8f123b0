.close {
    font-size: 1.4rem;
    opacity: 0.1;
    transition: opacity 0.3s;
}
.nav-link:hover > .close {
    opacity: 0.8;
}

.label-N {
    background-color: red;
    color: white;
    text-align: center;
    font-weight: 700;
    letter-spacing: 2px;
  }
  
  .label-P {
    background-color: orange;
    color: white;
    text-align: center;
    font-weight: 700;
    letter-spacing: 2px;
  }
  
  .label-A {
    background-color: darkgreen;
    color: white;
    text-align: center;
    font-weight: 700;
    letter-spacing: 2px;
  }
  
  .label-X {
    background-color: lightgray;
    color: white;
    text-align: center;
    font-weight: 700;
    letter-spacing: 2px;
}

table{
  width: 100%;
}
.mat-column-id {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: left;
}

.mat-column-nome {
  flex: 0 0 70% !important;
  width: 70% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-action {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: center;
}
