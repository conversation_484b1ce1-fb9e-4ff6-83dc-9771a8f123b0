<!-- HTML para a impressão do PDF -->
<div id="esdmchecklistAvaliacao" #esdmchecklistAvaliacao>
    <div *ngFor="let nivel of niveis" class="folha-horizontal-pdf" [ngStyle]="getStyleByNivelId(nivel.id)">
        <div class="cabecalho-pdf">
            <img class="cabecalho-img-pdf" src="assets/img/{{ idOrganizacao }}.png" width="100px">
            <div class="cabecalho-dados-pdf">
                Checklist&nbsp;de&nbsp;ESDM<br>
                {{ paciente.nome | sanitize }}<br>
                Data: {{esdmchecklist.data| date: 'dd/MM/yyyy'}}
            </div>
        </div>

        <div style="margin: 20px;">
            <h3 class="level-legend-container-pdf">
                <b>{{nivel.nome}}</b>
                <div class="legend-container-pdf">
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf adquirido-pdf"></div>
                        <p class="legend-text-pdf">Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf parcialmente-pdf"></div>
                        <p class="legend-text-pdf">Parcialmente Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-adquirido-pdf"></div>
                        <p class="legend-text-pdf">Não Adquirido</p>
                    </div>
                    <div class="legend-item-pdf">
                        <div class="color-box-pdf nao-observado-pdf"></div>
                        <p class="legend-text-pdf">Não Observado</p>
                    </div>
                </div>
            </h3>

            <div class="table-container-pdf">
                <table>
                    <tbody>
                        <tr>
                            <ng-container *ngFor="let dominio of dominioMap | filterdominio : nivel.id">
                                <td class="table-dados-pdf">
                                    <div *ngFor="let chkLst of getchecklistInfosPDF(nivel.id, dominio.id)">
                                        <p class="label label-N-pdf" *ngIf="chkLst.valor == 'N'">{{ chkLst.competencia.id.substring(1) }}</p>
                                        <p class="label label-P-pdf" *ngIf="chkLst.valor == 'P'">{{ chkLst.competencia.id.substring(1) }}</p>
                                        <p class="label label-A-pdf" *ngIf="chkLst.valor == 'A'">{{ chkLst.competencia.id.substring(1) }}</p>
                                        <p class="label label-X-pdf" *ngIf="chkLst.valor == 'X' || chkLst.valor == undefined" disabled>{{ chkLst.competencia.id.substring(1) }}</p>
                                        <!-- Formatação PDF -->
                                        <div *ngIf="chkLst.competencia.id.substring(1) == 'CEX19' && nivel.id == 'N4'">
                                            <br><br>
                                        </div>
                                    </div>
                                </td>
                            </ng-container>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Glossário -->
    <div class="glossario-pdf">
        <h3>Glossário</h3>
        <div *ngFor="let nivel of niveis" style="margin-top: 10px;" [ngStyle]="getStyleByNivelIdForGlossario(nivel.id)">
            <h5>{{ nivel.nome }}</h5>
            <div *ngFor="let dominio of dominioMap | filterdominio : nivel.id">
                <div class="glossario-dominio-pdf">
                    <p class="glossario-dominio-text-pdf">{{ dominio.id }} - {{ dominio.nome | sanitize }}</p>
                </div>
                <div class="glossario-competencia-pdf">
                    <div style="width: 48%;">
                        <ng-container *ngFor="let chkLst of getchecklistInfosPDF(nivel.id, dominio.id); let i = index">
                            <div *ngIf="i < (getchecklistInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleByNivelIdAndCompetencia(nivel.id, chkLst.competencia.id.substring(1))">
                                
                                <p class="glossario-competencia-text-pdf">{{ chkLst.competencia.id.substring(1) }} - {{ chkLst.competencia.nome | sanitize }}</p>

                            </div>
                        </ng-container>
                    </div>
                    <div style="width: 48%;">
                        <ng-container *ngFor="let chkLst of getchecklistInfosPDF(nivel.id, dominio.id); let i = index">
                            <div *ngIf="i >= (getchecklistInfosPDF(nivel.id, dominio.id).length / 2)" style="margin-bottom: 1px;" [ngStyle]="getStyleByNivelIdAndCompetencia(nivel.id, chkLst.competencia.id.substring(1))">
                                
                                <p class="glossario-competencia-text-pdf">{{ chkLst.competencia.id.substring(1) }} - {{ chkLst.competencia.nome | sanitize }}</p>

                            </div>
                        </ng-container>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>