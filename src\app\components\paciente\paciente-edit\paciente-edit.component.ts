import { ParenteService } from './../../parente/parente.service';
import { AuthService } from './../../template/auth/auth.service';
import { map, startWith } from 'rxjs/operators';
import { ParentescoService } from './../../parente/parentesco.service';
import { Parentesco } from './../../parente/parentesco-model';
import { Parente } from './../../parente/parente-model';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Router, ActivatedRoute } from '@angular/router';
import { PacienteService } from './../paciente.service';
import { Funcao } from './../../funcao/funcao-model';
import { Paciente } from './../paciente-model';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Observable } from 'rxjs';
import { EnderecoService } from 'src/app/shared/service/endereco-service';
import { Endereco } from 'src/app/shared/model/endereco-model';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-paciente-edit',
  templateUrl: './paciente-edit.component.html',
  styleUrls: ['./paciente-edit.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class PacienteEditComponent implements OnInit {

  paciente: Paciente = new Paciente();
  pacientes: Paciente[] = [];
  cpfMask = [/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'.',/\d/,/\d/,/\d/,'-',/\d/,/\d/];
  telefoneMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  celularMask = ['(',/\d/,/\d/,')',' ',/\d/,/\d/,/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/,/\d/];
  cepMask = [/\d/,/\d/,'.',/\d/,/\d/,/\d/,'-',/\d/,/\d/,/\d/];
  tipoTelefone = 'C';

  profissionais: Profissional[] = [];
  profissional: Profissional = new Profissional();
  profissionalAdd: Profissional;
  
  parentes: Parente[] = [];
  parente: Parente = new Parente();
  //parenteAdd: Parente;
  //parentescos: Parentesco[] = []

  funcaoProfissional: Funcao = new Funcao();
  parentescoParente: string;

  displayedColumnsEquipe = ['nome', 'funcao', 'action'];
  displayedColumnsParentes = ['nome', 'parentesco', 'action'];

  //Form Controls
  nome = new FormControl('', [Validators.required]);
  dataNascimento = new FormControl('', [Validators.required]);
  sexo = new FormControl('', [Validators.required]);
  tiposIntervencao = new FormControl('', [Validators.required]);
  parenteFC = new FormControl();

  filteredParents: Observable<Parente[]>;

  public hasAccessUpdate: boolean;
  public hasAccessEquipe: boolean;
  public hasAccessFamilia: boolean;

  @ViewChild(NgForm) form;
  inicialName: string;


  getErrorMessage() {
    if (this.nome.hasError('required')) {
      return 'Nome é obrigatório.';
    }

    if(this.dataNascimento.hasError('required')){
      return 'Data de nascimento é obrigatória.';
    }

    if(this.sexo.hasError('required')){
      return 'Sexo é obrigatória.';
    }
  }

  constructor(private pacienteService: PacienteService,
    private parenteService: ParenteService,
    private profissionalService: ProfissionalService,
    public authService: AuthService,
    private parentescoService: ParentescoService,
    private router: Router,
    private route: ActivatedRoute,
    private enderecoService: EnderecoService,
    public dialog: MatDialog,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
  
    try {
      const id = this.route.snapshot.paramMap.get('id');
      
      // Use Promise.all para aguardar todas as promessas
      const [paciente, profissionais, pacientes, parentes] = await Promise.all([
        this.pacienteService.findById(id).toPromise(),
        this.profissionalService.find().toPromise(),
        this.pacienteService.find().toPromise(),
        this.parenteService.find().toPromise()
      ]);

      // Verifico se o usuário tem os acessos necessários
      this.hasAccessEquipe = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Estabelecer equipe','delete');
      this.hasAccessFamilia = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de parentes (família)','delete');
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de pacientes','update');
  
      // Atribuir os valores carregados
      this.paciente = paciente;
      this.inicialName = this.paciente.nome;
      if (!this.paciente.endereco) {
        this.paciente.endereco = new Endereco();
      }
      // console.log(this.paciente.roles);
  
      this.profissionais = profissionais;
      this.pacientes = pacientes;
      this.parentes = parentes.filter(parente => parente.ativo);
  
      // Configurar o observable para o filtro de parentes
      this.filteredParents = this.parenteFC.valueChanges.pipe(
        startWith(''),
        map(value => (typeof value === 'string' ? value : value.nome)),
        map(nome => (nome ? this._filter(nome) : this.parentes.slice()))
      );
  
    } catch (error) {
      console.error("Erro: ", error);
    } finally {
      this.loadingService.hide();
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

   // console.log(funcoes)


    return funcoes;
  }

  addProfissional(){
    //Verifico se algum profissional foi selecionado
    if(this.profissional.id != undefined){
      //Verifico se algum profissional foi selecionado
      if(this.funcaoProfissional.id != undefined){
        //console.log('Adicionando profissional a equipe')
        
        //Se a equipe estiver vazia, inicializo a equipe
        if(this.paciente.equipe == undefined){
          this.paciente.equipe = [];
        }
        
        //Adiciono o profissional no array de equipe, mas antes verifico se ele já não foi incluído antes
        if(this.paciente.equipe.length == 0 || (this.profissional.id != undefined && this.paciente.equipe.filter(p => p.id == this.profissional.id).length == 0)){
          //Adiciono a função que o profissional vai exercer na equipe
          //console.log(this.funcaoProfissional)
          this.profissionalAdd = this.profissional;
          this.profissionalAdd.funcao = [this.funcaoProfissional];
          //console.log(this.profissionalAdd);

          //console.log(this.paciente.roles);
          //Inicalizo as roles, caso esteja nulo ainda
          //console.log(this.paciente.roles)
          if(this.paciente.roles == null){
            this.paciente.roles = new Map<string, string>();
          }
          //Adiciono profissional a roles (leitura)
          //console.log(this.paciente.roles)
          if(this.profissional.uid != undefined){ //Se tem usuário de login
            if(this.paciente.roles[this.profissional.uid] == null){ //Se o usuário não está nas roles
              this.paciente.roles[this.profissional.uid] = "read";
            }
          }
          //console.log(this.paciente.roles)
      
          //Adiciono o profissional no array de equipe
          this.paciente.equipe.push(this.profissionalAdd);
          this.paciente.equipe = [...this.paciente.equipe];
          this.profissional = new Profissional();
        } else {
          this.pacienteService.showMessage("Profissional já incluído anteriormente!", true);
          this.profissional = new Profissional();
        }
      } else {
        this.pacienteService.showMessage("Favor selecionar a função que o profissional irá exercer na equipe do paciente!", true);
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o profissional a ser adicionado na equipe do paciente!", true);
    }

  }

  deleteProfissional(id: number){
    var profissional: Profissional;

    //Pego o profissional a ser excluído da equipe
    profissional = this.paciente.equipe[id];

    //console.log(this.paciente.roles)
    var roles = new Map(Object.entries(this.paciente.roles));

    //Retiro o acesso do profissional, exceto caso ele seja o "owner"
    if(roles.get(profissional.uid) != "owner"){
      roles.delete(profissional.uid);
    }

    this.paciente.roles = new Map<string, string>();
    Array.from(roles.keys()).forEach(key => {
      this.paciente.roles[key] = roles.get(key);
    })
        
    //console.log(this.paciente.roles)

    this.paciente.equipe.splice(id, 1);
    this.paciente.equipe = [...this.paciente.equipe];
    //console.log(this.paciente.roles)
  }

  displayFn(parente: Parente): string {
    return parente && parente.nome ? parente.nome + " (" + parente.parentesco + ")" : '';
  }

  private _filter(value: string): Parente[] {
    const filterValue = value.toLowerCase();

    return this.parentes.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
    
  }

  addParente(){
    //console.log('Adicionando parente a família')
    //Verifico se algum parente foi selecionado
    if(this.parente.id != undefined){
      //Se parentes estiver vazia, inicializo parentes
      if(this.paciente.parentes == undefined){
        this.paciente.parentes = [];
      }
      
      //console.log(this.parente);
  
      //Adiciono o parente no array de família, mas antes verifico se ele já não foi incluído antes
      if(this.paciente.parentes.length == 0 || (this.parente.id != undefined && this.paciente.parentes.filter(p => p.id == this.parente.id).length == 0)){
        //Inicalizo as roles, caso esteja nulo ainda
        //console.log(this.paciente.roles)
        if(this.paciente.roles == null){
          this.paciente.roles = new Map<string, string>();
        }

        //Adiciono parente a roles (leitura)
        if(this.parente.uid != undefined){ //Se tem usuário de login
          //console.log(this.parente.uid)
          if(this.paciente.roles[this.parente.uid] == null){ //Se o usuário não está nas roles
            //console.log(this.parente.uid)
            this.paciente.roles[this.parente.uid] = "read";
          }
        }

        this.paciente.parentes.push(this.parente);
        this.paciente.parentes = [...this.paciente.parentes];
        this.parente = new Parente();
        //console.log(this.paciente.roles)
      } else {
        this.pacienteService.showMessage("Parente já incluído anteriormente!", true);
        this.parente = new Parente();
        //this.parenteFC.setValue(undefined);
      }
    } else {
      this.pacienteService.showMessage("Favor selecionar o parente a ser associado ao paciente!", true);
    }
    
  }

  deleteParente(id: number){
    var parente: Parente;

    //Pego o parente a ser excluído do paciente
    parente = this.paciente.parentes[id];

    //console.log(this.paciente.roles)
    var roles = new Map(Object.entries(this.paciente.roles));

    roles.delete(parente.uid);

    this.paciente.roles = new Map<string, string>();
    Array.from(roles.keys()).forEach(key => {
      this.paciente.roles[key] = roles.get(key);
    })
        
    //console.log(this.paciente.roles)

    this.paciente.parentes.splice(id, 1);
    this.paciente.parentes = [...this.paciente.parentes];
  }

  /*setFuncaoFamila(){
    this.funcaoParente = this.parente.funcao[0]
  }*/

  changeStatus(e: any){
    if(e.checked){
      this.paciente.ativo = true;
    }else{
      this.paciente.ativo = false;
    }
  }

  save(): void {
    // Verifica se existe outro paciente com o mesmo nome, exceto o paciente que está sendo editado
    let duplicados = this.pacientes.filter(p => p.nome.trim() == this.paciente.nome.trim() && p.ativo == true);
    
    if (this.form.valid) {
      if (this.paciente.id == undefined) {
        // Criação de novo paciente
        this.pacienteService.create(this.paciente).subscribe((id) => {
          this.pacienteService.showMessage('Paciente criado com sucesso!');
          this.router.navigate(['/paciente/' + id]);
        });
      } else {
        // Edição de paciente
        // Verifica se o nome foi alterado e se o novo nome é duplicado
        if (this.inicialName != this.paciente.nome && duplicados.length > 0) {
          this.havePacienteNameUpdate();
        } else {
          this.pacienteService.update(this.paciente).subscribe((paciente) => {
            this.pacienteService.showMessage('Paciente alterado com sucesso!');
            this.router.navigate(['/paciente/' + paciente.id]);
          });
        }
      }
    } else {
      this.pacienteService.showMessage('Existem campos inválidos no formulário!', true);
    }
  }  
  
  havePacienteNameUpdate(): void {
    let dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        valida: true,
        msg: 'Já existe um paciente cadastrado com o nome "<strong>' + this.paciente.nome + '</strong>", tem certeza que deseja continuar com a alteração?'
      }
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.pacienteService.update(this.paciente).subscribe((paciente) => {
          this.pacienteService.showMessage('Paciente alterado com sucesso!');
          this.router.navigate(['/paciente/' + paciente.id]);
        });
      }
    });
  }  

  consultaCep(){
    this.enderecoService.find(this.paciente.endereco.cep.replace('.','').replace('-','')).subscribe(data => {
      this.paciente.endereco.logradouro = data.logradouro;
      this.paciente.endereco.complemento = data.complemento;
      this.paciente.endereco.bairro = data.bairro;
      this.paciente.endereco.cidade = data.localidade;
      this.paciente.endereco.uf = data.uf;
      this.paciente.endereco.pais = 'Brasil';
    }, () => {
      
    })
  }

  cancel(): void{
    this.router.navigate(['/paciente/' + this.paciente.id]);
  }

  do() {

  }

}
