<div class="mat-elevation-z4"> 
    <table mat-table [dataSource]="listas"> 
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.nome}}</td>
        </ng-container>

        <!-- dataNascimento Column -->
        <ng-container matColumnDef="dataNascimento" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Data de Nascimento</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.dataNascimento | date: 'dd/MM/yyyy'}}</td>
        </ng-container>

        <!-- local Column -->
        <ng-container matColumnDef="local" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Local</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.local}}</td>
        </ng-container>

        <!-- nomeResponsavel Column -->
        <ng-container matColumnDef="nomeResponsavel" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome do Responsável</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nomeResponsavel}}</td>
        </ng-container>

        <!-- terapeuta Column -->
        <ng-container matColumnDef="terapeuta" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Terapeuta</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.terapeuta}}</td>
        </ng-container>

        <!-- status Column -->
        <ng-container matColumnDef="status" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.status}}</td>
        </ng-container>

      'nome', 'dataNascimento', '', '', '', ''
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="navigateToUpdate(row.id)" class="edit">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row.id)" class="delete">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  