<div class="row">
    <form #ngForm="ngForm" style="width: 100%; padding-top: 20px;">
        <mat-form-field style="width: 45%; padding-right: 30px;" appearance="outline">
            <input class="input" matInput placeholder="Título" 
                [(ngModel)]="nota.titulo" name="titulo" #titulo="ngModel" required>
            <mat-error *ngIf="titulo.invalid && titulo.touched">Título é obrigatório.</mat-error>  
        </mat-form-field>
        
        <mat-form-field style="width: 15%; padding-right: 30px;" appearance="outline"> 
            <input class="input" matInput placeholder="Data" 
                [(ngModel)]="nota.data" name="data" #data="ngModel"
                [matDatepicker]="picker" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-error *ngIf="data.invalid && data.touched">Data é obrigatória.</mat-error>  
        </mat-form-field>
    
        <mat-form-field style="width: 15%;" appearance="outline" *ngIf="isTeamMember()">
            <mat-label>Nível de acesso</mat-label>
            <mat-select class="select" placeholder="Nível de acesso"
                        [(ngModel)]="nota.nivelAcesso" name="nivelAcesso" #nivelAcesso="ngModel" required>
                <mat-option value="0">Público</mat-option>
                <mat-option value="1">Apenas equipe</mat-option>
            </mat-select>
            <mat-error *ngIf="nivelAcesso.invalid && nivelAcesso.touched">Nível de acesso é obrigatório.</mat-error>
        </mat-form-field>

        <mat-icon class="save"
            (click)="save()" 
            *ngIf="hasAccessCreate">
            note_add
        </mat-icon>

        <mat-form-field style="width: 100%;" appearance="outline">
            <textarea class="input" matInput placeholder="Comentário" 
                [(ngModel)]="nota.descricao" name="descricao" #descricao="ngModel"
                rows="4" style="white-space:pre-line" required>
            </textarea>
            <mat-error *ngIf="descricao.invalid && descricao.touched">Comentário é obrigatório.</mat-error>  
        </mat-form-field>
        
    </form>
</div>

<!-- LISTA AS NOTAS EXISTENTES -->
<ng-container *ngIf="hasAccessRead">
    <ng-container *ngFor="let nota of notas">
        <div *ngIf="(nota.nivelAcesso == undefined || nota.nivelAcesso == 0) || (nota.nivelAcesso == 1 && isTeamMember())">
            <mat-divider></mat-divider>
            <div class="row">
                <div class="action">
                    <!--mat-icon class="iconaction" (click)="edit()">edit</mat-icon-->
                    <mat-icon class="iconaction" (click)="changeNivelAcesso(nota)"
                        *ngIf="isTeamMember()">
                        {{ (nota.nivelAcesso == undefined || nota.nivelAcesso == 0) ? 'lock_open' : 'lock' }}
                    </mat-icon>

                    <mat-icon class="edit" *ngIf="canEdit(nota)" (click)="edit(nota)">
                        edit
                    </mat-icon>

                    <mat-icon class="deleteRed" (click)="delete(nota)"
                        *ngIf="hasAccessDelete || nota.profissional.uid == authService.getUser().uid">
                        delete
                    </mat-icon>
                </div>
                <div class="title"> 
                    {{ nota.data | date: 'dd/MM/yyyy' }} - {{ nota.titulo }}
                </div>
                <div class="author">
                    Autor: {{ nota.profissional.nome }}<br>
                    <div *ngIf="isTeamMember()">
                        Nível de Acesso: {{ (nota.nivelAcesso == undefined || nota.nivelAcesso == 0) ? 'Público' : 'Apenas equipe' }}
                    </div>
                </div>
                <div class="description" style="white-space:pre-line">
                    {{ nota.descricao }}
                </div>
            </div>
        </div>
    </ng-container>
</ng-container>
