import { VBMAPPMilestonesAssessmentItem } from './vbmapp-milestones-item-model';
import { Profissional } from './../profissional/profissional-model';
import { Paciente } from './../paciente/paciente-model';
import { Avaliacao } from '../avaliacao/avaliacao-model';
export class VBMAPPMilestonesAssessment {
    id?: string;
    idPaciente: string;
    paciente: Paciente;
    idProfissional:string;
    profissional: Profissional;
    data: Date;
    assessment: VBMAPPMilestonesAssessmentItem[]; 
    pontos: number;
    status: boolean;

    constructor(){
        this.paciente = new Paciente();
        this.profissional = new Profissional();
        this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
        this.assessment = [];
        this.status = true;
    }

    asAvaliacao(av: Avaliacao): Avaliacao{
        this.id = av.id;
        this.data = av.data;
        // this.idTipoAvaliacao = av.idTipoAvaliacao;
        this.idPaciente = av.idPaciente;
        this.idProfissional = av.idProfissional;
        this.assessment = []; 
        this.status = av.ativo;
        return undefined
    }
}