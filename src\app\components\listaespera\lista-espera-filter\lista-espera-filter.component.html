<mat-toolbar class="header mat-elevation-z4">
    <div class="v-middle" style="width: 15%;">
        Filtros  
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-around stretch" fxLayoutGap="10">
        <!--mat-form-field style="width: 40%;">
            <input class="input" matInput placeholder="Nome" 
                [(ngModel)]="listaEspera.nome" name="nome">
        </mat-form-field>
            
        <mat-form-field style="width: 30%;">
            <input class="input" matInput placeholder="Local" 
                [(ngModel)]="listaEspera.local" name="local">
        </mat-form-field-->
    
        <mat-form-field  style="width: 25%;">
            <mat-label>Preferência de Turno</mat-label>
            <mat-select placeholder="Turno" 
                [(ngModel)]="preferenciaTurno"
                name="turno" (selectionChange) = "setFilter()">
                <mat-option value=""></mat-option>
                <mat-option value="Manhã" >
                Manhã
                </mat-option>
                <mat-option value="Tarde" >
                    Tarde
                </mat-option>
                <mat-option value="Noite" >
                    Noite
                </mat-option>
            </mat-select>
        </mat-form-field>
    
        <mat-form-field  style="width: 30%;">
            <mat-label>Status</mat-label>
            <mat-select placeholder="status" 
                [(ngModel)]="status"
                name="status" (selectionChange) = "setFilter()">
                <mat-option value=""></mat-option>
                <mat-option value="Novo" >
                Novo
                </mat-option>
                <mat-option value="Aguardando avaliação" >
                    Aguardando avaliação
                </mat-option>
                <mat-option value="Em avaliação" >
                    Em avaliação
                </mat-option>
                <mat-option value="Aguardando atendimento" >
                    Aguardando atendimento
                </mat-option>
                <mat-option value="Em atendimento" >
                    Em atendimento
                </mat-option>
                <mat-option value="Desistiu" >
                    Desistiu
                </mat-option>
            </mat-select>
        </mat-form-field>
        
        <mat-form-field style="width: 30%;">
            <mat-label>Terapeuta</mat-label>
            <mat-select placeholder="terapeuta" 
                [(ngModel)]="terapeuta"
                name="terapeuta" (selectionChange) = "setFilter()">
                <mat-option value=""></mat-option>
                <mat-option *ngFor="let terapeuta of terapeutas" [value]="terapeuta.nome" >
                {{terapeuta.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="v-middle" style="font-size: small; cursor: pointer;">
        <a (click) = "cleanFilter()">Limpar filtros</a>
    </div>
</mat-toolbar>