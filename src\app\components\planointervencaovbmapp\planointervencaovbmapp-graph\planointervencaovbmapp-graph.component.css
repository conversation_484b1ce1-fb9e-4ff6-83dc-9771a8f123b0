div.subtitulo{
    display: flex; 
    flex-direction: row;
    width: 90%; 
    padding: 20px 0px 20px 0px;
}

.div-table{
    width: 100%; 
    display: table;
    break-inside:auto;
    border-collapse: collapse;
    border: 1px solid;
}

.div-table-row{
    text-align: center; 
    width: 100% !important; 
    display: table-row;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
    break-inside:avoid; 
    break-after:auto;
}

.div-data-cell{
    display: table-cell;
    /*width: 40% !important;*/
    padding: 0px !important;
    border: 1px solid;
    text-align: center;
    font-family: Helvetica;
    font-size: 8pt;
    font-weight: normal;
}

.div-header-cell{
    display: table-cell;
    /*width: 40% !important;*/
    padding: 0px !important;
    border: 1px solid;
    text-align: center;
    font-family: Helvetica;
    font-size: 10pt;
    font-weight: bold;
}

div.subtitulo{
    display: flex; 
    flex-direction: row;
    width: 90%; 
    padding: 20px 0px 20px 0px;
}

/* No arquivo de estilo (planopic-graph.component.css) */
canvas {
    box-shadow: 1px 1px 2px 4px rgba(0.1, 0.1, 0.1, 0.1); /* Adiciona uma sombra ao gráfico */
    border-radius: 8px; /* Arredonda os cantos do gráfico */
    background-color: #fff; /* Cor de fundo branco para os gráficos */
}

.mat-progress-spinner {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.no-data-message {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #ccc;
    background-color: #fcfcfc;
    color: #444;
    font-size: 16px;
    text-align: center;
    border-radius: 8px;
    margin: 10px 0;
} 

.report-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background: #fafafa;
}
  
.report-header {
    display: flex;
    align-items: center;
    gap: 16px;
  
    .logo img {
      max-width: 150px;
      max-height: 78px;
    }
    .report-title {
      flex: 1;
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      text-align: center;
    }
}
  
.report-subheader {
    display: flex;
    justify-content: space-between;
    .subtitle {
      font-size: 0.9rem;
      color: #555;
      margin: 0;
    }
}
  
.tabs {
    width: 100%;
}
  
.tab-content {
    padding: 8px;
}
  
.chart-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: space-between;
}
  
.chart-pair {
    display: flex;
    gap: 16px;
    width: 100%;
}
  
.chart-card {
    flex: 1 1 48%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    padding: 12px;
}
  
.full-width-chart {
    flex: 1 1 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    padding: 12px;
}
  
.esdm-title {
    width: 100%;
    text-align: center;
    margin: 24px 0 8px;
    font-size: 1.25rem;
    color: #444;
}
  
.chart-legend-grid {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}
  
.half {
    flex: 1 1 48%;
}
  
.legend-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    padding: 12px;
  
    .legend-title {
      font-weight: 600;
      margin-bottom: 8px;
    }
}
  
/* Tabela interna */
.div-table {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

.div-table-row {
    display: table-row;
}

.div-header-cell, .div-data-cell, .div-indice-cell, .div-indice-data-cell {
    display: table-cell;
    padding: 4px 8px;
    border-bottom: 1px solid #ddd;
}

.div-header-cell {
    font-weight: 600;
    background: #f0f0f0;
}  