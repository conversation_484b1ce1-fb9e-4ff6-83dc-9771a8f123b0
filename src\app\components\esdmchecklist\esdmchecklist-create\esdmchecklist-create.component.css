.logo{
    max-height: 33px;
}

table{
    width: 100%;
}

.mat-column-id {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
}

.mat-column-nome {
    flex: 0 0 65% !important;
    width: 65% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-prevchklst {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: center;
}

.mat-column-N {
    flex: 0 0 3% !important;
    width: 3% !important;
    text-align: center;
}

.mat-column-P {
    flex: 0 0 3% !important;
    width: 3% !important;
    text-align: center;
}

.mat-column-A {
    flex: 0 0 3% !important;
    width: 3% !important;
    text-align: center;
}

.mat-column-X {
    flex: 0 0 3% !important;
    width: 3% !important;
    text-align: center;
}

::ng-deep .custom-select-primary {
    background-color: #3f51b5;
    color: white;
    height: 37px; 
    border-radius: 4px;
    padding: 0 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
}
  
::ng-deep .custom-select-primary .mat-form-field-wrapper {
    padding-left: 10px;
}

::ng-deep .custom-select-primary .mat-select-arrow {
    color: white !important;
}

::ng-deep .custom-select-primary .mat-form-field-underline {
    display: none;
}

::ng-deep .custom-select-primary .mat-select-value-text {
    color: white;
}

::ng-deep .custom-select-primary .mat-option-text {
    color: black;
}

::ng-deep .custom-select-primary .mat-form-field-label {
    color: white !important;
    padding-bottom: 10px;
}

.mat-form-field .mat-form-field-label {
    flex: 1;
    text-align: center;
}

.all-label {
    display: flex;
    align-items: center; 
}
