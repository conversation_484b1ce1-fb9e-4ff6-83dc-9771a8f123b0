<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row wrap" style="display: flex;"> 
            <div style="display: flex; width: 80%;" fxLayout="row wrap">
                <mat-form-field  style="width: 20%; padding: 20px;" *ngIf="pic.id != undefined">
                    <mat-label>Plano de Intervenção Comportamental</mat-label>
                    <mat-select placeholder="PIC" 
                        [(ngModel)]="pic"
                        name="pic"
                        (selectionChange) = "findPICSelecionado(pic.id)">
                        <mat-option [value]="pic" *ngFor="let pic of PICs">
                            {{ pic?.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Status</mat-label>
                    <mat-select placeholder="Status" 
                        [(ngModel)]="pic.ativo"
                        name="ativo" disabled>
                        <mat-option [value]="false">
                            Ras<PERSON>nho 
                        </mat-option>
                        <mat-option [value]="true">
                            PIC liberado para coleta 
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div style="display: flex; width: 30%;" fxLayout="row wrap">
                <ng-container *ngIf="pic.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="saveToPDF()"
                        matTooltip="Imprimir"
                        *ngIf="hasAccessRead">
                        <mat-icon>print</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="pic.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="graph()"
                        matTooltip="Gráfico de Evolução"
                        *ngIf="hasAccessRead">
                        <mat-icon>leaderboard</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="pic.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="edit()"
                        matTooltip="Editar"
                        *ngIf="hasAccessUpdate">
                        <mat-icon>edit</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="pic.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="edit()"
                        matTooltip="Visualizar"
                        *ngIf="hasAccessRead &&
                               !hasAccessUpdate">
                        <mat-icon>remove_red_eye</mat-icon>
                    </button>
                </ng-container>
                <button mat-mini-fab color="primary" style="margin: 10px" (click)="add()"
                    matTooltip="Incluir Novo"
                    *ngIf="hasAccessCreate">
                    <mat-icon>add</mat-icon>
                </button>
                <ng-container *ngIf="pic.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="delete(pic.id)"
                        matTooltip="Excluir"
                        *ngIf="hasAccessDelete">
                        <mat-icon>delete</mat-icon>
                    </button>
                </ng-container>
            </div>
        </div>
        <div class="mat-elevation-z0" style="padding: 10px 0 30px 0;" 
            *ngIf="hasAccessRead && pic.data != undefined">
            <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows *ngIf="pic?.objetivos || pic.objetivos?.length > 0"> 
            
                <ng-container matColumnDef="index">
                    <th mat-header-cell *matHeaderCellDef >#</th>
                    <td mat-cell *matCellDef="let row">{{ pic.objetivos.indexOf(row) + 1 }}</td>
                </ng-container>

                <!-- Comportamento Alvo Column --> 
                <ng-container matColumnDef="nomeCompAlvo">
                    <th mat-header-cell *matHeaderCellDef>Comportamento Alvo</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.comportamentoAlvo}}</td>
                </ng-container>

                <!-- Tipo de Coleta Column --> 
                <ng-container matColumnDef="defOp">
                    <th mat-header-cell *matHeaderCellDef>Definição Operacional</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.definicaoOperacional}}</td>
                </ng-container>

                <!-- Tipo de Coleta Column --> 
                <ng-container matColumnDef="tipoColeta">
                    <th mat-header-cell *matHeaderCellDef>Tipo de Coleta</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.tipoColeta}}</td>
                </ng-container>
          
              <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
            </table>
            <div *ngIf="!pic?.objetivos || pic.objetivos?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                <h1>Nenhum objetivo cadastrado no PIC!</h1>
            </div>
        </div> 
        <div *ngIf="PICs?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
            <h1>Nenhum PIC cadastrado!</h1>
        </div>
    </mat-card-content>
</mat-card>