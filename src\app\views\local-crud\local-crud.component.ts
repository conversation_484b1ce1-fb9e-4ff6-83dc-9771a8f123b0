import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/components/template/auth/auth.service';
import { HeaderService } from 'src/app/components/template/header/header.service';

@Component({
  selector: 'app-local-crud',
  templateUrl: './local-crud.component.html',
  styleUrls: ['./local-crud.component.css']
})
export class LocalCrudComponent implements OnInit {

  constructor(private router: Router,
    public authService: AuthService,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Local',
        icon: 'source',
        routeUrl: '/local-atendimento'
      }
    }

  ngOnInit(): void {
  }

}
