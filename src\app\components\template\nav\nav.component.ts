import { NavService } from './nav.service';
import { HeaderService } from './../header/header.service';
import { AuthService } from './../auth/auth.service';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html',
  styleUrls: ['./nav.component.css']
})
export class NavComponent implements OnInit {

  hasFullView=false;

  navHidden = false;
  
  constructor(private activatedRoute: ActivatedRoute,
    public navService: NavService,
    private headerService: HeaderService,
    public authService: AuthService) {}

  ngOnInit() {
    this.setHasFullView();
  }

  private setHasFullView() {
    this.activatedRoute.queryParams.subscribe(params => {
      this.hasFullView = params["hasFullView"] || false;
      this.navService.navData = {
        hidden: this.hasFullView
      }
    });
  }

}
