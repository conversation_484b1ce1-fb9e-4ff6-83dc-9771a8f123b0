import { environment } from './../../../environments/environment';
import { ListaEspera } from './listaespera-model';
import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { Observable, EMPTY, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ListaesperaService {

  listaEsperaUrl = `${environment.API_URL}/listaespera`;
  
  public listas: BehaviorSubject<ListaEspera[]> = 
    new BehaviorSubject<ListaEspera[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(listaEspera: ListaEspera): Observable<ListaEspera>{
    //console.log(listaEspera);
    return this.http.post<ListaEspera>(this.listaEsperaUrl, listaEspera).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<ListaEspera>{
    return this.http.get<ListaEspera>(this.listaEsperaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<ListaEspera[]>{
    return this.http.get<ListaEspera[]>(this.listaEsperaUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<ListaEspera>{
    return this.http.delete<ListaEspera>(this.listaEsperaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
