<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        
        <div class="mat-elevation-z0"
            *ngIf="hasAccessRead">
            <table mat-table [dataSource]="objetivos">         
                <!-- Nome Column -->
                <ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a (click)="edit(row.id)" class="edit tdlinked"
                            *ngIf="hasAccessUpdate">
                            {{row.nome}}
                        </a>
                        <a class="edit"
                            *ngIf="!hasAccessUpdate">
                            {{row.nome}}
                        </a>
                    </td>
                </ng-container> 

                <!-- Habilidades Column -->
                <ng-container matColumnDef="habilidades" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Habilidades</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a (click)="edit(row.id)" class="edit tdlinked"
                            *ngIf="hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let habilidade of row.habilidades" [value]="habilidade">
                                    {{ getNameHabilidade(habilidade) }}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                        <a class="edit"
                            *ngIf="!hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let habilidade of row.habilidades" [value]="habilidade">
                                    {{ habilidade.sigla == undefined || habilidade.sigla == "" ? dominio.nome + " - " + habilidade.ordem : habilidade.sigla }}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                    </td>
                </ng-container> 

                <!-- Estimulos Column -->
                <ng-container matColumnDef="estimulos" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Estímulos</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a (click)="edit(row.id)" class="edit tdlinked"
                            *ngIf="hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let estimulo of row.estimulos" [value]="estimulo">
                                    {{estimulo.nome}}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                        <a class="edit"
                            *ngIf="!hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let estimulo of row.estimulos" [value]="estimulo">
                                    {{estimulo.nome}}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                    </td>
                </ng-container> 
                
                <!-- TipoSuporte Column -->
                <ng-container matColumnDef="tiposuporte" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Tipos de Suporte</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a (click)="edit(row.id)" class="edit tdlinked"
                            *ngIf="hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let tipoSuporte of row.tiposSuporte" [value]="tipoSuporte">
                                    {{tipoSuporte.sigla}}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                        <a class="edit"
                            *ngIf="!hasAccessUpdate">
                            <mat-chip-list role="list" style="padding: 5px;">
                                <mat-chip role="listitem" *ngFor="let tipoSuporte of row.tiposSuporte" [value]="tipoSuporte">
                                    {{tipoSuporte.sigla}}
                                </mat-chip>
                            </mat-chip-list>
                        </a>
                    </td>
                </ng-container> 

                <!-- Action Column -->
                <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef>Ações</th>
                    <td mat-cell *matCellDef="let row">
                        <a (click)="edit(row.id)" class="edit"
                            *ngIf="hasAccessUpdate">
                            <i class="material-icons">
                                edit
                            </i>
                        </a>
                        <a (click)="delete(row.id)" class="delete"
                            *ngIf="hasAccessDelete">
                            <i class="material-icons">
                                delete
                            </i>
                        </a>
                    </td>
                </ng-container>
          
                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>        
    </mat-card-content>
</mat-card>