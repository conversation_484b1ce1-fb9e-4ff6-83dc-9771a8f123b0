import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../service/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  constructor(private loadingService: LoadingService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // incrementa contador e mostra spinner
    this.loadingService.show();

    return next.handle(req).pipe(
      // quando a requisição completa (sucesso ou erro), decrementa e esconde se for zero
      finalize(() => this.loadingService.hide())
    );
  }
}
