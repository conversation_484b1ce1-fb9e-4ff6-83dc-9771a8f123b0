import { Paciente } from './../paciente/paciente-model';
import { CalendarEvent/*, EventAction, EventColor*/ } from 'angular-calendar';
import { Profissional } from './../profissional/profissional-model';
import { TipoProcedimento } from './../tipoprocedimento/tipoprocedimento-model';
export interface EventoAtendimento extends CalendarEvent{
    
    /*id?: string | number;
    start: Date;
    end?: Date;
    title: string;
    color?: EventColor;
    actions?: EventAction[];
    allDay?: boolean;
    cssClass?: string;*/
    status: string; //Desmarcado pelo paciente, Não compare<PERSON>u, Atendido, Marcado confirmado, Marcado não confirmado
    procedimento: TipoProcedimento; 
    idProcedimento: string;
    profissionais: Profissional[];
    idProfissionais: string[];
    paciente: Paciente;
    idPaciente: string;
}