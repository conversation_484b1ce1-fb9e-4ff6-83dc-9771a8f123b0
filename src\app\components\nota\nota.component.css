.row {
    display: flex;
    flex-direction: row; 
    width: 100%;
    flex-wrap: wrap;
}

.iconaction{
    cursor: pointer;
    padding: 0px 5px 0px 5px;
}

.iconaction:hover {
    color: rgb(63, 63, 63);
}

.action{
    width: 100%;
    color: gray;
    padding: 10px 0px 10px 0px;
    font-size: small;
    text-align: right;
}
.title{
    width: 70%;
    color: gray;
    padding: 10px 0px 10px 0px;
    font-weight: bold;
    font-family: Arial, Helvetica, sans-serif;
}

.author{
    width: 30%;
    color: gray;
    padding: 10px 0px 10px 0px;
    font-weight: normal;
    text-align: right;
    font-family: Arial, Helvetica, sans-serif;
}

.description{
    width: 100%;
    color: grey;
    padding: 10px 0px 10px 0px;
    font-weight: normal;
    font-family: Arial, Helvetica, sans-serif;
}

.save {
    color: rgb(84, 84, 255);
    margin-left: 4%;
    cursor: pointer;
    font-size: 35px;
    transition: color 0.5s ease;
}

.save:hover {
    color: #090598;
}

.deleteRed {
    color: #e35e6b;
    cursor: pointer;
    transition: color 0.5s ease;
}

.deleteRed:hover {
    color: #a91c2a;
}

.edit {
    color: #d9cd26;
    cursor: pointer;
    margin-left: 5px;
    transition: color 0.5s ease;
}

.edit:hover {
    color: #716603;
}
