<mat-card>
    <mat-card-header>
        <!--div mat-card-avatar>
            <img class="logo" src="assets/img/CAPACITEAUTISMO.png">
        </div-->
        <mat-card-title>ESDM Checklist</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field  style="width: 15%; padding: 20px;">
                    <mat-label>Paciente</mat-label>
                    <mat-select placeholder="Paciente" 
                        [(ngModel)]="esdmchecklist.idPaciente"
                        name="paciente" (selectionChange) = "do()" disabled required>
                        <!--mat-option *ngFor="let paciente of pacientes" [value]="paciente.id" >
                            {{paciente.nome}}
                        </mat-option-->
                        <mat-option [value]="esdmchecklist.idPaciente" >
                            {{esdmchecklist.paciente.nome}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="paciente.invalid">Paciente é obrigatório.</mat-error>  
                </mat-form-field>
                
                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="esdmchecklist.idProfissional"
                        name="profissional" (selectionChange) = "setProfissional($event)" required>  
                        <mat-option *ngFor="let profissional of profissionaisDoPaciente" [value]="profissional.id" >
                            {{profissional.nome}}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="profissional.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>
                
                <mat-form-field style="width: 15%; padding: 20px;">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data" 
                        [(ngModel)]="esdmchecklist.data"  name="data"
                        (dateChange) = "do2()"
                        [matDatepicker]="picker" required>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="data.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>
                
                <!--mat-form-field  style="width: 25%;  padding: 10px;">
                    <mat-label>Checklist</mat-label>
                    <mat-select placeholder="Checklist" 
                    [(ngModel)]="esdmchecklist.id"
                    name="checklist" (selectionChange) = "do()">
                    <mat-option *ngFor="let checklist of esdmchecklists" [value]="checklist.id" >
                        {{checklist.data}}
                    </mat-option>
                </mat-select>
                </mat-form-field>
                <button  mat-mini-fab color="primary" style="margin: 10px;" (click)="new()">
                    <mat-icon>add</mat-icon>
                </button-->
                
                <mat-form-field  style="width: 10%; padding: 10px;">
                    <mat-label>Nível</mat-label>
                    <mat-select placeholder="Nivel" 
                        [(ngModel)]="nivel"
                        name="nivel" (selectionChange) = "setDominios()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Domínio</mat-label>
                    <mat-select placeholder="Dominio" 
                        [(ngModel)]="dominio"
                        name="dominio" (selectionChange) = "filterChecklist()">
                        <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                            {{dominio.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </form>
        </div>
        <div class="mat-elevation-z4"
            *ngIf="hasAccessRead">
            
            <table mat-table [dataSource]="chklstcompView"> 
                <!-- Id Column --> 
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef >Id</th>
                    <td mat-cell *matCellDef="let row">{{row.competencia.id}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.competencia.nome}}</td>
                </ng-container> 

                <!-- prevchklst Column -->
                <ng-container matColumnDef="prevchklst" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">{{ prevesdmchecklist == undefined ? "" : prevesdmchecklist.data | date:'dd/MM/yyyy' }}</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        {{ getEsdmChkLstValue(esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].competencia.id) == undefined ?
                            "X" : getEsdmChkLstValue(esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].competencia.id) }}
                    </td>
                </ng-container> 

                <!-- N Column -->
                <ng-container matColumnDef="N" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">N</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30" style="padding-left: 5px;">
                        <mat-radio-group 
                            [(ngModel)]="esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].valor" 
                            aria-label="Select an option"
                            name="valor{{esdmchecklist.checklist.indexOf(row)}}">
                            <mat-radio-button value="N"></mat-radio-button>
                        </mat-radio-group>
                    </td>
                </ng-container>
                
                <!-- P Column -->
                <ng-container matColumnDef="P" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">P</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30" style="padding-left: 5px;">
                        <mat-radio-group 
                            [(ngModel)]="esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].valor" 
                            aria-label="Select an option"
                            name="valor{{esdmchecklist.checklist.indexOf(row)}}">
                            <mat-radio-button value="P"></mat-radio-button>
                        </mat-radio-group>  
                    </td>
                </ng-container>

                <!-- A Column -->
                <ng-container matColumnDef="A" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">A</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30" style="padding-left: 5px;">
                        <mat-radio-group 
                            [(ngModel)]="esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].valor" 
                            aria-label="Select an option"
                            name="valor{{esdmchecklist.checklist.indexOf(row)}}">
                            <mat-radio-button value="A"></mat-radio-button>
                        </mat-radio-group>
                    </td>
                </ng-container>
                
                <!-- X Column -->
                <ng-container matColumnDef="X" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">X</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30" style="padding-left: 5px;">
                        <mat-radio-group 
                            [(ngModel)]="esdmchecklist.checklist[esdmchecklist.checklist.indexOf(row)].valor" 
                            aria-label="Select an option"
                            name="valor{{esdmchecklist.checklist.indexOf(row)}}">
                            <mat-radio-button value="X"></mat-radio-button>
                        </mat-radio-group>
                    </td>
                </ng-container>
                
          
              <!-- Valor Column -->
              <!--ng-container matColumnDef="valor">
                <th mat-header-cell *matHeaderCellDef>Ações</th>
                <td mat-cell *matCellDef="let row">
                    <a (click)="navigateToUpdate(row.id)" class="edit">
                        <i class="material-icons">
                            edit
                        </i>
                    </a>
                    <a (click)="delete(row.id)" class="delete">
                        <i class="material-icons">
                            delete
                        </i>
                    </a>
                </td>
              </ng-container-->
          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>  
        </div>     
        <div style="width: 50%; display: table; margin: 0%; padding-top: 15px;">
            <div style="display: table-row-group;">
                <div style="display:table-row;">
                    <div style="display: table-cell;">
                        N – Não adquirido
                    </div>
                    <div style="display: table-cell; text-align: left; margin: 0%;">
                        P – Adquirido parcialmente
                    </div>
                </div>
                <div style="display:table-row;">
                    <div style="display: table-cell;">
                        A – Adquirido totalmente
                    </div>
                    <div style="display: table-cell; text-align: left; margin: 0%;">
                        X – Não observado
                    </div>
                </div>
            </div>
        </div>
    </mat-card-content>
    <mat-card-actions style="padding-bottom: 5px;">
        <button mat-raised-button color="primary" (click)="save(false)"
            *ngIf="hasAccessUpdate"
            id="btnSave"
            [disabled]="saveDisabled">
            Salvar
        </button>

        <button mat-raised-button color="primary" (click)="save(true)"
            *ngIf="hasAccessUpdate"
            id="btnSave"
            [disabled]="loading">
            Salvar & Sair
        </button>

        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>

        <button mat-raised-button color="primary" (click)="nextDominio()" style="float: right;" [(disabled)]="disabledButtomNext"
            *ngIf="hasAccessUpdate">
            {{ textoBotao }}
        </button>

        <mat-form-field class="custom-select-primary all-label" style="float: right;" *ngIf="hasAccessUpdate">
            <mat-label>Marcar todos como...</mat-label>
            <mat-select (selectionChange)="checkAllAcquired($event.value)" [(ngModel)]="allButton">
                <mat-option value="N">Não adquirido (N)</mat-option>
                <mat-option value="P">Parcialmente adquirido (P)</mat-option>
                <mat-option value="A">Adquirido (A)</mat-option>
            </mat-select>
        </mat-form-field>

    </mat-card-actions>
</mat-card>