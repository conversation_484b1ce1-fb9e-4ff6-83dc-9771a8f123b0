form{
    display: flex;
    flex-flow: row wrap;
}

button{
    margin-right: 15px;
    margin-top: 20px;
}

.subtitle{
    font-weight: 500;
    font-size: large;
}

.mat-column-action {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-nome {
    flex: 0 0 65% !important;
    width: 65% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-id {
    flex: 0 0 20% !important;
    width: 20% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

table{
    width: 100%;
    border-collapse: collapse;
}

td {
    border: 0px solid lightgray;
}

tr:nth-child(even){
    background-color: #f2f2f2;
}

.header{
    background-color: #3f51b5;
    font-weight: bold;
    color: white;
}

.rowNome{
    text-align: left;
}
.rowCRUD{
    text-align: center;
}

a{
    cursor: pointer;
    text-decoration: none;
}

textarea{
    height: auto;
}

.edit {
    margin-right: 10px;
}
.edit > i {
    color: #d9cd26;
    cursor: pointer;
}

.delete > i {
    color: #e35e6b;
    cursor: pointer;
}

.labelInput{
    font-size: 13px;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    letter-spacing: normal;
    text-align: left;
    color: #052B3B;
}


.group {
    color: rgba(0,0,0,.12);
    padding: 1em 0 1em 0;
    border: 1px solid currentColor;
    border-radius: 5px ;
    display:flex; 
    flex-direction: column;
    position: relative;

}

.group:hover {
    color: rgba(0,0,0,.87);
    /* border-width: 2px; */
}

.overlay{
    height:100vh;
    width:100%;
    background-color:rgba(0, 0, 0, 0.286);
    z-index:    10;
    top:        0; 
    left:       0; 
    position:   fixed;
}
  
.center {
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
} 

.add-habilidade {
    padding-left: 10px;
    display:flex;
    flex-direction: column;
    width: 10%;
    color: #000000;
    justify-content: center;
    cursor: pointer;
}

.create-categoria {
    cursor: pointer;
    justify-content:center;
    padding-left: 10px;
    padding-top: 20px;
}

.add-suporte {
    padding-top: 5px;
    padding-left: 10px;
}