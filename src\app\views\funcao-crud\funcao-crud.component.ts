import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-funcao-crud',
  templateUrl: './funcao-crud.component.html',
  styleUrls: ['./funcao-crud.component.css']
})
export class FuncaoCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Função',
        icon: 'psychology',
        routeUrl: '/funcao'
      }
    }
  ngOnInit(): void {
  }

}
