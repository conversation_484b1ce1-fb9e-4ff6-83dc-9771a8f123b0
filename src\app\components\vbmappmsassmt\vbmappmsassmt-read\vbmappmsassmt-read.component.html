<mat-card class="mat-elevation-z0" style="padding: 0px;">
    <mat-card-header>
        <!--div mat-card-avatar>
            <img class="logo" src="assets/img/CAPACITEAUTISMO.png">
        </div-->
        <mat-card-title>Avaliação de Marcos</mat-card-title>
    </mat-card-header>

    <div class="mat-elevation-z0" style="padding: 0;">   
        <mat-form-field  style="width: 15%; padding: 20px;">
            <mat-label>Paciente</mat-label>
            <mat-select placeholder="Paciente" 
                [(ngModel)]="vbmappmsassmt.idPaciente"
                name="paciente" disabled required>
                <!--mat-option *ngFor="let paciente of pacientes" [value]="paciente.id" >
                    {{paciente.nome}}
                </mat-option-->
                <mat-option [value]="vbmappmsassmt.idPaciente" >
                    {{vbmappmsassmt.paciente.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field  style="width: 15%;  padding: 0px;">
            <mat-label>Avaliação de Marco</mat-label>
            <mat-select placeholder="Avaliação de Marco" 
                [(ngModel)]="vbmappmsassmt"
                name="vbmappmsassmt" disabled>
                <mat-option [value]="vbmappmsassmt" >
                    {{vbmappmsassmt.data | date: 'dd/MM/yyyy'}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field  style="width: 20%; padding: 20px;">
            <mat-label>Profissional</mat-label>
            <mat-select placeholder="Profissional" 
                [(ngModel)]="vbmappmsassmt.idProfissional"
                name="profissional" (selectionChange) = "do()"
                disabled>
                <mat-option *ngFor="let profissional of profissionais" [value]="profissional.id" >
                    {{profissional.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <button mat-mini-fab alt="Sair da edição" color="primary" style="margin: 15px;" (click)="exitEdit()">
            <mat-icon>arrow_back</mat-icon>
        </button>

        <button mat-mini-fab color="primary" style="margin: 7px;" (click)="generatePDF()">
            <mat-icon>printer</mat-icon>
        </button>
    </div>
    
    <div class="mat-elevation-z0" style="padding: 0;"
        *ngIf="hasAccessRead">
        <span style="width: 90%; padding: 10px;">
            
            <mat-form-field  style="width: 10%; padding: 20px;">
                <mat-label>Nível</mat-label>
                <mat-select placeholder="Nivel" 
                    [(ngModel)]="nivel"
                    name="nivel" (selectionChange) = "setDominios()">
                    <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                        {{nivel.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%; padding: 20px;">
                <mat-label>Domínio</mat-label>
                <mat-select placeholder="Dominio" 
                    [(ngModel)]="dominio"
                    name="dominio" (selectionChange) = "filterAssessment()">
                    <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                        {{dominio.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            
            <span style="width: 40%; display: inline-block;">
                <strong style="font-weight: bold; ">Pontos: {{ vbmappmsassmt.pontos }} </strong>
            </span>
        </span>
        <table mat-table [dataSource]="vbmappMsAssmtItemView"> 
            <!-- Id Column --> 
            <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef >Id</th>
                <td mat-cell *matCellDef="let row">{{row.marco.id}}</td>
            </ng-container>
    
            <!-- Nome Column -->
            <ng-container matColumnDef="nome" fxFlex="30">
                <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                <td mat-cell *matCellDef="let row" fxFlex="30">{{row.marco.nome}}</td>
            </ng-container> 

                <!-- Status Column -->
                <ng-container matColumnDef="status" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <div class="label label-N" *ngIf="row.valor == 'N'"><small>Difícil obter</small></div>
                        <div class="label label-P" *ngIf="row.valor == 'P'"><small>Mais ou menos</small></div>
                        <div class="label label-A" *ngIf="row.valor == 'A'"><small>Consistente</small></div>
                        <div class="label label-X" *ngIf="row.valor == 'X' || row.valor == undefined" disabled><small>Não observado</small></div>
                    </td>
                </ng-container> 
          
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
        </div>
        <app-avaliacaovbmapppdf *ngIf="viewVbmappPDF" [idAvaliacao] = "idMsAssmt" [avaliacao] = "vbmappmsassmt" (pdfGenerated)="onPDFGenerated()"></app-avaliacaovbmapppdf>
</mat-card>
