<!--div #pdf style="display: flex; flex-direction: column; width: 580px;"--> 
<div #pdf class="tablewrapper" style="display: block; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 90%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">{{ paciente?.nome }}</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle"><u>Plano de Ensino Individualizado</u></p>
        </div>
        <div style="text-align: right; width: 66%;">
            <p class="subtitle" style="text-align: right; ">Data: {{ dia }} de {{ mes }} de {{ ano }}</p>
        </div>
        
    </div>
    <h1 *ngIf="!objetivosOrdenados" style="text-align: center; ">
        Nenhum objetivo cadastrado no PEI!
    </h1>
    <div *ngIf="objetivosOrdenados" class="div-table">
        <div class="div-table-row">
            <div class="div-indice-cell">
                #
            </div>
            <div class="div-header-cell">
                OBJETIVO
            </div>
            <div class="div-header-cell">
                DESCRIÇÃO
            </div>
        </div>
        <div class="div-table-row" *ngFor="let objetivo of objetivosOrdenados; let idx=index;">
            <ng-container *ngIf="objetivo.descricao_plano">
                <div class="div-indice-cell">
                    <p>
                        {{ idx + 1 }} 
                    </p>
                    <p>
                        {{ objetivo.nomeTipoAvaliacao }} - {{ objetivo.id }}
                    </p>
                </div>
                <div class="div-data-cell">
                    {{ objetivo.nome }}
                </div>
                <div class="div-data-cell">
                    {{ objetivo.descricao_plano }}
                </div>
            </ng-container>
            <ng-container *ngIf="!objetivo.descricao_plano">
                <div class="div-indice-cell">
                    <p>
                        {{ idx + 1 }} 
                    </p>
                    <ng-container *ngFor="let habilidade of objetivo.habilidades">
                        <div>
                            {{ habilidade.nomeTipoAvaliacao }} - {{ habilidade.sigla }}
                        </div>
                    </ng-container>
                </div>
                <div class="div-data-cell">
                    {{ objetivo.nome }}
                </div>
                <div class="div-data-cell">
                    {{ objetivo.descricao }}
                </div>
            </ng-container>
        </div>
    </div>
    <div *ngIf="objetivosOrdenados" style="width: 100%; break-inside:avoid; " >

        <p style="padding-top: 20px;">
            Estou à disposição para qualquer esclarecimento,
        </p>
        <p style="padding-top: 20px; text-align: center;">
            ________________________________________________________________________________
        </p>
        <div id="assinatura" style="padding-top: 0px; text-align: center;">
            
            
        </div>
    </div>
</div>