<mat-expansion-panel style="width: 100%; margin-bottom: 5px;">
    <mat-expansion-panel-header>
        <mat-panel-title> Filtros </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="group">
        <div style="flex-direction: row; width: 100%; justify-content: space-between;">
            
            <mat-form-field style="width: 15%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)] = "nome" name="nome"
                    (ngModelChange)="setFilter()">
            </mat-form-field>

            <mat-form-field style="width: 15%;">
                <mat-select 
                placeholder="Tipo de Avaliação" 
                [(ngModel)] = "tipoAvaliacao"
                name="tipoAvaliacao" 
                (selectionChange) = "setTipoAvaliacao($event)"
                (ngModelChange)="setFilter()">
                    <mat-option *ngFor="let tipoAvaliacao of tiposAvaliacao" [value]="tipoAvaliacao">
                        {{tipoAvaliacao.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field style="width: 10%;">
                <mat-select placeholder="Nível" 
                    [(ngModel)] = "nivel"
                    name="nivel" (selectionChange) = "setNivel($event)" (ngModelChange)="setFilter()">
                    <mat-option *ngFor="let nivel of niveis" [value]="nivel">
                        {{nivel.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <mat-select placeholder="Domínio" 
                    [(ngModel)] = "dominio"
                    name="dominio" (selectionChange) = "setDominio()" (ngModelChange)="setFilter()">
                        <mat-option *ngFor="let dom of dominios" [value]="dom">
                            {{dom.nome}}
                        </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <mat-select placeholder="Habilidade"
                [(ngModel)]="habilidade"
                name="habilidade" (ngModelChange)="setFilter()">
                    <ng-container *ngIf="tipoAvaliacao?.id != '5'">
                        <mat-option *ngFor="let habilidade of habilidades" [value]="habilidade">
                            {{habilidade.sigla == undefined || habilidade.sigla == "" ? dominio?.nome + " - " + habilidade?.ordem : habilidade?.sigla}}
                        </mat-option>
                    </ng-container>

                    <ng-container *ngIf="tipoAvaliacao?.id == '5'">
                        <mat-option *ngFor="let habilidade of habilidades" [value]="habilidade">
                            {{habilidade.sigla == undefined || habilidade.sigla == "" ? dominio.nome + " - " + habilidade.competencia.id : habilidade.competencia.id}}
                        </mat-option>
                    </ng-container>
                </mat-select>
            </mat-form-field>

            <mat-form-field style="width: 10%;">
                <mat-select placeholder="Tipos de suporte" 
                  [(ngModel)]="tiposSuporte" 
                  name="tiposSuporte" 
                  (ngModelChange)="setFilter()" 
                  multiple>
                  <mat-option *ngFor="let suporte of suportes" [value]="suporte.nome">
                    {{ suporte.nome }}
                  </mat-option>
                </mat-select>
            </mat-form-field>
              
        </div>
    </div>

    <div class="v-middle" style="font-size: small; cursor: pointer; width: 10%; text-align: right;">
        <a (click)="cleanFilter()">Limpar filtros</a>
    </div>
</mat-expansion-panel>
