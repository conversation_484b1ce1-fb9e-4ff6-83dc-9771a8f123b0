import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { StatusAgenda } from './statusagenda-model';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StatusAgendaService {

  statusAgendaUrl = `${environment.API_URL}/statusAgenda`;
  
  public funcoes: BehaviorSubject<StatusAgenda[]> = 
    new BehaviorSubject<StatusAgenda[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(statusAgenda: StatusAgenda): Observable<StatusAgenda>{
    //console.log(statusAgenda);
    return this.http.post<StatusAgenda>(this.statusAgendaUrl, statusAgenda).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(statusAgenda: StatusAgenda): Observable<StatusAgenda>{
    //console.log(statusAgenda);
    return this.http.put<StatusAgenda>(this.statusAgendaUrl + "/" + statusAgenda.id, statusAgenda).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<StatusAgenda>{
    return this.http.get<StatusAgenda>(this.statusAgendaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<StatusAgenda[]>{
    return this.http.get<StatusAgenda[]>(this.statusAgendaUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<StatusAgenda>{
    return this.http.delete<StatusAgenda>(this.statusAgendaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
