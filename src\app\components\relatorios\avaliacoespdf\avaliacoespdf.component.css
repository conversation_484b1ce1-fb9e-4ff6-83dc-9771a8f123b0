table{
  width: 100%;
}

.folha-horizontal-pdf {
  width: 850px;
  height: 585px;
  letter-spacing: 0.01px;
}

.cabecalho-pdf {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px;
  margin-right: 20px;
}

.cabecalho-img-pdf {
  text-align: left
}

.cabecalho-dados-pdf {
  text-align: right !important;
  font-size: 10px;
  font-weight: bold;
  margin-inline-end: 10px;
  letter-spacing: 0.5px;
}

.level-legend-container-pdf {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  letter-spacing: 0.1px;
}

.legend-container-pdf {
  display: flex;
  align-items: center;
  letter-spacing: 0.1px;
}

.legend-item-pdf {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.color-box-pdf {
  width: 7px;
  height: 7px;
  margin-right: 5px;
  border-radius: 10px;
}

.legend-text-pdf {
  margin: 0;
  font-size: 7px;
}

.adquirido-pdf {
  background-color: rgb(30, 126, 30);
}

.parcialmente3-pdf {
  background-color: #51c951;
}

.parcialmente-pdf {
  background-color: rgb(206, 146, 36);
}

.parcialmente1-pdf {
  background-color: #ff6f00;
}

.nao-adquirido-pdf {
  background-color: rgb(229, 40, 40);
}

.nao-observado-pdf {
  background-color: lightgray;
}

.table-container-pdf {
  max-width: calc(100% - 10px); /* Largura máxima da tabela com margem de 20px em cada lado */
  margin: 10px auto; /* Margens de 20px no topo e no fundo, e centralizar horizontalmente */
  background-color: rgb(230, 230, 230);
}

.table-dados-pdf {
  vertical-align: top;
}

.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  margin: .5em;
}

.label-N-pdf {
  width: 40px;
  height: 20px;
  background-color: rgb(229, 40, 40);
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 7px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 3px;
  margin-right: 2px;
  border-radius: 5px;
}

.label-P-pdf {
  width: 40px;
  height: 20px;
  background-color: rgb(206, 146, 36);
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 7px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 3px;
  margin-right: 2px;
  border-radius: 5px;
}

.label-A-pdf {
  width: 40px;
  height: 20px;
  background-color: rgb(30, 126, 30);
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 7px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 3px;
  margin-right: 2px;
  border-radius: 5px;
}

.label-X-pdf {
  width: 40px;
  height: 20px;
  background-color: rgb(170, 170, 170);
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 7px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 3px;
  margin-right: 2px;
  border-radius: 5px;
}


.label-ABLLS-N-pdf {
  width: 100px;
  height: 60px;
  background-color: #ff0000;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px;
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

.label-ABLLS-P1-pdf {
  width: 100px;
  height: 60px;
  background-color: #ff6f00;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

.label-ABLLS-P-pdf {
  width: 100px;
  height: 60px;
  background-color: #ffc800;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

.label-ABLLS-P3-pdf {
  width: 100px;
  height: 60px;
  background-color: #51c951;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

.label-ABLLS-A-pdf {
  width: 50px;
  height: 30px;
  background-color: #007612;
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

.label-ABLLS-X-pdf {
  width: 100px;
  height: 60px;
  background-color: rgb(170, 170, 170);
  color: white;
  text-align: center;
  font-weight: bold;
  font-size: 9px;
  letter-spacing: 1px;
  margin-left: 2px; 
  margin-bottom: 8px;
  margin-right: 2px;
  border-radius: 7px;
}

table{
  width: 100%;
}

.glossario-pdf {
  width: 750px;
  height: 485px;
  margin: 10px;
}

.glossario-dominio-pdf {
  background-color: grey;
  margin-top: 1px;
  margin-bottom: 1px;
  height: 20px;
  width: 820px;
  border-radius: 5px;
}

.glossario-dominio-text-pdf {
  font-size: 8px;
  padding-left: 5px;
}

.glossario-habilidade-pdf {
  display: flex;
  justify-content: space-around;
  width: 820px;
}

.glossario-habilidade-text-pdf {
  font-size: 7px;
  margin: 0;
}
