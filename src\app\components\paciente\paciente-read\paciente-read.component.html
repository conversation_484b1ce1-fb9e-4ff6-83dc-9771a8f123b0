<div style="display: flex; flex-direction: column; flex-wrap: wrap; width: 100%; align-items: flex-start;">
    <!-- DADOS BÁSICOS DO PACIENTE -->
    <mat-card class="mat-elevation-z0" style="width: 90%;">
        
        
            <ng-template #menino>
                <mat-card-header>
                    <div mat-card-avatar class="header-image" style="background-size: cover; background-image: url('/assets/img/menina_perfil.svg');"></div>
                    <mat-card-title>
                        {{ paciente.nome }}
                        <ng-container *ngIf="paciente.ativo == false && paciente.ativo != undefined">
                            (Inativo)
                        </ng-container>
                    <!-- <button mat-mini-fab  class="md-mini-fab-right" routerLink="/paciente/update/{{paciente.id}}"
                        color="primary" *ngIf="hasAccessUpdate">
                        <mat-icon>edit</mat-icon>
                    </button> -->
                    </mat-card-title>
                    <mat-card-subtitle>
                    {{paciente.dataNascimento | date: 'dd/MM/yyyy'}}
                    </mat-card-subtitle>
                </mat-card-header>
            </ng-template>
        
    </mat-card>
    <!-- LISTAGEM DE PROFISSIONAIS -->
    <div style="display: flex; flex-direction: column; align-items: flex-start; width: 100%;">
        <mat-card class="mat-elevation-z0" style="margin-top: 10px; width: 90%;">
            <mat-card-title class="subtitle">Equipe</mat-card-title>
            <div style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%; align-items: flex-start;">
                <mat-card *ngFor="let profissional of paciente.equipe; index as id"
                class="mat-elevation-z0" style="width: 25%; margin: 10px 10px 10px 0px;">
                <mat-card-header>
                    <div mat-card-avatar style="margin: 10px;">
                        <div *ngIf="profissional.sexo=='M'; then menino else menina"></div>
                        <ng-template #menino><img src="/assets/img/menino_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                        <ng-template #menina><img src="/assets/img/menina_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                    </div>
                    <mat-card-title class="mat-card-title">
                        <small style="text-align: left;" _ngcontent-c9>
                            {{profissional.nome}}
                        </small>
                    </mat-card-title>
                    <mat-card-subtitle>
                        <small  style="text-align: left;">{{ profissional?.funcao[0]?.nome }} </small>
                    </mat-card-subtitle>
                    </mat-card-header>
                </mat-card>
            </div>
        </mat-card>
    </div>

    <!-- LISTAGEM DE PARENTES -->
    <div style="display: flex; flex-direction: column; align-items: flex-start; width: 100%;" *ngIf="paciente.parentes != undefined && paciente.parentes.length > 0">
        <mat-card class="mat-elevation-z0" style="margin-top: 10px; width: 90%;">
            <mat-card-title class="subtitle">Família</mat-card-title>
            <div style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%; align-items: flex-start;">
                <mat-card *ngFor="let parente of paciente.parentes; index as id"
                class="mat-elevation-z0" style="width: 25%; margin: 10px 10px 10px 0px;;">
                <mat-card-header> 
                    <div mat-card-avatar style="margin: 10px;">
                        <img src="/assets/img/family.png" width="100" alt="{{ parente.nome }} ({{ parente.parentesco}})" class="mat-card-avatar img-circle-shadow">
                    </div>
                    <mat-card-title class="mat-card-title">
                        <small style="text-align: left;" _ngcontent-c9>
                            {{parente.nome}}
                        </small>
                    </mat-card-title>
                    <mat-card-subtitle>
                        <small  style="text-align: left;">{{ parente.parentesco }} </small>
                    </mat-card-subtitle>
                    </mat-card-header>
                </mat-card>
            </div>
        </mat-card>
    </div>
</div>
<div style="width: 100%;">
<button mat-mini-fab  class="md-mini-fab-right" routerLink="/paciente/update/{{paciente.id}}"
    color="primary"
    *ngIf="hasAccessUpdate">
    <mat-icon>edit</mat-icon>
</button>
</div>