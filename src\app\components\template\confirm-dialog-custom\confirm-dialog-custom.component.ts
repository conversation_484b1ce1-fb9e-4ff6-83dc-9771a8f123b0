import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface ConfirmDialogCustomData {
  title?: string;
  message: string;
  options: string[];
}

@Component({
  selector: 'app-confirm-dialog-custom',
  templateUrl: './confirm-dialog-custom.component.html',
  styleUrls: ['./confirm-dialog-custom.component.css']
})
export class ConfirmDialogCustomComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmDialogCustomComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogCustomData
  ) {}

  onSelect(option: string): void {
    this.dialogRef.close(option);
  }
}
