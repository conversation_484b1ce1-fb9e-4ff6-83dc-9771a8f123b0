import { Profissional } from './../../profissional/profissional-model';
import { NavService } from './../nav/nav.service';
import { UserService } from './../auth/user.service';
import { AuthService } from './../auth/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FirebaseUserModel } from './../auth/user-model';
import { HeaderService } from './header.service';
import { Component, OnInit, NgModule } from '@angular/core';
import { UserOrganizacoesModel } from '../auth/user-organizacoes-model';
import { UserOrganizacoesService } from '../auth/user-organizacoes-service';
import * as firebase from 'firebase/app';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})


export class HeaderComponent implements OnInit {
  //user: FirebaseUserModel = new FirebaseUserModel();

  organizacoes: UserOrganizacoesModel[] = [];
  organizacaoIdSelecionada: UserOrganizacoesModel;
  logoSrc: string;
  hasAccessCreateEstimulos: boolean = false;
  hasAccessCreateParentes: boolean = false;

  constructor(private headerService: HeaderService,
    public userService: UserService,
    private navService: NavService,
    public authService: AuthService,
    private router: Router,
    private userOrganizacoesService: UserOrganizacoesService,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    let self = this;
    // this.userService.getCurrentUser();
    this.userService.getCurrentUser()
    .then(async user => {
      // console.log("@222");
      await this.carregarOrganizacoes(user.uid);
      await this.setLogoSrc();
    }).catch(error => {
      console.log("Erro: ", error);
    }).finally( () => {
      this.loadingService.hide();
      // this.carregarOrganizacoes(user.uid);
    });
    
    firebase.auth().onAuthStateChanged(async function(user){
      if(user) {
        //  console.log("Oi")
        await self.carregarOrganizacoes(user.uid);
        await self.setLogoSrc();
      }
      //console.log(user)
    })


    /*
    this.route.data.subscribe(routeData => {
      let data = routeData['data'];
      console.log(data)
      if (data) {
        this.user = data;
        //this.createForm(this.user.name);
      }
    })*/
  }

  setSuperAdmin(){
    let p = new Profissional();
    p.email = this.user.email;
    this.authService.grantSuperAdminAccess(p).subscribe(u => {
      //console.log(u)
    })
  }

  toogleNavSidebar(){
    this.navService.navData = {
      hidden: !this.navService.navData.hidden
    }
  }

  get title(): string{
    return this.headerService.headerData.title
  }

  get icon(): string{
    return this.headerService.headerData.icon
  }

  get routeUrl(): string{
    return this.headerService.headerData.routeUrl
  }

  get user(): FirebaseUserModel{
    //console.log(this.headerService.user.name);
    //console.log(this.headerService.getUser())
    
    return this.authService.getUser();
    //return this.headerService.getUser();


    /*console.log(this.userService.getCurrentUser())
    this.userService.getCurrentUser()
      .then( (user) => {
        return user;
      }, (error) => {
        console.log('Usuário não está logado.')
        return null;
      });
      return null;*/
  }

  profile(){
    this.router.navigate(['/user']);
  }

  manageOrganization(){
    this.router.navigate(['/organizacao/update']);
  }

  manageUsers(){
    this.router.navigate(['/admin/users']);
  }

  manageAdmin(){
    this.router.navigate(['/useradmins']);
  }

  manageFuncoes(){
    this.router.navigate(['/funcao']);
  }

  manageAnamnese(){
    this.router.navigate(['/anamnese']);
  }

  manageStatusAgenda(){
    this.router.navigate(['/status-agenda']);
  }

  manageEstimulos(){
    this.router.navigate(['/estimulo']);
  }

  manageEstimulosCategory(){
    this.router.navigate(['/estimulo/category']);
  }

  manageTiposProcedimentos(){
    this.router.navigate(['/tipoprocedimento']);
  }

  manageParentesco(){
    this.router.navigate(['/parentesco']);
  }

  manageProfessionals(){
    this.router.navigate(['/profissional']);
  }

  manageLocalAtendimento(){
    this.router.navigate(['/local-atendimento']);
  }

  reportPacientesPorFuncao(){
    this.router.navigate(['/relatorio/pacientesporfuncao']);
  }

  reportColeta(){
    this.router.navigate(['/relatorio/coleta']);
  }

  logout(){
    this.authService.doLogout()
    .then((res) => {
      // this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      // this.router.onSameUrlNavigation = 'reload'; 
      this.router.navigateByUrl('/login?hasFullView=true');
    }, (error) => {
      console.log("Erro de Logout", error);
    });
  }

  carregarOrganizacoes(id:string){
    // console.log(id);
    this.userOrganizacoesService.findByUser(id).subscribe(retorno => {
      this.organizacoes = retorno.filter(org => org.ativo != false);
      this.organizacaoIdSelecionada = this.organizacoes.find(org => org.default);
      // console.log(this.organizacoes.find(org => org.default))
      this.loadingService.hide();
    })
  }

  async alterarOrganizacao(){
    this.router.navigate(['']);
    this.loadingService.show();
    await this.salvarUserOrganizacoes();
    // console.log(this.organizacaoIdSelecionada.idOrganizacao);
    this.user.organizationId = this.organizacaoIdSelecionada.idOrganizacao;
   // await this.salvarUserOrganizacoes();
    let p: Profissional = new Profissional();
    p.email = this.user.email;
    //Atualizo o profile do usuário no Auth do Firebase
    this.authService.updateUserOrganization (this.user).subscribe(async us => {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.onSameUrlNavigation = 'reload'; 
      await this.authService.refreshToken();
      this.router.navigate([""]).then(retorno => {
        this.setLogoSrc();
        this.loadingService.hide();
      });
    });

    // this.authService.updateUserOrganization (this.user).subscribe(async us => {
      /*
      if(this.user.superadmin){
        await this.authService.grantSuperAdminAccess(p).subscribe(async u => {
          if(this.user.admin){
            await this.authService.grantOrganizationAdminAccess(p,this.user.organizationId).subscribe(u => {
              console.log(u);
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload'; 
              this.router.navigate([""]).then(retorno => {
                this.loadingService.hide();
              });
            })
          } else {
            await this.authService.revokeOrganizationAdminAccess(p).subscribe(async u => {
              await this.authService.setOrganizacao(p, this.user.organizationId).subscribe(u => {
                console.log(u);
                this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload'; 
              this.router.navigate([""]).then(retorno => {
                this.loadingService.hide();
              });
              });
            })
          }
        })
      } else {
        await this.authService.revokeSuperAdminAccess(p).subscribe(async u => {
          if(this.user.admin){
            await this.authService.grantOrganizationAdminAccess(p,this.user.organizationId).subscribe(u => {
              console.log(u);
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload'; 
              this.router.navigate([""]).then(retorno => {
                this.loadingService.hide();
              });
            })
          } else {
            await this.authService.revokeOrganizationAdminAccess(p).subscribe(async u => {
              await this.authService.setOrganizacao(p, this.user.organizationId).subscribe(u => {
                this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload'; 
              this.router.navigate(['']).then(retorno => {
                this.loadingService.hide();
              });
              });
            })
          }
        })
      }
      */
    // });
  }

  async salvarUserOrganizacoes(){
    await this.organizacoes.forEach(org => {
      // console.log(org);
      if(org.idOrganizacao == this.organizacaoIdSelecionada.idOrganizacao){
        // console.log(org.idOrganizacao)
        org.default = true;
      }else{
        org.default = false;
      }
      this.userOrganizacoesService.update(org).subscribe( retorno => {
        // console.log(retorno);
      }); 
    })  
  }

  setLogoSrc(): void {
    // console.log(this.user.organizationId);
    const organizationLogo = `assets/img/${this.user.organizationId}.png`;
    const defaultLogo = 'assets/img/Dev.png';

    const img = new Image();
    img.src = organizationLogo;
    img.onload = () => {
      this.logoSrc = organizationLogo;
    };
    img.onerror = () => {
      this.logoSrc = defaultLogo;
    };
  }

}
