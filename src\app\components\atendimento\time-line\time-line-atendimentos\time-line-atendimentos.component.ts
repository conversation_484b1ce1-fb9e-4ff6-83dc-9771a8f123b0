import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CalendarEventImp } from '../../calendarEventImp-model';

export interface DialogData {
  agendamentos: CalendarEventImp[],
  choques:[]
}

@Component({
  selector: 'app-time-line-atendimentos',
  templateUrl: './time-line-atendimentos.component.html',
  styleUrls: ['./time-line-atendimentos.component.css']
})
export class TimeLineAtendimentosComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public agendamentos: DialogData
  ) { }

  ngOnInit(): void {
  }

  validaChoque(id:string){
    return this.agendamentos.choques.find(a => a == id);
  }

}
