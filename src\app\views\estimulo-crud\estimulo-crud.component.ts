import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-estimulo-crud',
  templateUrl: './estimulo-crud.component.html',
  styleUrls: ['./estimulo-crud.component.css']
})
export class EstimuloCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: '<PERSON>st<PERSON><PERSON><PERSON>',
        icon: 'toys',
        routeUrl: '/estimulo'
      }
    }

  ngOnInit(): void {
  }

}
