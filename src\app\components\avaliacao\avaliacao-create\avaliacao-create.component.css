.mat-column-id {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: left;
}

.mat-column-nome {
    flex: 0 0 60% !important;
    width: 60% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-prevavaliacao {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: center;
}

.mat-column-oDescricao {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: center;
    vertical-align: middle;
}

.mat-column-resp {
    flex: 1 1 auto !important;
    text-align: center;
    width: auto;
    text-align: center;
}

.resp-true {
    padding-left: 8px;
    opacity: 1;
    cursor: pointer;
    pointer-events: auto;
}

.resp-false {
    padding-left: 8px;
    opacity: 0.5;
    pointer-events: none;
}

table{
    width: 100%;
}

.center {
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
} 

.overlay{
    height:100vh;
    width:100%;
    background-color:rgba(0, 0, 0, 0.286);
    z-index:    10;
    top:        0; 
    left:       0; 
    position:   fixed;
}

.tooltip-container {
    position: relative;
    display: inline-block;
}
  
.tooltip-descricao-text {
    visibility: hidden;
    background-color: rgb(29, 29, 29);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 3px;
    position: absolute;
    z-index: 1;
    top: 50%; 
    left: -10px; 
    transform: translateX(-100%) translateY(-50%); 
    width: 300px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 9px;
    white-space: pre-line;
}

.tooltip-text {
    visibility: hidden;
    background-color: rgb(29, 29, 29);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 3px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -80px;
    width: 160px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 9px;
    white-space: pre-line;
}

.tooltip-container:hover .tooltip-descricao-text{
    visibility: visible;
    opacity: 0.65;
}

.tooltip-container:hover .tooltip-text{
    visibility: visible;
    opacity: 0.65;
}

.custom-cursor {
    pointer-events: none;
}

::ng-deep .custom-select-primary {
    background-color: #3f51b5;
    color: white;
    height: 37px; 
    border-radius: 4px;
    padding: 0 10px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
}
  
::ng-deep .custom-select-primary .mat-form-field-wrapper {
    padding-left: 10px;
}

::ng-deep .custom-select-primary .mat-select-arrow {
    color: white !important;
}

::ng-deep .custom-select-primary .mat-form-field-underline {
    display: none;
}

::ng-deep .custom-select-primary .mat-select-value-text {
    color: white;
}

::ng-deep .custom-select-primary .mat-option-text {
    color: black;
}

::ng-deep .custom-select-primary .mat-form-field-label {
    color: white !important;
    padding-bottom: 10px;
}

.mat-form-field .mat-form-field-label {
    flex: 1;
    text-align: center;
}

.all-label {
    display: flex;
    align-items: center; 
}
