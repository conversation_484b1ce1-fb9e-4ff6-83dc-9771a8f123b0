form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}

.form-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-top: 15px;
}

.form-field {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 10px;
}

.form-field.small {
    width: 20%;
    margin-right: 30px;
}

.form-container-small {
    display: flex;
    flex-direction: row;
    width: 100%;
}

.card-actions {
    display: flex;
    flex-direction: row;
    padding-left: 10px;
}

.title {
    font-weight: 500;
    font-size: x-large;
}

.labelInput {
    font-size: 13px;
    font-weight: normal;
    color: #052B3B;
}

.overlay {
    height: 100vh;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.286);
    z-index: 10;
    top: 0; 
    left: 0; 
    position: fixed;
}

.center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.header-container {
    display: flex;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: white; 
    border-radius: 8px;
    margin-bottom: 10px;
}

.header-field {
    display: flex;
    flex-direction: column;
    width: 15%;
}

.header-label {
    font-weight: bold;
}

.header-value {
    padding-top: 4px;
}
  