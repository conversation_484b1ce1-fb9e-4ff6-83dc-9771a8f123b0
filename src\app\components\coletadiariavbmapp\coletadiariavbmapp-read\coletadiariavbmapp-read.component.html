<mat-card class="mat-elevation-z0" *ngIf="PEIs?.length > 0 || PICs?.length > 0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row wrap" style="display: flex;"> 
            <div class="div-plano-intervencao" fxLayout="row wrap">
                <mat-form-field  class="field-plano-intervencao">
                    <mat-label>Tipo de Plano</mat-label>
                    <mat-select placeholder="Tipo de Plano" 
                        [(ngModel)]="planoSelecionado"
                        name="planoSelecionado">
                        <mat-option *ngIf="PEIs?.length > 0" value="PEI">PEI</mat-option>
                        <mat-option *ngIf="PICs?.length > 0" value="PIC">PIC</mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  class="field-plano-intervencao" *ngIf="planoSelecionado == 'PEI'">
                    <mat-label>Plano PEI</mat-label>
                    <mat-select placeholder="Plano PEI" 
                        [(ngModel)] = "pei"
                        (selectionChange) = "setPEI()"
                        name="PEI">
                        <mat-option *ngFor="let pei of PEIs" [value]="pei" >
                            {{pei.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            
                <mat-form-field  class="field-plano-intervencao" *ngIf="planoSelecionado == 'PIC'">
                    <mat-label>Plano PIC</mat-label>
                    <mat-select placeholder="Plano PIC" 
                        [(ngModel)] = "pic"
                        (selectionChange) = "setPIC()"
                        name="PIC">
                        <mat-option *ngFor="let pic of PICs" [value]="pic" >
                            {{pic.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <form [formGroup]="intervaloForm" class="field-plano-intervencao">
                    <mat-form-field>
                        <mat-label>Período</mat-label>

                        <mat-date-range-input [rangePicker]="picker"
                        formGroupName="data" [min]="minDate"
                        [max]="maxDate" [dateFilter]="dateFilter">
                        
                        <input matStartDate formControlName="start" placeholder="Data inicial" readonly (focus)="picker.open()" />
                        <input matEndDate formControlName="end" placeholder="Data final" readonly (focus)="picker.open()" />

                        </mat-date-range-input>

                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-date-range-picker #picker (opened)="onDateRangeOpened()" (closed)="onDateRangeSelected()"></mat-date-range-picker>
                    </mat-form-field>
                </form>
                
            </div>
        </div>
        
        <div class="div-nova-coleta" fxLayout="row wrap" style="display: flex; justify-content: space-between; width: 35%;">
            <button mat-button [matMenuTriggerFor]="menuGrafico" matTooltip="Gráfico de Evolução" color="primary" *ngIf="hasAccessRead && (pei?.id || pic?.id)">
                <mat-icon style="padding-right: 5px;">leaderboard</mat-icon>  Gráfico de Evolução
            </button>
            <mat-menu #menuGrafico="matMenu">
                <button mat-menu-item (click)="graphPEI()" *ngIf="pei?.id">
                    <mat-icon>bar_chart</mat-icon>
                    <span>Gráfico PEI</span>
                </button>
                <button mat-menu-item (click)="graphPIC()" *ngIf="pic?.id">
                    <mat-icon>bar_chart</mat-icon>
                    <span>Gráfico PIC</span>
                </button>
            </mat-menu>
            <button mat-button matTooltip="Imprimir Coleta" color="primary" (click)="saveToPDF()" *ngIf="hasAccessCreate && (pei?.id || pic?.id)">
                <mat-icon style="padding-right: 5px;">printer</mat-icon>  Imprimir Coleta
            </button>
            <button mat-button color="primary" (click)="add()" *ngIf="hasAccessCreate">
                <mat-icon style="padding-right: 5px;">iso</mat-icon>  Coletar Dados
            </button>
        </div>

        <div class="mat-elevation-z0" style="padding-bottom: 30px;" 
            *ngIf="hasAccessRead && planoSelecionado == 'PEI'">
            <table mat-table [dataSource]="datasourceObjetivosPEI" multiTemplateDataRows *ngIf="pei?.objetivos?.length > 0"> 
                <!-- Index Column --> 
                <ng-container matColumnDef="index">
                    <th mat-header-cell *matHeaderCellDef >#</th>
                    <td mat-cell *matCellDef="let row">{{ pei.objetivos?.indexOf(row) + 1 }}</td>
                </ng-container>

                <!-- Expand Column --> 
                <ng-container matColumnDef="expand">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row">
                        <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                            <mat-icon>{{ row.isExpanded ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                        </a>
                    </td>
                </ng-container>

                <!-- etapasSum Column --> 
                <ng-container matColumnDef="etapas">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row">
                        <div [ngClass]="percentualEtapas(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEtapas(row) < 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        <div class="label label-ok div-etapas" *ngIf="percentualEtapas(row) == 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                    </td>
                </ng-container>

                <!-- Nome Column --> 
                <ng-container matColumnDef="nomeObj">
                    <th mat-header-cell *matHeaderCellDef>Nome</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.nome}} </td>
                </ng-container>

                <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjs.length">
                        <!-- Coleta Naturalista: por Tipo de Suporte -->
                        <ng-container *ngIf="element.habilidades"> 
                            <div class="row objetivo-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                                <div style="padding: 0px 0px 10px 20px; width: 95%">{{element.descricao}}</div>
                                <div style="padding: 0px 0px 0px 20px; width: 95%"><b>Status Geral</b></div>
                                <div class="divTable" style="padding: 0px 0px 10px 20px; width: 95% !important;">
                                    <div class="divTableHeading">
                                        <div class="divTableRow"> 
                                            <div class="divTableHead">Status</div>
                                            <div class="divTableHead">Tipo de Suporte</div>
                                            <ng-container *ngIf="((element.tiposSuporte != undefined) && (element.tiposSuporte.length > 0)
                                                && (element.tiposSuporte[0].estimulos != undefined))">
                                                <ng-container *ngFor="let estimulo of (element.tiposSuporte[0].estimulos || [])">
                                                    <ng-container *ngIf="estimulo.ativo">
                                                        <div class="divTableHead">{{estimulo.nome}}</div>
                                                    </ng-container>
                                                </ng-container>
                                            </ng-container>
                                            <ng-container *ngIf="!((element.tiposSuporte != undefined) && (element.tiposSuporte.length > 0)
                                                && (element.tiposSuporte[0].estimulos != undefined))">
                                                <div class="divTableHead"></div>
                                            </ng-container>
                                        </div>
                                    </div>
                                    <div class="divTableBody">
                                        <ng-container *ngFor="let tipoSuporte of element.tiposSuporte" style="width:100%;">

                                            <div class="divTableRow">
                                                <div class="divTableCell v-middle">
                                                    <div style="width: 5%;"  *ngIf="tipoSuporte.status=='Adquirido'" >
                                                        <a style="cursor: pointer; color: green;"  (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                            <mat-icon>
                                                                check
                                                            </mat-icon>
                                                        </a>
                                                    </div>
                                                    <div style="width: 5%;" *ngIf="tipoSuporte.status=='Não adquirido' || tipoSuporte.status == undefined">
                                                        <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                            <mat-icon>
                                                                miscellaneous_services
                                                            </mat-icon>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="divTableCell v-middle" style="text-align: left;">{{tipoSuporte.nome}}</div>
                                                <ng-container *ngFor="let estimulo of tipoSuporte.estimulos">
                                                    <ng-container *ngIf="estimulo.ativo">
                                                        <div class="divTableCell v-middle">
                                                            <div style="width: 5%;"  *ngIf="estimulo.status=='Adquirido'" >
                                                                <a style="cursor: pointer; color: green;"  (click)="changeTipoSuporteEstimuloStatus(estimulo, element.id, tipoSuporte.id)">
                                                                    <mat-icon>
                                                                        check
                                                                    </mat-icon>
                                                                </a>
                                                            </div>
                                                            <div style="width: 5%;" *ngIf="estimulo.status=='Não adquirido' || estimulo.status == undefined">
                                                                <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteEstimuloStatus(estimulo, element.id, tipoSuporte.id)">
                                                                    <mat-icon>
                                                                        miscellaneous_services
                                                                    </mat-icon>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                                <div style="padding: 10px 0px 0px 20px; width: 95%"
                                    *ngIf="getColetasPorObjetivo(element) != undefined && getColetasPorObjetivo(element).length > 0"><b>Coletas</b></div>
                                <ng-container *ngIf="((element.tiposSuporte != undefined) && (element.tiposSuporte.length > 0)
                                    && (element.tiposSuporte[0].estimulos != undefined))">
                                    <ng-container *ngFor="let objEstimulo of element.tiposSuporte[0]?.estimulos">
                                        <ng-container *ngIf="getColetasPorObjetivo(element, objEstimulo) != undefined && objEstimulo.ativo && getColetasPorObjetivo(element, objEstimulo).length > 0">
                                            <div style="padding: 0px 0px 0px 20px; width: 95% !important;">
                                                <div style="padding: 0px 0px 0px 20px; background-color: lightblue;">
                                                    <b> {{objEstimulo.nome}}</b>
                                                </div>
                                
                                                <div class="div-coleta">
                                                    <div class="div-coleta-table">
                                                        <div class="div-coleta-table-row">
                                                            <div class="div-coleta-data-cell"><b><small>Data</small></b></div>
                                                            <div class="div-coleta-status-cell"><b><small>Profissional</small></b></div>
                                                            <div class="div-coleta-estimulo-cell"><b><small>Tipo de Suporte</small></b></div>
                                                            <div class="div-coleta-independentes-cell" *ngIf="hasIndependentes(getColetasPorObjetivo(element, objEstimulo))">
                                                                <b><small>Ind</small></b>
                                                            </div>
                                                            <div class="div-coleta-score-cell"><b><small>Score</small></b></div>
                                                            <div class="div-coleta-percentual-cell"><b><small>%</small></b></div>
                                                            <div class="div-coleta-status-cell"><b><small>Status</small></b></div>
                                                        </div>
                                                        <ng-container *ngFor="let coleta of getColetasPorObjetivo(element, objEstimulo) | slice:0:5; let idc = index;">
                                                            <ng-container *ngFor="let objetivo of coleta.objetivos; let idx=index;">
                                                                <ng-container *ngIf="objetivo.nome == element.nome">
                                                                    <ng-container *ngFor="let tipoSuporte of objetivo.tiposSuporte; let idt = index">
                                                                        <ng-container *ngIf="(tipoSuporte.estimulos.length > 0)">
                                                                            <ng-container *ngFor="let estimulo of tipoSuporte.estimulos; let ide = index;">
                                                                                <ng-container *ngIf="objEstimulo.id == estimulo.id && (estimulo.status != '' && estimulo.status != undefined) && estimulo.ativo">
                                                                                    <div class="div-coleta-table-row">
                                                                                        <div class="div-coleta-data-cell v-middle"><small>{{ coleta.data | date:"dd/MM" }} 
                                                                                            ({{ coleta.sessao.substr(0,1) + coleta.sessao.substr(coleta.sessao.indexOf(" ")) }})
                                                                                        </small></div>
                                                                                        <div class="div-coleta-status-cell v-middle"><small>{{ coleta.profissional?.nome }}</small></div>
                                                                                        <div class="div-coleta-estimulo-cell v-middle"><small>{{ tipoSuporte.nome }} 
                                                                                        </small></div>
                                                                                        <div class="div-coleta-independentes-cell v-middle"
                                                                                                *ngIf="hasIndependentes(getColetasPorObjetivo(element, objEstimulo))">
                                                                                            <small>{{ estimulo.independentes || 0 }}</small>
                                                                                        </div>
                                                                                        <div class="div-coleta-score-cell v-middle"><small>{{ estimulo.positivos + (estimulo.independentes || 0) }}/{{ estimulo.positivos + estimulo.negativos + (estimulo.independentes || 0) }}</small></div>
                                                                                        <div class="div-coleta-percentual-cell v-middle"><small>{{ estimulo.percentual * 100 | number:'1.0-2' }}%</small></div>
                                                                                        <div class="div-coleta-status-cell v-middle"><small>{{ estimulo.status }}</small></div>
                                                                                    </div>
                                                                                </ng-container>
                                                                            </ng-container>
                                                                        </ng-container>
                                                                    </ng-container>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                        <div *ngIf="getColetasPorObjetivo(element, objEstimulo).length > 5" class="div-coleta-table-row">
                                                            <a 
                                                                class="btn-toggle"
                                                                [class.closed]="getStateColetaRow(pei.objetivos.indexOf(element), objEstimulo.id)"
                                                                [class.opened]="!getStateColetaRow(pei.objetivos.indexOf(element), objEstimulo.id)"
                                                                (click)="toogleColetaRows(pei.objetivos.indexOf(element), objEstimulo.id)"
                                                                style="cursor: pointer; display: flex; align-items: center; gap: 4px;">
                                                                
                                                                <mat-icon>
                                                                {{ getStateColetaRow(pei.objetivos.indexOf(element), objEstimulo.id) ? 'keyboard_arrow_down' : 'keyboard_arrow_up' }}
                                                                </mat-icon>
                                                                <span>
                                                                {{ getStateColetaRow(pei.objetivos.indexOf(element), objEstimulo.id) ? 'Ver mais' : 'Ver menos' }}
                                                                </span>
                                                            </a>
                                                        </div>
                                                        <ng-container *ngIf="getColetasPorObjetivo(element, objEstimulo).length > 5 && !getStateColetaRow(pei.objetivos.indexOf(element), objEstimulo.id)">
                                                            <ng-container *ngFor="let coleta of getColetasPorObjetivo(element, objEstimulo) | slice:5; let idc = index;">
                                                                <ng-container *ngFor="let objetivo of coleta.objetivos; let idx=index;">
                                                                    <ng-container *ngIf="objetivo.nome == element.nome">
                                                                        <ng-container *ngFor="let tipoSuporte of objetivo.tiposSuporte; let idt = index">
                                                                            <ng-container *ngIf="(tipoSuporte.estimulos.length > 0)">
                                                                                <ng-container *ngFor="let estimulo of tipoSuporte.estimulos; let ide = index;">
                                                                                    <ng-container *ngIf="objEstimulo.id == estimulo.id && (estimulo.status != '' && estimulo.status != undefined)
                                                                                        && estimulo.ativo">
                                                                                        <div class="div-coleta-table-row">
                                                                                            <div class="div-coleta-data-cell v-middle"><small>{{ coleta.data | date:"dd/MM" }} 
                                                                                                ({{ coleta.sessao.substr(0,1) + coleta.sessao.substr(coleta.sessao.indexOf(" ")) }})
                                                                                            </small></div>
                                                                                            <div class="div-coleta-status-cell v-middle"><small>{{ coleta.profissional?.nome }}</small></div>
                                                                                            <div class="div-coleta-estimulo-cell v-middle"><small>{{ tipoSuporte.nome }} 
                                                                                            </small></div>
                                                                                            <div class="div-coleta-independentes-cell v-middle" *ngIf="hasIndependentes(getColetasPorObjetivo(element, objEstimulo))">
                                                                                                <small>{{ estimulo.independentes || 0 }}</small>
                                                                                            </div>
                                                                                            <div class="div-coleta-score-cell v-middle"><small>{{ estimulo.positivos + (estimulo.independentes || 0) }}/{{ estimulo.positivos + estimulo.negativos + (estimulo.independentes || 0) }}</small></div>
                                                                                            <div class="div-coleta-percentual-cell v-middle"><small>{{ estimulo.percentual * 100 | number:'1.0-2' }}%</small></div>
                                                                                            <div class="div-coleta-status-cell v-middle"><small>{{ estimulo.status }}</small></div>
                                                                                        </div>
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                            </ng-container>
                                                                        </ng-container>
                                                                    </ng-container>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="!element.habilidades">
                            <div class="row objetivo-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                                <mat-list>
                                  <div style="padding: 0px 0px 10px 0px;">{{element.descricao_plano}}</div>
                                  <mat-list-item *ngFor="let etapa of element.etapa" style="padding: 0px !important; margin: 0px !important;">
                                    <!--div matLine *ngIf="etapa.status=='Adquirida'"  class="div-mat-line"-->
                                    <div matLine class="div-mat-line">
                                        <div class="div-dados-etapa">
                                            <div class="div-dados-etapa-detail">
                                                <a *ngIf="etapa.status=='Adquirida'"  class="div-mat-line" class="v-middle" style="cursor: pointer; color: green; padding-right: 5px;"  (click)="changeEtapaStatusESDM(etapa)">
                                                    <mat-icon>
                                                        check
                                                    </mat-icon>
                                                </a>
                                                <a *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" class="v-middle" style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatusESDM(etapa)">
                                                    <mat-icon>
                                                        miscellaneous_services
                                                    </mat-icon>
                                                </a>
                                                {{etapa.id}} - {{etapa.nome}}
                                            </div>
                                            <div class="div-coleta-ESDM">
                                                <div class="div-coleta-ESDM-table">
                                                    <div class="div-coleta-ESDM-table-row" matLine *ngIf="getColetasPorObjetivoESDM(element, etapa)?.length > 0">
                                                        <div class="div-coleta-ESDM-data-cell"><b>Data</b></div>
                                                        <div class="div-coleta-ESDM-profissional-cell"><b>Profissional</b></div>
                                                        <div class="div-coleta-ESDM-score-cell"><b>Score</b></div>
                                                        <div class="div-coleta-ESDM-percentual-cell"><b>%</b></div>
                                                        <div class="div-coleta-ESDM-status-cell"><b>Status</b></div>
                                                    </div>
                                                    <ng-container *ngFor="let coleta of getColetasPorObjetivoESDM(element, etapa) | slice:0:5; let idc = index;">
                                                        <ng-container *ngFor="let objetivo of coleta.objetivos; let ido = index;">
                                                            <ng-container *ngIf="objetivo.id == element.id">
                                                                <ng-container *ngFor="let e of objetivo.etapa">
                                                                    <ng-container *ngIf="(etapa.id == e.id) && (e.status != '' && e.status != undefined)">
                                                                        <div class="div-coleta-ESDM-table-row" matLine>
                                                                            <div class="div-coleta-ESDM-data-cell">{{ coleta.data | date:"dd/MM" }}
                                                                            ({{ coleta.sessao.substr(0,1) + coleta.sessao.substr(coleta.sessao.indexOf(" ")) }})</div>
                                                                            <div class="div-coleta-ESDM-profissional-cell">{{ coleta.profissional?.nome }}</div>
                                                                            <div class="div-coleta-ESDM-score-cell">{{ e.positivos }}/{{ e.positivos + e.negativos }}</div>
                                                                            <div class="div-coleta-ESDM-percentual-cell">{{ e.percentual * 100 | number:'1.0-2' }}%</div>
                                                                            <div class="div-coleta-ESDM-status-cell">{{ e.status }}</div>
                                                                        </div>
                                                                    </ng-container>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>
                                                    <div *ngIf="getColetasPorObjetivoESDM(element, etapa)?.length > 5" class="div-coleta-table-row">
                                                        <a 
                                                            class="btn-toggle"
                                                            [class.closed]="getStateColetaRow(pei.objetivos.indexOf(element), etapa.id)"
                                                            [class.opened]="!getStateColetaRow(pei.objetivos.indexOf(element), etapa.id)"
                                                            (click)="toogleColetaRows(pei.objetivos.indexOf(element), etapa.id)"
                                                            style="cursor: pointer; display: flex; align-items: center; gap: 4px;">
                                                            
                                                            <mat-icon>
                                                            {{ getStateColetaRow(pei.objetivos.indexOf(element), etapa.id) ? 'keyboard_arrow_down' : 'keyboard_arrow_up' }}
                                                            </mat-icon>
                                                            <span>
                                                            {{ getStateColetaRow(pei.objetivos.indexOf(element), etapa.id) ? 'Ver mais' : 'Ver menos' }}
                                                            </span>
                                                        </a>
                                                    </div>
                                                    <ng-container *ngIf="getColetasPorObjetivoESDM(element, etapa)?.length > 5 && !getStateColetaRow(pei.objetivos.indexOf(element), etapa.id)">
                                                        <ng-container *ngFor="let coleta of getColetasPorObjetivoESDM(element, etapa) | slice:5; let idc = index;">
                                                            <ng-container *ngFor="let objetivo of coleta.objetivos; let ido = index;">
                                                                <ng-container *ngIf="objetivo.id == element.id">
                                                                    <ng-container *ngFor="let e of objetivo.etapa">
                                                                        <ng-container *ngIf="(etapa.id == e.id) && (e.status != '' && e.status != undefined)">
                                                                            <div class="div-coleta-ESDM-table-row" matLine>
                                                                                <div class="div-coleta-ESDM-data-cell">{{ coleta.data | date:"dd/MM" }}
                                                                                ({{ coleta.sessao.substr(0,1) + coleta.sessao.substr(coleta.sessao.indexOf(" ")) }})</div>
                                                                                <div class="div-coleta-ESDM-profissional-cell">{{ coleta.profissional?.nome }}</div>
                                                                                <div class="div-coleta-ESDM-score-cell">{{ e.positivos }}/{{ e.positivos + e.negativos }}</div>
                                                                                <div class="div-coleta-ESDM-percentual-cell">{{ e.percentual * 100 | number:'1.0-2' }}%</div>
                                                                                <div class="div-coleta-ESDM-status-cell">{{ e.status }}</div>
                                                                            </div>
                                                                        </ng-container>
                                                                    </ng-container>
                                                                </ng-container>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                  </mat-list-item>
                                </mat-list>
                            </div>
                        </ng-container>
                    </td>
                </ng-container>
          
                <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
                <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="objetivo-detail-row"></tr>
            </table>
            <div *ngIf="!pei?.objetivos || pei.objetivos?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                <h1>Nenhum objetivo cadastrado no PEI!</h1>
            </div>
        </div>

        <div class="mat-elevation-z0" style="padding-bottom: 30px;" *ngIf="hasAccessRead && planoSelecionado == 'PIC'">
            <table mat-table [dataSource]="datasourceObjetivosPIC" multiTemplateDataRows *ngIf="pic.objetivos?.length > 0"> 
                <!-- Index Column --> 
                <ng-container matColumnDef="index">
                    <th mat-header-cell *matHeaderCellDef>#</th>
                    <td mat-cell *matCellDef="let row">{{ pic.objetivos?.indexOf(row) + 1 }}</td>
                </ng-container>
          
                <!-- Expand Column --> 
                <ng-container matColumnDef="expand">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let row">
                    <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                        <mat-icon>{{ row.isExpanded ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                    </a>
                    </td>
                </ng-container>
          
                <!-- Nome Column --> 
                <ng-container matColumnDef="compAlvoObj">
                    <th mat-header-cell *matHeaderCellDef>Comportamento Alvo</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.comportamentoAlvo}} </td>
                </ng-container>
          
                <!-- Definição Operacional Column --> 
                <ng-container matColumnDef="defOperacionalObj">
                    <th mat-header-cell *matHeaderCellDef>Definição Operacional</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.definicaoOperacional}} </td>
                </ng-container>
          
                <!-- Tipo de Coleta Column --> 
                <ng-container matColumnDef="tipoColetaObj">
                    <th mat-header-cell *matHeaderCellDef>Tipo de Coleta</th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.tipoColeta}} </td>
                </ng-container>
          
                <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjsPIC.length">
                        <ng-container>
                            <div class="row objetivo-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'" *ngIf="coletasDiariasPIC?.length > 0">
                                <div style="padding: 10px 0px 0px 20px; width: 95%">
                                    <b>Coletas</b>
                                </div>
                                <ng-container>
                                    <div style="padding: 0px 0px 0px 20px; width: 95% !important; margin-bottom: 5px;">
                                        <div class="div-coleta">
                                            <div class="div-coleta-table">
                                                <!-- Cabeçalho da Tabela -->
                                                <div class="div-coleta-table-row">
                                                    <div class="div-coleta-data-PIC"><b><small>Data</small></b></div>
                                                    <div class="div-coleta-status-PIC"><b><small>Profissional</small></b></div>
                                                    <div class="div-coleta-status-PIC" *ngIf="isQtdOrDuracao(element) === 'qtd'">
                                                        <b><small>Qtd</small></b>
                                                    </div>
                                                    <div class="div-coleta-status-PIC" *ngIf="isQtdOrDuracao(element) === 'duracao'">
                                                        <b><small>Duração  </small></b>
                                                        <ng-container>
                                                            <span style="font-size: 10px;">(M = Manual)</span>
                                                        </ng-container>
                                                    </div>
                                                </div>

                                                <!-- Conteúdo da Tabela -->
                                                <div class="div-coleta-table-row" *ngFor="let sessao of coletasDiariasPIC" style="border-bottom: 1px solid rgb(13, 13, 13);">
                                                    <ng-container *ngFor="let coleta of sessao.objetivosColeta">
                                                        <ng-container *ngIf="coleta.idObjetivo == element.id">
                                                            <ng-container *ngIf="coleta.duracoes?.length > 0 || (coleta.qtdObservada >= 0 && coleta.qtdObservada != null)">
                                                                <!-- Data com Sessão Abreviada -->
                                                                <div class="div-coleta-data-PIC">
                                                                {{ sessao.data | date: 'dd/MM/yyyy' }}
                                                                <span *ngIf="sessao.sessao" style="font-size: smaller;">({{ sessao.sessao }})</span>
                                                                </div>
                                                                <div class="div-coleta-status-PIC">{{ sessao.nomeProfissional }}</div>
                                                                <div class="div-coleta-status-PIC" *ngIf="coleta.qtdObservada >= 0">{{ coleta.qtdObservada }}</div>
                
                                                                <!-- Duração com símbolo "M" menor e índice -->
                                                                <ng-container *ngIf="coleta.duracoes">
                                                                    <div *ngFor="let duracao of coleta.duracoes; let i = index">
                                                                        <div class="div-coleta-status-PIC">
                                                                        {{ i + 1 }}. {{ duracao.duracao | secondsFormat }} 
                                                                        <b *ngIf="duracao.manual" style="font-size: smaller;">(M)</b>
                                                                        </div>
                                                                    </div>
                                                                </ng-container>
                                                                <div  style="margin-bottom: 5px;"></div>
                                                            </ng-container>
                                                        </ng-container>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                        </ng-container>
                    </td>
                </ng-container>                                                                   
          
                <tr mat-header-row *matHeaderRowDef="displayedColumnsObjsPIC"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsObjsPIC;"></tr>
                <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="objetivo-detail-row"></tr>
            </table>
            <div *ngIf="!pic?.objetivos || pic.objetivos?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                <h1>Nenhum objetivo cadastrado no PIC!</h1>
            </div>
        </div>
                  
    </mat-card-content>
</mat-card>
<div *ngIf="PEIs?.length == 0 && PICs?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
    <h1>Nenhum plano cadastrado!</h1>
</div>