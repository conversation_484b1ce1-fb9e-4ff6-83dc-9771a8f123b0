import { AuthService } from './../template/auth/auth.service';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import * as firebase from 'firebase/app';
import { auth } from 'firebase';
import { Anamnese } from './anamnese-model';

@Injectable({
  providedIn: 'root'
})

//TODO: Não permitir pacientes com o mesmo nome

export class AnamneseService {

    anamneseUrl = `${environment.API_URL}/anamnese`;
  
  public anamneses: BehaviorSubject<Anamnese[]> = 
    new BehaviorSubject<Anamnese[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    // console.log(e)
    return EMPTY;
  }

  create(anamnese: Anamnese): Observable<Anamnese>{
    // console.log(anamnese);
    return this.http.post<Anamnese>(this.anamneseUrl, anamnese).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(anamnese: Anamnese): Observable<Anamnese>{
    // console.log(anamnese);
    return this.http.put<Anamnese>(this.anamneseUrl + "/" + anamnese.id, anamnese).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Anamnese>{
    return this.http.get<Anamnese>(this.anamneseUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Anamnese[]>{
    return this.http.get<Anamnese[]>(this.anamneseUrl).pipe(
      map(obj => obj)
    );

  }

  delete(id: string): Observable<Anamnese>{
    return this.http.delete<Anamnese>(this.anamneseUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

}