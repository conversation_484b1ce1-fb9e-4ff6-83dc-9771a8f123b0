import { DominioVBMAPP } from './../dominiovbmapp/dominiovbmapp-model';
import { MarcoVBMAPP } from './../marcovbmapp/marcovbmapp-model';
import { ObjetivoVBMAPP } from './../objetivovbmapp/objetivovbmapp-model';
import { Profissional } from './../profissional/profissional-model';
import { Paciente } from './../paciente/paciente-model';
import { HabilidadeAvaliacao } from '../avaliacao/habilidade-avaliacao-model';
export class PlanoIntervencaoVBMAPP {  
    id?: string;
    idPaciente: string;
    paciente: Paciente;
    idProfissional:string;
    profissional: Profissional;
    data: Date;
    idMsAssmt: string;
    idsAvaliacoes: string [];
    id_dominio?: string;
    dominio? : DominioVBMAPP;
    marcos: MarcoVBMAPP[];
    habilidades: HabilidadeAvaliacao[];
    objetivos: ObjetivoVBMAPP[];
    ativo: boolean;
    status: boolean;
    
    constructor(){
        this.paciente = new Paciente();
        this.profissional = new Profissional(); 
    }
}