.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin: .5em;
  }
  
  .label-atrasado {
    background-color: #ff902b;
  }
  
  .label-ok {
    background-color: #27c24c;
  }
  
  .label-aviso {
    background-color: #e2b902;
  }
  
  .objetivo-element-detail {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    width: 100%;
  }
  
  tr.objetivo-detail-row {
    height: 0;
  }



@media (max-width: 700px) {
  table{
    width: 100%;
  }

  .field-plano-intervencao{
    width: 80%; 
  }

  .div-plano-intervencao{
    display: flex; 
    width: 50%;
  }

  .div-nova-coleta{
    display: flex; 
    width: 50%;
  }

  .div-etapas{
    width: 20%;
  }

  .mat-column-index {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-expand {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-etapas {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }

  .mat-column-idNome {
    flex: 0 0 100% !important;
    width: 100% !important;
    padding-left: 5px;
    text-align: justify;
    overflow-wrap: normal !important;
    word-wrap: normal !important;
  }
  .mat-column-expandedDetail{
    width: 100%;
    overflow-wrap: normal !important;
    text-align: justify;
  }

  .mat-list-item{
    width: 100%;
    overflow-wrap: normal !important;
    word-wrap: normal !important;
    height:max-content!important;
  }

  .mat-list{
    height: max-content!important;
  }

  .div-mat-line{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 100%;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
    padding: 0px !important;
  }

  .div-status-etapa{
    width: 5%;
  }

  .div-dados-etapa{
    width: 100%;
    text-align: left;
    font-size: small;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .div-dados-etapa-detail{
    width: 100%;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    hyphens: auto;
    display: inline-block;
    white-space: pre-wrap !important;
  }

  .div-coleta{
    background-color: lightgray; 
    width: 100% !important;
  }

  .div-coleta-table{
    width: inherit !important; 
    display: table;
  }

  .div-coleta-table-row{
    text-align: center; 
    width: 100% !important; 
    display: table-row;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .div-coleta-data-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-score-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-percentual-cell{
    display: table-cell;
    width: 5% !important;
    padding: 0px !important;
  }

  .div-coleta-status-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-profissional-cell{
    display: table-cell;
    width: 40% !important;
    padding: 0px !important;
  }
}

@media (min-width: 701px) {
  table{
    width: 100%;
  }

  .field-plano-intervencao{
    width: 30%; 
    padding: 10px;
  }

  .div-plano-intervencao{
    display: flex; 
    width: 70%;
  }

  .div-nova-coleta{
    display: flex; 
    width: 30%;
  }

  .div-etapas{
    width: 20%;
  }

  .mat-column-index {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-expand {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-etapas {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }

  .mat-column-idNome {
    flex: 0 0 70% !important;
    width: 70% !important;
    text-align: left;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
  }

  .div-mat-line{
    width: 100%; 
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    flex: 0 0 100%; 
    padding: 0px; 
  }

  .div-status-etapa{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 5%; 
    background-color: aqua;
  }

  .div-dados-etapa{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 100%; 
    text-align: left;
  }

  .div-dados-etapa-detail{
    width: 100%;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    hyphens: auto;
    display: inline-block;
    white-space: pre-wrap !important;
  }

  .div-coleta{
    background-color: lightgray; 
    width: 50%;
  }

  .div-coleta-table{
    display: table;
    width: 100% !important;
    table-layout: fixed !important;
  }

  .div-coleta-table-row{
    display: table-row;
    /*text-align: center; */
    width: 100% !important; 
    /*padding: 0px !important;
    table-layout: fixed !important;*/
  }

  .center{
    text-align: center;
  }

  .div-coleta-data-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #27c24c;*/
  }

  .div-coleta-score-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
    /*min-width: 10%;
    max-width: 10%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #e2b902;*/
  }

  .div-coleta-percentual-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
    /*min-width: 10%;
    max-width: 10%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #ff902b;*/
  }

  .div-coleta-status-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: aqua;*/
  }
  .div-coleta-profissional-cell{
    display: table-cell;
    width: 150px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: aqua;*/
  }

}