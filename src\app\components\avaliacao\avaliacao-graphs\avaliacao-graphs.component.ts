import { Avaliacao } from './../avaliacao-model';
import { AvaliacaoGraph } from './../avaliacao-graph-model';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { VBMAPPMilestonesAssessment } from '../../vbmappmsassmt/vbmappmsassmt-model';
import { Paciente } from '../../paciente/paciente-model';
import { Nivel } from '../../nivel/nivel-model';
import { Dominio } from '../../dominio/dominio-model';
import { Observable } from 'rxjs';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { VBMAPPMsAssmtService } from '../../vbmappmsassmt/vbmappmsassmt.service';
import { AuthService } from '../../template/auth/auth.service';
import { MarcovbmappService } from '../../marcovbmapp/marcovbmapp.service';
import { NivelService } from '../../nivel/nivel.service';
import { NivelAvalicao } from '../nivel-avaliacao-model';
import { DominioAvaliacao } from '../dominio-avaliacao-model';
import { AvaliacaoService } from '../avaliacao.service';
import { NivelAvaliacaoService } from '../nivel-avaliacao.service';
import { HabilidadeAvaliacaoService } from '../habilidade-avaliacao.service';
import { Profissional } from '../../profissional/profissional-model';
import { TipoAvaliacaoService } from '../tipo-avaliacao.service';
import { TipoAvaliacao } from '../tipo-avaliacao-model';
import { HabilidadeAvaliacao } from '../habilidade-avaliacao-model';
import { MatSelectChange } from '@angular/material/select';
import { LoadingService } from '../../../shared/service/loading.service';
import { EsdmchecklistService } from '../../esdmchecklist/esdmchecklist.service';
import { ESDMChecklist } from '../../esdmchecklist/esdmchecklist-model';
import { CompetenciaService } from '../../competencia/competencia.service';
import html2canvas from 'html2canvas';
import * as moment from 'moment';

@Component({
  selector: 'app-avaliacao-graphs',
  templateUrl: './avaliacao-graphs.component.html',
  styleUrls: ['./avaliacao-graphs.component.css']
})
export class AvaliacaoGraphsComponent implements OnInit {

  public avaliacaoMarco: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public niveis: Nivel[];
  public nivel: Nivel = new Nivel();
  
  public avaliacoes: Avaliacao[];
  public avaliacoesFiltered: any[] = [];
  public avaliacao: Avaliacao = new Avaliacao();
  public avaliacoesMarco: VBMAPPMilestonesAssessment[] = [];
  public avaliacoesESDM: ESDMChecklist[] = [];
  public niveisAvaliacao: NivelAvalicao[];
  public nivelAvaliacao: NivelAvalicao = new NivelAvalicao();
  
  public paciente: Paciente = new Paciente();
  public tiposAvaliacoes: TipoAvaliacao[] = [];
  public tiposAvaliacoesView: TipoAvaliacao[] = [];
  public tipoAvaliacao: TipoAvaliacao;
  public idTipoAVal: string;
  public dominioMap: Map<string, Dominio[]|DominioAvaliacao[]> = new Map<string, Dominio[]|DominioAvaliacao[]>();
  public dominios: Dominio[] = [];
  public habilidadesMap: [];
  public colors: any[];
  public avParcMap: Map<string, Map<string, boolean>> = new Map<string, Map<string, boolean>>();
  public avDominioParc;
  public dominioParc: boolean;
  public showPercentage: boolean;
  public showLegenda: boolean = true;

  public avaliacaoGraph: AvaliacaoGraph;

  public graph: AvaliacaoGraph = new AvaliacaoGraph();

  public selectedChartType: string = 'radar';

  public hasAccessRead: boolean;

  @Input()
  $pacienteSearch: Observable<Paciente>;

  @ViewChild('vbmappmsassmt0') private checklistBox0: MatCheckbox;
  vbmappOrEsdm: string;

  @ViewChild('meuCanvas', { static: false }) meuCanvas!: ElementRef;
  @ViewChild('legendContainer', { static: false }) legendContainer!: ElementRef;

  constructor(private avaliacaoMarcoService: VBMAPPMsAssmtService,
    private esdmchecklistService: EsdmchecklistService,
    private nivelService: NivelService,
    public authService: AuthService,
    private marcoService: MarcovbmappService,
    private competenciaService: CompetenciaService,
    private avaliacaoService: AvaliacaoService,
    private nivelAvaliacaoService: NivelAvaliacaoService,
    private habilidadeService: HabilidadeAvaliacaoService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.avParcMap.clear();
    this.colors = [
      { backgroundColor: "rgba(144, 238, 144, 0.60)",
        fill: true,
        borderColor: "rgb(144, 238, 144)",
        pointBackgroundColor: "rgb(144, 238, 144)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(144, 238, 144)" },
      { backgroundColor:"rgba(54, 162, 235, 0.50)",
        fill:true,
        borderColor:"rgb(54, 162, 235)",
        pointBackgroundColor:"rgb(54, 162, 235)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(54, 162, 235)" },
      { backgroundColor:"rgba(138,43,226,0.50)",
        fill:true,
        borderColor:"rgb(138,43,226)",
        pointBackgroundColor:"rgb(138,43,226)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(138,43,226)" },
      { backgroundColor:"rgba(218, 165, 32,0.50)",
        fill:true,
        borderColor:"rgb(218, 165, 32)",
        pointBackgroundColor:"rgb(218, 165, 32)",
        pointBorderColor:"#fff",
        pointHoverBackgroundColor:"#fff",
        pointHoverBorderColor:"rgb(218, 165, 32)" }
    ];
  
    this.loadingService.show();
  
    this.$pacienteSearch.subscribe(async paciente => {
      this.paciente = paciente;
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')

      const tipos = await this.tipoAvaliacaoService.find().toPromise()
      this.tiposAvaliacoes = tipos
        .filter(ta => ta.ativo == true)
        .sort((a, b) => {
          const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
          const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
          return nomeA.localeCompare(nomeB);
        });

      // Busca as avaliações de VB-MAPP
      const avsM = await this.avaliacaoMarcoService.findResumoByPaciente(this.paciente.id).toPromise()
      this.avaliacoesMarco = avsM;
  
      // Adiciona manualmente o idTipoAvaliacao ao VB-MAPP
      for (const [, av] of this.avaliacoesMarco.entries()) {
        (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos")?.id;
      }

      // Busca outras avaliações (exceto VB-MAPP)
      const avs = await this.avaliacaoService.findResumoByPaciente(this.paciente.id).toPromise();
      this.avaliacoes = avs.filter(a => a.ativo == true).concat(this.avaliacoesMarco.filter(a => a.status != false) as unknown as Avaliacao[]);
  
      // Busca avaliações ESDM
      const esdm = await this.esdmchecklistService.findResumoByPaciente(this.paciente.id).toPromise();
      this.avaliacoesESDM = esdm;
      
      // Adiciona manualmente o idTipoAvaliacao ao ESDM
      for (const [, av] of this.avaliacoesESDM.entries()) {
        (av as unknown as Avaliacao).idTipoAvaliacao = "" + this.tiposAvaliacoes.find(ta => ta.nome === "[ESDM] Early Start Denver Model")?.id;
      }
  
      // Combina avaliações ESDM e outras
      this.avaliacoes = this.avaliacoes.concat(this.avaliacoesESDM.filter(a => a.status != false) as unknown as Avaliacao[]);
  
      // Ordena todas as avaliações por data
      this.avaliacoes.sort((a, b) => a.data < b.data ? 1 : -1);

      // Remove tipos sem avaliações realizadas
      this.tiposAvaliacoesView = [...this.tiposAvaliacoes];
      let i = this.tiposAvaliacoesView.length;
      while (i--) {
        if (this.avaliacoes.filter(a => a.idTipoAvaliacao == this.tiposAvaliacoesView[i].id).length == 0) {
          this.tiposAvaliacoesView.splice(i, 1);
        }
      }

      this.avaliacao = this.avaliacoes[0];
      this.tipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == this.avaliacao?.idTipoAvaliacao);
      this.idTipoAVal = this.tipoAvaliacao?.id;

      await this.findAvaliacoes(this.avaliacao?.idTipoAvaliacao);

      // Organiza o gráfico após carregar todas as avaliações
      await this.organizeGraph();
      this.loadingService.hide();
    });
  }

  async findAvaliacoes(idTipoAvaliacao: string) {
    if (idTipoAvaliacao == undefined) return;

    if (idTipoAvaliacao == this.tiposAvaliacoes?.find(ta => ta.nome === ("[VB-MAPP] Avaliação de Marcos"))?.id) {
      const avsM = await this.avaliacaoMarcoService.findByPaciente(this.paciente.id).toPromise();
      this.avaliacoesFiltered = avsM.filter(avs => avs.status != false);
    } else if (idTipoAvaliacao == this.tiposAvaliacoes?.find(ta => ta.nome === ("[ESDM] Early Start Denver Model"))?.id) {
      const avsESDM = await this.esdmchecklistService.findByPaciente(this.paciente.id).toPromise();
      this.avaliacoesFiltered = avsESDM.filter(avs => avs.status != false);
    } else {
      const avs = await this.avaliacaoService.findByPacienteTipoAvaliacao(this.paciente.id, idTipoAvaliacao).toPromise(); 
      this.avaliacoesFiltered = avs.filter(a => a.ativo == true)
    }
  }

  async organizeGraph(){
    if(this.avaliacao?.idTipoAvaliacao == this.tiposAvaliacoes?.find(ta => ta.nome === ("[VB-MAPP] Avaliação de Marcos"))?.id) {
      //Carregando Níveis da Avaliação de Marco
      this.nivelService.find().subscribe(niveis => {
        this.niveis = niveis;
        this.niveis.pop();
        this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)

        //Carregando Domínios dos Níveis (TAB) da Avaliação de Marco
        niveis.forEach(nivel => {
          this.marcoService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          async () => {
            if((this.dominioMap.size == 3)){// Só calculo quando os 3 níveis foram carregados, caso contrário dá erro.
              this.graph.datasets = [];
              if(this.avaliacoes != undefined){
                await this.calculateGraphNivel(0, new Date(this.avaliacoes[0].data).toLocaleDateString('pt-BR'), this.nivel.id, true);
              }
              this.loadingService.hide();
            }
          })
        })
      })
    } else if (this.avaliacao?.idTipoAvaliacao == this.tiposAvaliacoes?.find(ta => ta.nome === ("[ESDM] Early Start Denver Model"))?.id) {
      //Carregando Níveis
      this.nivelService.find().subscribe(niveis => {
        this.niveis = niveis;
        this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)

        //Carregando Domínios dos Níveis (TAB)
          niveis.forEach(nivel => {
          this.competenciaService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          async () => {
            if((this.dominioMap.size == 4)){// Só calculo quando os 4 níveis foram carregados, caso contrário dá erro.
              this.graph.datasets = [];
              if(this.avaliacoes != undefined){
                await this.calculateGraphNivel(0, new Date(this.avaliacoes[0].data).toLocaleDateString('pt-BR'), this.nivel.id, true);
              }
              this.loadingService.hide();
            }
          })
        })
      })
    } else {
      //Carregando Níveis da Avaliação
      this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao?.idTipoAvaliacao).subscribe(niveis => {
        this.niveisAvaliacao = niveis;
        // console.log(this.niveisAvaliacao)
        this.nivelAvaliacao = this.niveisAvaliacao[0];

        //Carregando Domínios dos Níveis (TAB) da Avaliação
        niveis.forEach(nivel => {
          this.habilidadeService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          async () => {
            if((this.dominioMap.size == niveis.length)){// Só calculo quando todos os níveis forem carregados, caso contrário dá erro.
              this.graph.datasets = [];
              if(this.avaliacoes != undefined){
                await this.calculateGraphNivel(0, new Date(this.avaliacoes[0].data).toLocaleDateString('pt-BR'), this.nivelAvaliacao.id, false);
              }
              this.loadingService.hide();
            }
          })
        })
      })
    }
  }

  getTiposAvaliacoesExistentes(){
    let tipos: TipoAvaliacao[];
    tipos = [...this.tiposAvaliacoes];
    for(const [i,ta] of tipos.entries()){
      if(this.avaliacoes?.filter(a => a.idTipoAvaliacao == ta.id).length == 0){
        tipos.splice(i,1);
      }
    }
    return tipos;
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];
    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  checked(index: number): boolean{
    if (index == 0){
      return true
    } else {
      return false
    }
  }

  async doGraphCal(avIndex: number, date: string, idNivel: string, isAvaliacaoMarcoOrESDM: boolean): Promise<number[]>{
    //Map para armazenar a nota máxima (100%) que um par nível + domínio pode ter
    let niveldominioTopMap: Map<string, number> = new Map<string, number>(); 

    //Variável auxiliar para armazenar o maior valor possível de um domínio de resposta de uma habilidade
    let maxValueResposta = 0;
    let resposta: number = 0;
    
    //Variável auxiliar para armazenar o somatório de valores respondidos em uma avaliação para o par nível + domínio
    let valueRespostas: number = 0;
    let n, p, a, t;
    let ipeCalc = false;
    let csoCalc = false;
    let jogCalc = false;
    let habilidades: HabilidadeAvaliacao[];
    let data: number[] = [];
    this.dominioParc = false;
    let internalDominioParc: Map<string, boolean> = new Map<string, boolean>();
    
    const chartType = this.selectedChartType as 'radar' | 'bar' | 'horizontalBar';
    
    //Busco a data selecionada nos checklists
    if(this.vbmappOrEsdm == "vbmapp"){
      this.graph.labels = [];
      this.dominioMap.get(idNivel).forEach(dom => {
          //Recuperando as quantidades de cada tipo
        n = (this.avaliacoesFiltered[avIndex] as unknown as VBMAPPMilestonesAssessment).assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel && chklst.marco.id_dominio == dom.id && chklst.valor == 'N')).length;

        p = (this.avaliacoesFiltered[avIndex] as unknown as VBMAPPMilestonesAssessment).assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel &&  chklst.marco.id_dominio == dom.id && chklst.valor == 'P')).length;

        a = (this.avaliacoesFiltered[avIndex] as unknown as VBMAPPMilestonesAssessment).assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel &&  chklst.marco.id_dominio == dom.id && chklst.valor == 'A')).length;

        t = (this.avaliacoesFiltered[avIndex] as unknown as VBMAPPMilestonesAssessment).assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel &&  chklst.marco.id_dominio == dom.id)).length;
        
        // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
        if(this.avParcMap.size == 0){
          internalDominioParc = new Map<string, boolean>();
          if(n+p+a != t){
            internalDominioParc.set(idNivel+dom.id, true);
            this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
            this.graph.labels.push(dom.id + '*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+dom.id, false);
            this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
            this.graph.labels.push(dom.id);
          }
        } else {
          if(n+p+a != t){
            internalDominioParc.set(idNivel+dom.id, true);
            this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
            this.graph.labels.push(dom.id + '*');
            this.dominioParc = true;
          } else {
            internalDominioParc.set(idNivel+dom.id, false);
            this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
            if(this.haveAvaliacaoNivelDominioParcial(idNivel, dom.id)){
              this.graph.labels.push(dom.id + '*');
              this.dominioParc = true;
            } else {
              this.graph.labels.push(dom.id);
            }
          }
        }

        data.push( (isNaN(Math.round( ((n*0 + p*0.5 + a*1)/((n+p+a)*1)) * 100))?0:Math.round( ((n*0 + p*0.5 + a*1)/((n+p+a)*1)) * 100)) );
      })
      if(this.isAvaliacaoParcial(this.avaliacoesFiltered[avIndex].id)){
        this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR') + "*", data: data});
      } else {
        this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR'), data: data});
      }
      
      this.organizeDatasets(chartType);

      this.graph.labels = [...this.graph.labels];
      this.graph.options = {
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            }
          }]
        },
        responsive: true,
        legend: {
          position: 'top',
        },
        tooltips: {
          enabled: true,
          mode: 'single',
          callbacks: {
            title: function (tooltipItems, data) {
              return " " + data.labels[tooltipItems[0].index];
            },
            label: function (tooltipItems, data) {
              return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
            }
          }
        },
        layout: {
          padding: {
            left: 20,
            right: 20,
            top: 20,
            bottom: 20 
          }
      },
      };
      
      if (chartType === 'radar') {
        this.graph.options.scales = {} as any;
        this.graph.options.scale = {
          ticks: {
            min: 0,
            max: 100,
            stepSize: 10
          },
        } as any;
      }

      if (chartType === 'horizontalBar') {
        this.graph.options.scales = {
          xAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            },
          }],
          yAxes: [{
            ticks: {
              beginAtZero: true
            }
          }]
        } as any;
        if (this.showPercentage) {
          this.graph.options.plugins = {
            datalabels: {
              display: true,
              align: 'center',
              anchor: 'center',
              formatter: (value, ctx) => {
                return value + '%'; 
              },
              color: '#000',
              font: {
                size: 6,
                weight: 'bold'
              }
            }
          } as any;
        } else {
          this.graph.options.plugins = {
            datalabels: {
              display: false
            }
          } as any;
        }
      }
      
      if (chartType === 'bar') {
        this.graph.options.scales = {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            }
          }]
        } as any;
        if (this.showPercentage) {
          this.graph.options.plugins = {
            datalabels: {
              display: true,
              align: 'center',
              anchor: 'center',
              formatter: (value, ctx) => {
                return value + '%'; 
              },
              color: '#000',
              font: {
                weight: 'bold',
                size: 6 // Reduzindo o tamanho da fonte para ficar dentro da barra
              }
            }
          } as any;
        } else {
          this.graph.options.plugins = {
            datalabels: {
              display: false
            }
          } as any;
        }
      }
      
      this.loadingService.hide();
    } else if (this.vbmappOrEsdm == "esdm") {
      this.graph.labels = [];
      this.dominioMap.get(idNivel).forEach(dom => {
        if( (dom.id == 'IPE' || dom.id == 'IPA' || dom.id == 'IPV' || dom.id == 'IPH' || dom.id == 'IPT') && ipeCalc == false ) {
          ipeCalc = true;
  
          //Recuperando as quantidades de cada tipo
          n = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
                (chklst.competencia.id_dominio == 'IPE' || 
                chklst.competencia.id_dominio == 'IPA' || 
                chklst.competencia.id_dominio == 'IPV' || 
                chklst.competencia.id_dominio == 'IPH' || 
                chklst.competencia.id_dominio == 'IPT')
              && chklst.valor == 'N')).length;
            
          p = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT')
            && chklst.valor == 'P')).length
            
          a = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT')
            && chklst.valor == 'A')).length
  
          t = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel == idNivel && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT'))).length;
          
          // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação 
          if(this.avParcMap.size == 0){
            internalDominioParc = new Map<string, boolean>();
            if(n+p+a != t){
              internalDominioParc.set(idNivel+'IPE', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('IPE*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'IPE', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('IPE');
            }
          } else {
            if(n+p+a != t) {
              internalDominioParc.set(idNivel+'IPE', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('IPE*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'IPE', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'IPE')) {
                this.graph.labels.push('IPE*');
                this.dominioParc = true;
              } else {
                this.graph.labels.push('IPE');
              }
            }
          }
  
          data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
        } else if((dom.id == 'CSO' || dom.id == 'CSA' || dom.id == 'CSP' || dom.id == 'CAP') && csoCalc == false ) {
          csoCalc = true;
  
          //Recuperando as quantidades de cada tipo
          n = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
                (chklst.competencia.id_dominio == 'CSO' || 
                chklst.competencia.id_dominio == 'CSA' || 
                chklst.competencia.id_dominio == 'CSP' || 
                chklst.competencia.id_dominio == 'CAP')
              && chklst.valor == 'N')).length;
            
          p = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'CSO' || 
                chklst.competencia.id_dominio == 'CSA' || 
                chklst.competencia.id_dominio == 'CSP' || 
                chklst.competencia.id_dominio == 'CAP')
            && chklst.valor == 'P')).length
            
          a = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'CSO' || 
                chklst.competencia.id_dominio == 'CSA' || 
                chklst.competencia.id_dominio == 'CSP' || 
                chklst.competencia.id_dominio == 'CAP')
            && chklst.valor == 'A')).length
  
          t = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel == idNivel && 
              (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP'))).length;
  
          // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
          if(this.avParcMap.size == 0){
            internalDominioParc = new Map<string, boolean>();
            if(n+p+a != t){
              internalDominioParc.set(idNivel+'CSO', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('CSO*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'CSO', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('CSO');
            }
          } else {
            if(n+p+a != t) {
              internalDominioParc.set(idNivel+'CSO', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('CSO*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'CSO', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'CSO')) {
                this.graph.labels.push('CSO*');
                this.dominioParc = true;
              } else {
                this.graph.labels.push('CSO');
              }
            }
          }
          
          data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
        } else if((dom.id == 'JOG' || dom.id == 'JOR' || dom.id == 'JOI') && jogCalc == false ) {
          jogCalc = true;
  
          //Recuperando as quantidades de cada tipo
          n = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
                (chklst.competencia.id_dominio == 'JOG' || 
                chklst.competencia.id_dominio == 'JOR' || 
                chklst.competencia.id_dominio == 'JOI')
              && chklst.valor == 'N')).length;
            
          p = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'JOG' || 
                chklst.competencia.id_dominio == 'JOR' || 
                chklst.competencia.id_dominio == 'JOI')
            && chklst.valor == 'P')).length
            
          a = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'JOG' || 
                chklst.competencia.id_dominio == 'JOR' || 
                chklst.competencia.id_dominio == 'JOI')
            && chklst.valor == 'A')).length
  
          t = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'JOG' || 
                chklst.competencia.id_dominio == 'JOR' || 
                chklst.competencia.id_dominio == 'JOI'))).length
  
          // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
          if(this.avParcMap.size == 0){
            internalDominioParc = new Map<string, boolean>();
            if(n+p+a != t){
              internalDominioParc.set(idNivel+'JOG', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('JOG*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'JOG', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('JOG');
            }
          } else {
            if(n+p+a != t) {
              internalDominioParc.set(idNivel+'JOG', true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push('JOG*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel+'JOG', false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              if(this.haveAvaliacaoNivelDominioParcial(idNivel, 'JOG')) {
                this.graph.labels.push('JOG*');
                this.dominioParc = true;
              } else {
                this.graph.labels.push('JOG');
              }
            }
          }
  
          data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
  
        } else if ( (dom.id != 'IPE' && dom.id != 'IPA' && dom.id != 'IPV' && dom.id != 'IPH' && dom.id != 'IPT') 
                    && (dom.id != 'CSO' && dom.id != 'CSA' && dom.id != 'CSP' && dom.id != 'CAP')
                    && (dom.id != 'JOG' && dom.id != 'JOR' && dom.id != 'JOI') ) { //Caso não seja um domínio a ser consolidado
    
          //Recuperando as quantidades de cada tipo
          n = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel && chklst.competencia.id_dominio == dom.id && chklst.valor == 'N')).length;
            
          p = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id && chklst.valor == 'P')).length
            
          a = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst => 
            (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id && chklst.valor == 'A')).length
  
          t = (this.avaliacoesFiltered[avIndex] as unknown as ESDMChecklist).checklist.filter(chklst =>
            (chklst.competencia.id_nivel==idNivel &&  chklst.competencia.id_dominio == dom.id)).length
  
          // Adiciono um label e verifico se o Domínio possui habilidades não observadas na avaliação
          if(this.avParcMap.size == 0){
            internalDominioParc = new Map<string, boolean>();
            if(n+p+a != t){
              internalDominioParc.set(idNivel + dom.id, true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push(dom.id + '*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel + dom.id, false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push(dom.id);
            }
          } else {
            if(n+p+a != t) {
              internalDominioParc.set(idNivel + dom.id, true);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              this.graph.labels.push(dom.id + '*');
              this.dominioParc = true;
            } else {
              internalDominioParc.set(idNivel + dom.id, false);
              this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
              if(this.haveAvaliacaoNivelDominioParcial(idNivel, dom.id)) {
                this.graph.labels.push(dom.id + '*');
                this.dominioParc = true;
              } else {
                this.graph.labels.push(dom.id);
              }
            }
          }
    
          data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
        }
      })
      if(this.isAvaliacaoParcial(this.avaliacoesFiltered[avIndex].id)){
        this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR') + "*", data: data});
      } else {
        this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR'), data: data});
      }

      this.organizeDatasets(chartType);

      this.graph.labels = [...this.graph.labels];
      this.graph.options = {
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            }
          }]
        },
        responsive: true,
        legend: {
          position: 'top',
        },
        tooltips: {
          enabled: true,
          mode: 'single',
          callbacks: {
            title: function (tooltipItems, data) {
              return " " + data.labels[tooltipItems[0].index];
            },
            label: function (tooltipItems, data) {
              return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
            }
          }
        },
        layout: {
          padding: {
            left: 20,
            right: 20,
            top: 20,
            bottom: 20 
          }
      },
      };
      
      if (chartType === 'radar') {
        this.graph.options.scales = {} as any;
        this.graph.options.scale = {
          ticks: {
            min: 0,
            max: 100,
            stepSize: 10
          }
        } as any;
      }

      if (chartType === 'horizontalBar') {
        this.graph.options.scales = {
          xAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            },
          }],
          yAxes: [{
            ticks: {
              beginAtZero: true
            }
          }]
        } as any;
        if (this.showPercentage) {
          this.graph.options.plugins = {
            datalabels: {
              display: true,
              align: 'center',
              anchor: 'center',
              formatter: (value, ctx) => {
                return value + '%'; 
              },
              color: '#000',
              font: {
                size: 6,
                weight: 'bold'
              }
            }
          } as any;
        } else {
          this.graph.options.plugins = {
            datalabels: {
              display: false
            }
          } as any;
        }
      }
      
      if (chartType === 'bar') {
        this.graph.options.scales = {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              max: 100,
              stepSize: 10
            }
          }]
        } as any;
        if (this.showPercentage) {
          this.graph.options.plugins = {
            datalabels: {
              display: true,
              align: 'center',
              anchor: 'center',
              formatter: (value, ctx) => {
                return value + '%'; 
              },
              color: '#000',
              font: {
                weight: 'bold',
                size: 6 // Reduzindo o tamanho da fonte para ficar dentro da barra
              }
            }
          } as any;
        } else {
          this.graph.options.plugins = {
            datalabels: {
              display: false
            }
          } as any;
        }
      }

      this.loadingService.hide();
    } else {
      (await this.habilidadeService.findByTipoAvaliacao(this.avaliacoesFiltered[avIndex].idTipoAvaliacao)).subscribe(habilidades => {
        this.graph.labels = [];
        if((this.dominioMap.size == this.niveisAvaliacao.length)){
          this.dominioMap.get(idNivel).forEach(dom => {
            this.avDominioParc = false;

            valueRespostas = 0;
            maxValueResposta = 0;

            // Filtra habilidades que possuem resposta "NA"
            const habilidadesFiltradas = habilidades.filter(hab => 
              hab.idNivelAvaliacao == idNivel && 
              hab.idDominioAvaliacao == dom.id &&
              this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + hab.id)?.valor !== "0.1"
            ).entries(); 

            //Navego pelas habilidades do par nível + domínio corrente
            for(const [, h] of habilidadesFiltradas) {

              //Inicializo o map de valores máximos (100%) do nível + domínio de avaliação com zero, caso seja a primeira habilidade do nível + domínio
              if(niveldominioTopMap.get("" + h.idNivelAvaliacao + h.idDominioAvaliacao) == undefined){
                niveldominioTopMap.set("" + h.idNivelAvaliacao + h.idDominioAvaliacao, 0);
              }

              //Recupero o valor máximo do domínio de resposta da habilidade corrente
              maxValueResposta = h.dominioResposta.filter(dr => dr.valor != undefined).sort(function(a, b) {
                if (a.valor === 0.1) return 1;
                if (b.valor === 0.1) return -1;
                if( a.valor < b.valor) {
                  return 1;
                } else {
                  return -1;
                } 
              })[0].valor;

              resposta = parseFloat(this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + h.id)?.valor == undefined
                                    || this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + h.id)?.valor == "" ? "0" : this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + h.id)?.valor);
              valueRespostas += resposta;

              //Incremento o valor do par nível + domínio com o valor mais alto do domínio de resposta da habilidade corrente. Faço isso apenas se a resposta teve valor, foi observada.
              if(this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + h.id)?.valor != undefined
                  && this.avaliacoesFiltered[avIndex].respostasHabilidades.find(r => "" + r.idHabilidadeAvaliacao == "" + h.id)?.valor != ""){
                niveldominioTopMap.set("" + h.idNivelAvaliacao + "" + h.idDominioAvaliacao, 
                  maxValueResposta + niveldominioTopMap.get("" + h.idNivelAvaliacao + "" + h.idDominioAvaliacao));
              } else {
                this.avDominioParc = true
              }
            }
            
            if(this.avParcMap.size == 0){
              internalDominioParc = new Map<string, boolean>();
              if(this.avDominioParc){
                internalDominioParc.set(idNivel+dom.sigla, true);
                this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
                if(this.tipoAvaliacao.nome == 'ABLLS-R'){
                  if(chartType == 'radar'){
                  this.graph.labels.push(dom.sigla + '*');
                  } else {
                    this.graph.labels.push(dom.nome + '*');
                  }
                } else if (this.tipoAvaliacao.id == "6") {
                  this.graph.labels.push(dom.nome + '*');
                } else {
                  this.graph.labels.push(dom.sigla + '*');
                }
                this.dominioParc = true;
              } else {
                internalDominioParc.set(idNivel+dom.sigla, false);
                this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
                if(this.tipoAvaliacao.nome == 'ABLLS-R'){
                  if(chartType == 'radar'){
                  this.graph.labels.push(dom.sigla);
                  } else {
                    this.graph.labels.push(dom.nome);
                  }
                } else if (this.tipoAvaliacao.id == "6") {
                  this.graph.labels.push(dom.nome);
                } else {
                  this.graph.labels.push(dom.sigla);
                }
              }
            } else {
              if(this.avDominioParc){
                internalDominioParc.set(idNivel+dom.sigla, true);
                this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
                if(this.tipoAvaliacao.nome == 'ABLLS-R'){
                  if(chartType == 'radar'){
                  this.graph.labels.push(dom.sigla + '*');
                  } else {
                    this.graph.labels.push(dom.nome + '*');
                  }
                } else if (this.tipoAvaliacao.id == "6") {
                  this.graph.labels.push(dom.nome + '*');
                } else {
                  this.graph.labels.push(dom.sigla + '*');
                }
                this.dominioParc = true;
              } else {
                internalDominioParc.set(idNivel+dom.sigla, false);
                this.avParcMap.set(this.avaliacoesFiltered[avIndex].id, internalDominioParc);
                if(this.haveAvaliacaoNivelDominioParcial(idNivel, dom.sigla)){
                  if(this.tipoAvaliacao.nome == 'ABLLS-R'){
                    if(chartType == 'radar'){
                    this.graph.labels.push(dom.sigla + '*');
                    } else {
                      this.graph.labels.push(dom.nome + '*');
                    }
                  } else if (this.tipoAvaliacao.id == "6") {
                    this.graph.labels.push(dom.nome + '*');
                  } else {
                    this.graph.labels.push(dom.sigla + '*');
                  }
                  this.dominioParc = true;
                } else {
                  if(this.tipoAvaliacao.nome == 'ABLLS-R'){
                    if(chartType == 'radar'){
                    this.graph.labels.push(dom.sigla);
                    } else {
                      this.graph.labels.push(dom.nome);
                    }
                  } else if (this.tipoAvaliacao.id == "6") {
                    this.graph.labels.push(dom.nome);
                  } else {
                    this.graph.labels.push(dom.sigla);
                  }
                }
              }
            }

            data.push( isNaN(Math.round( (valueRespostas/niveldominioTopMap.get("" + idNivel + dom.id)) * 100)) ? 0 : Math.round((valueRespostas/niveldominioTopMap.get("" + idNivel + dom.id)) * 100) );
          });
        }

        if(this.isAvaliacaoParcial(this.avaliacoesFiltered[avIndex].id)){
          this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR') + "*", data: data});
        } else {
          this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[avIndex].data).toLocaleDateString('pt-BR'), data: data});
        }

        this.organizeDatasets(chartType);

        this.graph.labels = [...this.graph.labels];
        this.graph.options = {
          responsive: true,
          scales: {
            yAxes: [{
              ticks: {
                beginAtZero: true,
                max: 100,
                stepSize: 10
              }
            }]
          },
          legend: {
            position: 'top',
          },
          tooltips: {
            enabled: true,
            mode: 'single',
            callbacks: {
              title: function (tooltipItems, data) {
                return " " + data.labels[tooltipItems[0].index];
              },
              label: function (tooltipItems, data) {
                return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
              }
            }
          },
          layout: {
            padding: {
              left: 20,
              right: 20,
              top: 20,
              bottom: 20 
            }
          },
        };

        if (chartType === 'radar') {
          this.graph.options.scales = {} as any;
          this.graph.options.scale = {
            ticks: {
              min: 0,
              max: 100,
              stepSize: 10
            }
          } as any;
        }

        if (chartType === 'horizontalBar') {
          this.graph.options.scales = {
            xAxes: [{
              ticks: {
                beginAtZero: true,
                max: 100,
                stepSize: 10
              },
            }],
            yAxes: [{
              ticks: {
                beginAtZero: true
              }
            }]
          } as any;
          if (this.showPercentage) {
            this.graph.options.plugins = {
              datalabels: {
                display: true,
                align: 'center',
                anchor: 'center',
                formatter: (value, ctx) => {
                  return value + '%'; 
                },
                color: '#000',
                font: {
                  size: 6,
                  weight: 'bold'
                }
              }
            } as any;
          } else {
            this.graph.options.plugins = {
              datalabels: {
                display: false
              }
            } as any;
          }
        }
        
        if (chartType === 'bar') {
          this.graph.options.scales = {
            yAxes: [{
              ticks: {
                beginAtZero: true,
                max: 100,
                stepSize: 10
              }
            }]
          } as any;
          if (this.showPercentage) {
            this.graph.options.plugins = {
              datalabels: {
                display: true,
                align: 'center',
                anchor: 'center',
                formatter: (value, ctx) => {
                  return value + '%'; 
                },
                color: '#000',
                font: {
                  weight: 'bold',
                  size: 6 // Reduzindo o tamanho da fonte para ficar dentro da barra
                }
              }
            } as any;
          } else {
            this.graph.options.plugins = {
              datalabels: {
                display: false
              }
            } as any;
          }
        }
        
        this.loadingService.hide();
      })
    }
    
    return data;
  }

  async calculateGraphNivel(avIndex: number, date: string, idNivel: string, isAvaliacaoMarcoOrESDM: boolean){
    this.loadingService.show();
    try {
      if (avIndex == 0) {
        const chartType = this.selectedChartType as 'radar' | 'bar' | 'horizontalBar';
        this.graph.chartType = chartType;
        this.graph.labels = [];
      }
      let data: number[] = [];

      await this.dominiosLegenda(idNivel);
      
      //Solicito o cálculo do nível desejado
      data = await this.doGraphCal(avIndex, date, idNivel, isAvaliacaoMarcoOrESDM);
      this.loadingService.hide();

    } catch (error) {
      console.log("Error", error);
    }
  }
  
  async updateChart() {
    let i;
    let selected: string[] = [];
    this.loadingService.show();
    this.graph.datasets.forEach(dataset => {
      selected.push(dataset.label.replace("*", ""));
    })

    for(const[,label] of selected.entries()){
      await this.removeMsAssmtFromGraph(label);
      i = this.avaliacoesFiltered.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == label);
      if(this.isAvaliacaoMarcoOrESDM()){
        await this.calculateGraphNivel(i, label, this.nivel.id, this.isAvaliacaoMarcoOrESDM());
      } else {
        await this.calculateGraphNivel(i, label, this.nivelAvaliacao.id, this.isAvaliacaoMarcoOrESDM());
      }
    }
  }

  async filterAvaliacoes(event:MatSelectChange){
    this.loadingService.show();
    
    this.tipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == event.value);
    this.idTipoAVal = this.tipoAvaliacao.id;
    if(this.idTipoAVal != undefined) {
      await this.findAvaliacoes(this.idTipoAVal);
      this.avaliacao = this.avaliacoesFiltered[0];
      this.graph.datasets = [];
      this.avParcMap.clear();
      await this.setPrimeiraAvaliacao();
    }
  }

  async removeAvaliacaoFromGraph(data: string){
    let datasets;
    let dtAvaliacao: string;
    let i;
    let index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data);
    if(index == -1){
      index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data + "*");
    }

    i = this.avaliacoesFiltered.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == data);
    this.avParcMap.delete(this.avaliacoesFiltered[i].id);

    this.graph.datasets.splice(index, 1);
    datasets = [...this.graph.datasets];
    datasets.forEach(dataset => {
      dtAvaliacao = dataset.label;
      index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(dtAvaliacao);
      this.graph.datasets.splice(index, 1);
      i = this.avaliacoesFiltered.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == dtAvaliacao.replace("*", ""));
      if (this.isAvaliacaoMarcoOrESDM()){
        this.calculateGraphNivel(i, dataset.label, this.nivel.id, this.isAvaliacaoMarcoOrESDM());
      } else {
        this.calculateGraphNivel(i, dataset.label, this.nivelAvaliacao.id, this.isAvaliacaoMarcoOrESDM());
      }
    })

    if(datasets.length == 0){
      this.loadingService.hide();
    }
  }

  async removeMsAssmtFromGraph(data: string){
    let datasets;
    let dtAvaliacao: string;
    let i;
    let index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data);
    if(index == -1){
      index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data + "*");
    }

    i = this.avaliacoesFiltered.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == data);
    this.avParcMap.delete(this.avaliacoesFiltered[i].id);

    this.graph.datasets.splice(index, 1);
    datasets = [...this.graph.datasets];
  }

  isAvaliacaoParcial(idAvaliacao: string){
    let internalDominioParc = this.avParcMap.get(idAvaliacao);

    for (const value of internalDominioParc.values()) {
      if (value) {
        return true;
      }
    }
    return false;
  
  }

  haveAvaliacaoParcial() {
    for(const [idAvaliacao,v] of this.avParcMap.entries()){
      for (const value of this.avParcMap.get(idAvaliacao)) {
        if (value) {
          return true;
        }
      }
      return false;
    }

  }

  haveAvaliacaoNivelDominioParcial(idNivel: string, idDominio: string) {
    let internalDominioParc: Map<string, boolean> = new Map<string, boolean>();
    let parcial: boolean = false;
    
    for(const [idAvaliacao,v] of this.avParcMap.entries()){
      internalDominioParc = this.avParcMap.get(idAvaliacao);
      if (internalDominioParc.get(idNivel + idDominio)){
        parcial = true;
      }
    }
    return parcial;

  }

  isAvaliacaoMarcoOrESDM(){
    if (this.tiposAvaliacoes.find(ta => ta.id == this.idTipoAVal)?.nome == ("[VB-MAPP] Avaliação de Marcos")){
      this.vbmappOrEsdm = "vbmapp"
      return true
    } else if (this.tiposAvaliacoes.find(ta => ta.id == this.idTipoAVal)?.nome == ("[ESDM] Early Start Denver Model")){
      this.vbmappOrEsdm = "esdm"
      return true
    } else {
      this.vbmappOrEsdm = "none"
      return false
    }
  }

  setMsAssessment(event:MatCheckboxChange){
    this.loadingService.show();
    let isAvaliacaoMarco = false;
    let isAvaliacaoESDM = false;

    isAvaliacaoMarco  = this.tipoAvaliacao.id == this.tiposAvaliacoes.find(ta => ta.nome == "[VB-MAPP] Avaliação de Marcos").id;
    isAvaliacaoESDM  = this.tipoAvaliacao.id == this.tiposAvaliacoes.find(ta => ta.nome == "[ESDM] Early Start Denver Model").id;

    if(event.checked == false){
      this.removeAvaliacaoFromGraph( event.source.name )

    } else {
        if(isAvaliacaoMarco){
          this.vbmappOrEsdm = "vbmapp"
          this.calculateGraphNivel(parseInt(event.source.id), event.source.name, this.nivel.id, isAvaliacaoMarco);
        } else if (isAvaliacaoESDM){
          this.vbmappOrEsdm = "esdm"
          this.calculateGraphNivel(parseInt(event.source.id), event.source.name, this.nivel.id, isAvaliacaoESDM);
        } else {
          this.vbmappOrEsdm = "none"
          this.calculateGraphNivel(parseInt(event.source.id), event.source.name, this.nivelAvaliacao.id, false);
        }
    }
  }

  setNivel(){
    let i;
    let selected: string[] = [];
    this.loadingService.show();
    this.graph.datasets.forEach(dataset => {
      selected.push(dataset.label.replace("*", ""));
    })

    for(const[,label] of selected.entries()){
      this.removeMsAssmtFromGraph(label);
      i = this.avaliacoesFiltered.findIndex(av => new Date(av.data).toLocaleDateString('pt-BR') == label);
      if(this.isAvaliacaoMarcoOrESDM()){
        this.calculateGraphNivel(i,label, this.nivel.id, this.isAvaliacaoMarcoOrESDM());
      } else {
        this.calculateGraphNivel(i,label, this.nivelAvaliacao.id, this.isAvaliacaoMarcoOrESDM());
      }
    }
  }
  
  setPrimeiraAvaliacao(){
    let isAvaliacaoMarco = false;
    let isAvaliacaoEsdm = false;
    let data: number[] = [];
    this.dominioMap = new Map<string, Dominio[]|DominioAvaliacao[]>();

    isAvaliacaoMarco  = this.tipoAvaliacao?.id == this.tiposAvaliacoes?.find(ta => ta.nome == ("[VB-MAPP] Avaliação de Marcos")).id;
    isAvaliacaoEsdm  = this.tipoAvaliacao?.id == this.tiposAvaliacoes?.find(ta => ta.nome == ("[ESDM] Early Start Denver Model")).id;
    
    //Incluindo a primeira avaliação no gráfico ainda sem dados
    if(this.avaliacoesFiltered.length > 0){
      this.graph.datasets.push({ label:  new Date(this.avaliacoesFiltered[0].data).toLocaleDateString('pt-BR'), data: undefined});
    }

    //Recuperando as avaliações apresentadas no gráfico
    let selected: string[] = [];
    this.graph.datasets.forEach(dataset => {
      // console.log(dataset.label)
      selected.push(dataset.label);
    })

    if(isAvaliacaoMarco) {
      this.vbmappOrEsdm = "vbmapp"
      //Carregando Níveis da Avaliação de Marco
      this.nivelService.find().subscribe(async niveis => {
        this.niveis = niveis;
        this.niveis.pop();
        this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)
        
        //Carregando Domínios dos Níveis (TAB) da Avaliação de Marco
        niveis.forEach(nivel => {
          this.marcoService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          () => {
            if((this.dominioMap.size == 3)){// Só calculo quando os 3 níveis foram carregados, caso contrário dá erro.
              
              selected.forEach(label =>{
                this.removeMsAssmtFromGraph(label);
                  for(const [i, av] of this.avaliacoesFiltered.entries()) {
                    if ( new Date(av.data).toLocaleDateString('pt-BR') == label){
                      this.calculateGraphNivel(i, label, this.nivel.id, isAvaliacaoMarco);
                    }
                  }
              })
            }
          })
        })
      })
    } else if (isAvaliacaoEsdm) {
      this.vbmappOrEsdm = "esdm"
      //Carregando Níveis
      this.nivelService.find().subscribe(niveis => {
        this.niveis = niveis;
        this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)

        //Carregando Domínios dos Níveis (TAB)
        niveis.forEach(nivel => {
          this.competenciaService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          async () => {
            if((this.dominioMap.size == 4)){// Só calculo quando os 4 níveis foram carregados, caso contrário dá erro.
              selected.forEach(label =>{
                this.removeMsAssmtFromGraph(label);
              })
              if(this.avaliacoes != undefined){
                await this.calculateGraphNivel(0, new Date(this.avaliacoes[0].data).toLocaleDateString('pt-BR'), this.nivel.id, isAvaliacaoEsdm);
              }
            }
          })
        })
      })
    } else {
      this.vbmappOrEsdm = "none"
      //Carregando Níveis da Avaliação
      this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao?.idTipoAvaliacao).subscribe(async niveis => {
        this.niveisAvaliacao = niveis;
        this.nivelAvaliacao = this.niveisAvaliacao[0];

        //Carregando Domínios dos Níveis (TAB) da Avaliação
        niveis.forEach(nivel => {

          this.habilidadeService.findDominiosByNivel(nivel.id).subscribe(dominios => {
            this.dominioMap.set(nivel.id,dominios)
          },
          (err) => console.error(err),
          () => {
            if((this.dominioMap.size == niveis.length)){// Só calculo quando todos os níveis forem carregados, caso contrário dá erro.
              
              selected.forEach(label =>{
                this.removeMsAssmtFromGraph(label);
                  for(const [i, av] of this.avaliacoesFiltered.entries()) {
                    if ( new Date(av.data).toLocaleDateString('pt-BR') == label){
                      this.calculateGraphNivel(i, label, this.nivelAvaliacao.id, false);
                    }
                  }
              })
            }
          })
        })
      })
    }
  }

  organizeDatasets(chartType: string) {
    if(chartType == 'horizontalBar'){
      this.graph.datasets.sort((a, b) => {
        const cleanLabelA = a.label.replace('*', '').trim();
        const cleanLabelB = b.label.replace('*', '').trim();
      
        // Converte o formato 'dd/mm/yyyy' em um objeto Date
        const [dayA, monthA, yearA] = cleanLabelA.split('/');
        const [dayB, monthB, yearB] = cleanLabelB.split('/');
      
        const dateA = new Date(+yearA, +monthA - 1, +dayA); // monthA - 1 porque o mês no objeto Date é baseado em zero (0=Janeiro)
        const dateB = new Date(+yearB, +monthB - 1, +dayB);
      
        return dateB.getTime() - dateA.getTime();  // Ordena em ordem decrescente
      });
    } else {
      this.graph.datasets.sort((a, b) => {
        const cleanLabelA = a.label.replace('*', '').trim();
        const cleanLabelB = b.label.replace('*', '').trim();
      
        // Converte o formato 'dd/mm/yyyy' em um objeto Date
        const [dayA, monthA, yearA] = cleanLabelA.split('/');
        const [dayB, monthB, yearB] = cleanLabelB.split('/');
      
        const dateA = new Date(+yearA, +monthA - 1, +dayA); // monthA - 1 porque o mês no objeto Date é baseado em zero (0=Janeiro)
        const dateB = new Date(+yearB, +monthB - 1, +dayB);
      
        return dateA.getTime() - dateB.getTime();  // Ordena em ordem decrescente
      });
    }
  }
  
  async downloadChart() {
    if (!this.meuCanvas) return;
  
    try {
      this.loadingService.show();
  
      const chartCanvas = this.meuCanvas.nativeElement;
      const finalCanvas = document.createElement('canvas');
      const ctx = finalCanvas.getContext('2d');
  
      let legendCanvas: HTMLCanvasElement | null = null;
      
      if (this.showLegenda) {
        legendCanvas = await html2canvas(this.legendContainer.nativeElement, { scale: 1 });
      }
  
      // Obtém as imagens do gráfico e da legenda
      const chartImage = await this.loadImage(chartCanvas.toDataURL('image/png'));
      const legendImage = legendCanvas ? await this.loadImage(legendCanvas.toDataURL('image/png')) : null;
  
      // Define as dimensões do canvas final
      finalCanvas.width = Math.max(chartCanvas.width, legendCanvas?.width ? legendCanvas.width + 100 : 0);
      finalCanvas.height = chartCanvas.height + (legendCanvas ? legendCanvas.height + 50 : 50);
  
      // Fundo branco
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
  
      // Centraliza as imagens no canvas
      const chartX = (finalCanvas.width - chartCanvas.width) / 2;
      ctx.drawImage(chartImage, chartX, 10);
  
      if (legendImage) {
        const legendX = (finalCanvas.width - legendCanvas.width) / 2;
        ctx.drawImage(legendImage, legendX, chartCanvas.height + 50);
      }
  
      // Baixa a imagem final
      this.downloadCanvas(finalCanvas, 'grafico_de_evolucao.png');
    } catch (error) {
      console.error('Erro ao baixar o gráfico:', error);
    } finally {
      this.loadingService.hide();
    }
  }
  
  // Função auxiliar para carregar imagens
  private loadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  }
  
  // Função auxiliar para baixar o canvas
  private downloadCanvas(canvas: HTMLCanvasElement, filename: string) {
    const link = document.createElement('a');
    link.href = canvas.toDataURL('image/png');
    link.download = filename;
    link.click();
  }  

  togglePercentage() {
    this.updateChart(); // Atualiza o gráfico após a modificação
  }

  async dominiosLegenda (idNivel: any) {
    let dominiosPermitidos;

    //Formatação da legenda
    if (this.vbmappOrEsdm == "esdm") {
      this.competenciaService.findDominiosByNivel(idNivel).subscribe(dominios => {
        if (idNivel == 'N1') {
          dominiosPermitidos = ['CRE', 'CEX', 'CSO', 'IMI', 'COG', 'JOG', 'MFI', 'MGR', 'COM'];
        } else if (idNivel == 'N2') {
          dominiosPermitidos = ['CRE', 'CEX', 'CAC', 'CSO', 'IMI', 'COG', 'JOG', 'MFI', 'MGR'];
        } else {
          dominiosPermitidos = ['CRE', 'CEX', 'CSO', 'COG', 'JOG', 'MFI', 'MGR'];
        }

        this.dominios = dominios.filter(dom => dominiosPermitidos.includes(dom.id));

        // Adicionando CSO e IPE no local desejado
        if (idNivel == 'N2') {
          this.dominios.push({ id: 'CSO', id_dominio_pai: 'CSO', nome: 'Competências Sociais', ordem: 31 });
          this.dominios.push({ id: 'JOG', id_dominio_pai: 'JOG', nome: 'Jogo', ordem: 71 });
        } else if (idNivel == 'N3') {
          this.dominios.push({ id: 'CSO', id_dominio_pai: 'CSO', nome: 'Competências Sociais', ordem: 21 });
        }

        this.dominios.push({ id: 'IPE', id_dominio_pai: 'IPE', nome: 'Independência Pessoal', ordem: 120 });

        this.dominios.sort((a, b) => a.ordem - b.ordem);
      })
    } else if (this.vbmappOrEsdm == "vbmapp") {
      this.marcoService.findDominiosByNivel(idNivel).subscribe(dominios => {
        this.dominios = dominios;
      })
    } else {
      this.habilidadeService.findDominiosByNivel(idNivel).subscribe(dominios => {
        this.dominios = dominios;
      })
    }
  }

}
