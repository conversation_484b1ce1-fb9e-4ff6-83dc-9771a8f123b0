<div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 100%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">Plano de Intervenção Comportamental - Gráfico de Evolução</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle">Paciente: {{ paciente.nome }}</p>
        </div>
        <div style="text-align: center; width: 33%;">
            <p class="subtitle">Profissional: {{ profissional.nome }}</p>
        </div>
        <div style="text-align: right; width: 33%;">
            <p class="subtitle">Data: {{ pic.data | date: 'dd/MM/yyyy' }}</p>
        </div>
    </div>
    
    <div style="display: flex; flex-wrap: wrap; justify-content: space-around;">
        <div *ngFor="let objetivo of pic.objetivos" style="width: 48%; margin-bottom: 20px;">
            <div *ngIf="graphs[objetivo.id] && graphs[objetivo.id].datasets && graphs[objetivo.id].datasets.length > 0">
                <canvas baseChart 
                    [datasets]="graphs[objetivo.id].datasets" 
                    [labels]="graphs[objetivo.id].labels" 
                    [options]="graphs[objetivo.id].options"
                    [chartType]="graphs[objetivo.id].chartType"
                    [colors]="colors" 
                    style="width: 100%; height: 450px;">
                </canvas>
            </div>
            <div *ngIf="!graphs[objetivo.id] || graphs[objetivo.id].datasets == 0 || graphs[objetivo.id].datasets.length == 0" class="no-data-message" style="width: 50%;" class="no-data-message">
                <div>
                    <strong>Nenhuma coleta foi realizada para este PEI.</strong>
                </div>
            </div>
        </div>
    </div>    
    
</div>