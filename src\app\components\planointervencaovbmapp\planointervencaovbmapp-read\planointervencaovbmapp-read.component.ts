import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { Profissional } from './../../profissional/profissional-model';
import { PlanointervencaovbmappService } from './../planointervencaovbmapp.service';
import { PacienteService } from './../../paciente/paciente.service';
import { AuthService } from './../../template/auth/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { forkJoin, Observable } from 'rxjs';
import { Paciente } from './../../paciente/paciente-model';
import { Estimulo } from './../../estimulo/estimulo-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { Component, OnInit, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { PlanoIntervencaoService } from '../../planointervencao/planointervencao.service';
import { Avaliacao } from '../../avaliacao/avaliacao-model';
import { Objetivo } from '../../objetivo/objetivo-model';
import { Etapa } from '../../etapa/etapa-model';
import { ConfirmDialogCustomComponent, ConfirmDialogCustomData } from '../../template/confirm-dialog-custom/confirm-dialog-custom.component';

@Component({
  selector: 'app-planointervencaovbmapp-read',
  templateUrl: './planointervencaovbmapp-read.component.html',
  styleUrls: ['./planointervencaovbmapp-read.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})

//TODO: Acertar expand no HTML para imprimir na tela de acordo com o Tipo de Coleta
export class PlanointervencaovbmappReadComponent implements OnInit {
  public planointervencao;
  
  public idPaciente: string;

  public planosintervencao: any[] = [];
  public planosintervencaoESDM: any[] = [];

  // public vbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();

  @Input()
  $pacienteSearch: Observable<Paciente>;
  
  @Input()
  $idVbmappPlanSearch: string;

  paciente: Paciente = new Paciente();
  public tiposAvaliacoes: TipoAvaliacao[] = [];

  datasourceObjetivos = new MatTableDataSource();
  datasourceMarcos = new MatTableDataSource();

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessRead: boolean;
  public hasAccessUpdateColeta: boolean;

  // displayedColumnsObjs = ['index', 'expand', 'etapas', 'idNome']

  // displayedColumnsObjs = ['index', 'expand', 'nomeObj', 'dominio', 'estimulos']
  displayedColumnsObjs = ['index', 'expand', 'etapas', 'nomeObj', 'habilidades', 'estimulos', 'tipoSuporte', 'tipoColeta']
  objetivosOrdenados: any;

  constructor(private planointervencaoService: PlanointervencaovbmappService,
    private PlanoIntervencaoESDMService: PlanoIntervencaoService,
    // private vbmappMsAssmtService: VBMAPPMsAssmtService,
    private pacienteService: PacienteService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    public authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private loadingService: LoadingService) { }

  public isTableExpanded = false;

  ngOnInit(): void {
    this.loadingService.show();
    this.idPaciente = this.route.snapshot.paramMap.get('id');
  
    // Carregar Tipos de Avaliações e Paciente de forma assíncrona
    forkJoin({
      tiposAvaliacoes: this.tipoAvaliacaoService.find(),
      paciente: this.pacienteService.findById(this.idPaciente),
      planosIntervencaoVBMAPP: this.planointervencaoService.findResumoByPaciente(this.idPaciente),
      planosIntervencaoESDM: this.PlanoIntervencaoESDMService.findResumoByPaciente(this.idPaciente)
    }).subscribe(async ({ tiposAvaliacoes, paciente, planosIntervencaoVBMAPP, planosIntervencaoESDM }) => {
      // Filtrar os tipos de avaliação ativos
      this.tiposAvaliacoes = tiposAvaliacoes.filter(ta => ta.ativo);
  
      // Adicionar o `idTipoAvaliacao` para avaliações ESDM
      const tipoAvaliacaoESDM = this.tiposAvaliacoes.find(ta => ta.nome === "[ESDM] Early Start Denver Model");
      if (tipoAvaliacaoESDM) {
        for (const av of planosIntervencaoESDM) {
          (av as unknown as Avaliacao).idTipoAvaliacao = tipoAvaliacaoESDM.id.toString();
        }
      } else {
        console.error("Tipo de Avaliação '[ESDM] Early Start Denver Model' não encontrado.");
      }
  
      this.paciente = paciente;

      //Verifico se o usuário tem permissão 
      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create');
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','update');
      this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','delete');
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','read');
      this.hasAccessUpdateColeta = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Coleta de Dados','update');
  
      // Filtrar e combinar planos de intervenção
      this.planosintervencao = [
        ...planosIntervencaoVBMAPP.filter(p => p.status !== false),
        ...planosIntervencaoESDM.filter(p => p.status !== false)
      ];
  
      // Ordenar planos por data (decrescente)
      this.planosintervencao.sort((a, b) => {
        const dateA = new Date(a.data);
        const dateB = new Date(b.data);
        return dateB.getTime() - dateA.getTime();
      });
  
      // Definir o plano de intervenção mais recente ou o selecionado
      if (this.planosintervencao.length > 0) {
        const lastPlanointervencao = this.$idVbmappPlanSearch 
          ? this.planosintervencao.find(chklst => chklst.id === this.$idVbmappPlanSearch)
          : this.planosintervencao[0];

        await this.findPlanoSelecionado(lastPlanointervencao.id);
        
        this.planointervencao?.habilidades?.forEach(h => {
          h.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == h.idTipoAvaliacao)?.nome
        })

        //Ordeno as habillidades e objetivos do plano
        await this.ordenarObjetivos();
        await this.ordenarHabilidades();

        // Configurar os objetivos do plano atual
        this.datasourceObjetivos.data = this.objetivosOrdenados;
      }

      // console.log(this.planointervencao.objetivos)
  
      this.loadingService.hide();
    }, error => {
      console.error("Erro ao carregar os dados", error);
      this.loadingService.hide();
    });
  }  

  async findPlanoSelecionado(idPlanoSelecionado) {
    this.planointervencao = await this.planointervencaoService.findById(idPlanoSelecionado).toPromise();

    if (!this.planointervencao) {
      this.planointervencao = await this.PlanoIntervencaoESDMService.findById(idPlanoSelecionado).toPromise();
      this.planointervencao.idTipoAvaliacao = 5;
    }

    if (this.planointervencao?.ativo == undefined) {
      this.planointervencao.ativo = true;
      this.save();
    }

    let idx = this.planosintervencao?.findIndex(p => p.id === idPlanoSelecionado);
    this.planosintervencao[idx] = this.planointervencao;
  }

  async ordenarObjetivos() {
    this.objetivosOrdenados = this.planointervencao?.objetivos;
    
    this.objetivosOrdenados?.forEach(obj => {
      if (!obj?.habilidades?.length) return;
      obj.habilidades[0].nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == obj.habilidades[0].idTipoAvaliacao)?.nome;
    });
    
    this.objetivosOrdenados.sort((a, b) => {
      const getNomeTipoAvaliacao = (obj) => obj.habilidades?.length ? obj.habilidades[0].nomeTipoAvaliacao.replace(/\[.*?\]\s*/, '') || "" : "";
      const getTipoId = (obj) => obj?.habilidades ? obj?.habilidades[0]?.idTipoAvaliacao == "5" : false;
    
      let nomeTipoAvaliacaoA = getNomeTipoAvaliacao(a);
      let nomeTipoAvaliacaoB = getNomeTipoAvaliacao(b);

      const tipoIdAESDM = getTipoId(a);
      const tipoIdBESDM = getTipoId(b);
    
      // Ajusta nome do ESDM para garantir ordenação correta sem empurrá-lo para o final
      if (a.idTipoAvaliacao == "5") {
        nomeTipoAvaliacaoA = "Early Start Denver Model";
      }
      if (b.idTipoAvaliacao == "5") {
        nomeTipoAvaliacaoB = "Early Start Denver Model";
      }
    
      // Ordena pelo nome do tipo de avaliação (alfabética)
      const compareTipoAvaliacao = nomeTipoAvaliacaoA.localeCompare(nomeTipoAvaliacaoB);
      if (compareTipoAvaliacao !== 0) return compareTipoAvaliacao;

      // Ordenação para objetivos ESDM
      if (a.idTipoAvaliacao == "5" || b.idTipoAvaliacao == "5" || tipoIdAESDM || tipoIdBESDM) {
        const dominioA = a.dominio?.ordem || a.habilidades[0]?.dominio?.ordem || Infinity;
        const dominioB = b.dominio?.ordem || b.habilidades[0]?.dominio?.ordem || Infinity;
        
        if (dominioA !== dominioB) {
          return dominioA - dominioB; // Ordena por domínio
        }
  
        const nivelA = parseInt(a.nivel?.id.replace(/\D/g, ''), 10) || parseInt(a.habilidades[0]?.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        const nivelB = parseInt(b.nivel?.id.replace(/\D/g, ''), 10) || parseInt(b.habilidades[0]?.nivel?.id.replace(/\D/g, ''), 10) || Infinity;
        
        if (nivelA !== nivelB) {
          return nivelA - nivelB; // Ordena por nível
        }
        
        let idA: number;
        if (!tipoIdAESDM) {
          idA = parseInt(a.id.replace(/\D/g, ''), 10) || parseInt(a.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        } else {
          idA = parseInt(a.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        }

        let idB: number;
        if (!tipoIdBESDM) {
          idB = parseInt(b.id.replace(/\D/g, ''), 10) || parseInt(b.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        } else {
          idB = parseInt(b.habilidades[0]?.id.replace(/\D/g, ''), 10) || Infinity;
        }
  
        return idA - idB; // Ordena pelo número final do ID
      }
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID do dominio da primeira habilidade (numérico)
      const idDominioA = a.habilidades?.length ? a.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      const idDominioB = b.habilidades?.length ? b.habilidades[0].idDominioAvaliacao || Infinity : Infinity;
      
      if (idDominioA !== idDominioB) return idDominioA - idDominioB;
      
      // Se os tipos de avaliação forem iguais, e o dominio for igual, ordena pelo ID do nivel da primeira habilidade (numérico)
      const idNivelA = a.habilidades?.length ? a.habilidades[0].idNivelAvaliacao || Infinity : Infinity;
      const idNivelB = b.habilidades?.length ? b.habilidades[0].idNivelAvaliacao || Infinity : Infinity;

      if (idNivelA !== idNivelB) return idNivelA - idNivelB;
      
      // Se os tipos de avaliação forem iguais, ordena pelo ID da primeira habilidade (numérico)
      const idHabilidadeA = a.habilidades?.length ? parseInt(a.habilidades[0].id, 10) || Infinity : Infinity;
      const idHabilidadeB = b.habilidades?.length ? parseInt(b.habilidades[0].id, 10) || Infinity : Infinity;
    
      return idHabilidadeA - idHabilidadeB;
    });
  }

  async ordenarHabilidades() {
    if (!this.planointervencao?.habilidades?.length) return;
  
    // Preenche o nomeTipoAvaliacao se não estiver preenchido
    this.planointervencao.habilidades.forEach(hab => {
      if (!hab.nomeTipoAvaliacao && hab.idTipoAvaliacao) {
        hab.nomeTipoAvaliacao = this.tiposAvaliacoes.find(ta => ta.id == hab.idTipoAvaliacao)?.nome;
      }
    });
  
    this.planointervencao.habilidades.sort((a, b) => {
      const getNomeTipoAvaliacao = (hab) =>
        hab.nomeTipoAvaliacao?.replace(/\[.*?\]\s*/, '') || '';
  
      let nomeA = getNomeTipoAvaliacao(a);
      let nomeB = getNomeTipoAvaliacao(b);
  
      // Corrige nome do ESDM, se necessário
      if (a.idTipoAvaliacao === "5") nomeA = "Early Start Denver Model";
      if (b.idTipoAvaliacao === "5") nomeB = "Early Start Denver Model";
  
      const compareTipoAvaliacao = nomeA.localeCompare(nomeB);
      if (compareTipoAvaliacao !== 0) return compareTipoAvaliacao;
  
      // Se forem do tipo ESDM, segue regras específicas
      if (a.idTipoAvaliacao === "5" || b.idTipoAvaliacao === "5") {
        const dominioOrdemA = a.dominio?.ordem ?? Infinity;
        const dominioOrdemB = b.dominio?.ordem ?? Infinity;
  
        if (dominioOrdemA !== dominioOrdemB) return dominioOrdemA - dominioOrdemB;
  
        const nivelA = parseInt(a.nivel?.id?.replace(/\D/g, ''), 10) || Infinity;
        const nivelB = parseInt(b.nivel?.id?.replace(/\D/g, ''), 10) || Infinity;
  
        if (nivelA !== nivelB) return nivelA - nivelB;
  
        const idA = parseInt(a.id, 10) || Infinity;
        const idB = parseInt(b.id, 10) || Infinity;
  
        return idA - idB;
      }
  
      // Para outros tipos de avaliação: domínio > nível > id
      const idDominioA = a.idDominioAvaliacao ?? Infinity;
      const idDominioB = b.idDominioAvaliacao ?? Infinity;
  
      if (idDominioA !== idDominioB) return idDominioA - idDominioB;
  
      const idNivelA = a.idNivelAvaliacao ?? Infinity;
      const idNivelB = b.idNivelAvaliacao ?? Infinity;
  
      if (idNivelA !== idNivelB) return idNivelA - idNivelB;
  
      const idA = parseInt(a.id, 10) || Infinity;
      const idB = parseInt(b.id, 10) || Infinity;
  
      return idA - idB;
    });
  
    // Atualiza referência para Angular detectar
    this.planointervencao.habilidades = [...this.planointervencao.habilidades];
  } 

  async setObjetivosPlano(){
    await this.findPlanoSelecionado(this.planointervencao.id)
    await this.ordenarHabilidades();
    await this.ordenarObjetivos();
    this.datasourceObjetivos.data = this.objetivosOrdenados;
    this.setMarcosPlano();
  }

  setMarcosPlano(){
    this.datasourceMarcos.data = this.planointervencao?.marcos;
  }

  getMarcoIdDominio(idDominio: string){
    //console.log(idDominio)
    //console.log(this.planointervencao.marcos)
    if(this.planointervencao?.marcos){
      return this.planointervencao?.marcos?.find(marco => marco.id_dominio == idDominio).id;
    }
  }
 
  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    return funcoes;
  }

  toggleTableRow(row: any){
    row.isExpanded = !row.isExpanded;
  }

  changeEstimuloStatus(estimulo: Estimulo, idObjetivo: string){
    let obj: ObjetivoVBMAPP;
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreate){
      let objAdq: boolean;
      if(this.hasAccessUpdateColeta){
        if(estimulo.status == undefined || estimulo.status == 'Não adquirido'){
          estimulo.status = "Adquirido"
        } else {
          estimulo.status = "Não adquirido"
        }
        //Verifico se todos os estímulos foram adquiridos e, caso postivo, coloco o objetivo como adquirido
        //obj = this.planointervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
        obj = this.planointervencao?.objetivos?.find(objetivo => objetivo.id == idObjetivo);
        //console.log(etapa.id.substr(0, etapa.id.lastIndexOf("E")));
        //console.log(obj.id);
        if (obj.estimulos.find(e => e.status != "Adquirido") == undefined){
          obj.status = "Adquirido";
        }  else {
          obj.status = "Não adquirido";
        }
        this.save();  
      }
    } 
  }

  changeTipoSuporteEstimuloStatus(estimulo: Estimulo, idObjetivo: string, idTipoSuporte: string){
    let obj: ObjetivoVBMAPP;
    let tipoSuporte: TipoSuporte;
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreate){
      let objAdq: boolean;
      if(this.hasAccessUpdateColeta){
        if(estimulo.status == undefined || estimulo.status == 'Não adquirido'){
          estimulo.status = "Adquirido"
        } else {
          estimulo.status = "Não adquirido"
        }
        //obj = this.planointervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
        obj = this.planointervencao?.objetivos?.find(objetivo => objetivo.id == idObjetivo);
        tipoSuporte = obj.tiposSuporte?.find(tipo => tipo.id == idTipoSuporte);
        
        //Verifico se todos os estímulos ativos foram adquiridos e, caso postivo, coloco o tipo de suporte como adquirido
        if (tipoSuporte.estimulos.find(e => e.ativo == true && e.status != "Adquirido") == undefined){
        tipoSuporte.status = "Adquirido";
        }  else {
        tipoSuporte.status = "Não adquirido";
        }

        //Verifico se todos os tipos de suporte foram adquiridos e, caso postivo, coloco o objetivo como adquirido
        if (obj.tiposSuporte?.find(t => t.status != "Adquirido") == undefined){
          obj.status = "Adquirido";
        }  else {
          obj.status = "Não adquirido";
        }
        this.save(); 
      }
    }
  }

  getNomeTipoAvaliacao(h: any){
    // console.log(idTipoAvaliacao)
    if(h == undefined){
      return '';
    } else {
      if (h.idTipoAvaliacao == 5){
        return '[ESDM] - ' + h.id_nivel + ' - ' + h.dominio.nome + ' - ' + h.id.substr(4);
      } else {
        return this.tiposAvaliacoes?.find(ta => ta.id == h.idTipoAvaliacao)?.nome + ' - ' + h.sigla;
      }
    }
  }

  getNomeTipoDominio(d: any, n: any, id: any){
    if(d == undefined){
      return '';
    } else {
      return "[ESDM]" + " - " + n.id + " - " + d.nome + " - " + id.substr(4)
    }
  }

  percentualEstimulos(row: ObjetivoVBMAPP){
    return this.countEstimulosAdquiridos(row) / this.countEstimulos(row)
  }

  countEstimulos(row: ObjetivoVBMAPP){
    //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
    return row.estimulos.length
  }

  countEstimulosAdquiridos(row: ObjetivoVBMAPP){
    return row.estimulos.filter(e => (e.status=='Adquirido')).length
  }

  changeTipoSuporteStatus(tipoSuporte: TipoSuporte, idObjetivo: string){
    let obj: ObjetivoVBMAPP;
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreate){
      let objAdq: boolean;
      if(this.hasAccessUpdateColeta){  
        if(tipoSuporte.status == undefined || tipoSuporte.status == 'Não adquirido'){
          tipoSuporte.status = "Adquirido"
        } else {
          tipoSuporte.status = "Não adquirido"
        }

        //Verifico se todos os tipos de suporte foram adquiridos e, caso postivo, coloco o objetivo como adquirido
        obj = this.planointervencao?.objetivos?.find(objetivo => objetivo.id == idObjetivo);
        if (obj.tiposSuporte?.find(t => t.status != "Adquirido") == undefined){
          obj.status = "Adquirido";
        }  else {
          obj.status = "Não adquirido";
        }
        this.save(); 
      }
    }
  }   
 
  percentualTiposSuporte(row: ObjetivoVBMAPP){
    return this.countTiposSuporteAdquiridos(row) / this.countTiposSuporte(row)
  }
 
  countTiposSuporte(row: ObjetivoVBMAPP){
    //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
    if(row.tiposSuporte == undefined){
      return 0;
    } else {
      return row.tiposSuporte?.length
    }
  }
 
  countTiposSuporteAdquiridos(row: ObjetivoVBMAPP){
    if(row.tiposSuporte == undefined){
      return 0;
    } else {
      return row.tiposSuporte?.filter(t => (t != undefined && t.status=='Adquirido')).length
    }
  }

  percentualEtapas(row: Objetivo){
    if(row){
      return this.countEtapasAdquiridas(row) / this.countEtapas(row)
    } else {
      return 0;
    }
  }

  countEtapas(row: Objetivo){
    if(row.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.length
    }
  }

  countEtapasAdquiridas(row: Objetivo){
    if(row.etapa == undefined){
      return 0;
    } else {
      return row.etapa?.filter(e => (e?.status == 'Adquirida')).length
    }
  }

  changeEtapaStatus(etapa: Etapa){
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de Plano de Intervenção','create')){
      let obj: Objetivo;
      if(etapa.status == undefined || etapa.status == 'Não adquirida'){
        etapa.status = "Adquirida"
      } else {
        etapa.status = "Não adquirida"
      }
      //Verifico se todas as etapas foram adquiridas e, caso postivo, coloco o objetivo como adquirido
      obj = this.planointervencao?.objetivos?.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
      if (obj.etapa.find(e => e.status != "Adquirida") == undefined){
        obj.status = "Adquirido";
      }  else {
        obj.status = "Não adquirido";
      }
      this.save();
    }
  }

  save(): void{
    this.loadingService.show();
    if(this.planointervencao?.idTipoAvaliacao != 5){
      if(this.planointervencao.id == undefined){
        this.planointervencaoService.create(this.planointervencao).subscribe((id) => {
          this.planointervencao.id = id;
          //console.log(id)
          //this.planointervencaoService.showMessage('Plano de intervenção criado com sucesso!');
          //this.router.navigate(['/paciente/' + id]);
          this.loadingService.hide();
        });
      } else {
        this.planointervencaoService.update(this.planointervencao).subscribe((paciente) => {
          //this.planointervencaoService.showMessage('Plano de intervenção alterado com sucesso!');
          //this.router.navigate(['/paciente/' + paciente.id]);
          this.loadingService.hide();
        });
      } 
    } else {
      this.PlanoIntervencaoESDMService.update(this.planointervencao).subscribe((paciente) => {
      });
      this.loadingService.hide();
    }
  }

  saveToPDF(){
    if (this.planointervencao?.idTipoAvaliacao != 5) {
      this.router.navigate([]).then(result => {
        window.open('vbmapp_planointervencao/pdf/' + this.planointervencao.id + "?hasFullView=true",'_blank');
      })
    } else {
      this.router.navigate([]).then(result => {
        window.open('planointervencao/pdf/' + this.planointervencao.id + "?hasFullView=true",'_blank');
      })
    }
  }

  saveToPDFResumido(){
    if (this.planointervencao?.idTipoAvaliacao != 5) {
      this.router.navigate([]).then(result => {
        window.open('vbmapp_planointervencaoresumido/pdf/' + this.planointervencao.id + "?hasFullView=true",'_blank');
      })
    } else {
      this.router.navigate([]).then(result => {
        window.open('planointervencao/pdf/resumo/' + this.planointervencao.id + "?hasFullView=true",'_blank');
      })
    }
  }

  edit(){
    if (this.planointervencao?.idTipoAvaliacao != 5) {
      this.router.navigate(['/vbmapp_planointervencao/create', {   
        idPaciente: this.idPaciente, 
        idVbmappPlan: this.planointervencao.id
      }])
    } else {
      this.router.navigate(['/planointervencao/create', {   
        idPaciente: this.idPaciente, 
        idPlanoIntervencao: this.planointervencao.id
      }])
    }
  }

  graph(){
    if (this.planointervencao?.idTipoAvaliacao != 5) {
      window.open('vbmapp_planointervencao/evolucao/' + this.planointervencao.id + "?hasFullView=true",'_blank');
    } else {
      this.router.navigate([]).then(result => {
        window.open('planointervencao/evolucao/' + this.planointervencao.id + "?hasFullView=true",'_blank');
      })
    }
  }

  add(){
    this.loadingService.reset();
    const data: ConfirmDialogCustomData = {
      message: 'Você deseja criar um novo PEI ou adicionar/editar um objetivo do PEI atual?',
      options: [
        'Criar um novo PEI',
        'Adicionar objetivos ao PEI atual',
        'Cancelar'
      ]
    };

    const dialogRef = this.dialog.open(ConfirmDialogCustomComponent, {
      width: 'auto',
      data
    });

    dialogRef.afterClosed().subscribe((selected: string) => {
      switch (selected) {
        case 'Criar um novo PEI':
          this.router.navigate(['/vbmapp_planointervencao/create', {   
            idPaciente: this.idPaciente
          }])
          break;

        case 'Adicionar objetivos ao PEI atual':
          this.edit();
          break;

        default:
          break;
      }
    });
  }

  async delete(id:string){
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: 'auto',
      data: {
        msg: 'Tem certeza que deseja excluir o PEI?'
      }
    });

    await dialogRef.afterClosed().subscribe(async result => {
      if(result){  

        this.loadingService.show();
        this.planointervencao = this.planosintervencao.find(p => p.id == id);
        this.planointervencao.status = false;
        if (this.planointervencao?.idTipoAvaliacao != 5){
          this.planointervencaoService.update(this.planointervencao).subscribe(
            () => {
              this.planointervencaoService.showMessage('PEI excluído!', false);
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload';  
              this.router.navigate(['/paciente/'+this.paciente.id, {   
                tab: 'vbmapp_plano'
              }])
              this.loadingService.hide();
            }
          )
        } else {
          this.PlanoIntervencaoESDMService.update(this.planointervencao).subscribe(
            () => {
              this.planointervencaoService.showMessage('PEI excluído!', false);
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = 'reload';  
              this.router.navigate(['/paciente/'+this.paciente.id, {   
                tab: 'vbmapp_plano'
              }])
              this.loadingService.hide();
            }
          )
        }
      }
    })  
  }

}
