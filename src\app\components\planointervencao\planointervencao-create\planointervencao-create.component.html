<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="column" fxLayoutAlign="space-between stretch"> 
            <form #ngForm>
                <mat-form-field  style="width: 15%;  padding-left: 20px;">
                    <mat-label>Data</mat-label>
                    <input class="input" matInput placeholder="Data" 
                        [(ngModel)]="planointervencao.data" name="data"
                        (dateChange)="setChecklistPorData()"
                        [matDatepicker]="picker" required>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="data.invalid">Data é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 15%;  padding-left: 20px;">
                    <mat-label>Checklist</mat-label>
                    <mat-select placeholder="Checklist" 
                        [(ngModel)]="esdmchecklist" disabled
                        name="checklist" (selectionChange) = "setChecklist()">
                        <mat-option *ngFor="let checklist of esdmchecklists" [value]="checklist" >
                            {{checklist.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding: 20px;">
                    <mat-label>Profissional</mat-label>
                    <mat-select placeholder="Profissional" 
                        [(ngModel)]="planointervencao.idProfissional"
                        name="profissionalFC" required
                        (selectionChange) = "setProfissional($event)">
                        <mat-option></mat-option>
                        <ng-container *ngFor="let profissional of profissionaisDoPaciente">
                            <mat-option [value]="profissional.id">
                                {{profissional.nome}}
                            </mat-option>
                        </ng-container>
                    </mat-select>
                    <mat-error *ngIf="profissionalFC.invalid">Profissional é obrigatório.</mat-error>  
                </mat-form-field>
                <mat-form-field  style="width: 0%; visibility: hidden;">
                    <input class="input" matInput placeholder="Paciente" 
                        [(ngModel)]="planointervencao.idPaciente" name="paciente">
                </mat-form-field>
                            
                <button mat-mini-fab alt="Sair da edição" color="primary" style="margin: 15px;" (click)="exitEdit()">
                    <mat-icon>arrow_back</mat-icon>
                </button>
            </form>  

        </div>
        
        <div class="mat-elevation-z0" style="padding-bottom: 30px;">
            <!--strong>Objetivos do Plano de Intervenção</strong-->
            <!--a mat-raised-button href="javascript:void()" (click)="toggleTableRows()" color="primary">Toggle Rows</a-->
            <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows> 
                <!-- Index Column --> 
                <ng-container matColumnDef="index">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row">{{ planointervencao.objetivos.indexOf(row) + 1 }}</td>
                </ng-container>

                <!-- Expand Column --> 
                <ng-container matColumnDef="expand">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row">
                        <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                            <mat-icon>{{ row.isExpanded ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                        </a>
                    </td>
                </ng-container>

                <!-- etapasSum Column --> 
                <ng-container matColumnDef="etapas">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row">
                        <div [ngClass]="percentualEtapas(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEtapas(row) < 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        <div class="label label-ok" style="width: 20%;" *ngIf="percentualEtapas(row) == 1">
                            <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                        </div>
                        
                    </td>
                </ng-container>

                <!-- Id Column --> 
                <ng-container matColumnDef="idNome">
                    <th mat-header-cell *matHeaderCellDef ></th>
                    <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.id}} - {{row.nome}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <!--ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
                </ng-container--> 

                <!-- Action Column -->
                <ng-container matColumnDef="action" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30"></th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <a style="padding-left: 10px; color: red; cursor: pointer;" (click)="deleteObjetivo(planointervencao.objetivos.indexOf(row))">
                            <mat-icon>
                                cancel
                            </mat-icon>
                        </a>
                        <a style="padding-left: 10px; color: orange; cursor: pointer;" (click)="editarObjetivo(planointervencao.objetivos.indexOf(row))">
                            <mat-icon>
                                edit
                            </mat-icon>
                        </a>    
                    </td>
                </ng-container> 
                <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjs.length">
              
                      <div class="row student-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                        <mat-list>
                          <div style="padding: 0px 0px 10px 0px;">{{element.descricao_plano}}</div>
                          <mat-list-item *ngFor="let etapa of element.etapa">
                            <div matline *ngIf="etapa.status=='Adquirida'" 
                                style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                <div style="width: 5%;">
                                    <a style="cursor: pointer; color: green;"  (click)="changeEtapaStatus(etapa)">
                                        <mat-icon>
                                            check
                                        </mat-icon>
                                    </a>
                                </div>
                                <div style="width: 90%; text-align: left;">
                                    <small>{{etapa.id}} - {{etapa.nome}}</small>
                                </div>
                            </div>
                            <div matline *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" 
                                style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                <div style="width: 5%;">
                                    <a style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatus(etapa)">
                                        <mat-icon>
                                            miscellaneous_services
                                        </mat-icon>
                                    </a>
                                </div>
                                <div style="width: 90%; flex: 0 0 90%; text-align: left;">
                                    <small>{{etapa.id}} - {{etapa.nome}}</small>
                                </div>
                            </div>
                          </mat-list-item>
                        </mat-list>
                      </div>
              
                    </td>
                  </ng-container>
          
              <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
              <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="student-detail-row"></tr>
            </table>
        </div>        
        <div class="mat-elevation-z0"
            *ngIf="hasAccessCreate">
            <strong>Checklist - {{ esdmchecklist.data | date: 'dd/MM/yyyy' }}</strong> 
            <br>
            <small>Adicione os objetivos a serem trabalhados neste plano selecionando as competências abaixo.</small>
            <small>
                <mat-form-field  [disabled]="checkFiltro" style="width: 10%; padding-left: 5px;">
                    <mat-label>Nível</mat-label>
                    <mat-select placeholder="Nivel" 
                        [(ngModel)]="nivel"
                        [disabled]="checkFiltro" 
                        name="nivel" (selectionChange) = "setDominios()">
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 15%; padding-left: 5px;">
                    <mat-label>Domínio</mat-label>
                    <mat-select placeholder="Dominio" 
                        [(ngModel)]="dominio"
                        [disabled]="checkFiltro" 
                        name="dominio" (selectionChange) = "filterChecklist()">
                        <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                            {{dominio.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-checkbox style="padding-left: 5px;" [(ngModel)]="checkFiltro" (change)="filtrarObjetivos()">
                    Apenas habilidades não consistentes 
                </mat-checkbox>
            </small>
            <table mat-table [dataSource]="chklstcompView"> 
                <!-- Id Column --> 
                <ng-container matColumnDef="id">
                    <th mat-header-cell *matHeaderCellDef >Id</th>
                    <td mat-cell *matCellDef="let row">{{row.competencia.id}}</td>
                </ng-container>
        
                <!-- Nome Column -->
                <ng-container matColumnDef="nome" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">{{row.competencia.nome}}</td>
                </ng-container> 

                <!-- Status Column -->
                <ng-container matColumnDef="status" fxFlex="30">
                    <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                    <td mat-cell *matCellDef="let row" fxFlex="30">
                        <div class="label label-N" *ngIf="row.valor == 'N'"><small>Difícil obter</small></div>
                        <div class="label label-P" *ngIf="row.valor == 'P'"><small>Mais ou menos</small></div>
                        <div class="label label-A" *ngIf="row.valor == 'A'"><small>Consistente</small></div>
                        <div class="label label-X" *ngIf="row.valor == 'X' || row.valor == undefined" disabled><small>Não observado</small></div>
                    </td>
                </ng-container> 
          
              <tr mat-header-row *matHeaderRowDef="displayedColumnsChkLst"></tr>
              <tr mat-row [ngClass]="[msIsSelected(row.competencia) ? 'msDisabled' : 'msEnabled']" *matRowDef="let row; columns: displayedColumnsChkLst;" (click)="addObjetivo(row.competencia.id)" style="cursor: pointer;"></tr>
            </table>
        </div>        
    </mat-card-content>
</mat-card>