import { LoadingService } from './../../../shared/service/loading.service';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import jsPDF from 'jspdf';
import { AvaliacaoService } from '../../avaliacao/avaliacao.service';
import { DominioAvaliacaoService } from '../../avaliacao/dominio-avaliacao.service';
import { HabilidadeAvaliacaoService } from '../../avaliacao/habilidade-avaliacao.service';
import { NivelAvaliacaoService } from '../../avaliacao/nivel-avaliacao.service';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { PacienteService } from '../../paciente/paciente.service';
import { ProfissionalService } from '../../profissional/profissional.service';
import { AuthService } from '../../template/auth/auth.service';
import { FirebaseUserModel } from '../../template/auth/user-model';
import { Avaliacao } from '../../avaliacao/avaliacao-model';
import { DominioAvaliacao } from '../../avaliacao/dominio-avaliacao-model';
import { DominioRespostaHabilidadeAvaliacao } from '../../avaliacao/dominio-resposta-habilidade-avaliacao-model';
import { HabilidadeAvaliacao } from '../../avaliacao/habilidade-avaliacao-model';
import { NivelAvalicao } from '../../avaliacao/nivel-avaliacao-model';
import { RespostaHabilidadeAvaliacao } from '../../avaliacao/resposta-habilidade-avaliacao-model';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { Paciente } from '../../paciente/paciente-model';
import { Profissional } from '../../profissional/profissional-model';

@Component({
  selector: 'app-avaliacoespdf',
  templateUrl: './avaliacoespdf.component.html',
  styleUrls: ['./avaliacoespdf.component.css']
})
export class AvaliacoespdfComponent implements OnInit {

  public tipoAvaliacao: TipoAvaliacao;
  public loading: boolean = false;
  public paciente: Paciente = new Paciente();
  public profissional: Profissional = new Profissional();
  
  public nivel: NivelAvalicao = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();
  public niveis: NivelAvalicao[] = [];
  public dominios: DominioAvaliacao[] = [];

  public respostasHabilidadesView: RespostaHabilidadeAvaliacao[] = [];
  public habilidadesView: HabilidadeAvaliacao[] = [];
  public habilidades: HabilidadeAvaliacao[] = [];
  public domoniosResposta: DominioRespostaHabilidadeAvaliacao[] = [];

  public displayedColumnsMsAssmt = ['id', 'nome', 'status']
  public displayedColumns: string[] = ['id', 'nome']

  dominiosPorNivel: { [key: number]: any[] } = {}; // Objeto para armazenar domínios por nível
  
  idOrganizacao: any;

  @Input()
  idAvaliacao: string;

  public avaliacao: Avaliacao;
  
  @Output() pdfGenerated: EventEmitter<void> = new EventEmitter<void>();
  
  @ViewChild('avaliacaoPDF', { static: false })
  el: ElementRef;

  constructor(
    private tipoAvaliacaoService: TipoAvaliacaoService,
    private avaliacaoService: AvaliacaoService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    private pacienteService: PacienteService,
    private profissionalService: ProfissionalService,
    private habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    public authService: AuthService,
    private cdr: ChangeDetectorRef,
    private loadingService: LoadingService
  ) { }

  async ngOnInit(): Promise<void> {
    try {
      this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
      const idAvaliacao = this.idAvaliacao;
      this.avaliacao = await this.avaliacaoService.findById(idAvaliacao).toPromise();
      this.paciente = await this.pacienteService.findById(this.avaliacao.idPaciente).toPromise();
      this.tipoAvaliacao = await this.tipoAvaliacaoService.findById(this.avaliacao.idTipoAvaliacao).toPromise();
  
      const n = await this.nivelAvaliacaoService.findByTipoAvaliacao(this.avaliacao.idTipoAvaliacao).toPromise();
      this.niveis = n;
      this.nivel = n[0];
  
      await await this.carregarDominiosPorNivel();

      const habs = await (await this.habilidadeAvaliacaoService.findByTipoAvaliacao(this.tipoAvaliacao.id)).toPromise();
      this.habilidades = habs;
  
      this.profissional = await this.profissionalService.findById(this.avaliacao.idProfissional).toPromise();

    } catch (error) {
      console.error('Erro ao buscar dados:', error);
    }
    this.generatePDF();
  }
    
  async generatePDF(){
    // Adicione um timeout para garantir que o DOM seja atualizado
    setTimeout(async () => {
      let pdf = new jsPDF('l', 'pt', 'a4', true);
      await pdf.html(this.el.nativeElement, {
        callback: (pdf) => {
          pdf.save('Avaliacao ' + this.tipoAvaliacao?.nome + '.pdf');
          this.pdfGenerated.emit();
          this.loadingService.hide();
        },
        x: 0, // Alinhamento horizontal (ajuste conforme necessário)
        y: 10 // Margem superior (ajuste conforme necessário)
      })
    }, 1500); // Tempo de espera para garantir a renderização
  };

  async carregarDominiosPorNivel(): Promise<void> {
    try {
      for (let nivel of this.niveis) {
        this.dominiosPorNivel[nivel.id] = await this.habilidadeAvaliacaoService.findDominiosByNivel(nivel.id).toPromise();
      }
    } catch (error) {
      console.error('Erro ao carregar domínios:', error);
    }
  }

  async filterHabilidades(): Promise<void> {
      return new Promise((resolve, reject) => {
          this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel.id, this.dominio.id).subscribe(habilidades => {
              this.respostasHabilidadesView = this.avaliacao.respostasHabilidades.filter(resposta => {
                  return habilidades.filter(h => "" + h.idDominioAvaliacao === "" + this.dominio.id
                      && "" + h.idNivelAvaliacao === "" + this.nivel.id
                      && "" + resposta.idHabilidadeAvaliacao === "" + h.id).length > 0
              });

              this.habilidadesView = this.habilidades.filter(habilidade => {
                  return "" + habilidade.idDominioAvaliacao === "" + this.dominio.id
                      && "" + habilidade.idNivelAvaliacao === "" + this.nivel.id
              });

              for (const [, h] of habilidades.entries()) {
                  for (const [, d] of h.dominioResposta.entries()) {
                      if (this.domoniosResposta.filter(resp => resp.sigla === d.sigla).length == 0) {
                          this.displayedColumns.push("resp-" + d.sigla)
                          this.domoniosResposta.push(d);
                      }
                  }
              }

              this.domoniosResposta.sort((a, b) => {
                  return a.valor < b.valor ? 1 : -1;
              });

              this.habilidadesView.sort((a, b) => {
                  return a.ordem > b.ordem ? 1 : -1;
              });

              resolve();
          }, error => reject(error));
      });
  }

  getDominioResposta(valor: string){
    let dr = this.domoniosResposta.find(d => "" + d.valor == valor);

    if (dr == undefined){
      dr = this.domoniosResposta.find(d => d.valor == undefined);
    }
    return dr;
  }

  findIndexHabilidade(idHabilidade: number){
    return this.avaliacao?.respostasHabilidades?.findIndex( resp => parseFloat(resp.idHabilidadeAvaliacao) == idHabilidade);
  }

  getRespostaHabilidade(idHabilidade: any){
    return this.avaliacao?.respostasHabilidades[this.findIndexHabilidade(idHabilidade)]
  }

  getClassRespostaHabilidadePDF(habilidade: any){
    const maxValor = habilidade?.dominioResposta
      .map(dom => parseFloat(dom.valor))
      .filter(v => !isNaN(v))
      .sort((a, b) => b - a)[0];

    const valorResposta = this.getRespostaHabilidade(habilidade.id)?.valor;
    
    // console.log(this.getRespostaHabilidade(idHabilidade))
    if (this.tipoAvaliacao.id == '2' || this.tipoAvaliacao.id == '7') {
      if (maxValor == 2) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-P-pdf";
          case '2':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      } else if (maxValor == 3) {
        switch (valorResposta){
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-P-pdf";
          case '2':
            return "label label-ABLLS-P3-pdf";
          case '3':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      } else if (maxValor == 4) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-P1-pdf";
          case '2':
            return "label label-ABLLS-P-pdf";
          case '3':
            return "label label-ABLLS-P3-pdf";
          case '4':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      } else if (maxValor == 1) {
        switch (valorResposta) {
          case '0':
            return "label label-ABLLS-N-pdf";
          case '1':
            return "label label-ABLLS-A-pdf";
          default:
            return "label label-ABLLS-X-pdf";
        }
      }
    } else if (this.tipoAvaliacao.id == '6') {
      switch (valorResposta){
        case '0':
          return "label label-ABLLS-N-pdf";
        case '1':
          return "label label-ABLLS-P3-pdf";
        case '2':
          return "label label-ABLLS-P-pdf";
        case '3':
          return "label label-ABLLS-A-pdf";
        default:
          return "label label-ABLLS-X-pdf";
      }
    } else {
      switch (valorResposta){
        case '0':
          return "label label-N-pdf";
        case '0.5':
          return "label label-P-pdf";
        case '1':
          return "label label-A-pdf";
        default:
          return "label label-X-pdf";
      }
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  getAvaliacaoInfosPDF(id_nivel, id_dominio){
    let avaliacaoInfosPDF = this.habilidades.filter(habilidade => { return "" + habilidade.idDominioAvaliacao == "" + id_dominio && "" + habilidade.idNivelAvaliacao == "" + id_nivel })
    
    return avaliacaoInfosPDF
  }

  getStyleByNivelId(nivelId: string): any {
    const styles = {
      '1': { height: '1183px' },
      '3': { height: '1180px' },
      '4': { height: '1177px' },
      '5': { height: '1180px' },
      '6': { height: '1175px' },
      '13': { height: '2960px' },
      '14': { height: '1173px' },
      '15': { height: '1180px' },
      '16': { height: '1180px' },
      '17': { height: '1180px' },
      '18': { height: '1770px' },
      '19': { height: '1180px' },
      '20': { height: '1180px' },
    };

    return styles[nivelId] || {};
  
  }

  getStyleByNivelIdForGlossario(nivelId: string): any {
    const styles = {
      '1': { height: '1735px' },
      '2': { height: '1185px' },
      '3': { height: '1780px' },
      '4': { height: '1175px' },
      '5': { height: '1175px' },
      '10': { height: '1730px' },
      '11': { height: '2365px' },
    };

    return styles[nivelId] || {};
  }

  getStyleForHabilidade(habilidade: any): any {
    let styles;
    if (this.tipoAvaliacao.id == '1') {
      styles = {
        // Formatação PDF Portage
        'Cognição-1': { marginBottom: '30px' },
        'Cognição-8': { marginBottom: '30px' },
        'Desenvolvimento Motor-12': { marginBottom: '35px' },
        'Desenvolvimento Motor-35': { marginBottom: '35px' },
        'Cognição-18': { marginBottom: '50px' },
        'Cognição-23': { marginBottom: '50px' },
        'Cognição-27': { marginBottom: '30px' },
        'Cognição-35': { marginBottom: '30px' },
        'Desenvolvimento Motor-68': { marginBottom: '30px' },
        'Desenvolvimento Motor-77': { marginBottom: '30px' },
        'Cognição-50': { marginBottom: '25px' },
        'Cognição-62': { marginBottom: '25px' },
        'Cognição-73': { marginBottom: '25px' },
        'Cognição-84': { marginBottom: '25px' },
        'Cognição-95': { marginBottom: '35px' },
        'Cognição-106': { marginBottom: '35px' }
      }
    } else if (this.tipoAvaliacao.id == '2') {
      styles = {
        // Formatação PDF ABLLS-R
        'B12': { marginBottom: '10px' },
        'B26': { marginBottom: '10px' },
        'C24': { marginBottom: '25px' },
        'C53': { marginBottom: '25px' },
        'E6': { marginBottom: '30px' },
        'E16': { marginBottom: '30px' },
        'G6': { marginBottom: '30px' },
        'G30': { marginBottom: '30px' },
        'H8': { marginBottom: '25px' },
        'H33': { marginBottom: '25px' },
        'J2': { marginBottom: '30px' },
        'J13': { marginBottom: '30px' },
        'L8': { marginBottom: '25px' },
        'L25': { marginBottom: '25px' },
        'Q1': { marginTop: '25px' },
        'Q10': { marginTop: '25px' },
        'S1': { marginBottom: '30px' },
        'S6': { marginBottom: '30px' },
        'W2': { marginBottom: '30px' },
        'W6': { marginBottom: '30px' },
        'Z2': { marginBottom: '25px' },
        'Z16': { marginBottom: '25px' }
      }
    } else if (this.tipoAvaliacao.id == '4') {
      styles = {
        // Formatação PDF Social Skills
        'Jogo-8': { marginBottom: '45px' },
        'SolProb-4': { marginBottom: '35px' },
        'SolProb-10': { marginBottom: '35px' },
        'Jogo-36': { marginBottom: '50px' },
        'Perspectiva-13': { marginBottom: '40px' },
        'Conversação-38': { marginBottom: '45px' },
        'Conversação-44': { marginBottom: '45px' },
        'Comunidade-19': { marginBottom: '20px' },
        'Comunidade-26': { marginBottom: '20px' },
        'Amizades-15': { marginTop: '20px' },
        'Amizades-19': { marginTop: '20px' },
      }
    } else if (this.tipoAvaliacao.id == '6') {
      styles = {
        //Formatação PDF Socially Savvy
        'SP10': { marginBottom: '35px' },
        'SL03': { marginBottom: '10px' },
        'SL14': { marginBottom: '10px' },
        'NV01': { marginTop: '30px' },
        'NV04': { marginTop: '30px' }
      }
    } else if (this.tipoAvaliacao.id == '7') {
      styles = {
        // Formatação PDF AFLS
        'CB05': { marginTop: '40px' },
        'CB18': { marginTop: '40px' },
        'VS18': { marginTop: '30px' },
        'VS37': { marginTop: '30px' },
        'HG03': { marginTop: '30px' },
        'HG20': { marginTop: '30px' },
        'SS04': { marginTop: '25px' },
        'SS24': { marginTop: '25px' },
        'RN07': { marginBottom: '45px' },
        'RN14': { marginBottom: '45px' },
        'CS02': { marginTop: '35px' },
        'CS18': { marginTop: '35px' },
        'FC09': { marginTop: '30px' },
        'FC32': { marginTop: '30px' },
        'CF09': { marginTop: '30px' },
        'CF24': { marginTop: '30px' },
        'TL11': { marginTop: '30px' },
        'TL27': { marginTop: '30px' },
        'CM11': { marginTop: '25px' },
        'CM35': { marginTop: '25px' },
        'RC08': { marginTop: '35px' },
        'RC22': { marginTop: '52px' },
        'RL07': { marginTop: '35px' },
        'RL15': { marginBottom: '10px' },
        'RL23': { marginTop: '35px' },
        'LT17': { marginTop: '40px' },
        'LT34': { marginTop: '40px' },
        'LA06': { marginTop: '25px' },
        'LA18': { marginTop: '25px' },
        'CZ14': { marginTop: '40px' },
        'CZ33': { marginTop: '58px' },
        'CL19': { marginTop: '30px' },
        'CL42': { marginTop: '30px' },
        'RE10': { marginTop: '25px' },
        'RE25': { marginTop: '25px' },
        'RO13': { marginTop: '30px' },
        'RO38': { marginTop: '30px' },
        'HS07': { marginTop: '35px' },
        'HS25': { marginTop: '35px' },
        'TC15': { marginTop: '35px' },
        'TC35': { marginTop: '35px' },
        'CG17': { marginTop: '25px' },
        'CG46': { marginTop: '25px' },
        'BC16': { marginTop: '30px' },
        'BC41': { marginTop: '35px' },
        'PR13': { marginTop: '35px' },
        'PR42': { marginTop: '52px' },
        'PE08': { marginTop: '35px' },
        'PE21': { marginTop: '35px' },
        'HB08': { marginTop: '30px' },
        'HB30': { marginTop: '30px' },
        'CT08': { marginTop: '50px' },
        'CT15': { marginTop: '50px' },
        'AF05': { marginTop: '30px' },
        'AF09': { marginTop: '50px' },
        'FX46': { marginTop: '35px' },
        'ES01': { marginTop: '25px' },
        'ES21': { marginTop: '25px' },
        'HI05': { marginTop: '25px' },
        'HI21': { marginTop: '25px' },
        'HR14': { marginTop: '30px' },
        'HR31': { marginTop: '30px' },
        'FE02': { marginTop: '30px' },
        'FE17': { marginTop: '30px' },
        'PM10': { marginTop: '25px' },
        'PM30': { marginTop: '25px' },
        'HO03': { marginTop: '30px' },
        'HO16': { marginTop: '30px' },
        'AC13': { marginTop: '35px' },
        'AC32': { marginTop: '35px' },
        'ML36': { marginTop: '35px' },
        'PA07': { marginTop: '55px' },
        'PA23': { marginTop: '35px' },
        'TR12': { marginTop: '35px' },
        'TR34': { marginTop: '35px' },
        'PC03': { marginTop: '32px' },
        'PC14': { marginTop: '32px' },
        'GF15': { marginTop: '30px' },
        'GF29': { marginTop: '30px' },
        'GP08': { marginTop: '52px' },
        'GP21': { marginTop: '52px' },
        'SE09': { marginTop: '35px' },
        'SE27': { marginTop: '55px' },
        'IS02': { marginTop: '35px' },
        'IS17': { marginTop: '35px' },
        'CO09': { marginTop: '35px' },
        'CO18': { marginTop: '35px' },
      }
    }

    return styles[habilidade] || { marginBottom: '1px' };
  }

  get formattedTipoAvaliacaoNome(): string {
    if (this.tipoAvaliacao?.nome === 'Social Skills') {
      return 'Social&nbsp;Skills';
    }
    return this.tipoAvaliacao?.nome || '';
  }
}