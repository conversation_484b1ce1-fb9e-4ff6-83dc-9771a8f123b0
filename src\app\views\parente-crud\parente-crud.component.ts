import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-parente-crud',
  templateUrl: './parente-crud.component.html',
  styleUrls: ['./parente-crud.component.css']
})
export class ParenteCrudComponent implements OnInit {

  constructor(private router: Router,
    public headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Parente',
        icon: 'family_restroom',
        routeUrl: '/parente'
      }
    }

  ngOnInit(): void {
  }

}
