import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../components/template/auth/auth.service';
import { HeaderService } from '../../components/template/header/header.service';

@Component({
  selector: 'app-objetivocomportamental-crud',
  templateUrl: './objetivocomportamental-crud.component.html',
  styleUrls: ['./objetivocomportamental-crud.component.css']
})
export class ObjetivocomportamentalCrudComponent implements OnInit {

  hasAccessCreate: boolean = false;

  constructor(
    private router: Router,
    public authService: AuthService,
    private headerService: HeaderService
  ) {
      headerService.headerData = {
        title: 'Objetivos (Comportamental)',
        icon: 'source',
        routeUrl: '/objetivo_comportamental'
      }
    }

  ngOnInit(): void {
    this.hasAccessCreate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','create');
  }

}
