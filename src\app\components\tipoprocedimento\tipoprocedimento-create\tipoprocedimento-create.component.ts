import { Activated<PERSON>oute, Router } from '@angular/router';
import { AuthService } from './../../template/auth/auth.service';
import { TipoprocedimentoService } from './../tipoprocedimento.service';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { TipoProcedimento } from './../tipoprocedimento-model';
import { Component, OnInit, ViewChild } from '@angular/core';
import createNumberMask from 'text-mask-addons/dist/createNumberMask'

@Component({
  selector: 'app-tipoprocedimento-create',
  templateUrl: './tipoprocedimento-create.component.html',
  styleUrls: ['./tipoprocedimento-create.component.css']
})
export class TipoprocedimentoCreateComponent implements OnInit {

  tipoprocedimento: TipoProcedimento = new TipoProcedimento();
  tiposprocedimento: TipoProcedimento[] = [];

  numberMask = createNumberMask({
    decimalSymbol: ',',
    thousandsSeparatorSymbol: '.',
    allowDecimal: true,
    prefix: '',
    suffix: '' // This will put the dollar sign at the end, with a space.
  })

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  minutosFC = new FormControl('', [Validators.required]);
  valorFC = new FormControl('', [Validators.required]);
  permiteAlterarValor = new FormControl('', [Validators.required]);

  public hasAccessUpdate: boolean = false;

  constructor(private tiposprocedimentoervice: TipoprocedimentoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router) { }

  async ngOnInit(): Promise<void> {
    let idtipoprocedimento = this.route.snapshot.paramMap.get('id');  

    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Tipo de Procedimento.Cadastro de tipos de procedimentos','update')

    if(idtipoprocedimento == undefined) { //Create
      this.tipoprocedimento = new TipoProcedimento();
      this.tipoprocedimento.ativo = true;
      this.tipoprocedimento.permiteAlterarValor = false;
    } else {  //Edit
        await this.tiposprocedimentoervice.findById(idtipoprocedimento).subscribe(async tipoprocedimento => {
          this.tipoprocedimento = tipoprocedimento; 
        })
    }

  }
  /*
  setTwoNumberDecimal(event) {
    
    this.value = parseFloat(this.value).toFixed(2);
  }*/

  save(){
    let valid: boolean = true;
    if(this.form.valid){  
      if(this.tipoprocedimento.id == undefined){
        this.tiposprocedimentoervice.create(this.tipoprocedimento).subscribe((id) => {
          this.tiposprocedimentoervice.showMessage('Tipo de procedimento criado com sucesso!');
          this.router.navigate(['/tipoprocedimento']);
        });
      } else {
        this.tiposprocedimentoervice.update(this.tipoprocedimento).subscribe((funcao) => {
          this.tiposprocedimentoervice.showMessage('Tipo de procedimento alterado com sucesso!');
          this.router.navigate(['/tipoprocedimento']);
        });
      }
        
    } else {
      this.tiposprocedimentoervice.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/tipoprocedimento']);
  }

}
