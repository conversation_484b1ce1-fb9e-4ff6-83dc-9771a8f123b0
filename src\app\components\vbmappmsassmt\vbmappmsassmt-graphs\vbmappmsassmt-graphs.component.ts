import { Profissional } from './../../profissional/profissional-model';
import { AuthService } from './../../template/auth/auth.service';
import { NivelService } from './../../nivel/nivel.service';
import { VBMAPPMsAssmtService } from './../vbmappmsassmt.service';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { Observable } from 'rxjs';
import { VBMAPPMsAssmtGraph } from './../vbmappmsassmt-graph-model';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { Paciente } from './../../paciente/paciente-model';
import { VBMAPPMilestonesAssessment } from './../vbmappmsassmt-model';
import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MarcovbmappService } from '../../marcovbmapp/marcovbmapp.service';

@Component({
  selector: 'app-vbmappmsassmt-graphs',
  templateUrl: './vbmappmsassmt-graphs.component.html',
  styleUrls: ['./vbmappmsassmt-graphs.component.css']
})
export class VbmappmsassmtGraphsComponent implements OnInit {

  public vbmappmsassmts: VBMAPPMilestonesAssessment[];
  public vbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public paciente: Paciente = new Paciente();
  public niveis: Nivel[];
  public nivel: Nivel = new Nivel();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>(); 
  public colors: any[];
  //public colorMap: Map<number, string> = new Map<number, string>();
  
  public vbmappGraph: VBMAPPMsAssmtGraph;
  //public esdmGraphConsolidado: VBMAPPMsAssmtGraph;

  public graph: VBMAPPMsAssmtGraph = new VBMAPPMsAssmtGraph();

  public hasAccessRead: boolean = false;

  @Input()
  $pacienteSearch: Observable<Paciente>;

  @ViewChild('vbmappmsassmt0') private checklistBox0: MatCheckbox;

  constructor(private vbmappMsAssmtService: VBMAPPMsAssmtService,
    private nivelService: NivelService,
    public authService: AuthService,
    private marcoService: MarcovbmappService) { }

  ngOnInit(): void {
        //Setando as cores padrão de cada checklist (por ordem)
        this.colors = [
          { backgroundColor:"rgba(255, 99, 132, 0.2)",
            fill:true,
            borderColor:"rgb(255, 99, 132)",
            pointBackgroundColor:"rgb(255, 99, 132)",
            pointBorderColor:"#fff",
            pointHoverBackgroundColor:"#fff",
            pointHoverBorderColor:"rgb(255, 99, 132)" },
          { backgroundColor:"rgba(54, 162, 235, 0.2)",
            fill:true,
            borderColor:"rgb(54, 162, 235)",
            pointBackgroundColor:"rgb(54, 162, 235)",
            pointBorderColor:"#fff",
            pointHoverBackgroundColor:"#fff",
            pointHoverBorderColor:"rgb(54, 162, 235)" },
          { backgroundColor:"rgba(138,43,226,0.2)",
            fill:true,
            borderColor:"rgb(138,43,226)",
            pointBackgroundColor:"rgb(138,43,226)",
            pointBorderColor:"#fff",
            pointHoverBackgroundColor:"#fff",
            pointHoverBorderColor:"rgb(138,43,226)" },
          { backgroundColor:"rgba(218, 165, 32,0.2)",
            fill:true,
            borderColor:"rgb(218, 165, 32)",
            pointBackgroundColor:"rgb(218, 165, 32)",
            pointBorderColor:"#fff",
            pointHoverBackgroundColor:"#fff",
            pointHoverBorderColor:"rgb(218, 165, 32)" }
        ];
        
    
        //Atribuindo o paciente vindo por parâmetro
        this.$pacienteSearch.subscribe(paciente => { 
          this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read');

          this.paciente = paciente;
          this.vbmappmsassmt.idPaciente = paciente.id;
          this.vbmappmsassmt.paciente = paciente;
    
          //Carregando Checklists do paciente
          this.vbmappMsAssmtService.findByPaciente(paciente.id).subscribe(assmts => {
            if(assmts.length > 0){
              this.vbmappmsassmts = assmts.filter(assmt => assmt.status != false);
    
              //Atribuindo o primeiro (mais novo) checklist para visualização
              if(this.vbmappmsassmts.length > 0) {
                this.vbmappmsassmt = this.vbmappmsassmts[0];
              }
            }
          }, (err) => console.error(err),
          () => {
            //Carregando Níveis
            this.nivelService.find().subscribe(niveis => {
              this.niveis = niveis;
              this.niveis.pop();
              //this.niveis.push({ id: 'C', nome: 'Consolidado' })
              this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)
    
              //Carregando Domínios dos Níveis (TAB)
                niveis.forEach(nivel => {
                this.marcoService.findDominiosByNivel(nivel.id).subscribe(dominios => {
                  this.dominioMap.set(nivel.id,dominios)
                },
                (err) => console.error(err),
                () => {
                  if((this.dominioMap.size == 3)){// Só calculo quando os 3 níveis foram carregados, caso contrário dá erro.
                    this.graph.datasets = [];
                    //this.nivel = { id: 'C', nome: 'Consolidado' }
                    //this.calculateGraphNivel(new Date(this.esdmchecklists[0].data).toLocaleDateString('pt-BR'));
                    if(this.vbmappmsassmts != undefined){
                      //this.calculateGraphConsolidado(new Date(this.esdmchecklists[0].data).toLocaleDateString('pt-BR'));
                      //console.log(this.vbmappmsassmts[0].data)
                      this.calculateGraphNivel(new Date(this.vbmappmsassmts[0].data).toLocaleDateString('pt-BR'), 'N1');
                    }
                  }
                })
              })
            })
          })
        });
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
   
    return funcoes;
  }

  checked(index: number): boolean{
    if (index == 0){
      return true
    } else {
      return false
    }
  }

  doGraphCal(date: string, idNivel: string): number[]{
    let n, p, a;

    let ipeCalc = false;
    let csoCalc = false;
    let jogCalc = false;

    let data: number[] = [];

    //Busco a data selecionada nos checklists
    let index = this.vbmappmsassmts.map(function(e) { return new Date(e.data).toLocaleDateString('pt-BR'); }).indexOf(date)

    this.dominioMap.get(idNivel).forEach(dom => {
      /*if( (dom.id == 'IPE' || dom.id == 'IPA' || dom.id == 'IPV' || dom.id == 'IPH' || dom.id == 'IPT') && ipeCalc == false ) {
        ipeCalc = true;

        this.graph.labels.push('IPE'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.vbmappmsassmts[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'IPE' || 
              chklst.competencia.id_dominio == 'IPA' || 
              chklst.competencia.id_dominio == 'IPV' || 
              chklst.competencia.id_dominio == 'IPH' || 
              chklst.competencia.id_dominio == 'IPT')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'IPE' || 
            chklst.competencia.id_dominio == 'IPA' || 
            chklst.competencia.id_dominio == 'IPV' || 
            chklst.competencia.id_dominio == 'IPH' || 
            chklst.competencia.id_dominio == 'IPT')
          && chklst.valor == 'A')).length



        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))
        //   + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'CSO' || dom.id == 'CSA' || dom.id == 'CSP' || dom.id == 'CAP') && csoCalc == false ) {
        csoCalc = true;

        this.graph.labels.push('CSO'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'CSO' || 
              chklst.competencia.id_dominio == 'CSA' || 
              chklst.competencia.id_dominio == 'CSP' || 
              chklst.competencia.id_dominio == 'CAP')
          && chklst.valor == 'A')).length

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) 
        //  + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );
      } else if((dom.id == 'JOG' || dom.id == 'JOR' || dom.id == 'JOI') && jogCalc == false ) {
        jogCalc = true;

        this.graph.labels.push('JOG'); //Incluindo o label do domínio

        //Recuperando as quantidades de cada tipo
        n = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
              (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
            && chklst.valor == 'N')).length;
          
        p = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'P')).length
          
        a = this.esdmchecklists[index].checklist.filter(chklst => 
          (chklst.competencia.id_nivel==idNivel && 
            (chklst.competencia.id_dominio == 'JOG' || 
              chklst.competencia.id_dominio == 'JOR' || 
              chklst.competencia.id_dominio == 'JOI')
          && chklst.valor == 'A')).length

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) 
        //  + ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) );

      } else if ( (dom.id != 'IPE' && dom.id != 'IPA' && dom.id != 'IPV' && dom.id != 'IPH' && dom.id != 'IPT') 
                  && (dom.id != 'CSO' && dom.id != 'CSA' && dom.id != 'CSP' && dom.id != 'CAP')
                  && (dom.id != 'JOG' && dom.id != 'JOR' && dom.id != 'JOI') ) { //Caso não seja um domínio a ser consolidado
      */
        this.graph.labels.push(dom.id); //Incluindo o label do domínio
  
        //Recuperando as quantidades de cada tipo
        n = this.vbmappmsassmts[index].assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel && chklst.marco.id_dominio == dom.id && chklst.valor == 'N')).length;
          
        p = this.vbmappmsassmts[index].assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel &&  chklst.marco.id_dominio == dom.id && chklst.valor == 'P')).length
          
        a = this.vbmappmsassmts[index].assessment.filter(chklst => 
          (chklst.marco.id_nivel==idNivel &&  chklst.marco.id_dominio == dom.id && chklst.valor == 'A')).length
  
        //let x = this.esdmchecklists[index].checklist.filter(chklst => 
        //  (chklst.competencia.id_nivel==idNivel && chklst.competencia.id_dominio == dom.id && (chklst.valor == 'X' || chklst.valor == undefined))).length

        //console.log("Domínio: " + dom.id + " (" + 
        //  (isNaN(Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100))?0:Math.round( ((n*0 + p*1 + a*2)/((n+p+a)*2)) * 100)) + 
        //  ") - N:" + n + ", P: " + p + ", A: " +  a)
        data.push( (isNaN(Math.round( ((n*0 + p*0.5 + a*1)/((n+p+a)*1)) * 100))?0:Math.round( ((n*0 + p*0.5 + a*1)/((n+p+a)*1)) * 100)) );
      //}
      
    })
    return data;
  }

  calculateGraphNivel(date: string, idNivel: string){
    this.graph.chartType = 'radar';
    //this.graph.datasets = [];
    this.graph.labels = [];
    //let datasets: ChartDataSets[] = [];
    let data: number[] = [];

    //Busco a data selecionada nos checklists
    let index = this.vbmappmsassmts.map(function(e) { return new Date(e.data).toLocaleDateString('pt-BR'); }).indexOf(date)
    //console.log(index)

    //Solicito o cálculo do nívei desejado
    data = this.doGraphCal(date, idNivel)
    //console.log(data)
    //console.log(this.vbmappmsassmts)
    this.graph.datasets.push({ label:  new Date(this.vbmappmsassmts[index].data).toLocaleDateString('pt-BR'), data: data});

    //console.log("IndexOf:" + this.graph.datasets.map(function(e) { return e.label; }).indexOf(new Date(this.esdmchecklists[index].data).toLocaleDateString('pt-BR')))
    this.graph.labels = [...this.graph.labels];
    this.graph.options = {
      scale: {
        ticks: {
          min: 0,
          max: 100,
          stepSize: 10
        }
      },
      responsive: true,
      legend: {
        position: 'top',
      },
      tooltips: {
        enabled: true,
        mode: 'single',
        callbacks: {
          label: function (tooltipItems, data) {
            return " " + data.datasets[tooltipItems.datasetIndex].label + " - " + data.datasets[tooltipItems.datasetIndex].data[tooltipItems.index] + ' %';
          }
        }
      },
    };
  }

  removeMsAssmtFromGraph(data: string){
    //console.log(this.graph)
    let index = this.graph.datasets.map(function(e) { return e.label; }).indexOf(data)
    this.graph.datasets.splice(index, 1);
  }

  setMsAssessment(event:MatCheckboxChange){
    if(event.checked == false){
      this.removeMsAssmtFromGraph( event.source.name )
    } else {
      //if(this.nivel.id == 'C'){
      //  this.calculateGraphConsolidado(event.source.name);
      //} else {
        this.calculateGraphNivel(event.source.name, this.nivel.id);
      //}
      //this.calculateGraphNivel( event.source.name );
    }

    //this.nivel = this.niveis.find(n => n.id == 'N1'); //Atribuindo Nível Default (N1)
    //this.calculateGraphNivel();
  }

  setNivel(){
    //console.log("Nível: " + this.nivel.id)
    //Recuperando os checklists apresentados no gráfico
    let selected: string[] = [];
    this.graph.datasets.forEach(dataset => {
      selected.push(dataset.label);
    })
    selected.forEach(label =>{
      this.removeMsAssmtFromGraph(label);
      //if(this.nivel.id == 'C'){
      //  this.calculateGraphConsolidado(label);
      //} else {
        this.calculateGraphNivel(label, this.nivel.id);
      //}
    })

    //this.calculateGraphNivel();
  }

}
