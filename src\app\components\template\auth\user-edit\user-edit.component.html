<mat-card style="width: 95%; margin: auto;">
    <mat-card-header>
      <mat-card-title style="padding-bottom: 10px;">Usuário: {{ user.name }}</mat-card-title>
    </mat-card-header>
    <!--
        image: string;
    name: string;
    provider: string;
    email: string;
    uid: string;
    admin: boolean
    superadmin: boolean
    funcao: string[];
    organizationId: string;
    -->
    <mat-card-content>
      <div style="display: flex; flex-direction: column; width: 100%; text-align: center;">
            <form #ngForm style="display: flex; flex-direction: column; width: 100%; text-align: center;">
                <mat-form-field>
                    <input class="input" matInput placeholder="UID" 
                        [(ngModel)]="user.uid" name="uid" disabled>
                </mat-form-field>
                <mat-form-field>
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="user.name" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field>
                <mat-form-field>
                    <input class="input" matInput placeholder="E-mail" 
                        [(ngModel)]="user.email" name="email" disabled>
                </mat-form-field>
                <mat-form-field>
                    <mat-label>Supervisor Geral</mat-label>
                    <mat-select (ngModelChange)="validaOrgSelecionadas()" placeholder="Supervisor Geral" 
                        [(ngModel)]="user.gsupervisor" name="admin">
                        <mat-option [value]="true" >Sim</mat-option>
                        <mat-option [value]="false" >Não</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field>
                    <mat-label>Admin</mat-label>
                    <mat-select placeholder="Admin" 
                        [(ngModel)]="user.admin" name="admin">
                        <mat-option [value]="true" >Sim</mat-option>
                        <mat-option [value]="false" >Não</mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field>
                    <mat-label>SuperAdmin</mat-label>
                    <mat-select placeholder="SuperAdmin" 
                        [(ngModel)]="user.superadmin" name="superadmin">
                        <mat-option [value]="true" >Sim</mat-option>
                        <mat-option [value]="false" >Não</mat-option>
                    </mat-select>
                </mat-form-field>
            </form>
      </div>
      <h3 style="text-align: left;">
         <strong> Organizações </strong>
     </h3>
            <div style="display: flex;" >                   
                <div style="width: 100%;">
                    <mat-form-field style="width: 40%;">
                        <mat-label>Organização</mat-label>
                        <mat-select placeholder="Organização" 
                            [(ngModel)]="organizationIdSelecionada" name="organiacao">
                            <mat-option *ngFor="let org of organizacoes" [value]="org.id" (click)="addOrganizacao()">
                                {{org.nome}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div>
                <table mat-table [dataSource]="organizacoesSelecionadas" style="width: 100%;"> 
                    <!-- Nome Column -->
                    <ng-container matColumnDef="nome" fxFlex="70">
                        <th mat-header-cell *matHeaderCellDef fxFlex="70">Razão Social</th>
            
                        <td mat-cell *matCellDef="let row"  fxFlex="70" >
                            {{recuperarNomeOrganizacao(row.idOrganizacao)}}
                        </td>
                    </ng-container>

                    <!-- Action Column -->
                    <ng-container matColumnDef="default">
                    <th mat-header-cell *matHeaderCellDef style="text-align: center;">Organização Padrão</th>
                    <td mat-cell *matCellDef="let row" style="cursor: pointer; text-align: center;" >
                        <a *ngIf="row.default" class="edit" matTooltip="Padrão">
                            <i class="material-icons">
                                check
                            </i>
                        </a>
                        <a *ngIf="!row.default"(click)="definirOrgPadrao(row.idOrganizacao)" class="edit" matTooltip="Padrão">
                            <i class="material-icons">
                                cancel
                            </i>
                        </a>
                    </td>
                    </ng-container>

                    <ng-container matColumnDef="action">
                        <th mat-header-cell *matHeaderCellDef style="text-align: center;">Remover</th>
                        <td mat-cell *matCellDef="let row" style="cursor: pointer; text-align: center;" >
                            <a (click)="removerOrganizacaoSelecionada(row.idOrganizacao)" class="edit" matTooltip="Remover">
                                <i class="material-icons">
                                    delete
                                </i>
                            </a>
                        </td>
                        </ng-container>
                
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
            </div>
          <div style="margin-top: 50px; text-align: right;">
              <button mat-flat-button style="width: fit-content;"	color="primary" (click)="save()">Salvar</button>
              <button mat-flat-button style="width: fit-content;" (click)="cancel()">Cancelar</button>
          </div>
    </mat-card-content>
  </mat-card>