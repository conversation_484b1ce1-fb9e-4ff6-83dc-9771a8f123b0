{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"inlineTemplate": false, "inlineStyle": false, "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:module": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "150kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "frontend:build"}, "configurations": {"production": {"browserTarget": "frontend:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "frontend:build"}}, "deploy": {"builder": "@angular/fire:deploy", "options": {}}}}}, "defaultProject": "frontend", "cli": {"analytics": false}}