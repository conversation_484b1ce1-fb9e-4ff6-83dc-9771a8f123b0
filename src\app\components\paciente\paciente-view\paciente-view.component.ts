import { Profissional } from './../../profissional/profissional-model';
import { FormControl } from '@angular/forms';
import { PacienteService } from './../paciente.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { Paciente } from './../paciente-model';
import { Component, OnInit, Output } from '@angular/core';
import { Anamnese } from '../../anamnese/anamnese-model';
import { AnamneseService } from '../../anamnese/anamnese.service';
import { Endereco } from '../../../shared/model/endereco-model';
import { AuthService } from '../../template/auth/auth.service';


@Component({
  selector: 'app-paciente-view',
  templateUrl: './paciente-view.component.html',
  styleUrls: ['./paciente-view.component.css']
})
export class PacienteViewComponent implements OnInit {


  id: string;
  aba: string;
  $pacienteSearch: Observable<Paciente>;
  $idPlanoIntervencaoSearch: string;
  $idChklstSearch: string;
  $idVbmappPlanSearch: string;
  $idPICPlanSearch: string;
  $idMsAssmtSearch: string;
  $idAvaliacaoSearch: string;
  paciente: Paciente = new Paciente();

  selected = new FormControl(0);
  selectedDenver = new FormControl(0);
  selectedVBMAPP = new FormControl(0);
  selectedPIC = new FormControl(0);

  idade: number;

  public anamneses: Anamnese[];

  displayedColumns = ['checked','descricao','action'];

  constructor(
    private route: ActivatedRoute,
    private pacienteService: PacienteService,
    private anamneseService: AnamneseService,
    private router: Router,
    public authService: AuthService
  ) { }

  ngOnInit() {
    this.id = this.route.snapshot.paramMap.get('id');
    this.aba = this.route.snapshot.paramMap.get('tab');
    this.$idPlanoIntervencaoSearch = this.route.snapshot.paramMap.get('idPlanoIntervencao');
    this.$idChklstSearch = this.route.snapshot.paramMap.get('idESDMChklst');
    this.$idMsAssmtSearch = this.route.snapshot.paramMap.get('idMsAssmt');
    this.$idAvaliacaoSearch = this.route.snapshot.paramMap.get('idAvaliacao');
    this.$idVbmappPlanSearch = this.route.snapshot.paramMap.get('idVbmappPlan');
    this.$idPICPlanSearch = this.route.snapshot.paramMap.get('idPICPlan');
    // console.log(this.authService.getUser())
    
    if (this.id == "new") {
      this.id = "";
      this.$pacienteSearch = new Observable<Paciente>(observer => {
        observer.next(new Paciente())
      });
    }
    else {
      /*this.pacienteService.findById(this.id).subscribe(paciente => {
        this.paciente = paciente;
        this.$pacienteSearch = new Observable<Paciente>(observer => {
          observer.next(this.paciente)
        });
      })*/
      this.$pacienteSearch = this.pacienteService.findById(this.id);
    }
    // First subscrible. There are many subscritor across the child tab's
    this.$pacienteSearch.subscribe(paciente => {
      this.paciente = paciente
      this.setAba();
      //retorna a idade em meses
      this.idade = this.pacienteService.getIdadeMeses(this.paciente.dataNascimento);

      //verifica se possui endereco
      if(this.paciente.endereco == undefined){
        this.paciente.endereco = new Endereco();
      }
    });
    
    this.anamneseService.find().subscribe(data => {
      this.anamneses = data;
    })
    
  }

  anamneseViewPaciente(idAnamnese: string){
    this.router.navigate(['/paciente/anamnese/'+this.id+'/'+idAnamnese]);
  }

  hasTipoIntervencao(tipo: string): boolean{
    if( (tipo == "ABA") && (this.paciente.tiposIntervencao == undefined) ){
      // this.setAba();
      return false;
    }

    if(this.paciente.tiposIntervencao != undefined && this.paciente.tiposIntervencao.indexOf(tipo) == -1){
      // this.setAba();
      return false;
    } else {
      // this.setAba();
      return true;
    }
  }

  setAba(){
    /*
    if(this.aba=="coleta"){
      //this.selectedDenver.setValue(3);
      this.selected.setValue(5);
    }

    if(this.aba=="checklist"){
      //this.selectedDenver.setValue(0);
      this.selected.setValue(2);
    }

    if(this.aba=="esdm_plano"){
      this.selectedDenver.setValue(2);
      this.selected.setValue(4);
    }
    */
    // this.aba = this.route.snapshot.paramMap.get('tab');
    // console.log(this.aba);

    if(this.aba=="checklist"){
      this.selectedDenver.setValue(0);
      this.selected.setValue(3);
    }

    if(this.aba=="esdm_plano"){
      this.selectedDenver.setValue(2);
      this.selected.setValue(3);
    }

    if(this.aba=="coleta"){
      this.selectedDenver.setValue(3);
      this.selected.setValue(3);
    }
    
    if(this.aba=="msassmt"){
      this.selectedVBMAPP.setValue(0);
      this.selected.setValue(4);
    }

    if(this.aba=="vbmapp_msassmt"){
      this.selectedVBMAPP.setValue(2);
      this.selected.setValue(0);
    }

    if(this.aba=="vbmapp_plano"){
      // console.log(this.aba)
      this.selectedVBMAPP.setValue(2);
      this.selected.setValue(4);
    }

    if(this.aba=="vbmapp_coleta"){
      this.selectedVBMAPP.setValue(4);
      this.selected.setValue(4);
    }  
    
    if(this.aba=="anamnese"){
      this.selected.setValue(1);
    } 

    if(this.aba=="avaliacao"){
      this.selectedVBMAPP.setValue(0);
      this.selected.setValue(4);
    }

    if(this.aba=="pic"){
      this.selectedVBMAPP.setValue(3);
      this.selected.setValue(4);
    }
    
  }

  checkRespostaAnamnese(idAnamnese: string){
    if(!this.paciente.anamnese){
      return false;
    }
    return this.paciente.anamnese.filter(anamnese => anamnese.id == idAnamnese).length > 0 ? true : false;
  }

}
