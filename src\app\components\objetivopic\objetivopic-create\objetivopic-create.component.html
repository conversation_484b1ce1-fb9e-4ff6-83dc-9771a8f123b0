<mat-card [style.overflow]="isModal ? 'auto' : ''" [style.width.vh]="isModal ? '90' : ''" [style.height.vh]=" isModal ? '80' : ''">
    <mat-card-header>
        <mat-card-title class="title">
            {{ objetivo?.comportamentoAlvo ? objetivo.comportamentoAlvo : "Novo Objetivo Comportamental" }}
        </mat-card-title>
    </mat-card-header>
    <mat-card-content>
        <form #ngForm="ngForm" class="form-container" (ngSubmit)="save()">
            <div class="form-field">
                <label class="labelInput">Comportamento Alvo</label>
                <mat-form-field appearance="outline">
                    <input matInput placeholder=""
                        [(ngModel)]="objetivo.comportamentoAlvo" name="compAlvo"
                        [formControl]="compAlvoFC"
                        required>
                    <mat-error *ngIf="compAlvoFC.invalid">Comportamento Alvo é obrigatório!</mat-error> 
                </mat-form-field>
            </div>
            <div class="form-field">
                <label class="labelInput">Definição Operacional</label>
                <mat-form-field appearance="outline">
                    <textarea matInput
                        [formControl]="defOpFC"
                        [(ngModel)]="objetivo.definicaoOperacional" name="defOp" required
                        rows="3"></textarea>
                    <mat-error *ngIf="defOpFC.invalid">Definição Operacional é obrigatória!</mat-error>
                </mat-form-field>
            </div>
            <div class="form-field">
                <label class="labelInput">Meta</label>
                <mat-form-field appearance="outline">
                    <textarea matInput 
                        [formControl]="metaFC"
                        [(ngModel)]="objetivo.meta" name="meta" required
                        rows="3"></textarea>
                    <mat-error *ngIf="metaFC.invalid">Meta é obrigatória!</mat-error>  
                </mat-form-field>
            </div>
            <div class="form-field">
                <label class="labelInput">Estratégias Antecedentes</label>
                <mat-form-field appearance="outline">
                    <textarea matInput 
                        [formControl]="estAntFC"
                        [(ngModel)]="objetivo.estrategiasAntecedentes" name="estAnt" required
                        rows="3"></textarea>
                    <mat-error *ngIf="estAntFC.invalid">Estratégias Antecedentes é obrigatória!</mat-error>  
                </mat-form-field>
            </div>
            <div class="form-field">
                <label class="labelInput">Estratégias de Consequência</label>
                <mat-form-field appearance="outline">
                    <textarea matInput 
                        [formControl]="estConsFC"
                        [(ngModel)]="objetivo.estrategiasConsequencia" name="estCons" required
                        rows="3"></textarea>
                    <mat-error *ngIf="estConsFC.invalid">Estratégias de Consequência é obrigatória!</mat-error>  
                </mat-form-field>
            </div>
            <div class="form-container-small">
                <div class="form-field small">
                    <label class="labelInput">Tipo de Coleta</label>
                    <mat-form-field appearance="outline">
                        <mat-select
                            [(ngModel)]="objetivo.tipoColeta"
                            [formControl]="tipoColetaFC"
                            name="tipoColeta" required>
                            <mat-option value="Registro de Eventos">Registro de Eventos</mat-option>
                            <mat-option value="Cronometragem">Cronometragem</mat-option>
                            <mat-option value="Amostragem de Tempo">Amostragem de Tempo</mat-option>
                        </mat-select>
                        <mat-error *ngIf="tipoColetaFC.invalid">Tipo de Coleta é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>
                <div class="form-field small" *ngIf="objetivo.tipoColeta === 'Amostragem de Tempo'">
                    <label class="labelInput">Intervalo</label>
                    <mat-form-field appearance="outline">
                        <input matInput placeholder="" 
                            [(ngModel)]="objetivo.intervalo" name="intervalo" 
                            [formControl]="intervaloFC"
                            type="number"
                            required>
                        <mat-error *ngIf="intervaloFC.invalid">Intervalo é obrigatório.</mat-error>  
                    </mat-form-field>
                </div>
                <div class="form-field small" *ngIf="objetivo.tipoColeta === 'Amostragem de Tempo'">
                    <label class="labelInput">Medida</label>
                    <mat-form-field appearance="outline">
                        <mat-select
                            [(ngModel)]="objetivo.medidaIntervalo"
                            [formControl]="medidaFC"
                            name="medida" required>
                            <mat-option value="Horas">Horas</mat-option>
                            <mat-option value="Minutos">Minutos</mat-option>
                            <mat-option value="Segundos">Segundos</mat-option>
                        </mat-select>
                        <mat-error *ngIf="medidaFC.invalid">Medida é obrigatória.</mat-error>  
                    </mat-form-field>
                </div>
            </div>
            <div class="form-field small" style="padding-left: 360px;" *ngIf="objetivo.tipoColeta === 'Amostragem de Tempo'">
                <label class="labelInput">Tipo de Amostragem</label>
                <mat-form-field appearance="outline">
                    <mat-select
                        [(ngModel)]="objetivo.tipoAmostragem"
                        [formControl]="tipoAmostFC"
                        name="tipoAmostragem" required>
                        <mat-option value="Inteiro">Inteiro</mat-option>
                        <mat-option value="Parcial">Parcial</mat-option>
                        <mat-option value="Momentânea">Momentânea</mat-option>
                    </mat-select>
                    <mat-error *ngIf="tipoAmostFC.invalid">Tipo de Amostragem é obrigatória.</mat-error>  
                </mat-form-field>
            </div>
            <section *ngIf="hasAccessUpdate">
                <mat-checkbox [(ngModel)] = "precadastro"
                    style="float: left; padding: 10px;" *ngIf="noExistInList"
                    name="precadastro">
                    Salvar no pré-cadastro de objetivos para que seja utilizado em outros Planos de Intervenção
                </mat-checkbox>
            </section>

            <section *ngIf="hasAccessUpdate">
                <mat-checkbox [(ngModel)] = "updatePrecadastro"
                    style="float: left; padding: 10px;" *ngIf="ExistInListEdit"
                    name="updatePrecadastro">
                    Atualizar no pré-cadastro de objetivos
                </mat-checkbox>
            </section>
        </form>
    </mat-card-content>
    <mat-card-actions class="card-actions" *ngIf="!data">
        <button mat-raised-button color="primary" (click)="save()"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()">
            Cancelar
        </button>
    </mat-card-actions>
    <mat-card-actions class="card-actions" *ngIf="data">
        <button mat-raised-button color="primary" (click)="addObjetivoPersonalizado()">
            Salvar
        </button>
        <button mat-raised-button mat-dialog-close>
            Cancelar
        </button>
    </mat-card-actions>
</mat-card>
