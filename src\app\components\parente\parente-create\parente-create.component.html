<mat-card> 
    <mat-card-title class="title">{{ parente.id == undefined ? "Novo Parente" : parente.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <!--
                parentesco: string;
            -->
            <mat-form-field style="width: 60%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="parente.nome" name="nome" required>
                <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" [textMask]="{mask: cpfMask }" matInput placeholder="CPF" 
                    [(ngModel)]="parente.cpf" name="cpf">
            </mat-form-field>

            <mat-form-field  style="width: 20%;">
                <mat-label>Parentesco</mat-label>
                <mat-select placeholder="Parentesco" 
                    [(ngModel)]="parente.parentesco"
                    name="parentesco" required>
                    <mat-option *ngFor="let parentesco of parentescos" [value]="parentesco.nome" >
                        {{parentesco.nome}}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="parentescoFC.invalid">Parentesco é obrigatório</mat-error>  
            </mat-form-field>

            <mat-form-field  style="width: 10%;">
                <mat-label>Sexo</mat-label>
                <mat-select class="select" placeholder="Sexo" 
                    [(ngModel)]="parente.sexo"
                    name="sexo" required>
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option value="M" >
                    Masculino
                    </mat-option>
                    <mat-option value="F" >
                        Feminino
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="sexo.invalid">Sexo é obrigatório</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Data de Nascimento" 
                    [(ngModel)]="parente.dataNascimento" name="dataNascimento"
                    [matDatepicker]="picker">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="dataNascimento.invalid">Data de nascimento é obrigatória.</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;" >
                <input class="input" *ngIf="tipoTelefone == 'C'" matInput placeholder="Telefone" [(ngModel)]="parente.telefone" (focusout)="validaMaskTelefone()" (focus)="tipoTelefone = 'C'" [textMask]="{mask: celularMask}" name="telefone" >
                <input class="input" *ngIf="tipoTelefone == 'T'" matInput placeholder="Telefone" [(ngModel)]="parente.telefone" (focusout)="validaMaskTelefone()" (focus)="tipoTelefone = 'C'" [textMask]="{mask: telefoneMask}" name="telefone" >
            </mat-form-field>
            
            <mat-form-field style="width: 40%;">
                <input class="input" matInput placeholder="E-mail" type="email" email
                    [(ngModel)]="parente.email" name="email" required
                    oninput="this.value = this.value.toLowerCase()">
                <mat-error *ngIf="email.invalid">E-mail inválido.</mat-error>  
            </mat-form-field>    
            
        </div>
        <mat-checkbox name="loginUser" [(ngModel)]="parente.loginUser">Usuário de login</mat-checkbox>
    </form>

    <button mat-raised-button (click)="save()" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>