import { HttpBackend, HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})

//TODO: Não permitir pacientes com o mesmo nome

export class EnderecoService {

 urlApi = 'http://viacep.com.br/ws/'

  constructor(private http: HttpClient, handler: HttpBackend) {
    this.http = new HttpClient(handler);
   }

    find(cep): Observable<any>{
        return this.http.get<any>(this.urlApi+cep+'/json');    
    }

}