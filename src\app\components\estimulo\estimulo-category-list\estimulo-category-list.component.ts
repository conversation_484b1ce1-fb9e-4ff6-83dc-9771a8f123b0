import { Component, OnInit } from "@angular/core";
import { GrupoEstimuloService } from "../grupoestimulo.service";
import { GrupoEstimulo } from "./../grupoestimulo-model";
import { Router } from "@angular/router";
import { MatDialog } from "@angular/material/dialog";
import { DeleteConfirmDialogComponent } from "../../template/delete-confirm-dialog/delete-confirm-dialog.component";
import { ConfirmDialogComponent } from "../../template/confirm-dialog/confirm-dialog.component";
import { Estimulo } from "../estimulo-model";
import { EstimuloService } from "../estimulo.service";

@Component({
  selector: "app-estimulo-category-list",
  templateUrl: "./estimulo-category-list.component.html",
  styleUrls: ["./estimulo-category-list.component.css"],
})
export class EstimuloCategoryListComponent implements OnInit {
  public grupoEstimulos: GrupoEstimulo[];
  public estimulos: Estimulo[];

  displayedColumns = ["categoria", "action"];

  constructor(
    private grupoEstimuloService: GrupoEstimuloService,
    private estimuloService: EstimuloService,
    public dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.grupoEstimuloService.find().subscribe((grupoEstimulos) => {
      this.grupoEstimulos = grupoEstimulos
        .filter((p) => p.ativo || p.ativo == null || p.ativo == undefined)
        .sort((a, b) => a.nome.localeCompare(b.nome));
    });
    
    this.estimuloService.find().subscribe(estimulos => {
      this.estimulos = estimulos.filter((p) => p.ativo || p.ativo == null || p.ativo == undefined);
    })
  }

  edit(grupo: GrupoEstimulo) {
    this.router.navigate(["/estimulo/category/" + grupo.id]);
  }

  delete(grupo: GrupoEstimulo): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: "500px",
      data: {
        msg: 'Ao excluir esta categoria, os estímulos vinculados a ela também serão removidos permanentemente. Deseja prosseguir com a exclusão?'
      }
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        grupo.ativo = false;
        this.grupoEstimuloService.update(grupo).subscribe(() => {
          this.grupoEstimuloService.showMessage("Categoria de estímulo inativado com sucesso!");
          this.estimulos.forEach(estimulo => {
            if (estimulo.grupo.id === grupo.id) {
              estimulo.ativo = false;
              estimulo.grupo.ativo = false;
              this.estimuloService.update(estimulo).subscribe()
            }}
        )
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = "reload";
          this.router.navigate(["/estimulo/category"]);
        });
      }
    });
  }
}
