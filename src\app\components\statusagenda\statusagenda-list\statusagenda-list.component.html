<div class="mat-elevation-z4">
    <table mat-table [dataSource]="statusAgendasList">  
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.nome}}</td>
        </ng-container>

         <!-- Confirmação cobrança Column -->
         <ng-container matColumnDef="confirmaCobranca" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Autoriza Cobrança</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.confirmaCobranca | boolean}}</td>
        </ng-container>

        
        <!-- Cor Agenda Column -->
        <ng-container matColumnDef="cor" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Cor Agenda</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70" >
                <div [ngStyle]="{'background-color': row.cor}" style="width: 20px; display: flex;">&nbsp;</div>
            </td>
        </ng-container>

        <!-- Status Inicial -->
        <ng-container matColumnDef="default" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Status Inincial</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70" >
                {{row.default | boolean}}
            </td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="edit(row)" class="edit">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row)" class="delete">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  