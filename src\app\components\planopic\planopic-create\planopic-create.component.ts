import { FirebaseUserModel } from './../../template/auth/user-model';
import { MatSelectChange } from '@angular/material/select';
import { PICService } from '../pic.service';
import { PacienteService } from './../../paciente/paciente.service';
import { AuthService } from './../../template/auth/auth.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Profissional } from './../../profissional/profissional-model';
import { Paciente } from './../../paciente/paciente-model';
import { MatTableDataSource } from '@angular/material/table';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { PIC } from '../pic-model';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { DateAdapter } from 'angular-calendar';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Avaliacao } from '../../avaliacao/avaliacao-model';
import { DominioAvaliacao } from '../../avaliacao/dominio-avaliacao-model';
import { NivelAvalicao } from '../../avaliacao/nivel-avaliacao-model';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import moment from 'moment';
import { async } from 'rxjs/internal/scheduler/async';
import { ConfirmDialogCustomData, ConfirmDialogCustomComponent } from '../../template/confirm-dialog-custom/confirm-dialog-custom.component';
import { ObjetivoPICService } from '../../objetivopic/objetivopic.service';
import { ObjetivoComportamental } from '../../objetivopic/objetivo-comportamental-model';
import { ObjetivoPICCreateComponent } from '../../objetivopic/objetivopic-create/objetivopic-create.component';

@Component({
  selector: 'app-planopic-create',
  templateUrl: './planopic-create.component.html',
  styleUrls: ['./planopic-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})

export class PlanoPICCreateComponent implements OnInit {

  public pic: PIC = new PIC();
  public paciente: Paciente = new Paciente();
  public profissionaisDoPaciente: Profissional[];
  public idPaciente: string;
  public objetivos: ObjetivoComportamental[];

  public nivel: NivelAvalicao = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();

  public profissionais: Profissional[];
  
  public avaliacoesPlano: Avaliacao[] = [];

  listaDeObjetivosDoPlano: any[] = [];

  saveDisabled: boolean = false;

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessCreateObjetivo: boolean;

  //Form Controls
  data = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);
  ativo = new FormControl(false, [Validators.required]);

  @ViewChild(NgForm) form;

  datasourceObjetivos = new MatTableDataSource();

  planosPIC: any;
  displayedColumnsObjs = ['index', 'compAlvo', 'defOp', 'tipoColeta', 'action']
  displayedColumnsObjsSelList = ['compAlvoObjSelList', 'defOpObjSelList', 'tipoColetaObjSelList'];
  
  constructor(private picService: PICService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private objetivoPICService: ObjetivoPICService,
    private loadingService: LoadingService
  ) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    this.pic.idPaciente = this.idPaciente;
    const idPlanoIntervencao = this.route.snapshot.paramMap.get('idPICPlan');
  
    try {
      // Carrego os profissionais vinculados ao paciente
      const paciente = await this.pacienteService.findById(this.idPaciente).toPromise();
      this.profissionaisDoPaciente = paciente.equipe;
      this.paciente = paciente;

      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','create');
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','update');
      this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','delete');
      this.hasAccessCreateObjetivo = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Objetivo.Cadastro de objetivos (VBMAPP)','create')
  
      // Carregando Profissionais
      const profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = profissionais;
  
      if (!idPlanoIntervencao) { // Create
        const user = this.authService.getUser() as FirebaseUserModel;
        if (user.profissional || !user.familia) {
          const profissional = this.profissionais.find(p => p.uid === user.uid);
          if (profissional) {
            this.pic.idProfissional = profissional.id;
          }
        }
        this.pic.ativo = false;
      }
  
      if (idPlanoIntervencao) { // Update
        // Carrego o plano de intervenção a ser editado
        const plano = await this.picService.findById(idPlanoIntervencao).toPromise();
        if (plano) {
          this.pic = plano;
          if (!this.pic.objetivos) {
            this.pic.objetivos = [];
          }
          this.datasourceObjetivos.data = this.pic.objetivos;
          this.listaDeObjetivosDoPlano = [...this.pic.objetivos];

          // Se o campo ativo não existir, inicialize como true (ou false, conforme sua regra)
          if (this.pic.ativo === undefined) {
            this.pic.ativo = false;
          }
  
          // Garantir que data seja do tipo Date
          this.avaliacoesPlano.forEach(a => {
            if (!(a.data instanceof Date)) {
              a.data = new Date(a.data);
            }
          });
  
          // Ordenando as avaliacoes do plano por data
          this.avaliacoesPlano.sort((a, b) => b.data.getTime() - a.data.getTime());
        } else {
          throw new Error('Plano de intervenção não encontrado');
        }
      } else {
        this.listaDeObjetivosDoPlano = [];
        // Aplicando a data de hoje ao Plano de Intervenção
        this.pic.data = new Date();
      }

      // Seto os objetivos em conexão com o filter
      const objetivos = await this.objetivoPICService.find().toPromise();
      this.objetivoPICService.objetivosPIC.next(objetivos);

      // Agora assine para filtrar
      this.objetivoPICService.objetivosPIC.subscribe((data) => {
        this.objetivos = data.filter(obj => !this.listaDeObjetivosDoPlano?.some(o => o.id == obj.id));
      });

    } catch (error) {
      console.error('Error loading data', error);
    } finally {
      this.loadingService.hide();
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }
  
  async addObjetivo(idObjetivo: any){
    let obj: any;

    if(this.validForm()){
      //Verifico se o array de objetivos está vazio. Caso esteja, inicializo.
      if(this.pic.objetivos == undefined){
        this.pic.objetivos = [];
      }

      //Verifico se o objetivo já foi adicionado anteriormente
      if( this.pic.objetivos.find(obj => obj.id == idObjetivo) == undefined) {

        obj = this.objetivos.find(o => o.id == idObjetivo);
        this.pic.objetivos.push(obj);

        //Ordenando o array de objetivos
        await this.ordenarObjetivos();
        
        // Certifique-se de que a lista atualizada será enviada ao filtro
        this.listaDeObjetivosDoPlano.push(obj);
        this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano]; // Atualiza a referência

        this.objetivos = [...this.objetivos.filter(obj => this.listaDeObjetivosDoPlano?.find(o => o.id == obj.id) == undefined)]

        this.datasourceObjetivos.data = this.pic.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        this.save().catch(e => {
        });
      }
    } else {
      this.picService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  async deleteObjetivo(objetivo: any){
    if(this.validForm()){
      var id = this.pic.objetivos.indexOf(objetivo);
      this.pic.objetivos.splice(id, 1);
      this.pic.objetivos = [...this.pic.objetivos];
      this.datasourceObjetivos.data = this.pic.objetivos;
      this.listaDeObjetivosDoPlano = this.listaDeObjetivosDoPlano.filter(o => o.id != objetivo.id);
      this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];

      this.objetivos.push(objetivo);
      this.objetivos = [...this.objetivos];

      this.save().catch(e => {
        console.log(e);
        // ok = false;
      });;
      
    } else {
      this.picService.showMessage('Existem campos inválidos no formulário!',true);
    } 
  }

  async editObjetivo(objetivo: any){
    localStorage.removeItem("pic_objetivo");
    if (!this.pic.id) {
      await this.save();
    }

    this.dialog.open(ObjetivoPICCreateComponent, {
    data:{
      idPaciente: this.idPaciente,
      idPICPlan: this.pic.id,
      idObjetivo: objetivo.id,
    }
    }).afterClosed().subscribe(async () => {
      if(localStorage.getItem("pic_objetivo")){
        let idx = this.pic.objetivos.findIndex(obj => obj.id == objetivo?.id);
        if (!idx) {
          idx = this.pic.objetivos.findIndex(obj => obj.comportamentoAlvo == objetivo?.comportamentoAlvo);
        }
        this.pic.objetivos[idx] = (JSON.parse(localStorage.getItem("pic_objetivo")) as ObjetivoComportamental);
        this.datasourceObjetivos.data = this.pic.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        await this.ordenarObjetivos();
        await this.save();
      }
    });      
  }

  async ordenarObjetivos() {
    this.pic.objetivos.sort(function (a, b) {
      if( a.comportamentoAlvo > b.comportamentoAlvo ) {
        return 1;
      }
      if( a.comportamentoAlvo < b.comportamentoAlvo ) {
        return -1;
      }
      return 0;
    })

    this.pic.objetivos = [...this.pic.objetivos];
  }

  setProfissional(event:MatSelectChange){
    let profissional = this.profissionais.find(p => p.id == event.value);
    this.pic.idProfissional = profissional.id;
    if(this.pic.objetivos != undefined){
      this.save();
    } 
  }

  validForm(){
    if(this.form.valid){
      return true;
    } else {
      this.picService.showMessage('Existem campos inválidos no formulário!',true);
      return false;
    }
  }

  save(): Promise<void>{
    let ok: boolean;
    const promise = new Promise<void>((resolve, reject) => {
      // console.log(this.pic);
      if(this.saveDisabled == false){
        if(this.form.valid){
          this.saveDisabled=true;
          if(this.pic.id == undefined){
            this.pic.status = true;
            this.picService.create(this.pic).subscribe((id) => {
                this.saveDisabled=false;
                this.pic.id = id;
              ok = true;
              resolve();
            });
          } else {
            this.pic.status = true;
            this.picService.update(this.pic).subscribe((paciente) => {
              this.saveDisabled=false;
              ok = true;
              resolve();
            });
          }
        } else {
          this.saveDisabled=false;
          this.picService.showMessage('Existem campos inválidos no formulário!',true);
          ok = false;
          reject();
        } 
      } else {
        this.saveDisabled=false;
        ok = false;
        reject();
      }
    })
    return promise;
  }

  async setMsAssmtPorData(){
    if(this.form.valid){
      if (await this.hasPEISameDate(this.pic.data)) {
        const data: ConfirmDialogCustomData = {
          message: 'Já existe um PIC com a mesma data, como deseja prosseguir?',
          options: [
            'Criar novo PIC mesmo assim',
            'Adicionar objetivos do outro PIC neste',
            'Cancelar'
          ]
        };
  
        const dialogRef = this.dialog.open(ConfirmDialogCustomComponent, {
          width: 'auto',
          data
        });
  
        dialogRef.afterClosed().subscribe(async result => {
          switch (result) {
            case 'Criar novo PIC mesmo assim':
              await this.save();
              break;
  
            case 'Adicionar objetivos do outro PIC neste':
              // Busca o outro plano com a mesma data
              const formattedDate = moment(this.pic.data).format('YYYY-MM-DD');
              const planosPIC = this.planosPIC.filter(plano => plano.data && moment(plano.data).format('YYYY-MM-DD') === formattedDate);
              const outroPlano = await this.picService.findById(planosPIC[0].id).toPromise();
  
              this.pic?.objetivos ? this.pic.objetivos : this.pic.objetivos = [];
              
              // Adiciona os objetivos do outro plano ao atual (evita duplicidade)
              outroPlano?.objetivos.forEach(obj => {
                if (!this.pic?.objetivos.find(o => o.id === obj.id)) {
                  this.pic?.objetivos.push(obj);
                  this.listaDeObjetivosDoPlano.push(obj);
                  this.listaDeObjetivosDoPlano = [...this.listaDeObjetivosDoPlano]; // Atualiza a referência
                }
              });

              this.objetivos = [...this.objetivos.filter(obj => this.listaDeObjetivosDoPlano?.find(o => o.id == obj.id) == undefined)]

              this.datasourceObjetivos.data = this.pic.objetivos;
              this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
  
              // Desativa o outro plano
              outroPlano.status = false;
              await this.picService.update(outroPlano).toPromise();
              
              // Salva o plano atual como ativo
              await this.save();
              break;
  
            case 'Cancelar':
              break;
          }
        });
      } else {
        await this.save();
      }
    }
  }

  async hasPEISameDate(date: Date = new Date()): Promise<boolean> {
    const formattedDate = moment(date).format('YYYY-MM-DD');

    // Caso a lista de planos ainda não tenha sido carregada, carrego-a
    if (!this.planosPIC) {
      this.planosPIC = await this.picService.findResumoByPaciente(this.idPaciente).toPromise();
    }
    // Filtra os planos que possuem a mesma data formatada
    const planosPIC = this.planosPIC.filter(plano => plano.data && moment(plano.data).format('YYYY-MM-DD') === formattedDate && plano?.id != this.pic?.id);
    return planosPIC.length > 0;
  }

  exitEdit(){
    if (this.pic.id != undefined) {
      if (this.pic.ativo == false) {
        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
          width: 'auto',
          height: 'auto',
          data: {
            valida: true,
            msg: 'Esse PIC está inativo para coleta! Caso mantenha assim o pic mais recente será disponibilizado para coleta. Deseja realmente sair?'
          } 
        });
    
        dialogRef.afterClosed().subscribe(result => {
          if(result){
            if(!this.pic.objetivos || this.pic.objetivos.length == 0){
              this.router.navigate(['/paciente/' + this.idPaciente, {
                tab:"pic"
              }])
            }
        
            if(this.pic.id == undefined){
              this.router.navigate(['/paciente/' + this.idPaciente, {
                tab:"pic"
              }])
            } else {
              this.router.navigate(['/paciente/' + this.idPaciente, {
                tab:"pic",
                idPICPlan: this.pic.id
              }])
            }
          } else {
            return;
          }
        });
      } else {
        if(!this.pic.objetivos || this.pic.objetivos.length == 0){
          this.router.navigate(['/paciente/' + this.idPaciente, {
            tab:"pic"
          }])
        }
    
        if(this.pic.id == undefined){
          this.router.navigate(['/paciente/' + this.idPaciente, {
            tab:"pic"
          }])
        } else {
          this.router.navigate(['/paciente/' + this.idPaciente, {
            tab:"pic",
            idPICPlan: this.pic.id
          }])
        }
      }
    } else {
      this.router.navigate(['/paciente/' + this.idPaciente, {
        tab:"pic"
      }])
    }
  }

  async newObjetivo(){
    if(this.pic.id == undefined){
      await this.save()
    }
    console.log("pic", this.pic);
    localStorage.removeItem("pic_objetivo")
    this.dialog.open(ObjetivoPICCreateComponent, {
      data:{
        idPaciente: this.idPaciente,
        idPICPlan: this.pic.id
      }
    }).afterClosed().subscribe(async () => {
      if(localStorage.getItem("pic_objetivo")){
        if (!this.pic.objetivos) {
          this.pic.objetivos = [];
        }
        let obj = (JSON.parse(localStorage.getItem("pic_objetivo")) as ObjetivoComportamental);
        this.pic.objetivos.push(obj);
        this.datasourceObjetivos.data = this.pic.objetivos;
        this.datasourceObjetivos.data = [...this.datasourceObjetivos.data];
        await this.ordenarObjetivos();
        await this.save();
      }
    });   
  }

  changeStatus(e: any){
    if(e.value){
      this.pic.ativo = true;
      this.save();
    }else{
      this.pic.ativo = false;
      this.save();
    }
  }

  do(){}
}
