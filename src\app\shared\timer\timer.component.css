.contador-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 10px;
    box-sizing: border-box;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    position: relative;
}

.cronometro {
    border: 3px solid #004A7F;
    border-radius: 50%;
    width: 150px; 
    height: 150px; 
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
    text-align: center;
    position: relative;
    margin-bottom: 10px; /* Espaçamento entre cronômetro e lista de tempos */
}

.timer-display {
    font-size: 18px;
    font-weight: bold;
}

.timer-label-container {
    text-align: center;
    width: 100%;
}

.timer-label {
    font-size: 12px;
    font-weight: bold;
    color: #004A7F;
    margin-top: 5px;
    border-top: 2px solid #004A7F;
    padding-top: 2px;
    display: inline-block;
    width: 90%;
}

.timer-description {
    font-size: 12px;
    font-weight: bold;
    color: #004A7F;
    text-align: center;
}

.cronometro-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.btn-play {
    background-color: #004A7F;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.btn-square:hover, .btn-play:hover {
    background-color: #002F4F;
}

.time-list {
    width: 100%;
    display: flex;
    flex-direction: column; /* Mantém a lista em uma coluna */
    align-items: center;
    margin: auto; /* Centraliza a lista dentro do item */
}

.time-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 7px;
    width: 100%; /* Mantém a largura fixa para evitar deslocamento */
    max-width: 200px; /* Controla a largura máxima para inputs */
}

.time-entry button {
    background: none;
    border: none;
    color: #004A7F;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
    min-width: 0;
    margin-left: 2px;
    text-align: center;
}

.time-edit-inputs {
    display: flex;
    align-items: center;
}

/* Novo CSS para estilizar o input */
.time-input {
    border: none;
    background-color: transparent;
    font-size: 12px;
    text-align: center; /* Centraliza o texto */
    width: 25px; /* Ajusta a largura para H, M e S */
    padding: 0;
    margin: 0 2px; /* Pequena margem lateral */
    border-bottom: 1px solid #ccc; /* Borda inferior para se assemelhar ao texto */
    font-family: inherit; /* Mantém a mesma fonte do texto */
}

.add-time-button {
    background: none;
    border: none;
    color: #004A7F;
    cursor: pointer;
    font-size: 12px;
    margin-left: 2px;
    text-align: center;
    margin-top: 10px;
}

.time-edit-inputs span {
    font-weight: bold;
    margin-right: 3px;
}

.time-input:focus {
    outline: none;
    border-bottom: 1px solid #004A7F; /* Destaque na borda inferior ao focar */
}

.custom-backdrop {
    background-color: rgba(0, 0, 0, 0.2); /* Ajuste a opacidade conforme necessário */
}

/* Estilização do checkbox */
.confirm-checkbox-container {
    margin-top: 15px;
    display: flex;
    align-items: center;
}
  
.confirm-checkbox-container label {
    font-size: 14px;
    margin-right: 10px;
    color: #333;
}

.confirm-checkbox-container input {
    transform: scale(1.2); 
}

.truncatable {         /* Evita que o texto quebre em várias linhas */
    overflow: hidden;            /* Oculta o texto que ultrapassar a largura da div */
    text-overflow: ellipsis;     /* Adiciona os "..." quando o texto for muito longo */
    max-width: 100%;             /* Limita a largura ao tamanho da div */
}
  
.tooltip-container {
    position: absolute;
    top: 0;
    right: 0;
    margin: 10px;
}

.tooltip-container {
    position: absolute;
    top: 0;
    right: 0;
    margin: 10px; /* Ajuste de margem opcional */
}

.tooltip-descricao-text {
    visibility: hidden;
    background-color: rgb(29, 29, 29);
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 3px;
    position: absolute;
    z-index: 1;
    top: 50%; 
    left: -10px; 
    transform: translateX(-100%) translateY(-50%); 
    width: 150px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 9px;
    white-space: pre-line;
    letter-spacing: 0.8px;
}

.tooltip-text {
    visibility: hidden;
    background-color: rgb(29, 29, 29);
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 3px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -80px;
    width: 160px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 9px;
    white-space: pre-line;
    letter-spacing: 0.8px;
}

.tooltip-container:hover .tooltip-descricao-text{
    visibility: visible;
    opacity: 0.65;
}

.tooltip-container:hover .tooltip-text{
    visibility: visible;
    opacity: 0.65;
}

.custom-cursor {
    pointer-events: none;
}
  