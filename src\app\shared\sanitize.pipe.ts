import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "sanitize"
})
export class SanitizePipe implements PipeTransform {

  transform(value: string): string {
    if (!value) return "";
    return value
      .replace(/“/g, "\"")
      .replace(/”/g, "\"")
      .replace(/‘/g, "\"")
      .replace(/’/g, "\"")
      .replace(/&/g, "\&")
      .replace(/ã/g, "\ã")
      .replace(/á/g, "\á")
      .replace(/é/g, "\é")
      .replace(/í/g, "\í")
      .replace(/ó/g, "\ó")
      .replace(/ú/g, "\ú")
      .replace(/ç/g, "\ç")
      .replace(/Á/g, "\Á")
      .replace(/É/g, "\É")
      .replace(/Í/g, "\Í")
      .replace(/Ó/g, "\Ó")
      .replace(/Ú/g, "\Ú")
      .replace(/Ç/g, "\Ç")
      .replace(/â/g, "\â")
      .replace(/ê/g, "\ê")
      .replace(/î/g, "\î")
      .replace(/ô/g, "\ô")
      .replace(/û/g, "\û")
      .replace(/Â/g, "\Â")
      .replace(/Ê/g, "\Ê")
      .replace(/Î/g, "\Î")
      .replace(/Ô/g, "\Ô")
      .replace(/Û/g, "\Û")
      .replace(/à/g, "\à")
      .replace(/è/g, "\è")
      .replace(/ì/g, "\ì")
      .replace(/ò/g, "\ò")
      .replace(/ù/g, "\ù")
      .replace(/À/g, "\À")
      .replace(/È/g, "\È")
      .replace(/Ì/g, "\Ì")
      .replace(/Ò/g, "\Ò")
      .replace(/Ù/g, "\Ù")
      .replace(/ä/g, "\ä")
      .replace(/ë/g, "\ë")
      .replace(/ï/g, "\ï")
      .replace(/ö/g, "\ö")
      .replace(/ü/g, "\ü")
      .replace(/Ä/g, "\Ä")
      .replace(/Ë/g, "\Ë")
      .replace(/Ï/g, "\Ï")
      .replace(/Ö/g, "\Ö")
      .replace(/Ü/g, "\Ü")
      .replace(/ñ/g, "\ñ")
      .replace(/Ñ/g, "\Ñ")
      .replace(/–/g, "\-")
      .replace(/—/g, "\-")
      .replace(/…/g, '...')
      .replace(/ā/g, "\ã"); 
  }
}
