<mat-dialog-content>
    <div *ngIf="agendamentos.choques.length" >
        <label style="color: red; font-size: 10px;" > * As datas em vermelho, indicam o choque de hor<PERSON>rio.</label>
        <hr>
    </div>
    
    <div class="history-tl-container">
        <ul class="tl">
        <li class="tl-item" *ngFor="let agenda of agendamentos.agendamentos" ng-repeat="item in retailer_history">
            <div class="timestamp" >
                <span [style.color]="validaChoque(agenda.id) ? 'red' : 'gray'">{{agenda.start | date}}</span>
            <br>
            {{agenda.title.split('-')[0]}}
            <br>
            {{agenda.meta.local.nome}}
            </div>
            <div class="item-title">{{agenda.meta.procedimento.nome}}</div>
            <div class="item-detail">{{agenda.meta.paciente.nome}}</div>
        </li>
        </ul>
    
    </div>
</mat-dialog-content>