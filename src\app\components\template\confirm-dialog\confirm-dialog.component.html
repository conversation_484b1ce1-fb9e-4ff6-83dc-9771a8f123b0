<div fxLayout="row" fxLayoutWrap="wrap">
	<!-- Card column -->
	<div fxFlex="100">
		<mat-card class="mat-elevation-z0">
			<mat-card-content>
				<!--mat-card-title>Confirmação de exclusão</mat-card-title-->
                <div mat-card fxLayout="row wrap" fxLayoutAlign="start none"
                    fxLayoutGap="10px">
                    <span fxFlex="100" class="area_interna" [innerHTML]="data.msg">
                    </span>
                </div>
            </mat-card-content>
            <mat-card-actions>
                <button mat-raised-button style="margin: 10px;" color="primary" 
                    [mat-dialog-close]="true" cdkFocusInitial>Sim</button>
                <button mat-raised-button style="margin: 10px;" (click)="cancel()">Não</button>
            </mat-card-actions>
        </mat-card>
    </div>
</div>