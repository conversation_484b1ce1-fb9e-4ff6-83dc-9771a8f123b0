<mat-card class="mat-elevation-z0">
    <mat-card-content fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row wrap" style="display: flex;"> 
            <div style="display: flex; width: 80%;" fxLayout="row wrap">
                <mat-form-field  style="width: 20%; padding: 20px;" *ngIf="planointervencao?.id != undefined">
                    <mat-label>Plano de Ensino Individualizado</mat-label>
                    <mat-select placeholder="Plano de Intervenção" 
                        [(ngModel)]="planointervencao"
                        name="planointervencao"
                        (selectionChange) = "setObjetivosPlano()">
                        <mat-option *ngFor="let plano of planosintervencao" [value]="plano" >
                            {{plano.data | date: 'dd/MM/yyyy'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <mat-form-field  style="width: 20%; padding: 20px;" *ngIf="hasAccessUpdate && planointervencao?.id != undefined">
                    <mat-label>Status</mat-label>
                    <mat-select placeholder="Status" 
                        [(ngModel)]="planointervencao.ativo"
                        name="ativo" disabled>
                        <mat-option [value]="false">
                            Rascunho 
                        </mat-option>
                        <mat-option [value]="true">
                            PEI liberado para coleta 
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div style="display: flex; width: 30%;" fxLayout="row wrap">
                <ng-container *ngIf="planointervencao?.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="saveToPDFResumido()"
                        matTooltip="Imprimir Resumido"
                        *ngIf="hasAccessRead">
                        <!--mat-icon>picture_as_pdf</mat-icon-->
                        <mat-icon>summarize</mat-icon>
                    </button>
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="saveToPDF()"
                        matTooltip="Imprimir Detalhado"
                        *ngIf="hasAccessRead">
                        <!--mat-icon>picture_as_pdf</mat-icon-->
                        <mat-icon>print</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="planointervencao?.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="graph()"
                        matTooltip="Gráfico de Evolução"
                        *ngIf="hasAccessRead">
                        <mat-icon>leaderboard</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="planointervencao?.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="edit()"
                        matTooltip="Editar"
                        *ngIf="hasAccessUpdate">
                        <mat-icon>edit</mat-icon>
                    </button>
                </ng-container>
                <ng-container *ngIf="planointervencao?.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="edit()"
                        matTooltip="Visualizar"
                        *ngIf="hasAccessRead &&
                               !hasAccessUpdate">
                        <mat-icon>remove_red_eye</mat-icon>
                    </button>
                </ng-container>
                <button mat-mini-fab color="primary" style="margin: 10px" (click)="add()"
                    matTooltip="Incluir Novo"
                    *ngIf="hasAccessCreate">
                    <mat-icon>add</mat-icon>
                </button>
                <ng-container *ngIf="planointervencao?.id != undefined">
                    <button mat-mini-fab color="primary" style="margin: 10px" (click)="delete(planointervencao.id)"
                        matTooltip="Excluir"
                        *ngIf="hasAccessDelete">
                        <!--mat-icon>picture_as_pdf</mat-icon-->
                        <mat-icon>delete</mat-icon>
                    </button>
                </ng-container>
            </div>
        </div>
        <div *ngIf="planosintervencao.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
            <h1>Nenhum PEI cadastrado!</h1>
        </div>
        <div>
            <div fxLayout="row wrap" style="width:100%"
                *ngIf="hasAccessRead && planointervencao?.data != undefined && planointervencao?.idTipoAvaliacao != 5"> 
                <strong>Habilidades</strong><br>
                <mat-chip-list style="padding: 10px;">
                    <mat-chip *ngFor="let habilidade of planointervencao?.habilidades" [value]="habilidade"
                        removable="false">
                        <ng-container *ngIf="habilidade != undefined && !habilidade.etapa">
                            {{ habilidade?.nomeTipoAvaliacao }} - {{habilidade.sigla}}
                        </ng-container>
                        <ng-container *ngIf="habilidade != undefined && habilidade.etapa">
                            [ESDM] - {{ habilidade?.id_nivel }} - {{ habilidade.dominio?.nome }} - {{ habilidade?.id.substr(4) }}
                        </ng-container>
                    </mat-chip>
                </mat-chip-list>
                <div *ngIf="!planointervencao?.habilidades || planointervencao.habilidades?.length == 0" style="align-items: center; text-align: center; margin-top: 30px;">
                    <h3>Nenhuma habilidade cadastrada no PEI!</h3>
                </div>
            </div>
            <div class="mat-elevation-z0" style="padding: 10px 0 30px 0;" 
                *ngIf="hasAccessRead && planointervencao?.data != undefined">
                <strong>Objetivos</strong>
                <!--a mat-raised-button href="javascript:void()" (click)="toggleTableRows()" color="primary">Toggle Rows</a-->
                <table mat-table [dataSource]="datasourceObjetivos" multiTemplateDataRows>
                    <!-- Index Column --> 
                    <ng-container matColumnDef="index">
                        <th mat-header-cell *matHeaderCellDef >#</th>
                        <td mat-cell *matCellDef="let row">{{ planointervencao?.objetivos?.indexOf(row) + 1 }}</td>
                    </ng-container>

                    <!-- Expand Column --> 
                    <ng-container matColumnDef="expand">
                        <th mat-header-cell *matHeaderCellDef ></th>
                        <td mat-cell *matCellDef="let row">
                            <a style="cursor: pointer; color: darkgray;" (click)="toggleTableRow(row)">
                                <mat-icon>{{ row.isExpanded ? "keyboard_arrow_up" : "keyboard_arrow_down" }}</mat-icon>
                            </a>
                        </td>
                    </ng-container>

                    <!-- etapasSum Column --> 
                    <ng-container matColumnDef="etapas"> 
                        <th mat-header-cell *matHeaderCellDef ></th>
                        <td mat-cell *matCellDef="let row">
                            <!-- Coleta Naturalista: por Tipo de Suporte -->
                            <ng-container *ngIf="row.tipoColeta == 'Naturalista'"> 
                                <div [ngClass]="percentualTiposSuporte(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualTiposSuporte(row) < 1">
                                    <small>{{ countTiposSuporteAdquiridos(row) }} / {{ countTiposSuporte(row) }}</small>
                                </div>
                                <div class="label label-ok" style="width: 20%;" *ngIf="percentualTiposSuporte(row) == 1">
                                    <small>{{ countTiposSuporteAdquiridos(row) }} / {{ countTiposSuporte(row) }}</small>
                                </div>
                            </ng-container>
                            
                            <!-- Coleta Tradicional: por Estímulo e Tipo de Suporte -->
                            <ng-container *ngIf="row.tipoColeta != 'Naturalista' && row.descricao_plano == undefined">
                                    <div [ngClass]="percentualEstimulos(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEstimulos(row) < 1">
                                        <small>{{ countEstimulosAdquiridos(row) }} / {{ countEstimulos(row) }}</small>
                                    </div>
                                    <div class="label label-ok" style="width: 20%;" *ngIf="percentualTiposSuporte(row) == 1">
                                        <small>{{ countEstimulosAdquiridos(row) }} / {{ countEstimulos(row) }}</small>
                                    </div>
                            </ng-container>

                            <!-- etapasSum Column --> 
                            <ng-container *ngIf="!row.habilidades">
                                <div [ngClass]="percentualEtapas(row) <= 0.79 ? 'label label-atrasado' : 'label label-aviso'" style="width: 20%;" *ngIf="percentualEtapas(row) < 1">
                                    <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                                </div>
                                <div class="label label-ok" style="width: 20%;" *ngIf="percentualEtapas(row) == 1">
                                    <small>{{ countEtapasAdquiridas(row) }} / {{ countEtapas(row) }}</small>
                                </div>
                            </ng-container>
                        </td>

                    </ng-container>

                    <!-- Nome Column --> 
                    <ng-container matColumnDef="nomeObj">
                        <th mat-header-cell *matHeaderCellDef >Nome</th>
                        <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.nome}}</td>
                    </ng-container>
                    
                    <!-- Habilidades Column --> 
                    <ng-container matColumnDef="habilidades">
                        <th mat-header-cell *matHeaderCellDef>Habilidades</th>
                        <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">
                            <mat-chip-list style="padding: 10px;">
                                <mat-chip *ngFor="let habilidade of row.habilidades" [value]="habilidade"
                                    removable="false">
                                    {{ getNomeTipoAvaliacao(habilidade) }}
                                </mat-chip>
                            </mat-chip-list>
                            <mat-chip-list style="padding: 10px;">
                                <mat-chip *ngIf="!row.habilidades" [value]="habilidade">
                                    {{ getNomeTipoDominio(row.dominio, row.nivel, row.id) }}
                                </mat-chip>
                            </mat-chip-list>
                        </td>
                    </ng-container>

                    <!-- Estímulos Column --> 
                    <ng-container matColumnDef="estimulos">
                        <th mat-header-cell *matHeaderCellDef >Estímulos</th>
                        <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">
                            <mat-chip-list style="padding: 10px;">
                                <mat-chip *ngFor="let estimulo of row.estimulos" [value]="estimulo"
                                    removable="false"
                                    [disabled]="!estimulo.ativo">
                                    {{estimulo.nome}}
                                </mat-chip>
                                <mat-chip *ngIf="!row.estimulos" [value]="estimulo" [matTooltip]="'Objetivo não aplicável'">
                                    N/A
                                </mat-chip>
                            </mat-chip-list>
                            <!-- {{row.tipoSuporte.nome}} -->
                        </td>
                    </ng-container>

                    <!-- Tipo Column -->
                    <ng-container matColumnDef="tipoSuporte" fxFlex="30">
                        <th mat-header-cell *matHeaderCellDef fxFlex="30">Tipo de Suporte</th>
                        <td mat-cell *matCellDef="let row" fxFlex="30">
                            <mat-chip-list role="list" style="padding: 5px;" class="mat-chip-list-stacked">
                                <mat-chip role="listitem" *ngFor="let tipoSuporte of row.tiposSuporte" 
                                    [value]="tipoSuporte" class="custom-chip">
                                    {{tipoSuporte?.sigla}}
                                </mat-chip>
                                <mat-chip role="listitem" *ngIf="!row.tiposSuporte" 
                                    [value]="tipoSuporte" class="custom-chip-na" [matTooltip]="'Objetivo não aplicável'">
                                    N/A
                                </mat-chip>
                            </mat-chip-list>
                        </td>
                    </ng-container> 
                    
                    <!-- Tipo de Coleta Column --> 
                    <ng-container matColumnDef="tipoColeta">
                        <th mat-header-cell *matHeaderCellDef >Tipo de Coleta</th>
                        <td mat-cell *matCellDef="let row" [ngStyle]="{'font-weight':row.isExpanded ? 600 : normal}">{{row.tipoColeta || "Naturalista"}}</td>
                    </ng-container>

                    <!-- Action Column -->
                    <!-- <ng-container matColumnDef="action" fxFlex="30"> -->
                        <!--th mat-header-cell *matHeaderCellDef fxFlex="30"></th-->
                        <!-- <td mat-cell *matCellDef="let row" fxFlex="30">
                            <a style="padding-left: 10px; color: gray; cursor: pointer;" (click)="deleteObjetivo(planointervencao.objetivos.indexOf(row))">x</a>
                        </td>
                    </ng-container>  -->

                    <ng-container matColumnDef="expandedDetail">
                        <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumnsObjs.length">
                            <!-- Coleta Naturalista: por Tipo de Suporte -->
                            <ng-container *ngIf="element.tipoColeta == 'Naturalista'"> 
                                <div class="row student-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                                    <div style="padding: 0px 0px 10px 20px; width: 100%">{{element.descricao}}</div>
                                    <div class="divTable" style="padding: 0px 0px 10px 20px;">
                                        <div class="divTableHeading"> 
                                            <div class="divTableRow">
                                                <div class="divTableHead">Status</div>
                                                <div class="divTableHead">Tipo de Suporte</div>
                                                <ng-container *ngIf="((element.tiposSuporte != undefined) && (element.tiposSuporte.length > 0)
                                                    && (element.tiposSuporte[0]?.estimulos != undefined))">
                                                    <ng-container *ngFor="let estimulo of (element.tiposSuporte[0].estimulos || [])">
                                                        <ng-container *ngIf="estimulo.ativo">
                                                            <div class="divTableHead">{{estimulo.nome}}</div>
                                                        </ng-container>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </div>
                                        <div class="divTableBody">
                                            <ng-container *ngFor="let tipoSuporte of element.tiposSuporte" style="width:100%;">
                                                <div class="divTableRow">
                                                    <div class="divTableCell v-middle">
                                                        <div style="width: 5%;"  *ngIf="tipoSuporte?.status=='Adquirido'" >
                                                            <a style="cursor: pointer; color: green;"  (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                                <mat-icon>
                                                                    check
                                                                </mat-icon>
                                                            </a>
                                                        </div>
                                                        <div style="width: 5%;" *ngIf="tipoSuporte?.status=='Não adquirido' || tipoSuporte?.status == undefined">
                                                            <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                                <mat-icon>
                                                                    miscellaneous_services
                                                                </mat-icon>
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div class="divTableCell v-middle">{{tipoSuporte?.nome}}</div>
                                                    <ng-container *ngFor="let estimulo of tipoSuporte.estimulos">
                                                        <ng-container *ngIf="estimulo.ativo">
                                                            <div class="divTableCell v-middle">
                                                                <div style="width: 5%;"  *ngIf="estimulo.status=='Adquirido'" >
                                                                    <a style="cursor: pointer; color: green;"  (click)="changeTipoSuporteEstimuloStatus(estimulo, element.id, tipoSuporte.id)">
                                                                        <mat-icon>
                                                                            check
                                                                        </mat-icon>
                                                                    </a>
                                                                </div>
                                                                <div style="width: 5%;" *ngIf="estimulo.status=='Não adquirido' || estimulo.status == undefined">
                                                                    <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteEstimuloStatus(estimulo, element.id, tipoSuporte.id)">
                                                                        <mat-icon>
                                                                            miscellaneous_services
                                                                        </mat-icon>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </ng-container>
                                                    </ng-container>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </div>
                                    <!-- <mat-list style="width:100%">
                                    <div style="padding: 0px 0px 10px 0px; width: 100%">{{element.descricao}}</div>
                                    <mat-list-item *ngFor="let tipoSuporte of element.tiposSuporte" style="width:100%;">
                                        <div matline
                                            style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                            <div style="width: 5%;"  *ngIf="tipoSuporte.status=='Adquirido'" >
                                                <a style="cursor: pointer; color: green;"  (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                    <mat-icon>
                                                        check
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 5%;" *ngIf="tipoSuporte.status=='Não adquirido' || tipoSuporte.status == undefined">
                                                <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                    <mat-icon>
                                                        miscellaneous_services
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 90%; text-align: left;">
                                                <small>{{tipoSuporte.nome}}</small>
                                            </div>
                                        </div>
                                    </mat-list-item>
                                    </mat-list> -->
                                </div>
                            </ng-container>
                            
                            <!-- Coleta Tradicional: por Estímulo e Tipo de Suporte -->
                            <ng-container *ngIf="element.tipoColeta != 'Naturalista' && element.descricao_plano == undefined">
                                <div class="row student-element-detail" [@detailExpand]="element.isExpanded ? 'expanded' : 'collapsed'">
                                    <mat-list style="width:100%">
                                    <div style="padding: 0px 0px 10px 0px; width: 100%">{{element.descricao}}</div>
                                    <mat-list-item *ngFor="let estimulo of element.estimulos" style="width:100%;">
                                        <div matline
                                            style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                            <div style="width: 5%;"  *ngIf="estimulo.status=='Adquirido'" >
                                                <a style="cursor: pointer; color: green;"  (click)="changeEstimuloStatus(estimulo, element.id)">
                                                    <mat-icon>
                                                        check
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 5%;" *ngIf="estimulo.status=='Não adquirido' || estimulo.status == undefined">
                                                <a style="cursor: pointer; color: darkgray;" (click)="changeEstimuloStatus(estimulo, element.id)">
                                                    <mat-icon>
                                                        miscellaneous_services
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 90%; text-align: left;">
                                                <small>{{estimulo.nome}}</small>
                                            </div>
                                        </div>
                                        <!-- <div matline *ngIf="tipoSuporte.status=='Não adquirido' || tipoSuporte.status == undefined" 
                                            style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">   
                                            <div style="width: 5%;" *ngIf="tipoSuporte.status=='Não adquirido' || tipoSuporte.status == undefined">
                                                <a style="cursor: pointer; color: darkgray;" (click)="changeTipoSuporteStatus(tipoSuporte, element.id)">
                                                    <mat-icon>
                                                        miscellaneous_services
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 90%; text-align: left; ">
                                                <small>{{tipoSuporte.nome}}</small>
                                            </div>
                                        </div> -->
                                    </mat-list-item>
                                    </mat-list>
                                </div>
                            </ng-container>
                            
                            <ng-container *ngIf="!element.habilidades">
                                <div class="row student-element-detail" [@detailExpand]="(element != null && element.isExpanded) ? 'expanded' : 'collapsed'">
                                    <mat-list>
                                    <div style="padding: 0px 0px 10px 0px;">{{element.descricao_plano}}</div>
                                    <mat-list-item *ngFor="let etapa of element.etapa">
                                        <div matline *ngIf="etapa.status=='Adquirida'" 
                                            style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">
                                            <div style="width: 5%;">
                                                <a style="cursor: pointer; color: green;"  (click)="changeEtapaStatus(etapa)">
                                                    <mat-icon>
                                                        check
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 90%; text-align: left;">
                                                <small>{{etapa.id}} - {{etapa.nome}}</small>
                                            </div>
                                        </div>
                                        <div matline *ngIf="etapa.status=='Não adquirida' || etapa.status == undefined" 
                                            style="display: flex; flex-direction: row; flex-wrap: wrap; flex: 0 0 100%;">   
                                            <div style="width: 5%;">
                                                <a style="cursor: pointer; color: darkgray;" (click)="changeEtapaStatus(etapa)">
                                                    <mat-icon>
                                                        miscellaneous_services
                                                    </mat-icon>
                                                </a>
                                            </div>
                                            <div style="width: 90%; text-align: left;">
                                                <small>{{etapa.id}} - {{etapa.nome}}</small>
                                            </div>
                                        </div>
                                    </mat-list-item>
                                    </mat-list>
                                </div>
                            </ng-container>
                
                        </td>
                    </ng-container>
            
                <tr mat-header-row *matHeaderRowDef="displayedColumnsObjs"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsObjs;"></tr>
                <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" 
                    class="student-detail-row"></tr>
                </table>
            </div> 
            <h3 *ngIf="planointervencao?.objetivos?.length == 0 || !planointervencao?.objetivos" style="text-align: center; ">
                Nenhum objetivo cadastrado no PEI!
            </h3>
        </div>  
    </mat-card-content>
</mat-card>