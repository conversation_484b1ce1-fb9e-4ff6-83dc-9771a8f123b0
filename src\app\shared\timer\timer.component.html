<div class="contador-container">
    <!-- Descrição (ícone com tooltip) -->
    <div class="tooltip-container">
        <mat-icon class="custom-cursor" mat-icon-button aria-label="Descrição">
            <mat-icon style="opacity: 0.8;">help_outline</mat-icon>
        </mat-icon>
        <span class="tooltip-descricao-text" [innerHTML]="respDescription()"></span>
    </div>


    <div class="cronometro">
        <div class="cronometro-controls">
            <div class="timer-display">{{ formattedTime }}</div>
            <button class="btn-play" (click)="toggleTimer()">
                <i class="material-icons" style="font-size: 15px">{{ isRunning ? 'stop' : 'play_arrow' }}</i>
            </button>
        </div>
        <div class="timer-label-container">
            <div class="timer-label truncatable" [title]="objetivoPIC.comportamentoAlvo">{{ objetivoPIC.comportamentoAlvo }}</div>
        </div>
        <div class="timer-description" *ngIf="objetivoPIC?.tipoColeta === 'Cronometragem'" [title]="objetivoPIC.tipoColeta">(Cron.)</div>
        <div class="timer-description" *ngIf="objetivoPIC?.tipoAmostragem === 'Inteiro'" [title]="objetivoPIC.tipoColeta + ' ' + objetivoPIC.tipoAmostragem">(ATI)</div>
        <div class="timer-description" *ngIf="objetivoPIC?.tipoAmostragem === 'Parcial'" [title]="objetivoPIC.tipoColeta + ' ' + objetivoPIC.tipoAmostragem">(ATP)</div>
        <div class="timer-description" *ngIf="objetivoPIC?.tipoAmostragem === 'Momentânea'" [title]="objetivoPIC.tipoColeta + ' ' + objetivoPIC.tipoAmostragem">(ATM)</div>
    </div>

    <div class="time-list">
        <!-- Checkbox para confirmação -->
        <div *ngIf="!confirmed" class="confirm-checkbox-container">
            <label for="confirm-coleta">Objetivo observado?</label>
            <input type="checkbox" id="confirm-coleta" (change)="toggleConfirmation($event)">
        </div>

        <!-- Exibe mensagem e botão de adicionar quando a lista está vazia ou não existe -->
        <div *ngIf="(!coletaDiariaPIC?.duracoes || coletaDiariaPIC?.duracoes?.length === 0) && confirmed ">
            <button (click)="addNewTime()" class="add-time-button">
                <span>Adicionar tempo! </span>
                <i class="material-icons">add</i>
            </button>
        </div>
    
        <div *ngFor="let time of coletaDiariaPIC?.duracoes; let i = index" class="time-entry">
            <span>{{ i + 1 }}. </span>
            
            <!-- Se não estiver editando, exibe o tempo formatado -->
            <span *ngIf="editingIndex !== i">{{ time.duracao | secondsFormat }}</span>
            
            <!-- Campos de edição de horas, minutos e segundos -->
            <div *ngIf="editingIndex === i" class="time-edit-inputs">
                <input [(ngModel)]="editedHours" type="number" min="0" placeholder="H" class="time-input" />
                <span>H</span>
                <input [(ngModel)]="editedMinutes" type="number" min="0" max="59" placeholder="M" class="time-input" />
                <span>M</span>
                <input [(ngModel)]="editedSeconds" type="number" min="0" max="59" placeholder="S" class="time-input" />
                <span>S</span>
            </div>
            
            <button (click)="editTime(i)" *ngIf="editingIndex !== i" [style.visibility]="editingIndex !== i ? 'visible' : 'hidden'">
                <i class="material-icons">edit</i>
            </button>
            <button *ngIf="editingIndex === i" (click)="saveEditedTime(i)">
                <i class="material-icons">save</i>
            </button>
            <button *ngIf="editingIndex === i" (click)="cancelEdit()">
                <i class="material-icons">cancel</i>
            </button>
            <button (click)="deleteTime(i)" *ngIf="editingIndex !== i" [style.visibility]="editingIndex !== i ? 'visible' : 'hidden'">
                <i class="material-icons">delete</i>
            </button>
            <button (click)="addNewTime()" *ngIf="editingIndex !== i" [style.visibility]="i === (coletaDiariaPIC?.duracoes.length - 1) ? 'visible' : 'hidden'">
                <i class="material-icons">add</i>
            </button>
        </div>
    </div>    
</div>
