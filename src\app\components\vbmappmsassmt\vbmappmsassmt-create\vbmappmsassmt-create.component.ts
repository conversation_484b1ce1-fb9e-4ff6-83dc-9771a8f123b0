import { FirebaseUserModel } from './../../template/auth/user-model';
import { MatSelectChange } from '@angular/material/select';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from './../../profissional/profissional.service';
import { AuthService } from './../../template/auth/auth.service';
import { PacienteService } from './../../paciente/paciente.service';
import { NivelService } from './../../nivel/nivel.service';
import { MarcovbmappService } from './../../marcovbmapp/marcovbmapp.service';
import { VBMAPPMsAssmtService } from './../vbmappmsassmt.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { VBMAPPMilestonesAssessmentItem } from './../vbmapp-milestones-item-model';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { Nivel } from './../../nivel/nivel-model';
import { Profissional } from './../../profissional/profissional-model';
import { Paciente } from './../../paciente/paciente-model';
import { VBMAPPMilestonesAssessment } from './../vbmappmsassmt-model';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { DateAdapter } from 'angular-calendar';
import { Component, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { LoadingService } from 'src/app/shared/service/loading.service';


@Component({
  selector: 'app-vbmappmsassmt-create',
  templateUrl: './vbmappmsassmt-create.component.html',
  styleUrls: ['./vbmappmsassmt-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class VbmappmsassmtCreateComponent implements OnInit {

  public vbmappmsassmts: VBMAPPMilestonesAssessment[];
  public pac: Paciente = new Paciente();
  public profissionais: Profissional[];
  public profissionaisDoPaciente: Profissional[];
  public niveis: Nivel[];
  public dominios: DominioVBMAPP[];
  public vbmappMsAssmtItemView: VBMAPPMilestonesAssessmentItem[] = [];
  public prevvbmappMsAssmtItemViewView: VBMAPPMilestonesAssessmentItem[] = [];

  public vbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public prevvbmappmsassmt: VBMAPPMilestonesAssessment = new VBMAPPMilestonesAssessment();
  public nivel: Nivel = new Nivel();
  public dominio: DominioVBMAPP = new DominioVBMAPP();

  saveDisabled: boolean = false;
  disabledButtomNext: boolean = false;
  textoBotao: string = 'Próximo Domínio';

  displayedColumns = ['id', 'nome', 'prevvbmappmsassmt', 'N', 'P', 'A', 'X']
  //displayedColumns = ['id', 'nome']

  //Form Controls
  paciente = new FormControl('', [Validators.required]);
  profissional = new FormControl('', [Validators.required]);
  data = new FormControl('', [Validators.required]);
  allButton: string;

  public hasAccessUpdate: boolean = false;
  public hasAccessRead: boolean = false;

  @ViewChild(NgForm) form;

  constructor(private vbmappMsAssmtService: VBMAPPMsAssmtService,
    private marcoService: MarcovbmappService,
    private nivelService: NivelService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    let idMsAssmt = this.route.snapshot.paramMap.get('idMsAssmt');

    try {
      //Carrego os profissionais vinculados ao paciente
      const paciente = await this.pacienteService.findById(idPaciente).toPromise();
      this.profissionaisDoPaciente = paciente.equipe;
      
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update');
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read');
  
      //Carregando Profissionais
      const profissionais = await this.profissionalService.find().toPromise()
      this.profissionais = profissionais;

      if(idMsAssmt == undefined) { //Create
        //Caso seja um profisisonal, seto como o criador
        if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
          if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
            this.vbmappmsassmt.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            this.vbmappmsassmt.idProfissional = this.vbmappmsassmt.profissional.id;
          }
        }
      }
      
      
      if(idMsAssmt == undefined) { //Create
        //Carregando Competências
        this.vbmappmsassmt.assessment = [];
        this.marcoService.find().subscribe(marcos => {
  
          //Carregando Paciente
          this.pacienteService.findById(idPaciente).subscribe(paciente => {
          this.pac = paciente;
          this.vbmappmsassmt.paciente = paciente;
          this.vbmappmsassmt.idPaciente = idPaciente;

          this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update')
          this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')
        })

        let assmt: VBMAPPMilestonesAssessmentItem;
        for(let marco of marcos) {
          assmt = new VBMAPPMilestonesAssessmentItem();
          assmt.marco = marco;
          this.vbmappmsassmt.assessment.push(assmt);
        }

        //console.log(this.vbmappmsassmt);
        this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
      })

        
      //console.log(this.vbmappMsAssmtItemView);
      //this.do2();

      //Pego o assessment anterior
      this.vbmappMsAssmtService.findLastByPacienteData(idPaciente, moment(new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate())).format('YYYY-MM-DD')).subscribe(assmts => {
        if(assmts.length > 0){
          // console.log(msassmts);
          this.vbmappmsassmts = assmts.filter(msassmt => msassmt.status != false);
          
          //Atribuindo o primeiro MsAssmt para visualização
          if (this.vbmappmsassmts.length > 0){
            this.prevvbmappmsassmt = this.vbmappmsassmts[0];
          }
          // console.log(this.vbmappmsassmt)
          
          //console.log(this.prevvbmappmsassmt)
          if(this.prevvbmappmsassmt != undefined){
            this.prevvbmappMsAssmtItemViewView = assmts[0].assessment.filter(assmt => 
              (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
          }
        }
      })
    } else {  //Edit
      //Carrego o assessment a ser editado
      this.vbmappMsAssmtService.findById(idMsAssmt).subscribe(assmt => {
        //Carregando Paciente
        this.pacienteService.findById(idPaciente).subscribe(paciente => {
          this.pac = paciente;
          this.vbmappmsassmt.paciente = paciente;
          this.vbmappmsassmt.idPaciente = idPaciente;
          
          this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','update')
          this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Avaliação de Marco (VBMAPP)','read')
        })

        this.vbmappmsassmt = assmt;
        this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
          (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
        
        //Pego o assessment anterior
        // console.log(moment(this.vbmappmsassmt.data).subtract(1, "days").format('YYYY-MM-DD'));

        this.vbmappMsAssmtService.findLastByPacienteData(idPaciente, moment(this.vbmappmsassmt.data).subtract(1, "days").format('YYYY-MM-DD')).subscribe(assmts => {
          if(assmts.length > 0){
            // console.log(msassmts);
            this.vbmappmsassmts = assmts.filter(msassmt => msassmt.status != false);
            
            //Atribuindo o primeiro MsAssmt para visualização
            if (this.vbmappmsassmts.length > 0){
              this.prevvbmappmsassmt = this.vbmappmsassmts[0];
            }
            // console.log(this.vbmappmsassmt)
            
            //console.log(this.prevvbmappmsassmt)
            if(this.prevvbmappmsassmt != undefined){
              this.prevvbmappMsAssmtItemViewView = assmts[0].assessment.filter(assmt => 
                (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
            }
          }
          // this.prevvbmappmsassmt = assmts[0];
          // if(this.prevvbmappmsassmt != undefined){
          //   this.prevvbmappMsAssmtItemViewView = assmts[0].assessment.filter(assmt => 
          //     (assmt.marco.id_nivel=='N1' && assmt.marco.id_dominio == 'Mando'))
          // }
        })
      })
    }
    
      //Carregando Níveis
      const niveis = await this.nivelService.find().toPromise();
      this.niveis = niveis;
      // console.log(this.niveis);
      this.niveis.pop();
      this.nivel = this.niveis.find(n => n.id == 'N1');
  
      //Carregando Domínios
      const dominios = await this.marcoService.findDominiosByNivel("N1").toPromise();
      this.dominios = dominios;
      // console.log(this.dominios);
      this.dominio = this.dominios.find(dom => dom.id == 'Mando');

    } catch (error) {
      console.log("Erro ao carregar: ", error);
    } finally {
      this.loadingService.hide();
    }
  }

  getVBMAPPMsAssmtValue(id: string) : string{
    //console.log(this.prevvbmappmsassmt);
    if(this.prevvbmappmsassmt != undefined && this.prevvbmappmsassmt.assessment != undefined && this.prevvbmappmsassmt.assessment.length > 0){
      return this.prevvbmappmsassmt.assessment[this.prevvbmappmsassmt.assessment.findIndex(e => e.marco.id == id)].valor
    } else {
      return "";
    }
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  setDominios(){
    this.marcoService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      //console.log(this.dominios)
      this.dominio = this.dominios[0];
      //console.log(this.dominio)
      this.filterAssessment();
    })
  }

  filterAssessment(){
    const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
    const ultimoIndice = this.dominios.length - 1;
    const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
    const ultimoNivel = this.niveis.length - 1;
    this.disabledButtomNext = false;
    this.textoBotao = 'Próximo Domínio';

    //Altero o botão de acordo com o indice
    if (indiceAtual === ultimoIndice) {
      this.textoBotao = 'Próximo Nível e Domínio';
      if (nivelAtual === ultimoNivel) {
        this.disabledButtomNext = true;
        this.textoBotao = 'Último nível alcançado!'
      }
    }

    
    if(this.prevvbmappmsassmt != undefined && this.prevvbmappmsassmt.assessment.length > 0){
      //console.log(this.prevvbmappmsassmt.assessment)

      this.prevvbmappMsAssmtItemViewView = this.prevvbmappmsassmt.assessment.filter(assmt => 
        (assmt.marco.id_nivel==this.nivel.id 
          && assmt.marco.id_dominio == this.dominio.id))
    }
    

    this.vbmappMsAssmtItemView = this.vbmappmsassmt.assessment.filter(assmt => 
      (assmt.marco.id_nivel==this.nivel.id 
        && assmt.marco.id_dominio == this.dominio.id))
    
  }

  setProfissional(event:MatSelectChange){
    this.vbmappmsassmt.profissional = this.profissionais.find(p => p.id == event.value);
    //if(this.vbmappmsassmt.id != undefined){
    //  this.save(false);
    //}
  }

  save(exit: boolean){
    let p, a: number;
    //var submitButton = document.getElementById('btnSave');
    //console.log(submitButton.innerText);
    this.saveDisabled=true;
    this.loadingService.show();
    //submitButton.setAttribute('disabled', 'true' );
    if(this.form.valid){
      p = this.vbmappmsassmt.assessment.filter(assmt => 
        (assmt.valor == "P")).length;
      a = this.vbmappmsassmt.assessment.filter(assmt => 
        (assmt.valor == "A")).length;
      this.vbmappmsassmt.pontos = (p * 0.5) + (a * 1);

      if(this.vbmappmsassmt.id == undefined){
        this.vbmappMsAssmtService.create(this.vbmappmsassmt).subscribe((vbmappMsAssmtId) => {
          //console.log(vbmappMsAssmtId)
          this.vbmappmsassmt.id = vbmappMsAssmtId
          //console.log(this.esdmchecklist.id)
          this.vbmappMsAssmtService.showMessage('Avaliação de Marcos criada com sucesso!');
          this.saveDisabled=false;
          this.loadingService.hide();
          if(exit){
            if(this.vbmappmsassmt.id == undefined){
              this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
                tab:"msassmt"
              }]); 
            } else {
              this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
                tab:"msassmt",
                idMsAssmt: this.vbmappmsassmt.id
              }]); 
            }
          }
        });
        
      } else {
          this.vbmappMsAssmtService.update(this.vbmappmsassmt).subscribe(() => {
            this.vbmappMsAssmtService.showMessage('Avaliação de Marcos alterada com sucesso!');
            this.saveDisabled=false;
            this.loadingService.hide();
            if(exit){
              if(this.vbmappmsassmt.id == undefined){
                this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
                  tab:"msassmt"
                }]); 
              } else {
                this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
                  tab:"msassmt",
                  idMsAssmt: this.vbmappmsassmt.id
                }]); 
              }
            }
          });

      }
      //console.log(this.esdmchecklist);
    } else {
      this.vbmappMsAssmtService.showMessage('Existem campos inválidos no formulário!',true);
      this.saveDisabled=false;
      this.loadingService.hide();
    }

  }

  cancel(){
    if(this.vbmappmsassmt.id == undefined){
      this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
        tab:"msassmt"
      }]); 
    } else {
      this.router.navigate(['/paciente/'+this.vbmappmsassmt.idPaciente, {
        tab:"msassmt",
        idMsAssmt: this.vbmappmsassmt.id
      }]); 
    }

    /*
    , {
              tab:"checklist",
              idESDMChklst: this.esdmchecklist.id
            }
    */
  }

  nextDominio() {
    const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
    const ultimoIndice = this.dominios.length - 1;
  
    if (indiceAtual < ultimoIndice) {
      // Ainda há domínios neste nível, então avança para o próximo domínio
      this.dominio = this.dominios[indiceAtual + 1];
    } else {
      // É o último domínio do nível atual, então avança para o próximo nível
      const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
  
      // Ainda há níveis, então avança para o próximo nível
      this.nivel = this.niveis[nivelAtual + 1];
      // Carrega os domínios do próximo nível
      this.carregarDominiosDoNivel(this.nivel.id);
      this.dominio = this.dominios[0];
    }
    this.filterAssessment();

    //Altero o botão de acordo com o indice
    if (indiceAtual === ultimoIndice) {
      this.textoBotao = 'Próximo Domínio';
    }
    window.scrollTo(0, 0);
  }

  carregarDominiosDoNivel(idNivel: string) {
    this.marcoService.findDominiosByNivel(idNivel).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];
    });
  }

  checkAllAcquired(value: string){
    this.allButton = value;

    this.vbmappMsAssmtItemView.forEach(assmt => {
      this.vbmappmsassmt.assessment[this.vbmappmsassmt.assessment.indexOf(assmt)].valor = value
    })

    // Reset da variável allButton após o hide do loading
    setTimeout(() => {
      this.allButton = "";
    }, 0);
  }

  onDataChange(novaData: Date) {
    let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    if (!idPaciente) {
      idPaciente = this.vbmappmsassmt?.idPaciente;
    }
  
    if (idPaciente && novaData) {
      this.vbmappMsAssmtService.findByPaciente(idPaciente).subscribe(avaliacoes => {
        // Filtro: tipo correto, ativas e com data anterior ou igual à novaData
        const avaliacoesFiltradas = avaliacoes.filter(a =>
          a.status === true &&
          a.data &&
          new Date(a.data) < novaData
        );
  
        if (avaliacoesFiltradas.length === 0) {
          this.prevvbmappmsassmt = null;
          return;
        }
  
        // Achar a avaliação mais próxima (anterior) da nova data
        const avaliacaoMaisProxima = avaliacoesFiltradas.reduce((anterior, atual) => {
          const diffAtual = Math.abs(new Date(atual.data).getTime() - novaData.getTime());
          const diffAnterior = Math.abs(new Date(anterior.data).getTime() - novaData.getTime());
          return diffAtual < diffAnterior ? atual : anterior;
        });
  
        this.prevvbmappmsassmt = avaliacaoMaisProxima;
      });
    }
  }  

}
