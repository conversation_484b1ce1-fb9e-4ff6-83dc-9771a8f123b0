import { UserService } from './../user.service';
import { FirebaseUserModel } from './../user-model';
import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, Router } from "@angular/router";


@Injectable()
export class UserResolver implements Resolve<FirebaseUserModel> {

  constructor(public userService: UserService, private router: Router) { }

  resolve(route: ActivatedRouteSnapshot) : Promise<FirebaseUserModel> {

    let user = new FirebaseUserModel();

    return new Promise((resolve, reject) => {
      //console.log("User resolver")
      this.userService.getCurrentUser()
      .then(res => {
        if(res.providerData[0].providerId == 'password'){
          user.image = 'https://via.placeholder.com/400x300';
          user.name = res.displayName;
          user.provider = res.providerData[0].providerId;
          user.email = res.email;
          user.uid = res.uid;
          user.admin = res.admin;
          user.superadmin = res.superadmin;
          user.gsupervisor = res.gsupervisor;
          user.familia = res.familia;
          user.profissional = res.profissional;
          return resolve(user);
        }
        else{
          user.image = res.photoURL;
          user.name = res.displayName;
          user.provider = res.providerData[0].providerId;
          return resolve(user);
        }
      }, err => {
        console.log(err);
        this.router.navigate(['/login'], {queryParams: {hasFullView: true}});
        return reject(err);
      })
    })
  }
}