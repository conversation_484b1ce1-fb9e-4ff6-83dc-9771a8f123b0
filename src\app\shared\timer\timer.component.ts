import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from 'src/app/components/coletadiariavbmapp/coletadiariapic-model';
import { ConfirmDialogComponent } from 'src/app/components/template/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-timer',
  templateUrl: './timer.component.html',
  styleUrls: ['./timer.component.css']
})

export class TimerComponent implements OnInit {
  counter: number = 0;
  isRunning: boolean = false;
  interval: any;
  timer: number = 0;
  editingIndex: number | null = null;
  editedTime: number | null = null;

  // Novo: Variáveis para horas, minutos e segundos
  editedHours: number | null = 0;
  editedMinutes: number | null = 0;
  editedSeconds: number | null = 0;

  @Input() objetivoPIC: any;
  @Input() idxSessao: any;
  @Input() coletasdiariasPIC: SessaoColetaDiariaPIC[] = [];
  @Output() coletaDiariaPICChange = new EventEmitter<{ coleta: ColetaDiariaPIC, sessaoId: string }>();

  coletaDiariaPIC: ColetaDiariaPIC = new ColetaDiariaPIC();

  public confirmed = false;
  public firstTime = false;

  constructor(private dialog: MatDialog) { }
  
  ngOnInit(): void {
    this.coletaDiariaPIC.idObjetivo = this.objetivoPIC.id;

    // Itera sobre as sessões para encontrar a coleta associada ao objetivo atual
    this.coletasdiariasPIC[this.idxSessao]?.objetivosColeta?.filter(coleta => coleta.idObjetivo === this.objetivoPIC.id)
      .forEach(coleta => {
        this.coletaDiariaPIC = coleta;
      });
  }

  // Método para ativar a modificação do contador com base na checkbox
  toggleConfirmation(event: any) {
    this.confirmed = event.target.checked;
    
    if (this.coletaDiariaPIC.duracoes,length == 0) {
      this.saveTime(0, false);
    }
    this.firstTime = true;
  }

  // Chame este método sempre que a coletaDiariaPIC for atualizada
  emitColetaDiariaPIC() {
    // Encontra a sessão associada ao objetivo atual para emitir a coleta com o id da sessão
    const sessao = this.coletasdiariasPIC[this.idxSessao];
    if (!this.coletaDiariaPIC.idObjetivo){
      this.coletaDiariaPIC.idObjetivo = this.objetivoPIC.id;
    }
    
    if (sessao) {
      this.coletaDiariaPICChange.emit({ coleta: this.coletaDiariaPIC, sessaoId: sessao.id });
    }
  }

  get formattedTime(): string {
    const hours = Math.floor(this.counter / 3600);
    const minutes = Math.floor((this.counter % 3600) / 60);
    const seconds = this.counter % 60;
  
    return hours > 0 ? `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(seconds)}` : `${this.pad(minutes)}:${this.pad(seconds)}`;
  }

  pad(value: number): string {
    return value < 10 ? '0' + value : value.toString();
  }

  toggleTimer() {
    this.isRunning ? this.stopTimer() : this.startTimer();
  }

  startTimer() {
    if (!this.confirmed){
      this.confirmed = true;
      this.saveTime(0, false);
      this.firstTime = true;
    }
    this.isRunning = true;
    this.interval = setInterval(() => {
      this.counter++;

      if (this.objetivoPIC.tipoAmostragem === 'Parcial') {
        let intervaloEmSegundos = this.convertToSeconds(this.objetivoPIC.intervalo, this.objetivoPIC.medidaIntervalo);

        if (this.counter >= intervaloEmSegundos) {
          clearInterval(this.interval); // Parar o intervalo antes de abrir a caixa de diálogo
          this.isRunning = false;
          
          this.timeInvalid('Parcial').then((result) => {
            if (result) {
              this.saveTime(this.counter, false);
            }
            this.counter = 0;
          });
        }
      } else if (this.objetivoPIC.tipoAmostragem === 'Momentânea') {
      let intervaloEmSegundos = this.convertToSeconds(this.objetivoPIC.intervalo, this.objetivoPIC.medidaIntervalo);
      //console.log(intervaloEmSegundos);
      const percentual = 0.10;
      const limiteSuperior = intervaloEmSegundos * (1 + percentual);

      //console.log(this.counter, limiteSuperior);
        if (this.counter >= limiteSuperior) {
          clearInterval(this.interval); // Parar o intervalo antes de abrir a caixa de diálogo
          this.isRunning = false;
          
          this.timeInvalid('Momentânea').then((result) => {
            if (result) {
              this.saveTime(this.counter, false);
            }
            this.counter = 0;
          });
        }
      }
    }, 1000);
  }

  stopTimer() {
    this.isRunning = false;
    clearInterval(this.interval);
    
    let intervaloEmSegundos = this.convertToSeconds(this.objetivoPIC.intervalo, this.objetivoPIC.medidaIntervalo);
    const percentual = 0.10;
    const limiteInferior = intervaloEmSegundos * (1 - percentual);
    const limiteSuperior = intervaloEmSegundos * (1 + percentual);

    if (this.objetivoPIC.tipoAmostragem === 'Inteiro' && this.counter < intervaloEmSegundos) {
      clearInterval(this.interval); // Parar o intervalo antes de abrir a caixa de diálogo
      this.timeInvalid('Inteiro').then((result) => {
        if (result) {
          this.saveTime(this.counter, false);
        }
        this.counter = 0;
      });
    } else if (this.objetivoPIC.tipoAmostragem === 'Momentânea' && !(this.counter >= limiteInferior && this.counter <= limiteSuperior)) {
      clearInterval(this.interval); // Parar o intervalo antes de abrir a caixa de diálogo
      this.timeInvalid('Momentânea').then((result) => {
        if (result) {
            this.saveTime(this.counter, false);
        }
        this.counter = 0;
      });
    } else {
      this.saveTime(this.counter, false);
      this.counter = 0;
    }
  }

  saveTime(duration: number, manual: boolean) {
    if (!this.coletaDiariaPIC.duracoes) {
      this.coletaDiariaPIC.duracoes = [];
    }

    if(this.firstTime) {
      this.coletaDiariaPIC.duracoes[0] = { duracao: duration, manual: manual };
      this.firstTime = false;
      this.emitColetaDiariaPIC();  // Emitir o valor atualizado
    } else {
      this.coletaDiariaPIC.duracoes.push({ duracao: duration, manual: manual });
      this.emitColetaDiariaPIC();  // Emitir o valor atualizado
    }

  }

  addNewTime() {
    this.saveTime(this.counter, true);
    this.counter = 0;
  }

  editTime(index: number) {
    this.editingIndex = index;

    // Conversão do tempo atual em horas, minutos e segundos para edição
    const totalSeconds = this.coletaDiariaPIC.duracoes[index].duracao;
    this.editedHours = Math.floor(totalSeconds / 3600);
    this.editedMinutes = Math.floor((totalSeconds % 3600) / 60);
    this.editedSeconds = totalSeconds % 60;
  }

  saveEditedTime(index: number) {
    let intervaloEmSegundos = this.convertToSeconds(this.objetivoPIC.intervalo, this.objetivoPIC.medidaIntervalo);
    const percentual = 0.10;
    const limiteInferior = intervaloEmSegundos * (1 - percentual);
    const limiteSuperior = intervaloEmSegundos * (1 + percentual);

    if (this.editedHours !== null && this.editedMinutes !== null && this.editedSeconds !== null) {
      const newTimeInSeconds = this.editedHours * 3600 + this.editedMinutes * 60 + this.editedSeconds;

      if(this.firstTime) {
        this.firstTime = false;
      }

      if (this.objetivoPIC.tipoAmostragem === 'Inteiro' && newTimeInSeconds < intervaloEmSegundos) {
        this.timeInvalid('Inteiro').then((result) => {
          if (result) {
            this.coletaDiariaPIC.duracoes[index].duracao = newTimeInSeconds;
            this.coletaDiariaPIC.duracoes[index].manual = true;
            this.emitColetaDiariaPIC();  // Emitir o valor atualizado
          }
        });
      } else if (this.objetivoPIC.tipoAmostragem === 'Momentânea' && !(newTimeInSeconds >= limiteInferior && newTimeInSeconds <= limiteSuperior)) {
        this.timeInvalid('Momentânea').then((result) => {
          if (result) {
            this.coletaDiariaPIC.duracoes[index].duracao = newTimeInSeconds;
            this.coletaDiariaPIC.duracoes[index].manual = true;
            this.emitColetaDiariaPIC();  // Emitir o valor atualizado
          }
        });
      } else if (this.objetivoPIC.tipoAmostragem === 'Parcial' && newTimeInSeconds > intervaloEmSegundos) {
        this.timeInvalid('Parcial').then((result) => {
          if (result) {
            this.coletaDiariaPIC.duracoes[index].duracao = newTimeInSeconds;
            this.coletaDiariaPIC.duracoes[index].manual = true;
            this.emitColetaDiariaPIC();  // Emitir o valor atualizado
          }
        });
      } else {
        this.coletaDiariaPIC.duracoes[index].duracao = newTimeInSeconds;
        this.coletaDiariaPIC.duracoes[index].manual = true;
        this.emitColetaDiariaPIC();  // Emitir o valor atualizado
      }
    }
    this.cancelEdit();
  }

  convertToSeconds(intervalo: number, medida: string): number {
    switch (medida) {
      case 'Horas':
        return intervalo * 3600;
      case 'Minutos':
        return intervalo * 60;
      case 'Segundos':
      default:
        return intervalo;
    }
  }

  deleteTime(index: number) {
    this.coletaDiariaPIC.duracoes.splice(index, 1);
    this.emitColetaDiariaPIC();  // Emitir o valor atualizado
  }

  cancelEdit() {
    this.editingIndex = null;
    this.editedTime = null;
  }

  timeInvalid(type: string): Promise<boolean> {
    let text: string;

    if (type === 'Inteiro') {
      text = `O tempo não chegou até o intervalo estipulado de ${this.objetivoPIC.intervalo} ${this.objetivoPIC.medidaIntervalo}. Deseja salvar o tempo?`;
    } else if (type === 'Momentânea') {
      text = `O tempo não está dentro do intervalo estipulado de ${this.objetivoPIC.intervalo} ${this.objetivoPIC.medidaIntervalo}. Deseja salvar o tempo?`;
    } else if (type === 'Parcial') {
      text = `O tempo atingiu o limite estipulado de ${this.objetivoPIC.intervalo} ${this.objetivoPIC.medidaIntervalo}. Deseja salvar o tempo?`;
    }

    return new Promise((resolve) => {
      const dialogRef = this.dialog.open(ConfirmDialogComponent, {
        width: '400px',
        data: { valida: true, msg: text },
        disableClose: true, // Impede que o diálogo seja fechado ao clicar fora dele
      });
      dialogRef.afterClosed().subscribe(result => resolve(result));
    });
  }

  respDescription(): string {
    let text = `<b>Def. Operacional:</b> ${this.objetivoPIC.definicaoOperacional} <br>`; 
    if (this.objetivoPIC.tipoColeta != 'Cronometragem') {
      text += `<b>Duração:</b> ${this.objetivoPIC.intervalo} ${this.objetivoPIC.medidaIntervalo}`;
    }
    return text;
  }  

}
