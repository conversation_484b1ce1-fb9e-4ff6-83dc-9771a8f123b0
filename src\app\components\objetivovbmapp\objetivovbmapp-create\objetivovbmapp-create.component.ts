import { isSameDay } from 'date-fns';
import { Observable } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { startWith, map } from 'rxjs/operators';
import { NivelMarcos } from './NivelMarcos-model';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { ActivatedRoute, Router } from '@angular/router';
import { NivelService } from './../../nivel/nivel.service';
import { Estimulo } from './../../estimulo/estimulo-model';
import { ObjetivoVBMAPP } from './../objetivovbmapp-model';
import { MatSelectChange } from '@angular/material/select';
import { MatChipInputEvent } from '@angular/material/chips';
import { Component, OnInit, ViewChild } from '@angular/core';
import { NgForm, FormControl, Validators } from '@angular/forms';
import { AuthService } from './../../template/auth/auth.service';
import { MarcoVBMAPP } from '../../marcovbmapp/marcovbmapp-model';
import { PacienteService } from '../../paciente/paciente.service';
import { ObjetivoVBMAPPService } from './../objetivovbmapp.service';
import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { EstimuloService } from './../../estimulo/estimulo.service';
import { GrupoEstimulo } from './../../estimulo/grupoestimulo-model';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { NivelAvalicao } from '../../avaliacao/nivel-avaliacao-model';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { LoadingService } from '../../../shared/service/loading.service';
import { DominioAvaliacao } from '../../avaliacao/dominio-avaliacao-model';
import { MarcovbmappService } from './../../marcovbmapp/marcovbmapp.service';
import { TipoSuporteService } from './../../tiposuporte/tiposuporte.service';
import { GrupoEstimuloService } from './../../estimulo/grupoestimulo.service';
import { MyErrorStateMatcher } from './../../../shared/default.error-matcher';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { NivelAvaliacaoService } from '../../avaliacao/nivel-avaliacao.service';
import { HabilidadeAvaliacao } from '../../avaliacao/habilidade-avaliacao-model';
import { DominioVBMAPPService } from './../../dominiovbmapp/dominiovbmapp.service';
import { DominioAvaliacaoService } from '../../avaliacao/dominio-avaliacao.service';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { HabilidadeAvaliacaoService } from '../../avaliacao/habilidade-avaliacao.service';
import { PlanoIntervencaoVBMAPP } from './../../planointervencaovbmapp/planointervencaovbmapp-model';
import { PlanointervencaovbmappService } from './../../planointervencaovbmapp/planointervencaovbmapp.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Inject } from '@angular/core';
import { Optional } from '@angular/core';
import { Nivel } from '../../nivel/nivel-model';
import { CompetenciaService } from '../../competencia/competencia.service';

export const _filterMarco = (opt: MarcoVBMAPP[], value: string): MarcoVBMAPP[] => {
  const filterValue = value.toLowerCase();

  return opt.filter(item => item.id.toLowerCase().indexOf(filterValue) === 0);
};

export interface DialogData {
  idPaciente: string,
  idVbmappPlan: string,
  idObjetivo: string,
  idHabilidade: string
  tipo: string
}

@Component({
  selector: 'app-objetivovbmapp-create', 
  templateUrl: './objetivovbmapp-create.component.html',
  styleUrls: ['./objetivovbmapp-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})

export class ObjetivovbmappCreateComponent implements OnInit {

  objetivo: ObjetivoVBMAPP = new ObjetivoVBMAPP();
  vbMappPlan: PlanoIntervencaoVBMAPP;

  public niveis: any[] = [];
  public dominios: any[] = [];
  public tiposAvaliacao: TipoAvaliacao[];
  public habilidades: any[] = [];
  
  public nivel: NivelAvalicao = new NivelAvalicao();
  public dominio: DominioAvaliacao = new DominioAvaliacao();
  public habilidade: HabilidadeAvaliacao = new HabilidadeAvaliacao();
  public siglaHabilidade: string;
  public tipoAvaliacao: TipoAvaliacao;
  public tiposAvaliacoes: TipoAvaliacao[];

  public tiposSuporte: TipoSuporte[];
  public tipoSuporte: TipoSuporte;
  public estimulos: Estimulo[];
  public filteredEstimulos: Estimulo[];
  public estimulo: Estimulo;
  public categoriasEstimulo: GrupoEstimulo[];
  public categoriaEstimulo: GrupoEstimulo;
  public precadastro: boolean;
  public updatePrecadastro: boolean;
  public idVbmappPlan: string;
  public idHabilidade: any;
  public tipo: string;
  public idPaciente: string;
  public paciente: any;

  public marcos: MarcoVBMAPP[] = [];
  public niveisMarcos: NivelMarcos[] = [
    {
      id: 'N1',
      descricao: 'Nível 1',
      marcos:[]
    },
    {
      id: 'N2',
      descricao: 'Nível 2',
      marcos:[]
    },
    {
      id: 'N3',
      descricao: 'Nível 3',
      marcos:[]
    }
  ]

  filteredCategsEstimulo: Observable<GrupoEstimulo[]>;
  marcoGroupOptions: Observable<NivelMarcos[]>;

  readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  addOnBlur = false;

  public hasAccessDelete: boolean;
  public hasAccessUpdate: boolean;

  displayedColumns = ['sigla', 'nome', 'action']

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  sdFC = new FormControl('', [Validators.required]);
  criterioSucessoFC = new FormControl('', [Validators.required]);
  materiaisFC = new FormControl('', [Validators.required]);
  procedimentoFC = new FormControl('', [Validators.required]);
  oportunidadesEstimuloFC = new FormControl('', [Validators.required]);
  corErrosValidator = new FormControl('', [Validators.required]);
  descricaoFC = new FormControl('', [Validators.required]);
  descricao_planoFC = new FormControl('', [Validators.required]);
  dominioFC = new FormControl('', [Validators.required]);
  tipoColetaFC = new FormControl('', [Validators.required]);
  categEstimuloFC = new FormControl();
  marcoFC = new FormControl();

  matcher = new MyErrorStateMatcher();
  noExistInList: boolean;
  ExistInListEdit: boolean;
  isModal: boolean = false;

  constructor(private objetivoService: ObjetivoVBMAPPService,
    private planointervencaoService: PlanointervencaovbmappService,
    private pacienteService: PacienteService,
    private nivelService: NivelService,
    private competenciaService: CompetenciaService,
    private dominioService: DominioVBMAPPService,
    private tipoSuporteService: TipoSuporteService,
    private categoriaEstimuloService: GrupoEstimuloService,
    private estimuloService: EstimuloService,
    private marcoService: MarcovbmappService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    public habilidadeAvaliacaoService: HabilidadeAvaliacaoService,
    public nivelAvaliacaoService: NivelAvaliacaoService,
    public dominioAvaliacaoService: DominioAvaliacaoService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router,
    private loadingService: LoadingService,
    @Optional() private dialogRef: MatDialogRef<ObjetivovbmappCreateComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: DialogData) { }

    //OBS: Existem 3 cenários para este componente, são eles:
    // 1. Criação de um novo objetivo ABA (idObjetivo=null)
    // 2. Edição de um objetivo ABA (idObjetivo!= null && idVbmappPlan==null)
    // 3. Edição de um objetivo ABA utilizado em um Plano de Intervenção (idObjetivo!= null && idVbmappPlan!=null)

  async ngOnInit(): Promise<void> {
    let idObjetivo = this.route.snapshot.paramMap.get("id");
    this.idVbmappPlan = this.route.snapshot.paramMap.get("idVbmappPlan");
    this.idPaciente = this.route.snapshot.paramMap.get("idPaciente");
    this.idHabilidade = this.route.snapshot.paramMap.get("idHabilidade");

    if (this.data) {
      idObjetivo = this.data?.idObjetivo;
      this.idVbmappPlan = this.data?.idVbmappPlan;
      this.idPaciente = this.data?.idPaciente;
      this.idHabilidade = this.data?.idHabilidade;
      this.tipo = this.data?.tipo;
    }

    this.loadingService.show();
    window.scrollTo(0, 0);

    this.hasAccessDelete = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','delete')
    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','update')

    if (!this.hasAccessUpdate) {
      this.marcoFC.disable();
      this.sdFC.disable();
      this.materiaisFC.disable();
      this.oportunidadesEstimuloFC.disable();
      this.procedimentoFC.disable();
    }

    try {
      const tiposAvaliacao = await this.tipoAvaliacaoService.find().toPromise();
      this.tiposAvaliacao = tiposAvaliacao.filter(ta => ta.ativo || ta.ativo == null || ta.ativo == undefined);

      if (!idObjetivo && !this.idVbmappPlan) {
        this.objetivo = new ObjetivoVBMAPP();
        this.objetivo.ativo = true;
        this.objetivo.diasSucesso = 3;
        this.objetivo.percOportunidadesSucesso = 80;
      } else if (!this.idVbmappPlan) {
        const objetivo = await this.objetivoService.findById(idObjetivo).toPromise();
        this.objetivo = objetivo;
        this.objetivo.diasSucesso = 3;
        this.objetivo.percOportunidadesSucesso = 80;
        this.converteMarco2Habilidade();
      } else {
        this.isModal = true;
        const plano = await this.planointervencaoService.findById(this.idVbmappPlan).toPromise();
        this.vbMappPlan = plano;
        if (idObjetivo === null || idObjetivo === undefined) {
          if (!this.vbMappPlan.objetivos) {
            this.vbMappPlan.objetivos = [];
          }
          this.vbMappPlan.objetivos.push(new ObjetivoVBMAPP());
          this.objetivo = this.vbMappPlan.objetivos[this.vbMappPlan.objetivos.length - 1];
        } else if (this.vbMappPlan.objetivos?.find(o => o?.id == idObjetivo)) {
          this.objetivo = this.vbMappPlan.objetivos?.find(o => o?.id == idObjetivo);
          if (!this.objetivo) {
          this.objetivo = await this.objetivoService.findById(idObjetivo).toPromise();
          }
        } else {
          this.objetivo = plano.objetivos[idObjetivo];
        }
        this.objetivo.diasSucesso = 3;
        this.objetivo.percOportunidadesSucesso = 80;
        this.converteMarco2Habilidade();
      }

      this.tiposAvaliacao .sort((a, b) => {
        const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
        const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
        return nomeA.localeCompare(nomeB);
      });
      
      const [dominios, tiposSuporte, categoriasEstimulo, estimulos, marcos] = await Promise.all([
        this.dominioService.find().toPromise(),
        this.tipoSuporteService.find().toPromise(),
        this.categoriaEstimuloService.find().toPromise(),
        this.estimuloService.find().toPromise(),
        this.marcoService.find().toPromise(),
      ]);

      this.dominios = dominios as DominioAvaliacao[];
      this.tiposSuporte = tiposSuporte;
      this.categoriasEstimulo = categoriasEstimulo
      .filter(p => p.ativo || p.ativo == null || p.ativo == undefined)
      .sort((a, b) => a.nome.localeCompare(b.nome));
      this.filteredCategsEstimulo = this.categEstimuloFC.valueChanges.pipe(
        startWith(""),
        map(value => typeof value === "string" ? value : value.nome),
        map(nome => nome ? this._filter(nome) : this.categoriasEstimulo.slice())
      );
      this.estimulos = estimulos
      .filter(p => p.ativo || p.ativo == null || p.ativo == undefined)
      .sort((a, b) => a.nome.localeCompare(b.nome));
      this.marcos = marcos;
      
      this.niveisMarcos.forEach(nivel => {
        nivel.marcos = this.marcos.filter(marco => marco.id_nivel === nivel.id);
        nivel.marcos.sort((a, b) => (a.dominio.ordem + a.ordem) - (b.dominio.ordem + b.ordem));
      });

      this.marcoGroupOptions = this.marcoFC.valueChanges.pipe(
        startWith(""),
        map(value => this._filterMarco(value))
      );

      if (this.idHabilidade) {
        let habilidade
        if (!this.tipo) {
          habilidade = await this.habilidadeAvaliacaoService.findById(this.idHabilidade).toPromise() as any;
          this.objetivo.nome = habilidade?.nome;
          this.objetivo.habilidades = [habilidade];
          if (habilidade?.descricao) {
            this.objetivo.descricao = habilidade?.descricao;
          } else if (habilidade?.objetivo) {
            this.objetivo.descricao = habilidade?.objetivo;
          } else {
            this.objetivo.descricao = habilidade?.nome;
          }
        } else {
          let habilidadesESDM = await this.competenciaService.find().toPromise(); //Carrego as habilidades do ESDM Checklist (ESDM)
          habilidade = habilidadesESDM.filter(h => h.id == this.idHabilidade)[0];
          this.objetivo.nome = habilidade?.nome;
          this.objetivo.habilidades = [habilidade];
          if (habilidade?.descricao) {
            this.objetivo.descricao = habilidade?.descricao;
          } else if (habilidade?.objetivo) {
            this.objetivo.descricao = habilidade?.objetivo;
          } else {
            this.objetivo.descricao = habilidade?.nome;
          }
        }
      }

      this.checkObjExistInList();
    } catch (error) {
      console.error('Erro ao carregar os dados', error);
    } finally {
      this.objetivo.tipoColeta = 'Naturalista';
      this.loadingService.hide();
    }
  }

  displayFn(categoriaEstimulo: GrupoEstimulo): string {
    return categoriaEstimulo && categoriaEstimulo.nome ? categoriaEstimulo.nome : '';
  }

  converteMarco2Habilidade(){
    //Carregado Tipos de Avaliações
    this.tipoAvaliacaoService.find().subscribe(tipos => {
      this.tiposAvaliacoes = tipos
        .filter(ta => ta.ativo == true || ta.ativo == null || ta.ativo == undefined)
        .sort((a, b) => {
          const nomeA = a.nome.replace(/\[.*?\]\s*/, '');
          const nomeB = b.nome.replace(/\[.*?\]\s*/, '');
          return nomeA.localeCompare(nomeB);
        });

      if(this.objetivo.habilidades == undefined){
        this.objetivo.habilidades = [];
      }
      //Converto o marco do objetivo para habilidade, caso ele ainda não esteja registrado como habilidade
      if(this.objetivo.marco != undefined && this.objetivo.habilidades.find(h => h.sigla == this.objetivo.id_marco) == undefined){
        this.objetivo.habilidades.push({
          id: this.objetivo.marco.id,
          nome: this.objetivo.marco.nome,
          ordem: this.objetivo.marco.ordem,
          idNivelAvaliacao: this.objetivo.marco.id_nivel,
          nivel: null,
          idDominioAvaliacao: this.objetivo.marco.id_dominio,
          dominio: null,
          sigla: this.objetivo.marco.id,
          idTipoAvaliacao: this.tiposAvaliacoes.find(ta => ta.nome === "[VB-MAPP] Avaliação de Marcos").id,
          dominioResposta:  [
            { id: "1", idHabilidadeAvaliacao: null, nome: "Adquirido totalmente", sigla: "A", valor: 1 },
            { id: "2", idHabilidadeAvaliacao: null, nome: "Não adquirido", sigla: "N", valor: 0 },
            { id: "3", idHabilidadeAvaliacao: null, nome: "Adquirido parcialmente", sigla: "P", valor: 0.5 },
            { id: "4", idHabilidadeAvaliacao: null, nome: "Não observado", sigla: "X", valor: undefined }
          ]
        })
      }
    });
  }
  
  private _filter(value: string): GrupoEstimulo[] {
    const filterValue = value.toLowerCase();

    return this.categoriasEstimulo.filter(option => option.nome.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
    
  }

  displayFnMarco(categoriaEstimulo: GrupoEstimulo): string {
    return categoriaEstimulo && categoriaEstimulo.nome ? categoriaEstimulo.nome : '';
  }

  private _filterMarco(value: string): NivelMarcos[] {
  //   id: 'N1',
  // descricao: 'Nível 1',
  // marcos:[]
  // console.log(value)
    if (value) {
      return this.niveisMarcos
        .map(nivelMarcos => ({id: nivelMarcos.id, descricao: nivelMarcos.descricao, marcos: _filterMarco(nivelMarcos.marcos, value)}))
        .filter(group => group.marcos.length > 0);
    }
    
    return this.niveisMarcos;

    // const filterValue = value.toLowerCase();

    // return this.niveisMarcos.filter(option => option.descricao.toLowerCase().indexOf(filterValue) === 0);
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
    
  }

  async addEstimulo(estimulo: Estimulo) {
    this.loadingService.show();
    
    try {
      // Simula uma operação assíncrona (exemplo: salvar no backend)
      await this.adicionarEstimuloAoObjetivo(estimulo);  
    } catch (error) {
      console.error(error);
    } finally {
      this.loadingService.hide(); // Só será chamado quando tudo terminar
    }
  }
  
  private async adicionarEstimuloAoObjetivo(estimulo: Estimulo) {
    if (!this.objetivo.estimulos.some(e => e.nome === estimulo.nome)) {
      this.objetivo.estimulos.push(estimulo);
  
      for (const tipo of this.objetivo.tiposSuporte) {
        tipo.estimulos = tipo.estimulos || [];
        tipo.estimulos.push(estimulo);
      }
    }
  
    // Simulação de um delay para representar uma chamada assíncrona ao backend
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  async addNewEstimulo(event: MatChipInputEvent): Promise<void> {
    this.loadingService.show();
    const input = event.input;
    const value = event.value;

    try {
      if (!this.categoriaEstimulo.id) {
        this.objetivoService.showMessage("Para incluir um novo estímulo, é preciso selecionar a qual categoria ele irá pertencer.", true);
      } else if (this.filteredEstimulos.some(e => e.nome === value)) {
          this.objetivoService.showMessage("Já existe um estímulo com esse nome.", true);
      } else {
        var est: Estimulo = new Estimulo();
        est.ativo=true;
        est.grupo = this.categoriasEstimulo.find(c => c.id == this.categoriaEstimulo.id);
        est.nome = value;
  
        this.estimuloService.create(est).subscribe((id) => {
          this.estimuloService.showMessage('Estímulo criado com sucesso!');
          est.id = id;
          this.estimulos.push(est);
          this.estimulos = [...this.estimulos];
          this.filteredEstimulos = this.estimulos.filter(e => e.grupo.id == est.grupo.id)
        })
        // Simulação de um delay para representar uma chamada assíncrona ao backend
        await new Promise(resolve => setTimeout(resolve, 500));
      }
  
      // Reset the input value
      if (input) input.value = '';
      
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async removeEstimulo(estimulo: Estimulo){
    this.loadingService.show();
    try {
      // console.log(this.objetivo.estimulos)
      const estimuloIndex = this.objetivo.estimulos.findIndex(e => e.id === estimulo.id);
      if (estimuloIndex > -1) {
        this.objetivo.estimulos.splice(estimuloIndex, 1);
        this.objetivo.estimulos = [...this.objetivo.estimulos];
      }
      
      //Retiro o estímulo de dentro dos Tipos de Suporte
      for (const tipo of this.objetivo.tiposSuporte) {
        const tipoEstimuloIndex = tipo.estimulos.findIndex(e => e.id === estimulo.id);
        if (tipoEstimuloIndex > -1) {
          tipo.estimulos.splice(tipoEstimuloIndex, 1);
          tipo.estimulos = [...tipo.estimulos];
        }
      }
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  toogleEstimulo(estimulo: Estimulo){
    this.loadingService.show();
    const estimuloIndex = this.objetivo.estimulos.indexOf(estimulo);
    if (estimuloIndex > -1) {
      this.objetivo.estimulos[estimuloIndex].ativo = !this.objetivo.estimulos[estimuloIndex].ativo;
      this.objetivo.estimulos = [...this.objetivo.estimulos];
    // console.log(this.objetivo.estimulos[this.objetivo.estimulos.indexOf(estimulo)].ativo)
    
      //Repasso a alteração para os estímulos de cada tipo de suporte
      for (const tipo of this.objetivo.tiposSuporte) {
        const tipoEstimuloIndex = tipo.estimulos.findIndex(t => t.id === estimulo.id);
        if (tipoEstimuloIndex > -1) {
          tipo.estimulos[tipoEstimuloIndex].ativo = this.objetivo.estimulos[estimuloIndex].ativo;
          tipo.estimulos = [...tipo.estimulos];
        }
      }
    }
    this.loadingService.hide();
  }

  setEstimulos(): void {
    this.filteredEstimulos = this.estimulos.filter(e => e.grupo.id === this.categoriaEstimulo.id);
  }

  addCategoria(event: any): void {
    this.loadingService.show();
    let categ: string;

    //Verifico se a categoria está em branco ou não foi definida
    if (this.categoriaEstimulo) {
      categ = this.categoriaEstimulo.nome || (this.categoriaEstimulo as unknown as string);

      // Verifico se a categoria existe
      if (!this.categoriasEstimulo.some(c => c.nome === categ)) {
        this.categoriaEstimulo = new GrupoEstimulo();
        this.categoriaEstimulo.nome = categ;

        this.categoriaEstimuloService.create(this.categoriaEstimulo).subscribe(async (id) => {
          this.categoriaEstimulo.id = id;
          this.categoriasEstimulo.push(this.categoriaEstimulo);
          this.categoriasEstimulo.sort((a, b) => a.nome.localeCompare(b.nome));
          this.categoriasEstimulo = [...this.categoriasEstimulo];
          this.setEstimulos();
          // Simulação de um delay para representar uma chamada assíncrona ao backend
          await new Promise(resolve => setTimeout(resolve, 500));
          this.categoriaEstimuloService.showMessage("Categoria incluída com sucesso!");
          this.loadingService.hide();
        });
      } else {
        this.categoriaEstimuloService.showMessage("Categoria já existe.", true);
        this.loadingService.hide();
      }
    } else {
      this.loadingService.hide();
    }
  }

  async addTipoSuporte(): Promise<void> {
    this.loadingService.show();
    // let tipoSuporte: TipoSuporte = new TipoSuporte();
    if(!this.tipoSuporte || !this.tipoSuporte.id){
      this.objetivoService.showMessage("É necessário selecionar o Tipo de Suporte a ser adicionado!", true);
      this.loadingService.hide();
    } else {
      //console.log(this.tipoSuporte)
      if(!this.objetivo.tiposSuporte){
        this.objetivo.tiposSuporte = [];
      }
      if (!this.objetivo.tiposSuporte.includes(this.tipoSuporte)){
        this.objetivo.tiposSuporte.push(this.tipoSuporte);
        this.tiposSuporte.splice(this.tiposSuporte.indexOf(this.tipoSuporte), 1);
        // Simulação de um delay para representar uma chamada assíncrona ao backend
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        this.objetivoService.showMessage("Tipo de suporte já adicionado!", true);
      }

      //this.formArray.push(new FormControl());      
      this.objetivo.tiposSuporte = [...this.objetivo.tiposSuporte];
      this.tipoSuporte = new TipoSuporte();
      this.loadingService.hide();
    }
  }

  async delete(id: string){
    this.loadingService.show();
    
    try {
      this.objetivo.tiposSuporte.splice(this.objetivo.tiposSuporte.indexOf(this.objetivo.tiposSuporte.find(t => t.id == id)), 1);
      this.objetivo.tiposSuporte = [...this.objetivo.tiposSuporte]
      
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  up(id: string) {
    const i = this.objetivo.tiposSuporte.findIndex(t => t.id === id);
    if (i > 0) {
      [this.objetivo.tiposSuporte[i - 1], this.objetivo.tiposSuporte[i]] = 
      [this.objetivo.tiposSuporte[i], this.objetivo.tiposSuporte[i - 1]];
      this.objetivo.tiposSuporte = [...this.objetivo.tiposSuporte];
    }
  }

  down(id: string) {
    const i = this.objetivo.tiposSuporte.findIndex(t => t.id === id);
    if (i < this.objetivo.tiposSuporte.length - 1) {
      [this.objetivo.tiposSuporte[i + 1], this.objetivo.tiposSuporte[i]] = 
      [this.objetivo.tiposSuporte[i], this.objetivo.tiposSuporte[i + 1]];
      this.objetivo.tiposSuporte = [...this.objetivo.tiposSuporte];
    }
  }

  async addObjetivoPersonalizado(){
    //Se a coleta for Naturalista, incluo os estímulos no Tipo de Suporte que está sendo adicionado
    if(this.objetivo.tipoColeta == "Naturalista"){
      for(const[,tipo] of this.objetivo.tiposSuporte.entries()){
        tipo.estimulos = [];
        for(const[,estimulo] of this.objetivo.estimulos.entries()){
          tipo.estimulos.push(estimulo);
        }
      }
    }

    // Create/Edit (Pelo plano de intervenção)
    if (this.form.valid && this.objetivo.estimulos?.length > 0 && this.objetivo.tiposSuporte?.length > 0) {
      try {
        this.objetivo.ativo = true;
        if (this.precadastro) {
          const id = await this.objetivoService.create(this.objetivo).toPromise();
          this.objetivo.id = id;
          await this.objetivoService.update(this.objetivo).toPromise();
          this.planointervencaoService.showMessage('Objetivo adicionado no pré-cadastro com sucesso!');
        } else if (this.updatePrecadastro) {
          await this.objetivoService.update(this.objetivo).toPromise();
          this.planointervencaoService.showMessage('Objetivo alterado com sucesso!');
        } else {
          if (this.objetivo.id == null) {
            this.objetivo.id = uuidv4();
            this.planointervencaoService.showMessage('Objetivo criado com sucesso!');
          } else {
            this.planointervencaoService.showMessage('Objetivo alterado com sucesso!');
          }
        }
        localStorage.setItem("vbmapp_objetivo",JSON.stringify(this.objetivo));
        this.dialogRef.close();
      } catch (error) {
        this.planointervencaoService.showMessage('Erro ao salvar o objetivo', true);
      }
    } else {
      this.markFormControlsAsTouched();
      this.loadingService.hide();
    }
  }
  
  async save(next: boolean) {
    this.loadingService.show();
    const idObjetivo = this.route.snapshot.paramMap.get('id');

    //Se a coleta for Naturalista, incluo os estímulos no Tipo de Suporte que está sendo adicionado
    if(this.objetivo.tipoColeta == "Naturalista"){
      for(const[,tipo] of this.objetivo.tiposSuporte.entries()){
        tipo.estimulos = [];
        for(const[,estimulo] of this.objetivo.estimulos.entries()){
          tipo.estimulos.push(estimulo);
        }
      }
    }

    if (this.form.valid && this.objetivo.estimulos?.length > 0 && this.objetivo.tiposSuporte?.length > 0) {
      try {
        if (!this.objetivo.id && !this.idVbmappPlan) { // Create (1)
          this.objetivo.id = idObjetivo;
          await this.objetivoService.create(this.objetivo).toPromise();
          this.objetivoService.showMessage('Objetivo criado com sucesso!');
          this.router.navigate(['/vbmapp_objetivo']);
        } else if (!this.idVbmappPlan) { // Edit (2)
          await this.objetivoService.update(this.objetivo).toPromise();
          this.objetivoService.showMessage('Objetivo alterado com sucesso!');
          if (!next) {
            this.router.navigate(['/vbmapp_objetivo']);
          }
        }
      } catch (error) {
        this.objetivoService.showMessage('Erro ao salvar o objetivo', true);
      } finally {
        this.loadingService.hide();
      }
    } else {
      this.markFormControlsAsTouched();
      this.loadingService.hide();
    }
  }

  private markFormControlsAsTouched() {
    this.sdFC.markAsTouched();
    this.materiaisFC.markAsTouched();
    this.procedimentoFC.markAsTouched();
    this.oportunidadesEstimuloFC.markAsTouched();
    this.corErrosValidator.markAsTouched();

    if (!this.objetivo.estimulos?.length) {
      this.objetivoService.showMessage('É obrigatório que o objetivo tenha ao menos um estímulo!', true);
    } else if (!this.objetivo.tiposSuporte?.length) {
      this.objetivoService.showMessage('É obrigatório que o objetivo tenha ao menos um tipo de suporte!', true);
    } else {
      this.objetivoService.showMessage('Existem campos inválidos no formulário!', true);
    }
  }
  
  cancel() {
    if (this.idVbmappPlan != undefined) {  //Edit (3)
      this.router.navigate(['/vbmapp_planointervencao/create', {
        idPaciente: this.vbMappPlan.idPaciente,//this.idPaciente,
        idVbmappPlan: this.vbMappPlan.id,
        tab: 'objetivos'
      }]);
    } else {
      this.router.navigate(['/vbmapp_objetivo']);
    }
  }

  setMarco(){
    this.objetivo.marco = this.marcos.find(p => p.id == this.objetivo.id_marco);
    // this.objetivo.id_marco = this.objetivo.marco.id;
  }

  async checkObjetivoExistente(): Promise<boolean>{
    let idObjetivo = this.route.snapshot.paramMap.get('id'); 
    let findObjetivo = await this.objetivoService.findById(this.vbMappPlan?.objetivos[idObjetivo]?.id as string).toPromise();

    if( this.vbMappPlan != undefined 
      && (idObjetivo == undefined || findObjetivo == undefined) ){
      return true;
    } else {
      return false;
    }
  }

  async checkObjExistInList() {
    let idObjetivo = this.route.snapshot.paramMap.get('id');
    if (this.data?.idObjetivo) {
      idObjetivo = this.data.idObjetivo;
    }
    let findObjetivo = await this.objetivoService.findById(this.objetivo?.id as string).toPromise();

    if (this.vbMappPlan) {
      if (!idObjetivo || !findObjetivo) {
        this.noExistInList = true;
        return;
      } else {
        this.ExistInListEdit = true;
        return;
      }
    }

    this.noExistInList = false;
    return;
  }

  async setTipoAvaliacao(event:MatSelectChange){
    // console.log("setTipoAvaliacao")
    try {
      this.loadingService.show();
      this.resetSelections(['nivel', 'dominio', 'habilidade', 'siglaHabilidade', 'habilidades', 'dominios']);
  
      // if(!this.isAvaliacaoMarco()){
      if (this.tipoAvaliacao.id == "5"){  
        this.niveis = await this.nivelService.find().toPromise(); //Carrego os níveis do ESDM Checklist (ESDM)
      } else {
        this.niveis = await this.nivelAvaliacaoService.findByTipoAvaliacao(this.tipoAvaliacao.id).toPromise(); //Carrego os níveis do tipo de avaliação (VBMAPP)
      }
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async setNivel(event:MatSelectChange){
    // console.log("setNivel")
    try {
      this.loadingService.show();
      if (!this.tipoAvaliacao) return;
      this.resetSelections(['dominio', 'habilidade', 'siglaHabilidade', 'habilidades']);

      if (this.tipoAvaliacao.id == "5"){  
        this.dominios = await this.competenciaService.findDominiosByNivel(this.nivel.id).toPromise(); //Carrego os domínios do ESDM Checklist (ESDM)
      } else {
        this.dominios = await this.habilidadeAvaliacaoService.findDominiosByNivel(this.nivel.id).toPromise(); //Carrego os domínios do tipo de avaliação (VBMAPP)
      }

      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async setDominio(){
    try {
      this.loadingService.show();
      if (!this.nivel) return;
      this.resetSelections(['habilidade', 'siglaHabilidade']);

      if (this.tipoAvaliacao.id == "5"){  
        this.habilidades = await this.competenciaService.find().toPromise(); //Carrego as habilidades do ESDM Checklist (ESDM)
        this.habilidades = this.habilidades.filter(h => h.id_dominio == this.dominio.id);
      } else {
        this.habilidades = await this.habilidadeAvaliacaoService.findByNivelDominio(this.nivel.id, this.dominio.id).toPromise(); //Carrego as habilidades do tipo de avaliação (VBMAPP)
      }

      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async setHabilidade(){
    try {
      this.loadingService.show();
      if (this.habilidades) {
        this.habilidade = this.habilidades.find(p => p.sigla === this.siglaHabilidade);
      }
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  private resetSelections(properties: string[]) {
    const defaults = {
      tipoAvaliacao: undefined,
      nivel: undefined,
      dominio: undefined,
      habilidade: undefined,
      siglaHabilidade: undefined,
      habilidades: undefined,
      dominios: undefined
    };

    properties.forEach(prop => {
        if (defaults.hasOwnProperty(prop)) {
          this[prop] = defaults[prop];
        }
    });
  }

  getTipoAvaliacao(idTipoAvaliacao: string){
    if(idTipoAvaliacao == undefined || idTipoAvaliacao == "5"){
      return "[ESDM] Early Start Denver Model";
    } else {
      return this.tiposAvaliacao.find(av => av.id == idTipoAvaliacao)?.nome
    }
  }

  getNameHabilidade(hab: any) {
    if (!hab.idTipoAvaliacao || hab.idTipoAvaliacao == "5"){  
      return hab.id.substr(1)
    } else {
      return hab.sigla == undefined || hab.sigla == "" ? this.dominio?.nome + " - " + hab.ordem : hab.sigla
    }
  }

  async addHabilidade(event: any){
    this.loadingService.show();
    try {
      this.objetivo.habilidades = this.objetivo.habilidades || [];
  
      if (!this.objetivo.habilidades.some(h => h.id === this.habilidade.id)) {
        if (!this.habilidade.idTipoAvaliacao){
          this.habilidade.idTipoAvaliacao = "5";
        }    
        this.objetivo.habilidades.push({ ...this.habilidade });
      }
      
      this.resetSelections(['tipoAvaliacao', 'nivel', 'dominio', 'habilidade', 'siglaHabilidade', 'habilidades', 'dominios']);
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  async removeHabilidade(hab: HabilidadeAvaliacao){
    this.loadingService.show();

    try {
      this.objetivo.habilidades.splice(this.objetivo.habilidades.findIndex(h => h.id == hab.id), 1);
      this.objetivo.habilidades = [...this.objetivo.habilidades]
      // Simulação de um delay para representar uma chamada assíncrona ao backend
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.log(error);
    } finally {
      this.loadingService.hide();
    }
  }

  isAvaliacaoMarco(){
    return (this.tipoAvaliacao.nome == "[VB-MAPP] Avaliação de Marcos");
  }

  do(){
    console.log("do")
  }
}
