import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DominioVBMAPP } from './dominiovbmapp-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DominioVBMAPPService {

  dominiolUrl = `${environment.API_URL}/dominio_vbmapp`;
  
  public dominios: BehaviorSubject<DominioVBMAPP[]> = 
    new BehaviorSubject<DominioVBMAPP[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(dominio: DominioVBMAPP): Observable<DominioVBMAPP>{
    // console.log(dominio);
    return this.http.post<DominioVBMAPP>(this.dominiolUrl, dominio).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<DominioVBMAPP>{
    return this.http.get<DominioVBMAPP>(this.dominiolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<DominioVBMAPP[]>{
    return this.http.get<DominioVBMAPP[]>(this.dominiolUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<DominioVBMAPP>{
    return this.http.delete<DominioVBMAPP>(this.dominiolUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
