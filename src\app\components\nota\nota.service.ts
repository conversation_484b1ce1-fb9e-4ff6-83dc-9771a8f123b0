import { map, catchError } from 'rxjs/operators';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';
import { Nota } from './nota-model';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class NotaService {

  pacienteUrl = `${environment.API_URL}/paciente`;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(nota: Nota): Observable<string>{
    //console.log(nota);
    return this.http.post<Nota>(this.pacienteUrl + "/" + nota.idPaciente + "/nota", nota).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(nota: Nota): Observable<string>{
    //console.log(nota);
    return this.http.put<Nota>(this.pacienteUrl + "/" + nota.idPaciente + "/nota/" + nota.id, nota).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string, idPaciente: string): Observable<Nota>{
    return this.http.get<Nota>(this.pacienteUrl + "/" + idPaciente + "/nota/" + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(idPaciente: string): Observable<Nota[]>{
    return this.http.get<Nota[]>(this.pacienteUrl + "/" + idPaciente + "/nota").pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string, idPaciente: string): Observable<Nota>{
    return this.http.delete<Nota>(this.pacienteUrl + "/" + idPaciente + "/nota/" + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
