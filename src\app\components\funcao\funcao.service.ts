import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Funcao } from './funcao-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class FuncaoService {

  funcaoUrl = `${environment.API_URL}/funcao`;
  
  public funcoes: BehaviorSubject<Funcao[]> = 
    new BehaviorSubject<Funcao[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(funcao: Funcao): Observable<Funcao>{
    //console.log(funcao);
    return this.http.post<Funcao>(this.funcaoUrl, funcao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(funcao: Funcao): Observable<Funcao>{
    //console.log(funcao);
    /*const convMap = {};

    (funcao.permission as Map <string, Map<string, string>>).forEach((val: Map<string, string>, key: string) => {
      convMap[key] = {}
      val.forEach( (val2: string, key2: string) => {
        convMap[key][key2] = val2; 
      })
    })
    console.log(convMap);
    funcao.permission = convMap;*/
    return this.http.put<Funcao>(this.funcaoUrl + "/" + funcao.id, funcao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Funcao>{
    return this.http.get<Funcao>(this.funcaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Funcao[]>{
    return this.http.get<Funcao[]>(this.funcaoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Funcao>{
    return this.http.delete<Funcao>(this.funcaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
