import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { DeleteConfirmDialogComponent } from '../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { LocalAtendimento } from '../local-atendimento-model'
import { LocalAtendimentoService } from '../local-atendimento.service'
@Component({
  selector: 'app-local-list',
  templateUrl: './local-list.component.html',
  styleUrls: ['./local-list.component.css']
})
export class LocalListComponent implements OnInit {

  public locais: LocalAtendimento[];

  displayedColumns = ['nome', 'permiteChoqueHorario','action']

  constructor(private localAtendimentoService: LocalAtendimentoService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {
    this.localAtendimentoService.find().subscribe(locais => {
      this.locais = locais ? locais : [];
    })
  }

  edit(localAtendimento: LocalAtendimento){
    this.router.navigate(['/local-atendimento/' + localAtendimento.id]);
  }

  delete(localAtendimento: LocalAtendimento): void{
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        localAtendimento.isAtivo = false;
        this.localAtendimentoService.update(localAtendimento).subscribe((p) => {
          this.localAtendimentoService.showMessage('Função inativada com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
          //this.router.navigate(['/profissional/update/'+this.profissional.id]);
          this.router.navigate(['/localAtendimento']);
        });
      }
    });
  }


}
