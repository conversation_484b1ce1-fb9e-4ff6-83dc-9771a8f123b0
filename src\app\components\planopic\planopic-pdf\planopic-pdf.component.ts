import { LoadingService } from 'src/app/shared/service/loading.service';
import { DominioVBMAPPService } from './../../dominiovbmapp/dominiovbmapp.service';
import { PICService } from '../pic.service';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { PIC } from '../pic-model';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { Router, ActivatedRoute } from '@angular/router';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { PacienteService } from '../../paciente/paciente.service';
import { ProfissionalService } from '../../profissional/profissional.service';
import { Paciente } from './../../paciente/paciente-model';
import { Profissional } from './../../profissional/profissional-model';

@Component({
  selector: 'app-planopic-pdf',
  templateUrl: './planopic-pdf.component.html',
  styleUrls: ['./planopic-pdf.component.css']
})
export class PlanoPICPdfComponent implements OnInit {

  public pic: PIC = new PIC();
  paciente: Paciente = new Paciente();
  profissional: Profissional = new Profissional();

  public tiposAvaliacoes: TipoAvaliacao[];
  public dominios: DominioVBMAPP[];
  public pdfGerado: boolean = false;
  public dataAtual: Date = new Date();

  @ViewChild("pdf") htmlData: ElementRef;

  displayedColumns = ['conteudo'];

  constructor(private picService: PICService,
    private pacienteService: PacienteService,
    private profissionalService: ProfissionalService,
    public authService: AuthService,
    private dominioService: DominioVBMAPPService,
    private router: Router,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
  
    let idPlano = this.route.snapshot.paramMap.get("id");
  
    try {
      // Obter o plano de intervenção
      const plano = await this.picService.findById(idPlano).toPromise();
      this.pic = plano;
  
      // Obter o paciente
      const paciente = await this.pacienteService.findById(plano.idPaciente).toPromise();
      this.paciente = paciente;
  
      // Obter o profissional
      const profissional = await this.profissionalService.findById(plano.idProfissional).toPromise();
      this.profissional = profissional;
    } catch (error) {
      console.error("Erro ao carregar os dados:", error);
    } finally {
      this.loadingService.hide();
    }
  }    

  get user(): FirebaseUserModel{    
    return this.authService.getUser();
  }
}
