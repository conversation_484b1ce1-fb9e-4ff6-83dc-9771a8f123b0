import { EstimuloService } from './../estimulo.service';
import { Component, OnInit, ViewChild } from "@angular/core";
import { GrupoEstimulo } from "./../grupoestimulo-model";
import { GrupoEstimuloService } from "./../grupoestimulo.service";
import { Observable } from "rxjs";
import { ActivatedRoute, Router } from "@angular/router";
import { Validators, FormControl, NgForm } from "@angular/forms";
import { Estimulo } from '../estimulo-model';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: "app-estimulo-category-create",
  templateUrl: "./estimulo-category-create.component.html",
  styleUrls: ["./estimulo-category-create.component.css"],
})
export class EstimuloCategoryCreateComponent implements OnInit {
  grupo: GrupoEstimulo = new GrupoEstimulo();
  grupos: GrupoEstimulo[] = [];
  estimulos: Estimulo[] = [];

  //datasource: MatTableDataSource<string>;
  datasource: string[];

  filteredCategorias: Observable<GrupoEstimulo[]>;

  @ViewChild(NgForm) form;

  //Form Controls
  categoriaFC = new FormControl("", [Validators.required]);
  estimulo: Estimulo[];

  constructor(
    private grupoEstimuloService: GrupoEstimuloService,
    private estimuloService: EstimuloService,
    private route: ActivatedRoute,
    private router: Router,
    private loadingService: LoadingService
  ) {}

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    let idGrupoEstimulo = this.route.snapshot.paramMap.get("id");
    //console.log(idEstimulo)

    this.estimuloService.find().subscribe(estimulos => {
      this.estimulos = estimulos;
    })

    this.grupoEstimuloService.find().subscribe(async grps => {
      this.grupos = grps;
      if (idGrupoEstimulo == undefined) {
        //Create
        this.grupo = new GrupoEstimulo();
        this.grupo.ativo = true;
      } else {
        //Edit
        await this.grupoEstimuloService.findById(idGrupoEstimulo).subscribe(
          (grupo) => {
            // console.log(this.grupo);
            this.grupo = grupo;
          }
        );
      }
      this.loadingService.hide();
    })

  }

  displayFn(grupo: GrupoEstimulo): string {
    return grupo && grupo.nome ? grupo.nome : "";
  }

  async save(exit: boolean) {
    // console.log(this.grupo.nome);
    //console.log("1")
    if (this.form.valid) {
      if (this.grupo.id == undefined) {
        //Verifico se a categoria (grupo) de estímulo existe.
        if (this.grupos.find((grp) => grp.nome == this.grupo.nome) == undefined) {
            this.grupoEstimuloService.create(this.grupo).subscribe((grupo) => {
              this.grupoEstimuloService.showMessage(
                "Categoria de estímulo criada com sucesso!"
              );
            });
          if (exit) {
            this.router.onSameUrlNavigation = "reload";
            this.router.navigate(["/estimulo/category"]);
          } else {
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = "reload";
            this.router.navigate(["/estimulo/category/create"]);
          }
        } else {
          this.grupoEstimuloService.showMessage(
            "Categoria de estímulo já existente!", true
          );
        }
      } else {
        if (
          this.grupos.find((grp) => grp.nome == this.grupo.nome) == undefined
        ) {
          this.grupoEstimuloService.update(this.grupo).subscribe((grupo) => {
            this.grupoEstimuloService.showMessage(
              "Categoria de estímulo alterada com sucesso!"
            );
            this.estimulos.forEach(estimulo => {
              if (estimulo.grupo.id === this.grupo.id) {
                estimulo.grupo.nome = this.grupo.nome;
                this.estimuloService.update(estimulo).subscribe()
              }}
            )
            if (exit) {
              this.router.onSameUrlNavigation = "reload";
              this.router.navigate(["/estimulo/category"]);
            } else {
              this.router.routeReuseStrategy.shouldReuseRoute = () => false;
              this.router.onSameUrlNavigation = "reload";
              this.router.navigate(["/estimulo/category/create"]);
            }
          });
        } else {
          this.grupoEstimuloService.showMessage(
            "Categoria de estímulo já existente!", true
          );
        }
      }
      // console.log(this.grupo)
    } else {
      this.grupoEstimuloService.showMessage(
        "Existem campos inválidos no formulário!",
        true
      );
    }
  }

  cancel() {
    this.router.navigate(["/estimulo/category"]);
  }

}
