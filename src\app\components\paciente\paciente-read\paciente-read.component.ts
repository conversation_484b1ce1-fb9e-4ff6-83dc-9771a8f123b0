import { Profissional } from './../../profissional/profissional-model';
import { AuthService } from './../../template/auth/auth.service';
import { Observable } from 'rxjs';
import { PacienteService } from './../paciente.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Paciente } from './../paciente-model';
import { Component, OnInit, Input } from '@angular/core';
import { now } from 'moment';
import { NgbPopoverWindow } from '@ng-bootstrap/ng-bootstrap/popover/popover';
import { Endereco } from 'src/app/shared/model/endereco-model';


@Component({
  selector: 'app-paciente-read',
  templateUrl: './paciente-read.component.html',
  styleUrls: ['./paciente-read.component.css']
})
export class PacienteReadComponent implements OnInit {

  @Input()
  //$pacienteSearch: Paciente;
  $pacienteSearch: Observable<Paciente>;

  paciente: Paciente = new Paciente();
  idade: number;
  hasAccessUpdate: boolean;

  constructor(private route: ActivatedRoute,
    private pacienteService: PacienteService,
    public authService: AuthService,
    private router: Router) { }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    //console.log("Id:" + id);
    
    //console.log(this.$pacienteSearch)
    //this.paciente = this.$pacienteSearch;
    this.paciente.ativo == undefined;
    this.$pacienteSearch.subscribe(paciente => {
      this.paciente = paciente;
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de pacientes','update');
      this.idade = this.pacienteService.getIdadeMeses(this.paciente.dataNascimento);
      
      //console.log("Nascimento paciente: " + paciente.dataNascimento);
    });

    //this.pacienteService.findById(id).subscribe(p => {
    //  this.paciente = p;
    //});

  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    //console.log(funcoes)


    return funcoes;
  }

  /*save(): void{
    this.pacienteService.delete(this.paciente.id).subscribe(() => {
      console.log('Candidato excluído.');
      this.pacienteService.create(this.paciente).subscribe(tipo => {
        this.paciente = tipo;
        this.pacienteService.showMessage('Candidato alterado com sucesso!');
        console.log('Candidato alterado.');
        this.router.navigate(['/paciente']);
      })
      
    });
  }

  cancel(): void{
    this.router.navigate(['/paciente']);
  }
  */
}
