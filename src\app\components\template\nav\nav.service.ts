import { NavData } from './nav-data-model';
import { BehaviorSubject } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class NavService {

  private _navData = new BehaviorSubject<NavData>({
    hidden: false
  })

  constructor() { }

  get navData(): NavData {
    return this._navData.value;
  }

  set navData(navData: NavData){
    this._navData.next(navData)
  }

}
