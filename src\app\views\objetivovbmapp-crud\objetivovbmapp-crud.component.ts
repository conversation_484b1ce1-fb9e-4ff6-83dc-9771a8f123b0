import { HeaderService } from './../../components/template/header/header.service';
import { AuthService } from './../../components/template/auth/auth.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-objetivovbmapp-crud',
  templateUrl: './objetivovbmapp-crud.component.html',
  styleUrls: ['./objetivovbmapp-crud.component.css']
})
export class ObjetivovbmappCrudComponent implements OnInit {

  hasAccessCreate: boolean = false;

  constructor(private router: Router,
    public authService: AuthService,
    private headerService: HeaderService) {
      headerService.headerData = {
        title: 'Objetivos (ABA)',
        icon: 'source',
        routeUrl: '/vbmapp_objetivo'
      }
    }

  ngOnInit(): void {
    this.hasAccessCreate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','create') //Reveer
  }

}
