.center {
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.overlay{
  height:100vh;
  width:100%;
  background-color:rgba(0, 0, 0, 0.286);
  z-index:    10;
  top:        0; 
  left:       0; 
  position:   fixed;
}

.close {
    font-size: 1.4rem;
    opacity: 0.1;
    transition: opacity 0.3s;
}
.nav-link:hover > .close {
    opacity: 0.8;
}

.label-N {
  background-color: #ff0000;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}
  
.label-P1 {
  background-color: #ff6f00;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-P {
  background-color: #ffc800;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-P3 {
  background-color: #51c951;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}
  
.label-A {
  background-color: #007612;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-X {
  background-color: lightgray;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .50em;
  margin: .5em;
}

table{
  width: 100%;
}

.mat-column-id {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: left;
}

.mat-column-nome {
  flex: 0 0 63% !important;
  width: 63% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-status {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: center;
}

table{
  width: 100%;
}
