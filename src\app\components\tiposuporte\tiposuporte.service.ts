import { TipoSuporte } from './tiposuporte-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';
import { environment } from './../../../environments/environment';


@Injectable({
  providedIn: 'root'
})
export class TipoSuporteService {

  tipoSuporteUrl = `${environment.API_URL}/tiposuporte`;

  public $tipossuporte: Observable<TipoSuporte[]>;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  
  create(tipoSuporte: TipoSuporte): Observable<TipoSuporte>{
    //console.log(tipoSuporte);
    return this.http.post<TipoSuporte>(this.tipoSuporteUrl, tipoSuporte).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  
  update(tipoSuporte: TipoSuporte): Observable<TipoSuporte>{
    return this.http.put<TipoSuporte>(this.tipoSuporteUrl + "/" + tipoSuporte.id, tipoSuporte).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<TipoSuporte>{
    return this.http.get<TipoSuporte>(this.tipoSuporteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<TipoSuporte[]>{
    return this.http.get<TipoSuporte[]>(this.tipoSuporteUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  /*
  delete(id: string): Observable<tipoSuporte>{
    return this.http.delete<tipoSuporte>(this.tipoSuporteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */
}
