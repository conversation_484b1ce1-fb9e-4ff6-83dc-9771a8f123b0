import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'filterdominio',
    pure: false
})
export class FilterDominioPipe implements PipeTransform {
    transform(items: Map<any, any>, filter: string): any {
        if (!items || !filter) {
            return items;
        }
        // filter items array, items which match and return true will be
        // kept, false will be filtered out
        return items.get(filter);
    }
}