import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Anamnese } from '../anamnese-model';
import { AnamneseService } from '../anamnese.service';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-anamnese-list',
  templateUrl: './anamnese-list.component.html',
  styleUrls: ['./anamnese-list.component.css']
})
export class AnamneseListComponent implements OnInit {

  public anamneses: Anamnese[];

  displayedColumns = ['descricao','action'];

  constructor(private anamneseService: AnamneseService,
    private router: Router,
    public dialog: MatDialog,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();

    try {
      const data = await this.anamneseService.find().toPromise()
      this.anamneses = data;
    } catch (error) {
      console.log(error);
      this.loadingService.hide();
    } finally {
      this.loadingService.hide();
    }
  }

  edit(anamnese: Anamnese){
    this.router.navigate(['/anamnese/' + anamnese.id]);
  }

  delete(anamnese: Anamnese){
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.anamneseService.delete(anamnese.id).subscribe((p) => {
          this.anamneseService.showMessage('Template anamnense excluido com sucesso!');
          this.router.routeReuseStrategy.shouldReuseRoute = () => false;
          this.router.onSameUrlNavigation = 'reload';  
        });
      }
    });
  }

}
