.label-N {
  background-color: #ff0000;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}
  
.label-P1 {
  background-color: #ff6f00;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-P {
  background-color: #ffc800;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-P3 {
  background-color: #51c951;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}
  
.label-A {
  background-color: #007612;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}

.label-X {
  background-color: lightgray;
  color: white;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
}
  
.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  margin: .5em;
}

.label-atrasado {
  background-color: #ff902b;
}

.label-ok {
  background-color: #27c24c;
}

.label-aviso {
  background-color: #e2b902;
}

.objetivoAdquirido {
  background-color: lightgreen;
}

table{
  width: 100%;
}
.mat-column-id {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: left;
}

.mat-column-nome {
  flex: 0 0 65% !important;
  width: 65% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-status {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: center;
}

.mat-column-index {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
}

.mat-column-expand {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
}

.mat-column-etapas {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
}
  
.mat-column-idNome {
  flex: 0 0 70% !important;
  width: 70% !important;
  text-align: left;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  
}

.mat-column-action {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: right;
}

.mat-column-nomeObjSelList {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-habilidadeObjSelList {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-estimulosObjSelList {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-tipoSuporteObjSelList {
  flex: 0 0 30% !important;
  width: 30% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* ['nomeObj', 'dominio', 'tipoColeta', 'tipoSuporte', 'descricao', 'estimulos', 'action'] */
.mat-column-nomeObj {
  flex: 0 0 20% !important;
  width: 20% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* .mat-column-dominio {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
} */

.mat-column-tipoColeta {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-tipoSuporte {
  flex: 0 0 7% !important;
  width: 7% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
  align-items: center;
}

.mat-column-descricao {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-habilidades {
  flex: 0 0 20% !important;
  width: 20% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-estimulos {
  flex: 0 0 20% !important;
  width: 20% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.custom-chip {
  max-width: 50%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

tr.readonly {
  background-color: #eeeeee;
}
  
.edit {
  margin-right: 10px;
}
.edit > i {
  color: #d9cd26;
  cursor: pointer;
}

.delete > i {
  color: #e35e6b;
  cursor: pointer;
}

.student-element-detail {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.student-element-detail-old {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e1e1e1;
  box-shadow: inset 2px 3px 8px #c1c1c1;
  border-radius: 8px;
}

tr.student-detail-row {
  height: 0;
}

.msDisabled{
  background-color: lightgrey;
  cursor: default !important;
}

.msEnabled{
  cursor: pointer;
}

.center {
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.overlay{
  height:100vh;
  width:100%;
  background-color:rgba(0, 0, 0, 0.286);
  z-index:    10;
  top:        0; 
  left:       0; 
  position:   fixed;
}

mat-form-field{
  padding: 10px;
  font-weight: 300;
  font-size: small;
}

.header{
  display: flex;
  align-items: center;
}

.header a {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.header .logo{
  max-height: 32px;
}

.header .title-group{
  padding-left: 25px;
}

.header .title-group i{
  padding-right: 5px;
}

.group {
  color: rgba(0, 0, 0, 0.12);
  padding: 1em 0 1em 0;
  display:flex; 
  flex-direction: column;
  position: relative;
}

.input {
  color: black;
}
