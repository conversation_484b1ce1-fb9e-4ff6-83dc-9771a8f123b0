import { Nivel } from './../../nivel/nivel-model';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { Estimulo } from './../../estimulo/estimulo-model';
import { TipoSuporte } from './../../tiposuporte/tiposuporte-model';
import { DominioVBMAPPService } from './../../dominiovbmapp/dominiovbmapp.service';
import { ColetadiariavbmappService } from './../coletadiariavbmapp.service';
import { PlanointervencaovbmappService } from './../../planointervencaovbmapp/planointervencaovbmapp.service';
import { ObjetivoVBMAPP } from './../../objetivovbmapp/objetivovbmapp-model';
import { PlanoIntervencaoVBMAPP } from './../../planointervencaovbmapp/planointervencaovbmapp-model';
import { ColetaDiariaVBMAPP } from './../coletadiariavbmapp-model';
import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { MatSelectChange } from '@angular/material/select';
import { PacienteService } from './../../paciente/paciente.service';
import { Paciente } from './../../paciente/paciente-model';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ProfissionalService } from './../../profissional/profissional.service';
import { FormControl, Validators, NgForm, FormBuilder, FormGroup } from '@angular/forms';
import { Profissional } from './../../profissional/profissional-model';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { PIC } from '../../planopic/pic-model';
import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from '../coletadiariapic-model';
import { ColetadiariaPICService } from '../coletadiariapic.service';
import { PICService } from '../../planopic/pic.service';
import { Etapa } from '../../etapa/etapa-model';
import { LoadingService } from '../../../shared/service/loading.service';
import { catchError, pairwise, startWith } from 'rxjs/operators';
import { PlanoIntervencaoService } from '../../planointervencao/planointervencao.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Objetivo } from '../../objetivo/objetivo-model';
import { PlanoIntervencao } from '../../planointervencao/planointervencao-model';

@Component({
  selector: 'app-coletadiariavbmapp-create',
  templateUrl: './coletadiariavbmapp-create.component.html',
  styleUrls: ['./coletadiariavbmapp-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ColetadiariavbmappCreateComponent implements OnInit {

  public coletasdiarias: any[] = [];
  public profissionais: Profissional[] = [];
  public profissionaisDoPaciente: Profissional[];
  public idProfissional: string;
  public idPaciente: string;
  public profissionalLogado: Profissional = new Profissional();
  public paciente: Paciente = new Paciente();
  public data: Date;
  public today: Date = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
  //public profissional: Profissional;
  public idPlanoIntervencao: string;
  public planoIntervencao: PlanoIntervencaoVBMAPP = new PlanoIntervencaoVBMAPP();
  public planosVBMAPP: PlanoIntervencaoVBMAPP[] = [];
  public planosESDM: PlanoIntervencao[] = [];
  public planosPIC: PIC[] = [];

  public pic: PIC = new PIC();
  public coletasdiariasPIC: SessaoColetaDiariaPIC[] = [];

  public PICs: PIC[] = [];

  public cantChangeData: boolean = false;

  public dominioMap: Map<string, ObjetivoVBMAPP[]>[] = [];

  public domAtual: string = "";

  tabs = [] as string[];
  selected = new FormControl(0);

  hours = [
    "07:00", "07:15", "07:30", "07:45",
    "08:00", "08:15", "08:30", "08:45",
    "09:00", "09:15", "09:30", "09:45",
    "10:00", "10:15", "10:30", "10:45",
    "11:00", "11:15", "11:30", "11:45",
    "12:00", "12:15", "12:30", "12:45",
    "13:00", "13:15", "13:30", "13:45",
    "14:00", "14:15", "14:30", "14:45",
    "15:00", "15:15", "15:30", "15:45",
    "16:00", "16:15", "16:30", "16:45",
    "17:00", "17:15", "17:30", "17:45",
    "18:00", "18:15", "18:30", "18:45",
    "19:00", "19:15", "19:30", "19:45",
    "20:00", "20:15", "20:30", "20:45"
  ]
  
  public TittlePEI: string = "PEI";
  public TittlePIC: string = "PIC";

  public controles: { [sessaoId: number]: { [estimuloId: string]: FormGroup } } = {};

  //Form Controls
  dataFC = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  constructor(
    private planoIntervencaoService: PlanointervencaovbmappService,
    private planoIntervencaoESDMService: PlanoIntervencaoService,
    private picService: PICService,
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    private coletaDiariaService: ColetadiariavbmappService,
    private coletaDiariaPICService: ColetadiariaPICService,
    public authService: AuthService,
    private dominioService: DominioVBMAPPService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private loadingService: LoadingService,
    private snackBar: MatSnackBar,
    private fb: FormBuilder,
  ) { }

  async ngOnInit(): Promise<void> {
    //this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    this.idPlanoIntervencao = this.route.snapshot.paramMap.get('id');
    const currentUser = this.authService.getUser() as FirebaseUserModel;
    
    try {
      let plano 

      //Recupero o Plano de Intervenção
      plano = await this.planoIntervencaoService.findById(this.idPlanoIntervencao).toPromise();
      if (!plano) {
        plano = await this.planoIntervencaoESDMService.findById(this.idPlanoIntervencao).toPromise();
      }
      
      if (plano?.id){
        //Acrescentando id_dominio e dominio na raiz do objetivo para podermos compatibilizar com a versão anterior da estrutura de armazenamento de objetivos
        for(const [, obj] of plano.objetivos.entries()){
          if(obj.id_dominio == undefined && obj.marco != undefined){
            // console.log(obj.marco)
            obj.id_dominio = obj.marco.id_dominio;
            obj.dominio = obj.marco.dominio;
          } else {
            obj.marco = { "id_dominio": obj.id_dominio,
              "dominio": (obj.dominio as DominioVBMAPP),
              "nome": "",
              "objetivo": "",
              "id_nivel": (obj.id_nivel as string),
              "nivel": (obj.nivel as Nivel),
              "ordem": 0
            };
          }
        }
        this.planoIntervencao = plano;
      }
      
      this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
      
      let PICs;
      if(this.planoIntervencao?.id){
        PICs = await this.picService.findByPaciente(this.planoIntervencao.idPaciente).toPromise();
        this.paciente = await this.pacienteService.findById(this.planoIntervencao.idPaciente).toPromise();
      } else {
        const pic = await this.picService.findById(this.idPlanoIntervencao).toPromise();
        PICs = await this.picService.findByPaciente(pic.idPaciente).toPromise();
        this.paciente = await this.pacienteService.findById(pic.idPaciente).toPromise();
      }

      this.profissionaisDoPaciente = this.paciente.equipe;
      this.profissionalLogado = this.profissionaisDoPaciente.find(p => p.uid === currentUser.uid);
      if (this.profissionalLogado) {
        this.idProfissional = this.profissionalLogado.id; // Define o valor inicial do campo
      }
      
      //Carregando Profissionais
      const profissionais = await this.profissionalService.find().toPromise();
      this.profissionais = profissionais;
      this.coletasdiarias?.forEach(coleta => {
        coleta.profissional = this.profissionalLogado;
        coleta.idProfissional = this.profissionalLogado.id;
      })
      
      if (PICs.length > 0) {
        this.PICs = PICs.filter(p => p.status !== false);
        this.pic = this.PICs[0];
        this.coletasdiariasPIC.forEach(coleta => {
          coleta.idProfissional = this.profissionalLogado.id;
          coleta.nomeProfissional = this.profissionalLogado.nome;
        })
      }

      await this.addColetaDiaria();
      
      const planos = await this.picService.findByPaciente(this.paciente.id).toPromise();
      this.PICs = planos.filter(p => p.status !== false && (p.ativo === true || p.ativo === undefined));
      this.pic = this.PICs[0];
      this.coletasdiariasPIC?.forEach(coleta => {
        coleta.idProfissional = this.profissionalLogado.id;
        coleta.nomeProfissional = this.profissionalLogado.nome;
      })
      await this.addColetaDiariaPIC();

      //Recupero o resumo dos planos para pesquisa 
      this.planosVBMAPP = await this.planoIntervencaoService.findResumoByPaciente(this.paciente.id).toPromise()
      this.planosVBMAPP = this.planosVBMAPP.filter(p => p.ativo === true || p.ativo === undefined);

      this.planosESDM = await this.planoIntervencaoESDMService.findResumoByPaciente(this.paciente.id).toPromise()
      
      this.planosPIC = await this.picService.findResumoByPaciente(this.paciente.id).toPromise()
      this.planosPIC = this.planosPIC.filter(p => p.ativo === true || p.ativo === undefined);
      
      //ATENÇÃO: Código abaixo permite a edição das coletas caso seja no mesmo dia
      /*
      //Recupero as coletas diárias do plano de intervenção
      this.coletaDiariaService.findByPlanoIntervencaoData(this.idPlanoIntervencao, 
      //moment(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())).format('YYYY-MM-DD[T]HH:mm:ss.SSS[Z]')).subscribe(coletas => {
        new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()).toISOString()).subscribe(coletas => {
          this.coletasdiarias = coletas;
          
          if(this.coletasdiarias.length == 0) { //Create (Nenhuma coleta encontrada para o plano)
          console.log(this.coletasdiarias.length)
          //Seto a data atual
          this.data = new Date();
          this.addColetaDiaria();
          } else { //Edit
          //this.cantChangeData = true;
          this.data = this.coletasdiarias[0].data;
          this.idProfissional = this.coletasdiarias[0].idProfissional;
          this.coletasdiarias.forEach(coleta => {
            //this.addTab("Sessão " + (this.coletasdiarias.indexOf(coleta) + 1),false)
            this.addTab(coleta.sessao,false)
            this.setObjetivosPorDominioEdit(this.coletasdiarias.indexOf(coleta));
            })
            }
            
            })
            */

    } catch (error) {
      console.error("Erro ao carregar os dados:", error);
    }
  }

  //Disparado ao clicar no ícone
  async setRespostaPeriodo(
    sessaoId: number,
    objetivo: ObjetivoVBMAPP,
    ts: any,
    estimulo: Estimulo,
    tipo: 'positivos' | 'negativos' | 'independentes'
  ): Promise<void> {
    const totalAnterior = estimulo.positivos
                        + estimulo.negativos
                        + estimulo.independentes;
    const limite = objetivo.oportunidadesEstimulo;
    const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    const fg = this.controles[sessaoId][key];

    // se já atingiu o limite, bloqueia e mostra aviso
    if (totalAnterior >= limite) {
      this.snackBar.open(
        `Limite de ${limite} respostas atingido.`,
        'Fechar', { duration: 3000 }
      );
      
      // atualiza FormControl sem disparar valueChanges
      fg.get(tipo)!.setValue(estimulo[tipo], { emitEvent: false });
      return;
    }

    // caso não tenha atingido, incrementa 1 no tipo
    const valorAtual = estimulo[tipo];
    const novo = valorAtual + 1;
    estimulo[tipo] = novo;

    // atualiza FormControl sem disparar valueChanges
    fg.get(tipo)!.setValue((estimulo as any)[tipo], { emitEvent: false });

    estimulo.positivos     = fg.get('positivos')!.value;
    estimulo.negativos     = fg.get('negativos')!.value;
    estimulo.independentes = fg.get('independentes')!.value;

    await this.save(sessaoId);
  }

  //Tratamento de edição manual no input (blur/evento valueChanges)
  async onControlTouched(
    sessaoId: number,
    objetivo: ObjetivoVBMAPP,
    ts: any,
    estimulo: Estimulo,
    tipo: 'positivos' | 'negativos' | 'independentes'
  ) {
    const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    const fg = this.controles[sessaoId][key];
    const novoValor = fg.get(tipo)!.value as number;

    const totalAnterior = estimulo.positivos + estimulo.negativos + estimulo.independentes;
    const totalNovo = 
      tipo === 'positivos'   ? totalAnterior - estimulo.positivos   + novoValor :
      tipo === 'negativos'   ? totalAnterior - estimulo.negativos   + novoValor :
      /* independentes */      totalAnterior - estimulo.independentes + novoValor;

    const limite = objetivo.oportunidadesEstimulo;
    // Se tentou incrementar além do limite, bloqueia apenas esse campo
    if (totalNovo > limite && novoValor > (estimulo as any)[tipo]) {
      this.snackBar.open(`Limite de ${limite} respostas atingido.`, 'Fechar', { duration: 3000 });

      // rollback completo nos 3 campos
      ['positivos', 'negativos', 'independentes'].forEach(campo => {
        fg.get(campo)!.setValue(estimulo[campo], { emitEvent: false });
      });

      fg.markAsTouched();
      fg.updateValueAndValidity();
      return;
    }

    // Caso válido (diminuição ou dentro do limite): atualiza o modelo
    estimulo[tipo] = novoValor;

    // Persiste
    await this.save(sessaoId);
  }

  private onControlChange(
    sessaoId: number,
    objetivo: ObjetivoVBMAPP,
    ts: any,
    estimulo: Estimulo,
    oldVals: { positivos: number; negativos: number; independentes: number },
    newVals: { positivos: number; negativos: number; independentes: number }
  ) {
    const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    const fg = this.controles[sessaoId][key];

    // 1. Atualiza os valores do estimulo com o que está no FormControl (sincroniza)
    estimulo.positivos = fg.get('positivos')!.value;
    estimulo.negativos = fg.get('negativos')!.value;
    estimulo.independentes = fg.get('independentes')!.value;

    const limite = objetivo.oportunidadesEstimulo;
    const totalAnterior = oldVals.positivos + oldVals.negativos + oldVals.independentes;
    const totalNovo     = newVals.positivos + newVals.negativos + newVals.independentes;

    const tipoAlterado = ['positivos','negativos','independentes']
      .find(k => newVals[k] > oldVals[k]) as 'positivos'|'negativos'|'independentes';

    // 2. Impede incremento além do limite
    if (totalNovo > limite && totalNovo > totalAnterior) {
      this.snackBar.open(`Limite de ${limite} respostas atingido.`, 'Fechar', { duration: 3000 });

      // reverte o input ao valor antigo, rollback visual e modelo
      fg.get(tipoAlterado)!.setValue(oldVals[tipoAlterado], { emitEvent: false });
      estimulo[tipoAlterado] = oldVals[tipoAlterado];

      // força revalidação no formulário
      fg.markAsTouched();
      fg.updateValueAndValidity();

      return;
    }

    // 3. Atualiza os valores no estimulo (modelo)
    estimulo[tipoAlterado] = newVals[tipoAlterado];

    // 4. Persiste normalmente
    this.save(sessaoId);
  }

  // Função auxiliar para totalizador no HTML
  getTotalRespostas(estimulo: Estimulo): number {
    return estimulo.positivos + estimulo.negativos + estimulo.independentes;
  }

  setRespostaPeriodoESDM(sessaoId: number, periodoId: number, objetivo: any, etapa: any){
    let obj = this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)];
    let eta = obj.etapa[obj.etapa.indexOf(etapa)];

    if(eta.periodo[periodoId] == "") {
      // console.log("+")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = "+"
    } else if(eta.periodo[periodoId] == "+") {
      // console.log("-")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = "-"
    } else if(eta.periodo[periodoId] == "-") {
      // console.log("")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = ""
    }
    this.save(sessaoId);
    
  }

  async setObjetivosPorDominioCreate(sessaoId: number){
    try {
      let dMap = new Map<string, any[]>();
      let objetivos: any[] = [];
  
      // await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      if (this.coletasdiarias[sessaoId].objetivos == undefined) {
        this.coletasdiarias[sessaoId].objetivos = [];
      }
      for(const [,objetivo] of this.coletasdiarias[sessaoId]?.objetivos?.entries()){
  
        if (!objetivo.habilidades) {
          if(dMap.get(objetivo.dominio.id) != undefined){
            objetivos = dMap.get(objetivo.dominio.id);
            objetivos.push(objetivo);
            dMap.set(objetivo.dominio.id, objetivos)
          } else {
            dMap.set(objetivo.dominio.id, [objetivo])
          }
        } else {
          if(objetivo.marco.dominio == undefined && dMap.get("SD") != undefined) {
            objetivos = dMap.get("SD");
            objetivos.push(objetivo);
            dMap.set("SD", objetivos)
          } else if(objetivo.marco.dominio == undefined && dMap.get("SD") == undefined){
            dMap.set("SD", [objetivo])
          }
          
          if(objetivo.marco.dominio != undefined && dMap.get(objetivo.marco.dominio.id) != undefined){
            objetivos = dMap.get(objetivo.marco.dominio.id);
            objetivos.push(objetivo);
            dMap.set(objetivo.marco.id_dominio, objetivos)
          } else if(objetivo.marco.dominio != undefined && dMap.get(objetivo.marco.dominio.id) == undefined){
              dMap.set(objetivo.marco.id_dominio, [objetivo])
          }
        }
      }
      // })
      this.dominioMap.push(dMap);
    } catch (error) {
      console.log("Erro", error)
    }
    // console.log(dMap)
    // console.log(this.coletasdiarias[sessaoId])
    // console.log(this.dominioMap[this.dominioMap.length - 1])
  }

  async setObjetivosPorDominioEdit(sessaoId: number){
    let dMap = new Map<string, any[]>();
    let objetivos: any[] = [];

    await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    this.dominioMap.push(dMap);
  }

  checkDominioAtual(dominio: string): boolean {
    if(this.domAtual ==  dominio){
      return false;
    } else {
      this.domAtual = dominio;
      return true;
    }
  }

  getPeriodoIcon(periodo: string): string{
    // console.log(periodo)
    if(periodo == "+") {
      return "thumb_up"
    }
    if(periodo == "-") {
      return "thumb_down"
    }
    if(periodo == "") {
      return "thumbs_up_down"
    }
  }
  
  async setObjetivos(sessaoId: number){
    try {
      let objetivoNovo: any;
      let tipoSuporteNovo: TipoSuporte;
      let estimuloNovo: Estimulo;
      let etapaDefinida: boolean;
      let estimulos: string[]=[];
      let estimuloDefinido: boolean;
  
      // Recupero os objetivos do plano de intervenção atual
      this.planoIntervencao?.objetivos?.forEach(objetivo => {
        if (!objetivo.habilidades) {
          objetivoNovo = {...objetivo};
          etapaDefinida = false;
  
          if(objetivoNovo.status == "Adquirido") {
            //Se o objetivo foi adquirido, incluo o mesmo sem etapas
            objetivoNovo.etapa = []
            if(this.coletasdiarias[sessaoId].objetivos == undefined){
              this.coletasdiarias[sessaoId].objetivos = [];
            }
            this.coletasdiarias[sessaoId].objetivos.push(this.filtrarCamposObjetivo(objetivoNovo));
          } else {
            objetivoNovo.etapa.forEach(etapa => {
              if(!etapaDefinida){
                if(etapa.status == undefined || etapa.status != "Adquirida"){
                  objetivoNovo.etapa = []
                  objetivoNovo.etapa.push({...etapa})
                  objetivoNovo.etapa[0].periodo = []
                  objetivoNovo.etapa[0].periodo.push("", "", "", "")
                  etapaDefinida = true;
                } else {
                  objetivoNovo.etapa = [];
                }
              }
            })
            if(this.coletasdiarias[sessaoId].objetivos == undefined){
              this.coletasdiarias[sessaoId].objetivos = [];
            }
            this.coletasdiarias[sessaoId].objetivos.push(this.filtrarCamposObjetivo(objetivoNovo));
            // console.log(this.coletasdiarias[sessaoId])
          }
        } else {
          objetivoNovo = {...objetivo};
          estimuloDefinido = false;
    
          // Retiro todos os tipos de suporte do objetivo a ser trabalhado para que eu possa incluir apenas os
          // tipos de suporte que ainda precisam ser trabalhados.
          objetivoNovo.tiposSuporte = [];
    
          // Caso o Tipo de Coleta seja Naturalista, incluo na sessão apenas os tipos de suporte que estão em andamento.
          if(objetivoNovo.tipoColeta == "Naturalista"){
            if(objetivoNovo.status == "Adquirido") {
              //Se o objetivo foi adquirido, incluo o mesmo sem tipos de suporte
              if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }
              this.coletasdiarias[sessaoId].objetivos.push(this.filtrarCamposObjetivo(objetivoNovo));
            } else {
              // Se o objetivo não estiver Adquirido, incluo o tipo de suporte e seus estímulos que precisam ser trabalhado.
              estimulos = [];
              objetivo.tiposSuporte.forEach(tipoSuporte => {
                tipoSuporteNovo = {...tipoSuporte};
    
                if(tipoSuporteNovo.status == undefined || tipoSuporteNovo.status != "Adquirido"){
                  // objetivoNovo.tiposSuporte = []
                  tipoSuporteNovo.estimulos = [];
                  
                  tipoSuporte.estimulos.forEach(estimulo => {
                    estimuloNovo = {...estimulo};
                    estimuloNovo.status = "";
                    if(estimulo.status == undefined || estimulo.status != "Adquirido"){
                      if(estimulos.find(e => e == estimulo.id) == undefined){ //Estímulo ainda não foi adicionado para trabalhar
                        estimulos.push(estimulo.id)
                        estimuloNovo.periodo = []
                        estimuloNovo.independentes = 0;
                        estimuloNovo.positivos = 0;
                        estimuloNovo.negativos = 0;
                        //Adiciono o número de períodos de acordo com o número de oportunidades descritas no objetivo
                        for(let i = 1; i <= (objetivoNovo.oportunidadesEstimulo == undefined ? 3 : objetivoNovo.oportunidadesEstimulo); i++){
                          estimuloNovo.periodo.push("")
                        }
                        tipoSuporteNovo.estimulos.push({...estimuloNovo});
                      }
                    }
                  })
                  objetivoNovo.tiposSuporte.push({...tipoSuporteNovo});
                }
              })
              if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }
              this.coletasdiarias[sessaoId].objetivos.push(this.filtrarCamposObjetivo(objetivoNovo));
              // console.log(this.coletasdiarias[sessaoId])
            }
          }
        }
      })
      
      // console.log(this.coletasdiarias);
      //Incluo os objetivos em um domínio de Map para facilitar a listagem no HTML
      if (this.planoIntervencao?.objetivos) {
        await this.setObjetivosPorDominioCreate(sessaoId);
        await this.setControles(sessaoId);
      }
    } catch (error) {
      console.log("Erro", error);
    }
  }

  async setControles(sessaoId: number){
    this.controles[sessaoId] = {};
    this.coletasdiarias[sessaoId].objetivos.forEach((objetivo: any) => {
      if (objetivo.tiposSuporte) {
        objetivo.tiposSuporte.forEach(ts => {
          ts.estimulos.forEach((est: Estimulo) => {
            const fg = this.fb.group({
              positivos:    [{ value: est.positivos,     disabled: !this.coletasdiarias[sessaoId]?.id }],
              negativos:    [{ value: est.negativos,     disabled: !this.coletasdiarias[sessaoId]?.id }],
              independentes:[{ value: est.independentes, disabled: !this.coletasdiarias[sessaoId]?.id }],
            });
            fg.valueChanges.pipe(
              startWith(fg.value),
              pairwise()
            ).subscribe(([oldVals, newVals]) =>
              this.onControlChange(sessaoId, objetivo, ts, est, oldVals, newVals)
            );
            // Chave composta: objetivoId_tipoSuporteId_estimuloId
            const key = `${objetivo.id}_${ts.id}_${est.id}`;
            this.controles[sessaoId][key] = fg;
          });
        });
      }
    });
  }

  enableCamposEstimulo(sessaoId: number): void {
    const coletaTemId = !!this.coletasdiarias[sessaoId]?.id;
    if (!coletaTemId) return;

    Object.values(this.controles[sessaoId] || {}).forEach((fg: FormGroup) => {
      fg.get('positivos')?.enable();
      fg.get('negativos')?.enable();
      fg.get('independentes')?.enable();
    });
  }
  
  private filtrarCamposObjetivo(obj: any): any {
    // Campos básicos sempre mantidos
    const base = {
      ativo: obj.ativo,
      id: obj.id,
      nome: obj.nome,
      marco: obj.marco,
      status: obj.status,
    };

    // VBMAPP: tem array de habilidades
    if (Array.isArray(obj.habilidades)) {
      return {
        ...base,
        estimulos: obj.estimulos,
        habilidades: obj.habilidades,
        oportunidadesEstimulo: obj.oportunidadesEstimulo,
        tipoColeta: obj.tipoColeta,
        tiposSuporte: obj.tiposSuporte
      };
    } else {
      // ESDM (Naturalista): não tem `habilidades`, mas tem etapas
      return {
        ...base,
        dominio: obj.dominio,
        etapa: obj.etapa,
        // oportunidadesEtapa: obj.oportunidadesEtapa,
      };
    }

  }

  async addTab(name: string, selectAfterAdding: boolean) {
    this.tabs.push(name);
    // console.log(this.tabs)
    // console.log(name)

    if (selectAfterAdding) {
      this.selected.setValue(this.tabs.length - 1);
    }
  }

  async addColetaDiaria(){
    try {
      let cd = new ColetaDiariaVBMAPP();
      let dataInicio: Date;
      //console.log(this.coletasdiarias.length)
  
      //Incluindo os dados do paciente
      cd.idPaciente = this.planoIntervencao.idPaciente;
      cd.paciente = this.paciente;
  
      //Incluindo os dados do plano de intervenção
      cd.idPlanoIntervencao = this.idPlanoIntervencao;
  
      //Incluindo os dados do profissional
      if (this.coletasdiarias.length == 0) {
        if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
          if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
            cd.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            cd.idProfissional = cd.profissional?.id;
            // this.idProfissional = cd.idProfissional;
          }
        }
      } else {
        cd.profissional = this.coletasdiarias[0]?.profissional;
        cd.idProfissional = this.coletasdiarias[0]?.profissional?.id;
      }
  
      //Incluindo o nome da sessão
      cd.sessao = "Sessão " + (this.coletasdiarias.length + 1);
  
      //Aplicando a data e hora atual a coleta diária
      cd.data = new Date(new Date(this.data).getFullYear(), new Date(this.data).getMonth(), new Date(this.data).getDate(), 0,0,0,0);
      // console.log(cd.data)
      if(this.coletasdiarias.length > 0){
        dataInicio = new Date(this.data);
        dataInicio.setHours(parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(0,2)) + 1,
          parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(3,2)))
        // console.log(dataInicio)
        cd.horaInicio = ("0" + dataInicio.getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cd.horaTermino = ("0" + (dataInicio.getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      } else {
        cd.horaInicio = ("0" + new Date().getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cd.horaTermino = ("0" + (new Date().getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      }
  
      this.coletasdiarias.push(cd);
  
      //this.addTab("Sessão " + (this.coletasdiarias.length), true)
      if (this.planoIntervencao?.id) {
        await this.addTab(cd.sessao, true)
      }
      await this.setObjetivos(this.coletasdiarias.length - 1);
    } catch (error) {
      console.log("Erro: " + error);
    }
  }

  async addColetaDiariaPIC(){
    if (this.pic?.id) {
      let cdPIC = new SessaoColetaDiariaPIC();
      let dataInicio: Date;
  
      //Incluindo os dados do paciente
      cdPIC.idPaciente = this.pic.idPaciente;
  
      //Incluindo os dados do plano de intervenção
      cdPIC.idPlanoIntervencao = this.pic.id;
  
      //Incluindo os dados do profissional
  
      if (this.coletasdiariasPIC.length == 0) {
        if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
          if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
            let profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            cdPIC.idProfissional = profissional.id;
            cdPIC.nomeProfissional = profissional.nome;
          }
        }
      } else {
        cdPIC.idProfissional = this.coletasdiariasPIC[0]?.idProfissional;
        cdPIC.nomeProfissional = this.coletasdiariasPIC[0]?.nomeProfissional;
      }
  
      //Incluindo o nome da sessão
      cdPIC.sessao = "Sessão " + (this.coletasdiariasPIC.length + 1);
  
      //Aplicando a data e hora atual a coleta diária
      cdPIC.data = new Date(new Date(this.data).getFullYear(), new Date(this.data).getMonth(), new Date(this.data).getDate(), 0,0,0,0);
      //console.log(cdPIC.data)
      if(this.coletasdiariasPIC.length > 0){
        dataInicio = new Date(this.data);
        dataInicio.setHours(parseInt(this.coletasdiariasPIC[this.coletasdiariasPIC.length - 1].horaInicio.substr(0,2)) + 1,
          parseInt(this.coletasdiariasPIC[this.coletasdiariasPIC.length - 1].horaInicio.substr(3,2)))
        // console.log(dataInicio)
        cdPIC.horaInicio = ("0" + dataInicio.getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cdPIC.horaTermino = ("0" + (dataInicio.getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      } else {
        cdPIC.horaInicio = ("0" + new Date().getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
        cdPIC.horaTermino = ("0" + (new Date().getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
      }
  
      // Incluindo os dados da coleta para cada objetivo no plano
      cdPIC.objetivosColeta = this.pic.objetivos?.map(objetivo => {
        let coletaObjetivo = new ColetaDiariaPIC();
        coletaObjetivo.idObjetivo = objetivo.id;
        if(objetivo.tipoColeta == 'Cronometragem' || objetivo.tipoColeta == 'Amostragem de Tempo'){
          coletaObjetivo.duracoes = [];
        } else {
          coletaObjetivo.qtdObservada = null;
        }
        // Adicionar outros dados, como qtdObservada e duracoes, se necessário
        return coletaObjetivo;
      });
  
      this.coletasdiariasPIC.push(cdPIC);
      if (!this.planoIntervencao?.id) {
        this.addTab(cdPIC.sessao, true)
      }
      // console.log(this.coletasdiariasPIC)
    }
  }

  startSession(sessaoId: number){
    if(this.form.valid){
      // console.log(this.coletasdiarias)
      if (this.planoIntervencao?.objetivos?.length > 0){
        this.save(sessaoId);
      }
      if (this.pic?.objetivos?.length > 0){
        this.savePIC(sessaoId);
      }
      //this.coletaDiariaService.showMessage("Sessão iniciada...");
    } else {
      this.coletaDiariaService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  async setColetasPorData() {
    const dataEscolhida = new Date(this.data);
    let plano;
    let planoPIC;
    const mensagens: string[] = [];

    try {
      const novoPlanoVBMAPP = this.findPlanoPorDataRanged(this.planosVBMAPP, dataEscolhida);
      const novoPlanoESDM = this.findPlanoPorDataRanged(this.planosESDM, dataEscolhida);
      const novoPlanoPIC = this.findPlanoPorDataRanged(this.planosPIC, dataEscolhida);

      // Verifica PEI
      if (novoPlanoVBMAPP?.id || novoPlanoESDM?.id){
        // console.log("PEI")
        if (!novoPlanoESDM?.data || new Date(novoPlanoVBMAPP?.data) > new Date(novoPlanoESDM?.data)) {
          // console.log("PEI VBMAPP")
          if (novoPlanoVBMAPP?.id != this.planoIntervencao?.id) {
            plano = await this.planoIntervencaoService.findById(novoPlanoVBMAPP?.id).toPromise();
          }
        } else if (!novoPlanoVBMAPP?.data || new Date(novoPlanoESDM?.data) > new Date(novoPlanoVBMAPP?.data)) {
          // console.log("PEI ESDM")
          if (novoPlanoESDM?.id != this.planoIntervencao?.id) {
            plano = await this.planoIntervencaoESDMService.findById(novoPlanoESDM?.id).toPromise();
          }
        }
      }

      // Verifica PIC
      if (novoPlanoPIC?.id && novoPlanoPIC?.id !== this.pic?.id) {
        planoPIC = await this.picService.findById(novoPlanoPIC?.id).toPromise();
      }

      // console.log(plano)
      // console.log(planoPIC)

      // Avalia ausência de planos
      if (!novoPlanoVBMAPP && !novoPlanoESDM && this.planoIntervencao?.id) {
        mensagens.push('PEI');
        this.planoIntervencao = null;
      }

      if (!novoPlanoPIC && this.pic?.id) {
        mensagens.push('PIC');
        this.pic = null;
      }

      if (!novoPlanoVBMAPP && !novoPlanoESDM && !novoPlanoPIC) {
        this.snackBar.open('Nenhum plano PEI e PIC válido encontrado para a data escolhida.', 'Fechar', { duration: 4000 });
        return;
      }

      if (mensagens.length === 1) {
        this.snackBar.open(`Nenhum plano ${mensagens[0]} válido encontrado para a data escolhida.`, 'Fechar', { duration: 4000 });
      }

      // Atualiza plano e PIC
      if (plano?.id) {
        for (const obj of plano.objetivos ?? []) {
          if (!obj.id_dominio && obj.marco) {
            obj.id_dominio = obj.marco.id_dominio;
            obj.dominio = obj.marco.dominio;
          } else {
            obj.marco = {
              id_dominio: obj.id_dominio,
              dominio: obj.dominio,
              nome: '',
              objetivo: '',
              id_nivel: obj.id_nivel,
              nivel: obj.nivel,
              ordem: 0
            };
          }
        }
        this.planoIntervencao = plano;
      }

      if (planoPIC?.id) {
        this.pic = planoPIC;
      }

      await this.setNovaColeta(dataEscolhida);
    } catch (error) {
      console.error("Erro ao carregar os dados:", error);
      this.snackBar.open('Erro ao carregar os planos para a data selecionada.', 'Fechar', { duration: 4000 });
    }
  }

  findPlanoPorDataRanged(planos: any[], dataEscolhida: Date): any {
    if (!planos || planos.length === 0) return null;

    const planosOrdenados = planos
      .filter(p => p?.data)
      .sort((a, b) => new Date(a.data).getTime() - new Date(b.data).getTime());

    const data = new Date(dataEscolhida);

    for (let i = 0; i < planosOrdenados.length - 1; i++) {
      const planoAtual = planosOrdenados[i];
      const dataPlanoAtual = this.normalizarData(new Date(planoAtual.data));
      const dataPlanoSeguinte = planosOrdenados[i + 1] ? this.normalizarData(new Date(planosOrdenados[i + 1].data)) : null;

      if (dataPlanoSeguinte && data >= dataPlanoAtual && data < dataPlanoSeguinte) {
        // console.log('Dentro do range');
        return planoAtual; // dentro do range
      }
    }

    // Se data for maior que o último plano, retorna o último plano
    const dataUltimoPlano = this.normalizarData(new Date(planosOrdenados[planosOrdenados.length - 1].data));
    if (data >= dataUltimoPlano) {
      return planosOrdenados[planosOrdenados.length - 1];
    }

    return null;
  }

  normalizarData(data: Date): Date {
    return new Date(data.getFullYear(), data.getMonth(), data.getDate());
  }

  async setNovaColeta(dataEscolhida: Date): Promise<void> {
    this.data = dataEscolhida;

    this.coletasdiarias = [];
    this.coletasdiariasPIC = [];
    this.tabs = [];
    this.controles = [];
    this.dominioMap = [];
    this.selected.setValue(0);

    if (this.planoIntervencao?.id) {
      await this.addColetaDiaria();
      // Aplica profissional logado às coletas
      this.coletasdiarias?.forEach(coleta => {
        coleta.profissional = this.profissionalLogado;
        coleta.idProfissional = this.profissionalLogado?.id;
      });
    }

    if (this.pic?.id) {
      await this.addColetaDiariaPIC();
      this.coletasdiariasPIC?.forEach(coleta => {
        coleta.nomeProfissional = this.profissionalLogado?.nome;
        coleta.idProfissional = this.profissionalLogado?.id;
      });
    }

  }

  exitEdit(){
    this.router.navigate(['/paciente/' + this.paciente.id, {  tab: "vbmapp_coleta"}])
  }

  async save(sessaoId: number){
    //this.cleanEmptyPeriods();
    /*if(sessaoId == -1) { //Salvar todas
      if(this.form.valid){
        this.coletasdiarias.forEach(coleta => {
          if(coleta.id == undefined){
            this.coletaDiariaService.create(coleta).subscribe((coleta) => {
              this.coletasdiarias[this.coletasdiarias.indexOf(coleta)] = coleta;
            });
          } else {
            this.coletaDiariaService.update(this.coletasdiarias[sessaoId]).subscribe((coleta) => {
            });
          }
        })
      } else {
        this.coletaDiariaService.showMessage('Existem campos inválidos no formulário!',true);
      }
    } else { //Salvar apenas uma sessão específica
      */
      // console.log(this.form.valid)
      if(this.form.valid){
        if(this.coletasdiarias[sessaoId].id == undefined){

          this.coletaDiariaService.create(this.coletasdiarias[sessaoId]).subscribe((id) => {
            //this.cantChangeData = true;
            this.coletasdiarias[sessaoId].id = id;
            this.coletasdiarias = [...this.coletasdiarias];
            this.enableCamposEstimulo(sessaoId);
            // console.log(this.coletasdiarias)
            // console.log(id)
            //this.planointervencaoService.showMessage('Plano de intervenção criado com sucesso!');
            //this.router.navigate(['/paciente/' + id]);
          });
        } else {
          this.coletaDiariaService.update(this.coletasdiarias[sessaoId]).subscribe((coleta) => {
            //this.planointervencaoService.showMessage('Plano de intervenção alterado com sucesso!');
            //this.router.navigate(['/paciente/' + paciente.id]);
          });
        }
      } else {
        this.coletaDiariaService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    //}
  }

  savePIC(sessaoId: number): void {
    const coleta = this.coletasdiariasPIC[sessaoId];
    if(this.form.valid){
      if(this.coletasdiariasPIC[sessaoId].id == undefined){
        this.coletaDiariaPICService.create(coleta).subscribe(id => {
          this.coletasdiariasPIC[sessaoId].id = id;
          this.coletasdiariasPIC = [...this.coletasdiariasPIC];
        });
        // console.log(this.coletasdiariasPIC)
        // console.log('create')
      } else {
        this.coletaDiariaPICService.update(coleta).subscribe(response => {});
        // console.log(this.coletasdiariasPIC)
        // console.log('update')
      }
    } else {
      this.coletaDiariaPICService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel(){

  }

  setProfissional(event:MatSelectChange){
    this.coletasdiarias.forEach(coleta => {
      coleta.profissional = this.profissionais.find(p => p.id == event.value);
      coleta.idProfissional = coleta.profissional.id;
    })
    this.coletasdiariasPIC.forEach(coleta => {
      coleta.idProfissional = this.profissionais.find(p => p.id == event.value).id;
      coleta.nomeProfissional = this.profissionais.find(p => p.id == event.value).nome;
    })
  }

  onHoraInicioChange(value: string, idxSessao: number) {
    // Atualiza a hora de início em coletasdiariasPIC
    this.coletasdiariasPIC[idxSessao].horaInicio = value;
  }

  onHoraTerminoChange(value: string, idxSessao: number) {
    // Atualiza a hora de término em coletasdiariasPIC
    this.coletasdiariasPIC[idxSessao].horaTermino = value;
  }

  test(){
    //console.log(this.coletasdiarias)
    // this.planoIntervencaoService.setStatusEtapaPlano(this.planoIntervencao);
  }

  onColetaDiariaPICChange(updatedData: { coleta: ColetaDiariaPIC, sessaoId: string }) {
    const updatedColetaDiariaPIC = updatedData.coleta;
    const sessaoId = updatedData.sessaoId;
  
    // console.log("Received updatedColetaDiariaPIC:", updatedColetaDiariaPIC);
  
    // Encontra a sessão correspondente no array coletadiariasPIC usando o sessaoId
    const sessaoIndex = this.coletasdiariasPIC.findIndex(sessao => sessao.id === sessaoId);

    if (sessaoIndex !== -1) {
      const sessao = this.coletasdiariasPIC[sessaoIndex];
  
      // Verifica se o idObjetivo já existe no array objetivosColeta da sessão encontrada
      const objetivoIndex = sessao.objetivosColeta.findIndex(coleta => coleta.idObjetivo === updatedColetaDiariaPIC.idObjetivo);
  
      if (objetivoIndex !== -1) {
        // Se existir, atualiza o elemento no array
        sessao.objetivosColeta[objetivoIndex] = updatedColetaDiariaPIC;
        this.savePIC(sessaoIndex);
      } else {
        // Se não existir, adiciona o novo elemento ao array
        sessao.objetivosColeta.push(updatedColetaDiariaPIC);
        this.savePIC(sessaoIndex);
      }
  
      // console.log("Updated coletadiariasPIC array:", this.coletasdiariasPIC);
    } else {
      console.error(`Sessão com id ${sessaoId} não encontrada.`);
    }
  }

  saveToPDF() {
    if (this.planoIntervencao.objetivos.length > 0) {
      this.router.navigate([]).then(result => {
        window.open('coletadiaria/pdf/' + this.planoIntervencao.id + "?hasFullView=true",'_blank');
      })
    } else {
      this.router.navigate([]).then(result => {
        window.open('coletadiaria/pdf/' + this.pic.id + "?hasFullView=true",'_blank');
      })
    }
  }

}

// Funcões paara esdm na adequeção do vbmapp
//Disparado ao clicar no ícone
  // setRespostaPeriodoESDM(
  //   sessaoId: number,
  //   objetivo: any,
  //   etapa: Etapa,
  //   tipo: 'positivos' | 'negativos' | 'independentes'
  // ): void {
  //   const totalAnterior = etapa.positivos
  //                       + etapa.negativos
  //                       + etapa.independentes;
  //   const limite = objetivo.oportunidadesEtapa || 4;
    // const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    // const fg = this.controles[sessaoId][key];

  //   console.log(this.controles[sessaoId])
  //   console.log(etapa)
  //   console.log(etapa[tipo])
  //   console.log(limite)

  //   // se já atingiu o limite, bloqueia e mostra aviso
  //   if (totalAnterior >= limite) {
  //     this.snackBar.open(
  //       `Limite de ${limite} respostas atingido.`,
  //       'Fechar', { duration: 3000 }
  //     );
      
  //     // atualiza FormControl sem disparar valueChanges
  //     fg.get(tipo)!.setValue(etapa[tipo], { emitEvent: false });
  //     return;
  //   }

  //   // caso não tenha atingido, incrementa 1 no tipo
  //   const valorAtual = etapa[tipo];
  //   const novo = valorAtual + 1;
  //   etapa[tipo] = novo;

  //   console.log(etapa)
  //   console.log(etapa[tipo])

  //   // atualiza FormControl sem disparar valueChanges
  //   fg.get(tipo)!.setValue((etapa as any)[tipo], { emitEvent: false });

  //   console.log(fg.get(tipo)!.value)

  //   etapa.positivos     = fg.get('positivos')!.value;
  //   etapa.negativos     = fg.get('negativos')!.value;
  //   etapa.independentes = fg.get('independentes')!.value;
    
  //   console.log('🎯 Etapa atual modificada:', etapa);

  //   const etapaOriginal = this.coletasdiarias[sessaoId]
  //     .objetivos.find(o => o.id === objetivo.id)
  //     ?.etapa.find(e => e.id === etapa.id);

  //   console.log('📦 Etapa dentro da coleta:', etapaOriginal);

  //   this.save(sessaoId);
  // }

  // //Tratamento de edição manual no input (blur/evento valueChanges)
  // async onControlTouchedESDM(
  //   sessaoId: number,
  //   objetivo: Objetivo,
  //   etapa: Etapa,
  //   tipo: 'positivos' | 'negativos' | 'independentes'
  // ) {
    // const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    // const fg = this.controles[sessaoId][key];
  //   const novoValor = fg.get(tipo)!.value as number;

  //   // Encontre a instância real de Estimulo dentro de coletasdiarias
  //   const obj = this.coletasdiarias[sessaoId].objetivos.find(o => o.id === objetivo.id)!;
  //   const etapaObj = obj.etapa.find(e => e.id === etapa.id)!;

  //   const totalAnterior = etapaObj.positivos + etapaObj.negativos + etapaObj.independentes;
  //   const totalNovo = 
  //     tipo === 'positivos'   ? totalAnterior - etapaObj.positivos   + novoValor :
  //     tipo === 'negativos'   ? totalAnterior - etapaObj.negativos   + novoValor :
  //     /* independentes */      totalAnterior - etapaObj.independentes + novoValor;

  //   const limite = objetivo.oportunidadesEtapa || 4;

  //   // Se tentou incrementar além do limite, bloqueia apenas esse campo
  //   if (totalNovo > limite && novoValor > (etapaObj as any)[tipo]) {
  //     this.snackBar.open(`Limite de ${limite} respostas atingido.`, 'Fechar', { duration: 3000 });

  //     // reverte o input ao valor antigo, rollback visual e modelo
  //     fg.get(tipo)!.setValue((etapaObj as any)[tipo], { emitEvent: false });
  //     etapaObj[tipo] = (etapaObj as any)[tipo];

  //     fg.markAsTouched();
  //     fg.updateValueAndValidity();

  //     return;
  //   }

  //   // Caso válido (diminuição ou dentro do limite): atualiza o modelo
  //   etapaObj[tipo] = novoValor;

  //   console.log(this.coletasdiarias[sessaoId].objetivos);

  //   // Persiste
  //   await this.save(sessaoId);
  // }

  // private onControlChangeESDM(
  //   sessaoId: number,
  //   objetivo: any,
  //   etapa: Etapa,
  //   oldVals: { positivos: number; negativos: number; independentes: number },
  //   newVals: { positivos: number; negativos: number; independentes: number }
  // ) {
    // const key = `${objetivo.id}_${ts.id}_${estimulo.id}`;
    // const fg = this.controles[sessaoId][key];

  //   // 1. Atualiza os valores do estimulo com o que está no FormControl (sincroniza)
  //   etapa.positivos = fg.get('positivos')!.value;
  //   etapa.negativos = fg.get('negativos')!.value;
  //   etapa.independentes = fg.get('independentes')!.value;

  //   const limite = objetivo.oportunidadesEtapa || 4;
  //   const totalAnterior = oldVals.positivos + oldVals.negativos + oldVals.independentes;
  //   const totalNovo     = newVals.positivos + newVals.negativos + newVals.independentes;

  //   const tipoAlterado = ['positivos','negativos','independentes']
  //     .find(k => newVals[k] > oldVals[k]) as 'positivos'|'negativos'|'independentes';

  //   // 2. Impede incremento além do limite
  //   if (totalNovo > limite && totalNovo > totalAnterior) {
  //     this.snackBar.open(`Limite de ${limite} respostas atingido.`, 'Fechar', { duration: 3000 });

  //     // reverte o input ao valor antigo, rollback visual e modelo
  //     fg.get(tipoAlterado)!.setValue(oldVals[tipoAlterado], { emitEvent: false });
  //     etapa[tipoAlterado] = oldVals[tipoAlterado];

  //     // força revalidação no formulário
  //     fg.markAsTouched();
  //     fg.updateValueAndValidity();

  //     return;
  //   }

  //   // 3. Atualiza os valores no estimulo (modelo)
  //   etapa[tipoAlterado] = newVals[tipoAlterado];

  //   // 4. Persiste normalmente
  //   this.save(sessaoId);
  // }
