import { DominioAvaliacao } from './dominio-avaliacao-model';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { EMPTY, Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DominioAvaliacaoService {

  dominioUrl = `${environment.API_URL}/dominio_avaliacao`;

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(dominio: DominioAvaliacao): Observable<DominioAvaliacao>{
    return this.http.post<DominioAvaliacao>(this.dominioUrl, dominio).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(dominio: DominioAvaliacao): Observable<DominioAvaliacao>{
    return this.http.put<DominioAvaliacao>(this.dominioUrl + "/" + dominio.id, dominio).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<DominioAvaliacao>{
    return this.http.get<DominioAvaliacao>(this.dominioUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<DominioAvaliacao[]>{
    return this.http.get<DominioAvaliacao[]>(this.dominioUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  
  }

  findByTipoAvaliacao(idTipoAvaliacao: string): Observable<DominioAvaliacao[]>{
    return this.http.get<DominioAvaliacao[]>(this.dominioUrl + '/tipo_avaliacao/' + idTipoAvaliacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<DominioAvaliacao>{
    return this.http.delete<DominioAvaliacao>(this.dominioUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
