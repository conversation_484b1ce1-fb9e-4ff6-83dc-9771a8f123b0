import { Component, Input, OnInit } from '@angular/core';
import { Anamnese, Valor } from 'src/app/components/anamnese/anamnese-model';
import { Paciente } from 'src/app/components/paciente/paciente-model';
import { AuthService } from 'src/app/components/template/auth/auth.service';
import { FirebaseUserModel } from 'src/app/components/template/auth/user-model';
import { UserService } from 'src/app/components/template/auth/user.service';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-anamnese-relatorio',
  templateUrl: './anamnese-relatorio.component.html',
  styleUrls: ['./anamnese-relatorio.component.css']
})
export class AnamneseRelatorioComponent implements OnInit {

  @Input()
  paciente: Paciente = new Paciente();

  @Input()
  anamnese: Anamnese = new Anamnese();

  idOrganizacao: string
  

  constructor(
    private authService: AuthService,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.loadingService.show();
    this.idOrganizacao = (this.authService.getUser() as FirebaseUserModel).organizationId;
    this.loadingService.hide();
  }

  retornaDescricaoPegunta(valores:Valor[], valor:string){
    return valores.find(p => p.value == valor).descricao != 'undefined' ? valores.find(p => p.value == valor).descricao : null;
  }

  getSexo(valor:string){
    return valor == 'F' ? 'Feminino' : 'Masculino';
  }

}
