import { map, catchError } from 'rxjs/operators';
import { Organizacao } from './organizacao-model';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class OrganizacaoService {

  organizacaoUrl = `${environment.API_URL}/organizacao`;
  
  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(organizacao: Organizacao): Observable<Organizacao>{
    return this.http.post<Organizacao>(this.organizacaoUrl, organizacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(organizacao: Organizacao): Observable<Organizacao>{
    return this.http.put<Organizacao>(this.organizacaoUrl + "/" + organizacao.id, organizacao).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Organizacao>{
    return this.http.get<Organizacao>(this.organizacaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Organizacao[]>{
    return this.http.get<Organizacao[]>(this.organizacaoUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Organizacao>{
    return this.http.delete<Organizacao>(this.organizacaoUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
