<div class="row text-center">
    <div class="col-md-4">
      <div class="btn-group">
        <div
          class="btn btn-primary"
          mwlCalendarPreviousView
          [view]="view"
          [(viewDate)]="viewDate"
          (viewDateChange)="viewDateChange.next(viewDate)"
        >
          Anterior
        </div>
        <div
          class="btn btn-outline-secondary"
          mwlCalendarToday
          [(viewDate)]="viewDate"
          (viewDateChange)="viewDateChange.next(viewDate)"
        >
          Hoje
        </div>
        <div
          class="btn btn-primary"
          mwlCalendarNextView
          [view]="view"
          [(viewDate)]="viewDate"
          (viewDateChange)="viewDateChange.next(viewDate)"
        >
          Próximo
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <h3>{{ viewDate | calendarDate: view + 'ViewTitle':locale | titlecase}}</h3>
    </div>
    <div class="col-md-4">
      <div class="btn-group">
        <div
          class="btn btn-primary"
          (click)="viewChange.emit(CalendarView.Month)"
          [class.active]="view === CalendarView.Month"
        >
          Mês
        </div>
        <div
          class="btn btn-primary"
          (click)="viewChange.emit(CalendarView.Week)"
          [class.active]="view === CalendarView.Week"
        >
          Semana
        </div>
        <div
          class="btn btn-primary"
          (click)="viewChange.emit(CalendarView.Day)"
          [class.active]="view === CalendarView.Day"
        >
          Dia
        </div>
      </div>
    </div>
  </div>
  <br />