import { Profissional } from './../../profissional/profissional-model';
import { AuthService } from './../../template/auth/auth.service';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Anamnese } from '../../anamnese/anamnese-model';
import { AnamneseService } from '../../anamnese/anamnese.service';
import { Paciente } from '../paciente-model';
import { PacienteService } from '../paciente.service';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-paciente-view-anamnese',
  templateUrl: './paciente-view-anamnese.component.html',
  styleUrls: ['./paciente-view-anamnese.component.css']
})

export class PacienteViewAnamneseComponent implements OnInit {
  idPaciente: string;
  idAnamnese: string;
  indexAnamnese: number;
  viewRelatorio: boolean = false;

  paciente: Paciente = new Paciente();
  anamnese: Anamnese = new Anamnese();

  idade: number;
  edicao: boolean = false;
  hasAccessUpdate: boolean;

  @ViewChild('content', {static: false}) content: ElementRef;

  constructor(private route: ActivatedRoute,
    private router: Router,
    private anamneseService: AnamneseService,
    public authService: AuthService,
    private pacienteService: PacienteService,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.loadingService.show();
    this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    this.idAnamnese = this.route.snapshot.paramMap.get('idAnamnese');

    this.anamneseService.findById(this.idAnamnese).subscribe(data => {
      this.anamnese = data;
      //console.log("===== Anamnese =====",this.anamnese)

      this.pacienteService.findById(this.idPaciente).subscribe(data => {
        this.paciente = data;
        this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(this.paciente), 'Paciente.Cadastro de pacientes','update');
        //console.log("===== Paciente =====",this.paciente)
        //retorna a idade em meses
        this.idade = this.pacienteService.getIdadeMeses(this.paciente.dataNascimento);
        
        if(this.checkRespostaAnamnese(this.idAnamnese)){
          this.paciente.anamnese.forEach((anamnese,index) => {
             if(anamnese.id == this.idAnamnese){
              this.indexAnamnese = index;
             }
          })
          this.anamnese = this.paciente.anamnese[this.indexAnamnese]; 
          this.edicao = true;
        }
      })

      this.loadingService.hide();
    })
  }

  save(){
    if(this.paciente.anamnese && this.paciente.anamnese.length > 0){
      if(this.checkRespostaAnamnese(this.idAnamnese)){
        this.paciente.anamnese[this.indexAnamnese] = this.anamnese;
      }else{
        this.paciente.anamnese.push(this.anamnese);
      }
    }else{
      this.paciente.anamnese = [];
      this.paciente.anamnese.push(this.anamnese);
    }
    this.pacienteService.update(this.paciente).subscribe((paciente) => {
      this.pacienteService.showMessage('Anamnese salva com sucesso!');
      this.router.navigate(['/paciente/' + paciente.id]);
    });    
  }

  back(){
    this.router.navigate(['/paciente/'+this.idPaciente, {
      tab:"anamnese"
    }]);
  }

  cancel(){
    this.edicao = true;
  }

  checkRespostaAnamnese(idAnamnese: string){
    if(!this.paciente.anamnese){
      return false;
    }
    return this.paciente.anamnese.filter(anamnese => anamnese.id == idAnamnese).length > 0 ? true : false;
  }

  editar(){
    this.edicao = false;
  }

  atualizarRefazer(){
    this.edicao = false;
     this.anamneseService.findById(this.idAnamnese).subscribe(data => {
      this.anamnese = data;
    })
  }

  editarAtualizar(){
    this.edicao = false;
    var anamneseAtual: Anamnese;
    this.anamneseService.findById(this.idAnamnese).subscribe(async data => {
      anamneseAtual = data;
      //Verifica add de novo grupo
      if(this.anamnese.grupos.length < anamneseAtual.grupos.length){
        await anamneseAtual.grupos.forEach((grupo, indexGrupo) => {
          if(indexGrupo > (this.anamnese.grupos.length-1)){
            this.anamnese.grupos.push(grupo);
          }
          
        })

      }      

      //Verifica add de nova pergunta
      await anamneseAtual.grupos.forEach((grupo, indexGrupo) => {
        if(grupo.perguntas.length > this.anamnese.grupos[indexGrupo].perguntas.length){
          grupo.perguntas.forEach((pergunta,indexPergunta) => {
              if(indexPergunta > (this.anamnese.grupos[indexGrupo].perguntas.length-1)){
                this.anamnese.grupos[indexGrupo].perguntas.push(pergunta);
              }
          })
        }
      })
    });
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];
    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }
  
  exibeTemplate(){
    this.viewRelatorio = true;
  }

   gerarPDF(){
    this.loadingService.show();
    this.exibeTemplate();
    setTimeout(() => { 
      //console.log(document.getElementById("view-relatorio"));
      html2canvas(document.getElementById("view-relatorio"), {
        // Opciones
        allowTaint: true,
        useCORS: false,
        // Calidad del PDF
        scale: 1
      }).then(function(canvas) {
        var img = canvas.toDataURL("application/pdf");
        var doc = new jsPDF('p','mm','a4');
        let imgWidth = 208;
        let imgHeigth = (canvas.height * imgWidth / canvas.width);
        var position = 10;
        doc.addImage(img,'JPEG',0, position, imgWidth, imgHeigth);
        doc.save('anamnese.pdf');
       
      }).finally(() => {
        this.viewRelatorio = false;
      });
      this.loadingService.hide();
     }, 5000);
  }
}
