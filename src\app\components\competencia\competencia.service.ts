import { environment } from './../../../environments/environment';
import { Dominio } from './../dominio/dominio-model';
import { Competencia } from './competencia-model';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CompetenciaService {

  competencialUrl = `${environment.API_URL}/competencia`;
  
  //public competencias: BehaviorSubject<Competencia[]> = 
  //  new BehaviorSubject<Competencia[]>([]);
  
  public $competencias: Observable<Competencia[]>;
  public $domsNivel: Map<string, Observable<Dominio[]>> = new Map<string, Observable<Dominio[]>>();

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  /*
  create(competencia: Competencia): Observable<Competencia>{
    console.log(competencia);
    return this.http.post<Competencia>(this.competencialUrl, competencia).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  /*
  findById(id: string): Observable<Competencia>{
    return this.http.get<Competencia>(this.competencialUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  findDominiosByNivel(id_nivel: string): Observable<Dominio[]>{
    /*
    if(this.$domsNivel.get(id_nivel) == null){
      console.log('Carreguei os domínios do nível ' + id_nivel);
      this.$domsNivel.set(id_nivel, this.http.get<Dominio[]>(this.competencialUrl + '/' + id_nivel + '/dominio').pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      ));
    }

    return this.$domsNivel.get(id_nivel);
    */
    
    return this.http.get<Dominio[]>(this.competencialUrl + '/' + id_nivel + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
    
  }

  /*
  findByNivelDominio(id_nivel: string, id_dominio: string): Observable<Competencia>{
    return this.http.get<Competencia>(this.competencialUrl + '/' + id_nivel + 'nivel/'
        + id_dominio + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  find(): Observable<Competencia[]>{
    /*
    if(this.$competencias == null){
      //Carrego as competências pela primeira vez
      console.log('Carreguei as competências...');
      this.$competencias = this.http.get<Competencia[]>(this.competencialUrl).pipe(
          map(obj => obj),
          catchError(e => this.errorHandler(e) )
        );
    }
    return this.$competencias;
    */

    return this.http.get<Competencia[]>(this.competencialUrl);
  }

  /*
  delete(id: string): Observable<Competencia>{
    return this.http.delete<Competencia>(this.competencialUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */
}