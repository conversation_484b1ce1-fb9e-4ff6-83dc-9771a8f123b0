import { Permissao } from './../../funcao/permissao-model';
import { LoadingService } from './../../../shared/service/loading.service';
import { Profissional } from './../../profissional/profissional-model';
import { AuthService } from './../../template/auth/auth.service';
import { DeleteConfirmDialogComponent } from '../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { PacienteService } from '../paciente.service';
import { Paciente } from '../paciente-model';
import { Component, OnInit } from '@angular/core';
import { PlanoIntervencaoService } from '../../planointervencao/planointervencao.service';
import { PlanointervencaovbmappService } from '../../planointervencaovbmapp/planointervencaovbmapp.service';
import { PICService } from '../../planopic/pic.service';

@Component({
  selector: 'app-paciente-list',
  templateUrl: './paciente-list.component.html',
  styleUrls: ['./paciente-list.component.css']
})
export class PacienteListComponent implements OnInit {

  public pacientes: Paciente[];

  displayedColumns = ['nome', 'kpis', 'dataNascimento', 'action']
  //displayedColumns = ['nome', 'kpis', 'action']


  constructor(
    private pacienteService: PacienteService,
    private PEIService: PlanointervencaovbmappService,
    private PEIESDMService: PlanoIntervencaoService,
    private PICService: PICService,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    public loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      const pacientes = await this.pacienteService.getResumoPacientes().toPromise();

      pacientes.sort((a, b) => a.nome.localeCompare(b.nome, 'pt-BR', { sensitivity: 'base' }));
  
      // Atualizar o BehaviorSubject com os pacientes ativos
      this.pacienteService.pacientes.next(pacientes.filter(p => p.ativo === true));
  
      // Subscrevendo ao BehaviorSubject para obter a lista de pacientes atualizada
      this.pacienteService.pacientes.subscribe((data) => {
        this.pacientes = data;
      });
  
    } catch (error) {
      console.error(error);
    } finally {
      this.loadingService.hide();
    }
  }    

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];
    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      // console.log(this.authService.getUser())
      // console.log("Usuário logado: " + this.authService.getUser().email)
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
      // console.log(profissional)
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          //console.log(funcao)
          funcoes.push(funcao.nome);
        })
      }
    }

    return funcoes;
  }

  async addColeta(id: string){
    let PEIs

    PEIs = await this.PEIService.findByPaciente(id).toPromise();
    PEIs = PEIs.filter(p => p.status !== false);

    if (PEIs.length == 0){
      PEIs = await this.PEIESDMService.findByPaciente(id).toPromise();
      PEIs = PEIs.filter(p => p.status !== false);
    }
    let PEI = PEIs[0];

    let pic
    pic = await this.PICService.findByPaciente(id).toPromise();
    pic.filter(p => p.status !== false);
    pic = pic[0];

    if(PEI?.objetivos.length > 0){
      this.router.navigate(['/coletadiaria_vbmapp/' + PEI.id])
    } else if (pic?.objetivos.lenght > 0) {
      this.router.navigate(['/coletadiaria_vbmapp/' + pic.id])
    } else {
      this.PEIService.showMessage('Paciente ainda não possui PEI ou PIC!', true);
    }
    
  }

  navigateToRead(id: string){
    this.router.navigate(['/paciente/' + id]);
  }

  delete(id: string): void{
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });
    //TODO: Receber o paciente inteiro ao invés apenas do ID, eliminando uma consulta ao banco (findById)
    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.pacienteService.findById(id).subscribe(paciente => {
          this.loadingService.show();
          paciente.ativo = false;
          this.pacienteService.update(paciente).subscribe((p) => {
            this.pacienteService.showMessage('Paciente inativado com sucesso!');
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            //this.router.navigate(['/paciente/update/'+this.paciente.id]);
            this.router.navigate(['/paciente']);
            this.loadingService.hide();
          });
        })
        /*this.pacienteService.delete(id).subscribe(
          () => {
            this.pacienteService.showMessage('Paciente excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/paciente']);
          }
        );*/
      }
    });
  }

}
