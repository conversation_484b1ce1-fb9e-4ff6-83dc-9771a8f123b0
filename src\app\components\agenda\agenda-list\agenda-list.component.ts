import { Component, OnInit } from '@angular/core';
import { CalendarEvent, CalendarEventTimesChangedEvent, CalendarView } from 'angular-calendar';
import { isSameDay, isSameMonth } from 'date-fns';
import { Atendimento } from '../../atendimento/atendimento-model';
import { Router } from '@angular/router';
import { CalendarEventImp } from '../../atendimento/calendarEventImp-model';
import { AtendimentoService } from '../../atendimento/atendimento.service';
import { TipoprocedimentoService } from '../../tipoprocedimento/tipoprocedimento.service';
import { UserService } from '../../template/auth/user.service';
import { Profissional } from '../../profissional/profissional-model';
import { ProfissionalService } from '../../profissional/profissional.service';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-agenda-list',
  templateUrl: './agenda-list.component.html',
  styleUrls: ['./agenda-list.component.css']
})
export class AgendaListComponent implements OnInit {
  
  view: CalendarView = CalendarView.Month;
  CalendarView = CalendarView;
  groupedSimilarEvents: {cor:string, qtd:number}[] = [];
  similarEvent: {
    cor:string ,
    qtd:number
  } = {cor: '', qtd: 0}

  viewDate: Date = new Date();
  activeDayIsOpen: boolean = false;

  selected: Date | null;

  events: CalendarEvent<Atendimento>[] = [];
  
  evento: CalendarEvent<Atendimento> = {start: new Date(), title: ''};
  eventoAtendimento: CalendarEventImp[] = [];

  dayStartHour:number = 8

  dayEndHour:number = 20;

  profissional: Profissional
  idPacienteSelect: string;

  constructor(
    private router: Router,
    private atendimentoService: AtendimentoService,
    private procedimentoService: TipoprocedimentoService,
    private userService: UserService,
    private profissionalService: ProfissionalService,
    private loadingService: LoadingService
  ) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    await this.userService.getCurrentUser()
    .then(user => {      
      //console.log(user.email)
      this.profissionalService.find().subscribe(profissionais => {
        this.profissional = profissionais.find(p => p.email == user.email);
        this.filtrarCalendario('PROFISSIONAL');
      })
    });    

   /* await this.atendimentoService.find().subscribe(eventosAtendimentos => {
      let listaCalendar = [];
        eventosAtendimentos.forEach(evento => {
            let evt = this.mapearEventoAtentoToEventCalendar(evento)
            listaCalendar.push(evt);
         });
      
          this.events = listaCalendar;

    });   */
  }



  mapearEventoAtentoToEventCalendar(eventoAtendimento: CalendarEventImp){
    let evento:CalendarEvent = {start: new Date(), title: ''};

      evento.id = eventoAtendimento.id;
      evento.title = eventoAtendimento.title;
      evento.start = new Date(Date.parse(eventoAtendimento.start.toString()));
      evento.end = new Date(Date.parse(eventoAtendimento.end.toString()));
      evento.color = eventoAtendimento.color;
      evento.meta = eventoAtendimento.meta;
      evento.resizable = eventoAtendimento.resizable;
      evento.draggable = eventoAtendimento.draggable;
      
      //Verifica se existe o procedimento,  pelo ID, se não existir retorna a versão inicial
      let procedimento = null;
      this.procedimentoService.findById(evento.meta.idProcedimento).subscribe(proc => {
        procedimento  = proc;
        evento.meta.procedimento = procedimento ? procedimento : eventoAtendimento.meta.procedimento;
        // Atualiza titulo com novo nome do exame
        if(procedimento){
          evento.title = eventoAtendimento.title.split('-')[0] + ' - ' + eventoAtendimento.meta.procedimento.nome
        }
      })

      return evento;
      
  }




  dayClicked({ date, events }: { date: Date; events: CalendarEvent[] }): void {
    if (isSameMonth(date, this.viewDate)) {
      if (
        (isSameDay(this.viewDate, date) && this.activeDayIsOpen === true) ||
        events.length === 0
      ) {
        this.activeDayIsOpen = false;
      } else {
        this.activeDayIsOpen = true;
      }
      this.viewDate = date;
    }
  }

  eventTimesChanged({
    event,
    newStart,
    newEnd,
  }: CalendarEventTimesChangedEvent): void {
    this.events = this.events.map((iEvent) => {
      if (iEvent === event) {
        return {
          ...event,
          start: newStart,
          end: newEnd,
        };
      }
      return iEvent;
    });

  }

  addEvent(evento): void {
    // console.log(evento);
    this.events = [
      ...this.events,
      evento
    ];
  }

  deleteEvent(eventToDelete: CalendarEvent) {
    this.events = this.events.filter((event) => event !== eventToDelete);
  }

  setView(view: CalendarView) {
    this.view = view;
  }

  closeOpenMonthViewDay() {
    this.activeDayIsOpen = false;
  }

  handleEvent(action: string, event: CalendarEvent): void {
    this.router.navigate(['/atendimento', {data: event.id}]);
  }
  

  async filtrarCalendario(tipoFiltro:String){
    this.loadingService.show();
    this.atendimentoService.find().subscribe(eventosAtendimentos => {
      let listaCalendar = [];
        eventosAtendimentos.forEach(evento => {
            let evt = this.mapearEventoAtentoToEventCalendar(evento)

            switch(tipoFiltro){
              case 'PROFISSIONAL' :
                if(evento.meta.profissionais.filter(p => p.id == this.profissional.id).length > 0){
                  listaCalendar.push(evt);
                }
                break;
              
              case 'CLINICA' :
                listaCalendar.push(evt);
                break;

              case 'PACIENTE' :
                if(evento.meta.idPaciente == this.idPacienteSelect){
                  listaCalendar.push(evt);
                }
                break;
              
            }
         });
      
          this.events = listaCalendar;
          this.loadingService.hide();
    },() => {
      this.loadingService.hide();
      
    });   
  }
}
