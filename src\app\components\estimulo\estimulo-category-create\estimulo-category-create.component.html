<mat-card> 
    <mat-card-title class="title">{{ grupo.id == undefined || grupo.nome == undefined ? "Nova Categoria de Estímulo" :
        grupo.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <mat-form-field  style="width: 30%; padding: 10px;">
                <input type="text"
                            placeholder="Categoria" 
                            matInput
                            [(ngModel)]="grupo.nome"
                            name="categoria"
                            [formControl]="categoriaFC" required>
                <mat-error *ngIf="categoriaFC.invalid">Categoria é obrigatória.</mat-error>  
            </mat-form-field>
        </div>
    </form>
    
    <button mat-raised-button (click)="save(false)" color="primary">
        Salvar e Incluir Nova
    </button>

    <button mat-raised-button (click)="save(true)" color="primary">
        <PERSON><PERSON>
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>
