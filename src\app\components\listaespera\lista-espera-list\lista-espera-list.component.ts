import { BehaviorSubject } from 'rxjs';
import { DeleteConfirmDialogComponent } from '../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { Router } from '@angular/router';
import { ListaesperaService } from '../listaespera.service';
import { ListaEspera } from '../listaespera-model';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-lista-espera-list',
  templateUrl: './lista-espera-list.component.html',
  styleUrls: ['./lista-espera-list.component.css']
})
export class ListaEsperaListComponent implements OnInit {

  public listas: ListaEspera[];

  /*public listas: BehaviorSubject<ListaEspera[]> = 
    new BehaviorSubject<ListaEspera[]>([]);*/

  /*get listas(): ListaEspera[] {
    let candidatos: ListaEspera[];
    this._listas.forEach(candidato => {
      let obj: any;
      obj = candidato;
      candidatos.push(obj);
    })
    return candidatos;
  }

  set listas(listas: ListaEspera[]){
    this._listas.next(listas)
  }*/ 

  displayedColumns = ['nome', 'dataNascimento', 'local', 'nomeResponsavel', 'terapeuta', 'status', 'action']

  constructor(private listaEsperaService: ListaesperaService,
    public dialog: MatDialog,
    private router: Router) { }

  ngOnInit(): void {

    this.listaEsperaService.listas.subscribe((data) => {
      this.listas = data;
    });

    this.listaEsperaService.find().subscribe(listas => {
      this.listaEsperaService.listas.next(listas);
      //console.log(this.listas);
    })
  }

  navigateToUpdate(id: string){
    this.router.navigate(['/listaespera/update/'+id]);
  }

  delete(id: string): void{
    //this.router.navigate(['/funcao/new']);
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.listaEsperaService.delete(id).subscribe(
          () => {
            this.listaEsperaService.showMessage('Candidato excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/listaespera']);
          }
        );
      }
      //console.log('The dialog was closed');
      //this.animal = result;
    });
  }

}
