form{
    display: flex;
    flex-flow: row wrap;
}

button{
    margin-right: 15px;
    margin-top: 20px;
}

.subtitle{
    font-weight: 500;
    font-size: large;
}

.mat-column-nome {
    flex: 0 0 40% !important;
    width: 40% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-create {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: center;
    overflow-wrap: break-word;
    word-wrap: break-word;
}
.mat-column-update {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: center;
    overflow-wrap: break-word;
    word-wrap: break-word;
}
.mat-column-delete {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: center;
    overflow-wrap: break-word;
    word-wrap: break-word;
}
.mat-column-read {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: center;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

table{
    width: 100%;
    border-collapse: collapse;
}

td {
    border: 1px solid lightgray;
}

tr:nth-child(even){
    background-color: #f2f2f2;
}

.header{
    background-color: #3f51b5;
    font-weight: bold;
    color: white;
}

.rowNome{
    text-align: left;
}
.rowCRUD{
    text-align: center;
}

a{
    cursor: pointer;
    text-decoration: none;
}
