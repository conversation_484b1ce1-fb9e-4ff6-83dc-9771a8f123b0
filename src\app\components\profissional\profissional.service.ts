import { Profissional, ProfissionalSimple } from './profissional-model';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ProfissionalService {

  profissionalUrl = `${environment.API_URL}/profissional`;

  public profissionais: BehaviorSubject<Profissional[]> = 
    new BehaviorSubject<Profissional[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(profissional: Profissional): Observable<Profissional>{
    //console.log(profissional);
    return this.http.post<Profissional>(this.profissionalUrl, profissional).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(profissional: Profissional): Observable<Profissional>{
    //console.log(profissional);
    return this.http.put<Profissional>(this.profissionalUrl + '/' + profissional.id, profissional).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Profissional>{
    return this.http.get<Profissional>(this.profissionalUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByEmail(email: string): Observable<Profissional>{
    return this.http.get<Profissional>(this.profissionalUrl + '/email/' + email.toLowerCase()).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByUserId(uid: string): Observable<Profissional[]>{
    return this.http.get<Profissional[]>(this.profissionalUrl + '/uid/' + uid).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Profissional[]>{
    return this.http.get<Profissional[]>(this.profissionalUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  getResumoProfissional(): Observable<any[]> {
    return this.http.get<any[]>(`${this.profissionalUrl}/resumo`).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e))
    );
  }
  
  verificarEmailGlobal(email: string) {
    return this.http.get<any[]>(`${this.profissionalUrl}/ativos/email`, {
      params: { email }
    });
  }

  delete(id: string): Observable<Profissional>{
    return this.http.delete<Profissional>(this.profissionalUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

}
