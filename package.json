{"name": "CapaciTEAutismo", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~10.0.9", "@angular/cdk": "^10.1.3", "@angular/common": "~10.0.9", "@angular/compiler": "~10.0.9", "@angular/core": "~10.0.9", "@angular/fire": "^6.0.2", "@angular/forms": "~10.0.9", "@angular/localize": "~10.0.9", "@angular/material": "^10.1.3", "@angular/platform-browser": "~10.0.9", "@angular/platform-browser-dynamic": "~10.0.9", "@angular/router": "~10.0.9", "@ng-bootstrap/ng-bootstrap": "^7.0.0", "@types/node-schedule": "^1.3.0", "angular-calendar": "^0.28.22", "angular2-text-mask": "^9.0.0", "bootstrap": "^4.5.0", "bootstrap-css-only": "^4.4.1", "bootstrap-icons": "^1.1.0", "bootstrap-sass": "^3.4.1", "chart.js": "^2.9.3", "chartjs-gauge": "^0.2.0", "chartjs-plugin-datalabels": "^0.7.0", "date-fns": "^2.16.1", "firebase": "^7.20.0", "firebase-admin": "^9.1.1", "html2canvas": "^1.4.1", "json2typescript": "^1.4.1", "jspdf": "^2.1.1", "moment": "^2.29.1", "ng2-charts": "^2.4.0", "ngx-loading": "^8.0.0", "ngx-material-timepicker": "^5.5.3", "node-schedule": "^1.3.2", "rrule": "^2.6.6", "rxjs": "~6.5.5", "text-mask-addons": "^3.8.0", "tslib": "^2.0.0", "uuid": "^10.0.0", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/architect": ">= 0.900 < 0.1100", "@angular-devkit/build-angular": "~0.1000.6", "@angular/cli": "~10.0.6", "@angular/compiler-cli": "~10.0.9", "@types/jspdf": "^1.3.3", "@types/node": "^12.11.1", "@types/uuid": "^10.0.0", "firebase-tools": "^8.0.0", "fuzzy": "^0.1.3", "inquirer": "^6.2.2", "inquirer-autocomplete-prompt": "^1.0.1", "open": "^7.0.3", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.9.5"}}