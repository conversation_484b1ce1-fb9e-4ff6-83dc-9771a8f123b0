import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ColetaDiariaPIC, SessaoColetaDiariaPIC } from 'src/app/components/coletadiariavbmapp/coletadiariapic-model';

@Component({
  selector: 'app-count',
  templateUrl: './count.component.html',
  styleUrls: ['./count.component.css']
})
export class CountComponent {
  @Input() objetivoPIC: any;
  @Input() idxSessao: any;
  @Input() coletasdiariasPIC: SessaoColetaDiariaPIC[] = [];
  @Output() coletaDiariaPICChange = new EventEmitter<{ coleta: ColetaDiariaPIC, sessaoId: string }>();
  @Output() counter: number;

  coletaDiariaPIC: ColetaDiariaPIC = new ColetaDiariaPIC();
  public confirmed = false;

  ngOnInit(): void {
    // Itera sobre as sessões para encontrar a coleta associada ao objetivo atual
    this.coletasdiariasPIC[this.idxSessao]?.objetivosColeta?.filter(objetivo => objetivo.idObjetivo === this.objetivoPIC.id)
      .forEach(objetivo => {
        this.coletaDiariaPIC = objetivo;
      }
    );
  }

  // Método para ativar a modificação do contador com base na checkbox
  toggleConfirmation(event: any) {
    this.confirmed = event.target.checked;
    
    if (this.coletaDiariaPIC.qtdObservada == null) {
      this.coletaDiariaPIC.qtdObservada = 0;
      this.emitColetaDiariaPIC();
    }
  }

  increaseCounter() {
    if (!this.confirmed) {
      this.confirmed = true;
      this.coletaDiariaPIC.qtdObservada = 0;
    } else {
      this.coletaDiariaPIC.qtdObservada++;
      this.emitColetaDiariaPIC();
    }
  }

  decreaseCounter() {
    if (this.confirmed && this.coletaDiariaPIC.qtdObservada > 0) {
      this.coletaDiariaPIC.qtdObservada--;
      this.emitColetaDiariaPIC();
    }
  }

  emitColetaDiariaPIC() {
    // Encontra a sessão associada ao objetivo atual para emitir a coleta com o id da sessão
    const sessao = this.coletasdiariasPIC.find(sessao => sessao.objetivosColeta.some(obj => obj.idObjetivo === this.objetivoPIC.id));
    if (!this.coletaDiariaPIC.idObjetivo) {
      this.coletaDiariaPIC.idObjetivo = this.objetivoPIC.id;
    }
    
    if (sessao) {
      this.coletaDiariaPICChange.emit({ coleta: this.coletaDiariaPIC, sessaoId: sessao.id });
    }
  }

  respDescription(): string {
    let text = `<b>Def. Operacional:</b> ${this.objetivoPIC.definicaoOperacional}`;  
    return text;
  }
  
}
