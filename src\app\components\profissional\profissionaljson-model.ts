import { <PERSON>caoJSO<PERSON> } from './../funcao/funcaojson-model';
import { DateConverter } from './../../shared/dateconverter';
import { JsonObject, JsonProperty } from 'json2typescript';

@JsonObject("ProfissionalJSON")
export class ProfissionalJSON{
    @JsonProperty("id", String, true)
    id?: string = "";

    @JsonProperty("uid", String, false)
    uid: string = "";

    @JsonProperty("nome", String, false)
    nome: string = "";

    @JsonProperty("email", String, false)
    email: string = "";

    @JsonProperty("telefone", String, false)
    telefone: string = "";

    @JsonProperty("dataNascimento", DateConverter, false)
    dataNascimento: Date = undefined;

    @JsonProperty("sexo", String, false)
    sexo: string = "";

    @JsonProperty("funcao", [FuncaoJSON], false)
    funcao: FuncaoJSON[] = [];

    @JsonProperty("ativo", Boolean, false)
    ativo: boolean = true;

    @JsonProperty("loginUser", Boolean, false)
    loginUser: boolean = false;

}