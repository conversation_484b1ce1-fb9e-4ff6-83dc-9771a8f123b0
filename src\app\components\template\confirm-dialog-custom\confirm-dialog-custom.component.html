<div fxLayout="row" fxLayoutWrap="wrap">
  <!-- Container do card -->
  <div fxFlex="100">
    <mat-card class="mat-elevation-z0">
      <mat-card-content>
        <!-- <PERSON><PERSON><PERSON><PERSON> do card -->
        <mat-card-title *ngIf="data.title">{{ data.title }}</mat-card-title>

        <div mat-card-content
             fxLayout="row wrap"
             fxLayoutAlign="start none"
             fxLayoutGap="10px">
          <span fxFlex="100" class="area_interna" [innerHTML]="data.message">
          </span>
        </div>
      </mat-card-content>

      <mat-card-actions fxLayout="row wrap" fxLayoutGap="10px" align="end">
        <ng-container *ngFor="let opt of data.options">
          <button
            mat-raised-button
            style="margin: 10px;"
            color="primary"
            *ngIf="opt === data.options[0]"        
            [mat-dialog-close]="opt"
            cdkFocusInitial>
            {{ opt }}
          </button>
          <button
            mat-raised-button
            style="margin: 10px;"
            *ngIf="opt !== data.options[0]"        
            (click)="onSelect(opt)">
            {{ opt }}
          </button>
        </ng-container>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
