import { TerapeutaService } from './../../terapeuta/terapeuta.service';
import { Terapeuta } from './../../terapeuta/terapeuta-model';
import { ListaesperaService } from './../listaespera.service';
import { ListaEspera } from './../listaespera-model';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {DateAdapter, MAT_DATE_FORMATS} from '@angular/material/core';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';

@Component({
  selector: 'app-lista-espera-edit',
  templateUrl: './lista-espera-edit.component.html',
  styleUrls: ['./lista-espera-edit.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ListaEsperaEditComponent implements OnInit {

  listaEspera: ListaEspera;
  terapeutas: Terapeuta[];

  constructor(private route: ActivatedRoute,
    private listaEsperaService: ListaesperaService,
    private terapeutaService: TerapeutaService,
    private router: Router) { }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    //console.log("Id:" + id);
    
    this.listaEsperaService.findById(id).subscribe(tipo => {
      this.listaEspera = tipo;
    });

    this.terapeutaService.find().subscribe(terapeutas => {
      this.terapeutas = terapeutas;
    });

  }

  save(): void{
    this.listaEsperaService.delete(this.listaEspera.id).subscribe(() => {
      //console.log('Candidato excluído.');
      this.listaEsperaService.create(this.listaEspera).subscribe(tipo => {
        this.listaEspera = tipo;
        this.listaEsperaService.showMessage('Candidato alterado com sucesso!');
        //console.log('Candidato alterado.');
        this.router.navigate(['/listaespera']);
      })
      
    });
  }

  cancel(): void{
    this.router.navigate(['/listaespera']);
  }

}
