<!--div #pdf style="display: flex; flex-direction: column; width: 580px;"--> 
<div #pdf class="tablewrapper" style="display: block; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 90%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">{{ planointervencao.paciente.nome }}</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle"><u>Plano de Intervenção</u></p>
        </div>
        <div style="text-align: right; width: 66%;">
            <p class="subtitle" style="text-align: right; ">Data: {{ dia }} de {{ mes }} de {{ ano }}</p>
        </div>
        
    </div>
    <div class="div-table">
        <div class="div-table-row">
            <div class="div-indice-cell">
                #
            </div>
            <div class="div-header-cell">
                OBJETIVO
            </div>
            <div class="div-header-cell">
                DESCRIÇÃO
            </div>
        </div>
        <div class="div-table-row" *ngFor="let objetivo of planointervencao.objetivos; let idx=index;">
            <div class="div-indice-cell">
                <p>
                    {{ idx + 1 }} 
                </p>
                <p>
                    {{ objetivo.id}}
                </p>
            </div>
            <div class="div-data-cell">
                {{ objetivo.nome }}
            </div>
            <div class="div-data-cell">
                {{ objetivo.descricao_plano}}
            </div>
        </div>
    </div>
    <div style="width: 100%; break-inside:avoid; " >

        <p style="padding-top: 20px;">
            Estou à disposição para qualquer esclarecimento,
        </p>
        <p style="padding-top: 20px; text-align: center;">
            ________________________________________________________________________________
        </p>
        <div id="assinatura" style="padding-top: 0px; text-align: center;">
            
            
        </div>
    </div>
</div>