<mat-card> 
    <mat-card-title class="title">
        {{ paciente.nome }}
        <ng-container *ngIf="!paciente.ativo">
            (Inativo)
        </ng-container>
    </mat-card-title>
    <form #ngForm>
        <!--
            id?: string;
            nome: string;
            dataNascimento: Date;
            sexo: string;
            equipe: Pessoa[];
            parentes: Pessoa[];
        -->
        <div fxLayout="row wrap" fxLayoutAlign="space-around stretch" fxFlex="100" fxLayoutGap="10" style="width: 100%;">
            <mat-form-field style="width: 37%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="paciente.nome" name="nome" required>
                    <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" [textMask]="{mask: cpfMask }" matInput placeholder="CPF" 
                    [(ngModel)]="paciente.cpf" name="cpf">
            </mat-form-field>
            
            <mat-form-field style="width: 16%;">
                <input class="input" matInput placeholder="Data de Nascimento" 
                    [(ngModel)]="paciente.dataNascimento" name="dataNascimento"
                    [matDatepicker]="picker" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="dataNascimento.invalid">Data de nascimento é obrigatória.</mat-error>  
            </mat-form-field>

            <mat-form-field  style="width: 10%;">
                <mat-label>Sexo</mat-label>
                <mat-select class="select" placeholder="Sexo" 
                    [(ngModel)]="paciente.sexo"
                    name="sexo" required>
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option value="M" >
                    Masculino
                    </mat-option>
                    <mat-option value="F" >
                        Feminino
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="sexo.invalid">Sexo é obrigatório</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;" >
                <input class="input" *ngIf="tipoTelefone == 'C'" matInput placeholder="Telefone" [(ngModel)]="paciente.telefone" (focusout)="validaMaskTelefone()" (focus)="tipoTelefone = 'C'" [textMask]="{mask: celularMask}" name="telefone">
                <input class="input" *ngIf="tipoTelefone == 'T'" matInput placeholder="Telefone" [(ngModel)]="paciente.telefone" (focusout)="validaMaskTelefone()" (focus)="tipoTelefone = 'C'" [textMask]="{mask: telefoneMask}" name="telefone">
            </mat-form-field>

            <mat-form-field style="width: 30%;">
                <input class="input" matInput placeholder="E-mail" 
                    [(ngModel)]="paciente.email" name="email">
            </mat-form-field>

            <mat-form-field  style="width: 17%;">
                <mat-label>Tipo de Intervenção</mat-label>
                <mat-select class="select" placeholder="Tipo de Intervenção" 
                    [(ngModel)]="paciente.tiposIntervencao"
                    name="tiposIntervencao" multiple required>
                    <mat-option value="ESDM" >
                        ESDM
                    </mat-option>
                    <mat-option value="ABA" >
                        ABA
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="tiposIntervencao.invalid">Tipo(s) de Intervenção é obrigatório</mat-error>  
            </mat-form-field>
            <mat-form-field  style="width: 10%; text-align: right;" floatLabel="always" appearance="none">
                <textarea matInput hidden></textarea>
                <mat-slide-toggle
                    class="example-margin"
                    color="primary"
                    [checked]="paciente.ativo"
                    (change)="changeStatus($event)">
                    Ativo
                </mat-slide-toggle>
            </mat-form-field>

            <mat-divider inset="true"></mat-divider>
            <mat-form-field style="width: 20%;">
                <input class="input" [textMask]="{mask: cepMask }" matInput placeholder="CEP" 
                [(ngModel)]="paciente.endereco.cep" (focusout)="consultaCep()" name="cep">
            </mat-form-field>

            <mat-form-field style="width: 40%;">
                <input class="input" matInput placeholder="Logradouro" 
                [(ngModel)]="paciente.endereco.logradouro" name="logradouro">
            </mat-form-field>

            <mat-form-field style="width: 10%;">
                <input class="input" type="number" matInput placeholder="Nº" 
                [(ngModel)]="paciente.endereco.numero" name="numero">
            </mat-form-field>

            <mat-form-field style="width: 30%;">
                <input class="input" matInput placeholder="Complemento" 
                [(ngModel)]="paciente.endereco.complemento" name="complemento">
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Bairro" 
                [(ngModel)]="paciente.endereco.bairro" name="bairro">
            </mat-form-field>

            <mat-form-field style="width: 15%;">
                <input class="input" matInput placeholder="Cidade" 
                [(ngModel)]="paciente.endereco.cidade" name="cidade">
            </mat-form-field>

            <mat-form-field style="width: 5%;">
                <input class="input" matInput placeholder="UF" 
                [(ngModel)]="paciente.endereco.uf" name="uf">
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="País" 
                [(ngModel)]="paciente.endereco.pais" name="pais">
            </mat-form-field>
        </div>
    </form>

    <mat-divider inset="true"></mat-divider>

    <!-- Lista de Profissionais -->
    <mat-card class="mat-elevation-z0"  style="margin-top: 10px;">
        <mat-card-title class="subtitle">Equipe</mat-card-title>
        <div fxFlex.gt-sm="100%" fxFlex.gt-xs="100%" 
        fxFlex="100" fxLayoutWrap="wrap"
        fxLayout="row wrap" fxLayout.xs="column" 
        fxLayoutAlign="start none" fxLayoutGap="10px grid">
            <mat-card fxFlex="31" *ngFor="let profissional of paciente.equipe; index as id"
            class="mat-elevation-z0" style="margin: 10px;"
            fxFlexAlign="stretch" fxLayoutGap="20px">
            <mat-card-header> 
                <div mat-card-avatar style="margin: 10px;">
                    <div *ngIf="profissional.sexo=='M'; then menino else menina"></div>
                    <ng-template #menino><img src="/assets/img/menino_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                    <ng-template #menina><img src="/assets/img/menina_perfil.svg" width="100" alt="{{ profissional.nome }}" class="mat-card-avatar img-circle-shadow"></ng-template>
                </div>
                <mat-card-title class="mat-card-title">
                    <small style="text-align: left;" _ngcontent-c9>
                        {{profissional.nome}}
                        <a style="padding-left: 10px; color: gray; cursor: pointer;"
                            (click)="deleteProfissional(paciente.equipe.indexOf(profissional))"
                            *ngIf="hasAccessEquipe">
                            <mat-icon>person_remove</mat-icon>
                        </a>
                    </small>
                </mat-card-title>
                <mat-card-subtitle>
                    <small  style="text-align: left;">{{ profissional.funcao[0].nome }} </small>
                </mat-card-subtitle>
                </mat-card-header>
            </mat-card>
        </div>
        <!-- Fim da lista de profissionais-->
        <!-- Início da adição de profissionais-->
        <mat-form-field  style="width: 25%; padding: 20px;">
            <mat-label>Profissional</mat-label>
            <mat-select placeholder="Profissional" 
                [(ngModel)]="profissional"
                name="profissional" (selectionChange) = "do()">
                <mat-option *ngFor="let profissional of profissionais" [value]="profissional" >
                    {{profissional.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field  style="width: 15%; padding: 20px;">
                <mat-label>Função</mat-label>
                <mat-select placeholder="Função" 
                    [(ngModel)]="funcaoProfissional"
                    name="funcaoProfissional" (selectionChange) = "do()">
                    <mat-option *ngFor="let funcao of profissional?.funcao" [value]="funcao" (click)="addProfissional()">
                        {{funcao.nome}}
                    </mat-option>
                </mat-select>
        </mat-form-field>
        <!-- Fim da adição de profissionais-->
    </mat-card> 

    <mat-divider inset="true"></mat-divider>
    <mat-card class="mat-elevation-z0" style="margin-top: 10px;">
        <!-- Lista de Parentes -->
        <mat-card-title class="subtitle">Família</mat-card-title>

        <div fxFlex.gt-sm="100%" fxFlex.gt-xs="100%" 
        fxFlex="100" fxLayoutWrap="wrap"
        fxLayout="row wrap" fxLayout.xs="column" 
        fxLayoutAlign="start none" fxLayoutGap="10px grid">
            <mat-card fxFlex="31" *ngFor="let parente of paciente.parentes; index as id"
            class="mat-elevation-z0" style="margin: 10px;"
            fxFlexAlign="stretch" fxLayoutGap="20px">
            <mat-card-header> 
                <div mat-card-avatar style="margin: 10px;">
                    <img src="/assets/img/family.png" width="100" alt="{{ parente.nome }} ({{ parente.parentesco}})" class="mat-card-avatar img-circle-shadow">
                </div>
                <mat-card-title class="mat-card-title">
                    <small style="text-align: left;" _ngcontent-c9>
                        {{parente.nome}}
                        <a style="padding-left: 10px; color: gray; cursor: pointer;"
                            (click)="deleteParente(paciente.parentes.indexOf(parente))" 
                            *ngIf="hasAccessFamilia">
                            <mat-icon>person_remove</mat-icon>
                        </a>
                    </small>
                </mat-card-title>
                <mat-card-subtitle>
                    <small  style="text-align: left;">{{ parente.parentesco }} </small>
                </mat-card-subtitle>
                </mat-card-header>
            </mat-card>
        </div>
        <!-- Fim da lista de parentes-->
        <!-- Início da adição de parentes-->
        <mat-form-field  style="width: 40%; padding: 20px;">
            <mat-label>Parente</mat-label>
            <input type="text"
                placeholder="Parente" 
                matInput
                [(ngModel)]="parente"
                name="parente"
                [formControl]="parenteFC"
                [matAutocomplete]="auto">
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                <mat-option *ngFor="let parente of filteredParents | async" [value]="parente" (click)="addParente()">
                    {{parente.nome}} ({{ parente.parentesco}})
                </mat-option>
            </mat-autocomplete>
        </mat-form-field>
        <!-- Fim da adição de parentes-->
    </mat-card>
    <button mat-raised-button (click)="save()" color="primary"
        *ngIf="hasAccessUpdate">
        Salvar
    </button>
    
    <button mat-raised-button (click)="cancel()"
        *ngIf="hasAccessUpdate">
        Cancelar
    </button>
</mat-card>