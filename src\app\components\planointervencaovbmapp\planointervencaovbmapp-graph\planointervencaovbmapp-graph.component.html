<div #pdf style="display: flex; flex-direction: column; width: 100%;"> 
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 100%;">
        <div style="display: flex; width: 30%; text-align: left;">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px;"  alt="">
        </div>
        <div style="width: 70%; text-align: center; margin: auto;">
            <p class="title">Plano de Ensino Individualizado - Gráficos de Evolução</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%;">
            <p class="subtitle">Paciente: {{ planoIntervencaoVBMAPP.paciente.nome }}</p>
        </div>
        <div style="text-align: center; width: 33%;">
            <p class="subtitle">Profissional: {{ planoIntervencaoVBMAPP.profissional.nome }}</p>
        </div>
        <div style="text-align: right; width: 33%;">
            <p class="subtitle">Data: {{ planoIntervencaoVBMAPP.data | date: 'dd/MM/yyyy' }}</p>
        </div>
    </div>
    
    <mat-tab-group mat-tab-group style="width: 100%;"
        [selectedIndex]="selected.value"
        (selectedIndexChange)="onTabChange($event)"
        dynamicHeight>

        <mat-tab *ngIf="objetivos.length > 0" label="VBMAPP" style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
            <ng-template mat-tab-label>
                <span matBadgeOverlap="false">Objetivos ABA</span>
            </ng-template>

            <div style="padding: 10px;" *ngIf="selected.value === 0">
                <div fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10" 
                    *ngIf="hasAccessRead">
                    <div fxLayout="row wrap" style="display: flex; flex-wrap: wrap; justify-content: space-around;" fxLayoutAlign="end center" fxLayoutGap="10"> 
                        <div *ngFor="let objetivo of objetivos;" style="width: 100%; margin-bottom: 20px; display: flex; justify-content: space-between; flex-wrap: wrap;">
                            <h1 style="width: 100%; text-align: center; padding-top: 20px;">Objetivo: {{ objetivo.nome }}</h1>
                            <!-- Primeiro Gráfico -->
                            <div style="width: 48%;">
                                <div *ngIf="graphsOportunidades[objetivo.id]?.datasets && graphsOportunidades[objetivo.id]?.datasets.length > 0 && graphsOportunidades[objetivo.id]?.labels.length > 0">
                                    <canvas baseChart 
                                        [datasets]="graphsOportunidades[objetivo.id].datasets" 
                                        [labels]="graphsOportunidades[objetivo.id].labels" 
                                        [options]="graphsOportunidades[objetivo.id].options"
                                        legend="true" 
                                        [chartType]="graphsOportunidades[objetivo.id].chartType"
                                        style="width: 100%; height: 450px;">
                                    </canvas>
                                </div>
                                <div *ngIf="!graphsOportunidades[objetivo.id]?.datasets || graphsOportunidades[objetivo.id]?.datasets.length == 0 || graphsOportunidades[objetivo.id]?.labels.length == 0" class="no-data-message">
                                    <div>
                                        <strong>{{ objetivo.nome }}</strong><br>
                                        Nenhuma coleta foi realizada para este objetivo ainda.
                                    </div>
                                </div>
                            </div>
                            <!-- Segundo Gráfico e Tabelas -->
                            <div style="width: 48%;">
                                <div *ngIf="graphsSimples[objetivo.id]?.datasets && graphsSimples[objetivo.id]?.datasets.length > 0 && graphsSimples[objetivo.id]?.labels.length > 0 && graphsSimples[objetivo.id]?.labels.length > 0">
                                    <canvas baseChart 
                                        [datasets]="graphsSimples[objetivo.id].datasets" 
                                        [labels]="graphsSimples[objetivo.id].labels" 
                                        [options]="graphsSimples[objetivo.id].options"
                                        legend="true" 
                                        [chartType]="graphsSimples[objetivo.id].chartType"
                                        style="width: 100%; height: 450px;">
                                    </canvas>
                                </div>
                                <div *ngIf="!graphsSimples[objetivo.id]?.datasets || graphsSimples[objetivo.id]?.datasets.length == 0 || graphsSimples[objetivo.id]?.labels.length == 0" class="no-data-message">
                                    <div>
                                        <strong>{{ objetivo.nome }}</strong><br>
                                        Nenhuma coleta foi realizada para este objetivo ainda.
                                    </div>
                                </div>
                                
                            </div>
                            <!-- Terceiro Gráfico (Oportunidades) - MODIFICADO PARA CENTRALIZAR -->
                            <div style="width: 100%; padding-top: 15px; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center;">
                                <div *ngIf="graphs[objetivo.id]?.datasets && graphs[objetivo.id]?.datasets.length > 0 && graphs[objetivo.id]?.meta.mapDatasColetas?.length > 0" style="width: 48%;">
                                    <canvas baseChart 
                                        [datasets]="graphs[objetivo.id].datasets" 
                                        [labels]="graphs[objetivo.id].labels" 
                                        [options]="graphs[objetivo.id].options"
                                        legend="true" 
                                        [chartType]="graphs[objetivo.id].chartType"
                                        style="width: 100%; height: 450px;">
                                    </canvas>
                                </div>
                                <br>
                                <div *ngIf="graphs[objetivo.id]?.datasets && graphs[objetivo.id]?.datasets.length > 0 && graphs[objetivo.id]?.meta.mapDatasColetas?.length > 0"
                                    style="width: 75%; padding-top: 10px; overflow-x: auto;">
                                <div class="div-table" style="min-width: max-content;">
                                        <div class="div-table-row">
                                            <div class="div-header-cell">
                                                Estímulo
                                            </div>
                                            <div class="div-header-cell" *ngFor="let data of graphs[objetivo.id]?.meta.mapDatasColetas; let i=index">
                                                {{ data | slice:0:5 }}
                                            </div>
                                        </div>
                                        <div class="div-table-row" *ngFor="let estimulo of getEstimulos(objetivo.id); let idx = index">
                                            <div class="div-data-cell">
                                                {{ estimulo }}
                                            </div>
                                            <ng-container *ngFor="let valor of graphs[objetivo.id]?.meta.mapEstimulos.get(estimulo); let idxData=index;">
                                                <div class="div-data-cell" *ngIf="valor == null" style="padding: 2px !important;">
                                                    -
                                                </div>
                                                <div class="div-data-cell" *ngIf="valor != null" style="padding: 2px !important;">
                                                    {{ valor || 0 }}% <small>({{ graphs[objetivo.id]?.meta.tipoSuporteData.get(graphs[objetivo.id]?.meta.mapDatasColetas[idxData]).get(estimulo) }})</small>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="graphs[objetivo.id]?.datasets && graphs[objetivo.id]?.datasets.length > 0 && graphs[objetivo.id]?.meta.mapDatasColetas?.length > 0" style="width: 75%; display: flex; padding-top: 10px; flex-direction: row; flex-wrap: wrap;">
                                    <div style="width: 100%;">
                                        <p><b>Limites por Tipo de Suporte</b></p>
                                    </div>
                                    <div class="div-table">
                                        <div class="div-table-row">
                                            <div class="div-header-cell">
                                                Tipo de Suporte
                                            </div>
                                            <div class="div-header-cell">
                                                Percentual Máximo
                                            </div>
                                        </div>
                                        <div class="div-table-row" *ngFor="let tpSuporte of objetivo?.tiposSuporte; let idx=index;">
                                            <div class="div-data-cell">
                                                {{ tpSuporte.nome }} ({{ tpSuporte.sigla}})
                                            </div>
                                            <div class="div-data-cell">
                                                {{ getLimiteTipoSuporte(tpSuporte, objetivo) | number:"1.0-2" }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="!graphs[objetivo.id]?.datasets || graphs[objetivo.id]?.datasets.length == 0 || graphs[objetivo.id]?.meta.mapDatasColetas?.length <= 0" class="no-data-message">
                                    <div>
                                        <strong>{{ objetivo.nome }}</strong><br>
                                        Nenhuma coleta foi realizada para este objetivo ainda.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h1 style="width: 100%; text-align: center; padding-top: 20px;">Gráfico obtenção dos Objetivos ABA</h1>
                    <div fxLayout="row wrap" style="display: flex; flex-wrap: wrap; justify-content: space-around;" fxLayoutAlign="end center" fxLayoutGap="10">
                        <div class="chartjs-container" *ngIf="graphObjetivos?.datasets && graphObjetivos?.datasets.length > 0 && graphObjetivos?.labels.length > 0" style="width: 50%;">
                            <canvas baseChart 
                                [datasets]="graphObjetivos.datasets" 
                                [labels]="graphObjetivos.labels" 
                                [options]="graphObjetivos.options"
                                legend="true" 
                                [chartType]="graphObjetivos.chartType"
                                style="width: 100%; height: 450px;">
                            </canvas>
                        </div>
                        <div *ngIf="!graphObjetivos?.datasets || graphObjetivos?.datasets.length == 0 || graphObjetivos?.labels.length == 0" class="no-data-message" style="width: 50%;">
                            <div>
                                <strong>Nenhuma coleta foi realizada para este PEI.</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </mat-tab>

        <mat-tab *ngIf="objetivosESDM.length > 0" label="ESDM" style="display: flex; flex-direction: row; flex-wrap: wrap; width: 100%;">
            <ng-template mat-tab-label>
                <span matBadgeOverlap="false">Objetivos ESDM</span>
            </ng-template>

            <div style="padding: 10px;" *ngIf="selected.value === 1">
                <div fxLayout="row wrap" fxLayoutAlign="space-between stretch" fxLayoutGap="10" 
                    *ngIf="hasAccessRead">
                    <div fxLayout="row wrap" style="display: flex; flex-wrap: wrap; justify-content: space-around;" fxLayoutAlign="end center" fxLayoutGap="10"> 
                        <div *ngFor="let objetivoESDM of objetivosESDM;" style="width: 100%; margin-bottom: 20px; display: flex; justify-content: space-between; flex-wrap: wrap;">
                            <h1 style="width: 100%; text-align: center; padding-top: 20px;">Objetivo: {{ objetivoESDM.nome }}</h1>
                            <div style="width: 48%;">
                                <div class="chartjs-container" *ngIf="graphsESDMColeta[objetivoESDM.id]?.datasets && graphsESDMColeta[objetivoESDM.id]?.datasets?.length > 0 && graphsESDMColeta[objetivoESDM.id]?.labels?.length > 0">
                                    <canvas baseChart 
                                        [datasets]="graphsESDMColeta[objetivoESDM.id].datasets" 
                                        [labels]="graphsESDMColeta[objetivoESDM.id].labels" 
                                        [options]="graphsESDMColeta[objetivoESDM.id].options"
                                        legend="true" 
                                        [chartType]="graphsESDMColeta[objetivoESDM.id].chartType"
                                        style="width: 100%; height: 450px;">
                                    </canvas>
                                </div>
                                <div *ngIf="!graphsESDMColeta[objetivoESDM.id]?.datasets || graphsESDMColeta[objetivoESDM.id]?.datasets?.length == 0 || graphsESDMColeta[objetivoESDM.id]?.labels?.length == 0" class="no-data-message">
                                    <div>
                                        <strong>{{ objetivoESDM.nome }}</strong><br>
                                        Nenhum dado coletado!
                                    </div>
                                </div>
                            </div>
                            <div style="width: 48%;">
                                <div class="chartjs-container" *ngIf="graphsESDMObtencao[objetivoESDM.id]?.datasets && graphsESDMObtencao[objetivoESDM.id]?.datasets?.length > 0 && graphsESDMObtencao[objetivoESDM.id]?.labels?.length > 0">
                                    <canvas baseChart 
                                        [datasets]="graphsESDMObtencao[objetivoESDM.id].datasets" 
                                        [labels]="graphsESDMObtencao[objetivoESDM.id].labels" 
                                        [options]="graphsESDMObtencao[objetivoESDM.id].options"
                                        legend="true" 
                                        [chartType]="graphsESDMObtencao[objetivoESDM.id].chartType"
                                        style="width: 100%; height: 450px;">
                                    </canvas>
                                </div>
                                <div *ngIf="!graphsESDMObtencao[objetivoESDM.id]?.datasets || graphsESDMObtencao[objetivoESDM.id]?.datasets?.length == 0 || graphsESDMObtencao[objetivoESDM.id]?.labels?.length == 0" class="no-data-message">
                                    <div>
                                        <strong>{{ objetivoESDM.nome }}</strong><br>
                                        Nenhum dado coletado!
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h1 style="width: 100%; text-align: center; padding-top: 20px;">Gráfico obtenção dos Objetivos ESDM</h1>
                    <div fxLayout="row wrap" style="display: flex; flex-wrap: wrap; justify-content: space-around;" fxLayoutAlign="end center" fxLayoutGap="10">
                        <div class="chartjs-container" *ngIf="graphESDM.datasets && graphESDM.datasets.length > 0 && graphESDM.labels.length > 0" style="width: 50%;">
                            <canvas baseChart 
                                [datasets]="graphESDM.datasets" 
                                [labels]="graphESDM.labels" 
                                [options]="graphESDM.options"
                                legend="true" 
                                [chartType]="graphESDM.chartType"
                                style="display: flex; width: 100%; height: 450px;">
                            </canvas>
                        </div> 
                        <div class="chartjs-container" *ngIf="graphESDM.datasets && graphESDM.datasets.length > 0" style="width: 2%;">
                        </div>
                        <div class="chartjs-container" *ngIf="graphESDM.datasets && graphESDM.datasets.length > 0" style="width: 48%; height: 80%;">
                            <p><b>Legenda</b></p>
                            <div class="div-table">
                                <div class="div-table-row">
                                    <div class="div-indice-cell">
                                        #
                                    </div>
                                    <div class="div-header-cell">
                                        OBJETIVO
                                    </div>
                                </div>
                                <div class="div-table-row" *ngFor="let objetivo of planoIntervencaoVBMAPP.objetivos; let idx=index;">
                                    <div class="div-indice-data-cell" *ngIf="!objetivo.habilidades">
                                        {{ objetivo.id }}
                                    </div>
                                    <div class="div-data-cell" *ngIf="!objetivo.habilidades">
                                        {{ objetivo.nome }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!graphESDM?.datasets || graphESDM?.datasets.length == 0 || graphESDM?.labels.length == 0" class="no-data-message" style="width: 50%;" class="no-data-message">
                        <div>
                            <strong>Nenhuma coleta foi realizada para este PEI.</strong>
                        </div>
                    </div>
                </div>
            </div>
        </mat-tab>
    </mat-tab-group>
</div>