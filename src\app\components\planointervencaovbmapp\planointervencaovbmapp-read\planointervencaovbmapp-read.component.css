.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
  margin: .5em;
}

.label-atrasado {
  background-color: #ff902b;
}

.label-ok {
  background-color: #27c24c;
}

.label-aviso {
  background-color: #e2b902;
}

table{
  width: 100%;
}

.mat-column-index {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
}
  
.mat-column-expand {
  flex: 0 0 5% !important;
  width: 5% !important;
  text-align: left;
}

.mat-column-nomeObj {
  flex: 0 0 25% !important;
  width: 25% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-habilidades {
  flex: 0 0 20% !important;
  width: 20% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-estimulos {
  flex: 0 0 15% !important;
  width: 15% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-tipoSuporte {
  flex: 0 0 7% !important;
  width: 7% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-tipoColeta {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: left;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.mat-column-action {
  flex: 0 0 10% !important;
  width: 10% !important;
  text-align: right;
}

.custom-chip {
  max-width: 50%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-chip-na {
  max-width: 60%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.objetivoAdquirido {
  background-color: lightgreen;
}

.student-element-detail {
  overflow: hidden;
  display: flex;
  align-items: left;
  justify-content: left;
  border-radius: 8px;
  width: 100%;
}

tr.student-detail-row {
  height: 0;
}

.row{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

  /* DivTable.com */
.divTable{ display: table; width: 100%}
.divTableRow { display: table-row; }
.divTableRow:nth-child(even) {
  background: lightgray;
}
.divTableHeading { display: table-header-group;}
.divTableCell, .divTableHead { display: table-cell;}
.divTableFoot { display: table-footer-group;}
.divTableBody { display: table-row-group;}