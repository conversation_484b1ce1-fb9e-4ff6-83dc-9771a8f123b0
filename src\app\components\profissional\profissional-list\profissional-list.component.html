<div class="mat-elevation-z4">
    <table mat-table [dataSource]="profissionais"> 
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.nome}}</td>
        </ng-container>

        <!-- email Column -->
        <ng-container matColumnDef="email" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">E-mail</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.email }}</td>
        </ng-container>

        <!-- funcoes Column -->
        <ng-container matColumnDef="funcoes" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Funções</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{ getFuncoes(row) }}</td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="edit(row)" class="edit">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row)" class="delete">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  