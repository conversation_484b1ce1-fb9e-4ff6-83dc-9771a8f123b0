<mat-card [style.overflow]="isModal ? 'auto' : ''" [style.height.vh]=" isModal ? '80' : ''">
    <mat-card-header>
        <mat-card-title>Objetivo: {{objetivo.id}}</mat-card-title>
    </mat-card-header>
    <mat-card-content fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
        <div fxLayout="row" fxLayoutAlign="space-between stretch" fxLayoutGap="10">
            <form #ngForm> 
                <mat-form-field style="width: 60%;">
                    <input class="input" matInput placeholder="Nome" 
                        [(ngModel)]="objetivo.nome" name="nome" required>
                    <mat-error *ngIf="nomeFC.invalid">Nome é obrigatório.</mat-error>  
                </mat-form-field>

                <mat-form-field  style="width: 10%; padding-left: 10px;">
                    <mat-label>Nível</mat-label>
                    <mat-select placeholder="Nivel" 
                        [(ngModel)]="objetivo.id_nivel"
                        name="nivel" (selectionChange) = "setDominios()" disabled>
                        <mat-option *ngFor="let nivel of niveis" [value]="nivel.id" >
                            {{nivel.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field  style="width: 20%; padding-left: 10px;">
                    <mat-label>Domínio</mat-label>
                    <mat-select placeholder="Dominio" 
                        [(ngModel)]="objetivo.id_dominio"
                        name="dominio" disabled>
                        <mat-option *ngFor="let dominio of dominios" [value]="dominio.id" >
                            {{dominio.nome}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                
                <mat-form-field style="width: 100%;">
                    <textarea class="textarea" matInput placeholder="Descrição" 
                        [(ngModel)]="objetivo.descricao" name="descricao" required
                        rows="3"></textarea>
                    <mat-error *ngIf="descricaoFC.invalid">Descrição é obrigatória.</mat-error>  
                </mat-form-field>

                <mat-form-field style="width: 100%;">
                    <textarea class="textarea" matInput placeholder="Descrição no Plano de Intervenção" 
                        [(ngModel)]="objetivo.descricao_plano" name="descricao_plano" required
                        rows="5"></textarea>
                    <mat-error *ngIf="descricao_planoFC.invalid">Descrição no Plano de Intervenção é obrigatória.</mat-error>  
                </mat-form-field>
            </form>
        </div>
        <mat-card>
            <mat-card-header>
                <mat-card-title>Etapas</mat-card-title>
            </mat-card-header>
            <mat-card-content>
                <div class="mat-elevation-z4">
                    <table mat-table [dataSource]="objetivo.etapa"> 
                        <!-- Id Column --> 
                        <ng-container matColumnDef="id">
                            <th mat-header-cell *matHeaderCellDef >Id</th>
                            <td mat-cell *matCellDef="let row">
                                <!--mat-form-field style="width: 60%;">
                                    <input class="input" matInput
                                        [(ngModel)]="objetivo.etapa[objetivo.etapa.indexOf(row)].id" name="id" required>
                                    <mat-error *ngIf="idEtapaFC.invalid">Id é obrigatório.</mat-error>  
                                </mat-form-field-->
                                {{ row.id }}
                            </td>
                        </ng-container>
                
                        <!-- Nome Column -->
                        <ng-container matColumnDef="nome" fxFlex="30">
                            <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                            <td mat-cell *matCellDef="let row" fxFlex="30">
                                <mat-form-field style="width: 90%;">
                                    <input class="input" matInput
                                        [(ngModel)]="objetivo.etapa[objetivo.etapa.indexOf(row)].nome" name="nome" required>
                                    <mat-error *ngIf="nomeEtapaFC.invalid">Nome é obrigatório.</mat-error>  
                                </mat-form-field>
                            </td>
                        </ng-container> 
        
                        <!-- Action Column -->
                        <ng-container matColumnDef="action">
                            <th mat-header-cell *matHeaderCellDef>Ações</th>
                            <td mat-cell *matCellDef="let row">
                                <!--a (click)="edit(row.id)" class="edit"
                                    *ngIf="hasAccessUpdate">
                                    <i class="material-icons">
                                        edit
                                    </i>
                                </a-->
                                <a (click)="up(row.id)" class="edit"
                                    [ngStyle]="objetivo.etapa.indexOf(row) == 0 && {'visibility': 'hidden'}"
                                    *ngIf="hasAccessUpdate">
                                    <i class="material-icons">
                                        expand_less
                                    </i>
                                </a>
                                <a (click)="down(row.id)" class="edit"
                                    [ngStyle]="objetivo.etapa.indexOf(row) == (objetivo.etapa.length - 1) && {'visibility': 'hidden'}"
                                    *ngIf="hasAccessUpdate">
                                    <i class="material-icons">
                                        expand_more
                                    </i>
                                </a>
                                <a (click)="delete(row.id)" class="delete"
                                    *ngIf="hasAccessDelete">
                                    <i class="material-icons">
                                        delete
                                    </i>
                                </a>
                            </td>
                        </ng-container>
                  
                      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                    </table>
                    <button mat-button (click)="addEtapa()">
                        <mat-icon class="icon" style="padding-right: 10px;">
                            playlist_add
                        </mat-icon>
                        Nova etapa
                    </button>
                    <!--mat-expansion-panel class="expansion" hideToggle>
                        <mat-expansion-panel-header>
                            <mat-panel-title>
                                <mat-icon class="icon" style="padding-right: 10px;">
                                    playlist_add
                                </mat-icon>
                                Nova etapa
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                    </mat-expansion-panel-->
                </div>        
            </mat-card-content>
        </mat-card>
    </mat-card-content>
    <mat-card-actions *ngIf="!this.data">
        <button mat-raised-button color="primary" (click)="save(true)"
            *ngIf="hasAccessUpdate">
            Salvar e Ir para Próximo
        </button>
        <button mat-raised-button color="primary" (click)="save(false)"
            *ngIf="hasAccessUpdate">
            Salvar
        </button>
        <button mat-raised-button (click)="cancel()"
            *ngIf="hasAccessUpdate">
            Cancelar
        </button>
      </mat-card-actions>
      <mat-card-actions *ngIf="this.data">
        <button mat-raised-button mat-dialog-close color="primary" (click)="addObjetivoPersonalizado()">
            Salvar
        </button>
        <button mat-raised-button mat-dialog-close>
            Cancelar
        </button>
      </mat-card-actions>
</mat-card>