<!-- <div #pdf style="display: flex; flex-direction: column; width: 100%"> -->
    <!-- TÍTULO -->
    <div style="display: flex; flex-direction: row; width: 90%">
        <div style="display: flex; width: 30%; text-align: left">
            <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px" alt="" />
        </div>
        <div style="width: 70%; text-align: center; margin: auto">
            <p class="title">Plano de Ensino Individualizado</p>
        </div>
    </div>

    <!-- SUB-TÍTULO -->
    <div class="subtitulo">
        <div style="text-align: left; width: 34%">
            <p class="subtitle">Paciente: {{ planointervencao?.paciente?.nome }}</p>
        </div>
        <div style="text-align: center; width: 33%">
            <p class="subtitle">
                Profissional: {{ planointervencao?.profissional?.nome }}
            </p>
        </div>
        <div style="text-align: right; width: 33%">
            <p class="subtitle">
                Data: {{ planointervencao?.data | date: "dd/MM/yyyy" }}
            </p>
        </div>
    </div>

    <h1 *ngIf="planointervencao?.objetivos?.length == 0 || !planointervencao?.objetivos" style="text-align: center; ">
        Nenhum objetivo cadastrado no PEI!
    </h1>

    <mat-card *ngFor="let objetivo of planointervencao?.objetivos; let idxObj = index" [ngClass]="objetivo?.status == 'Adquirido' ? 'objetivoAdquirido' : 'objetivo'">
        <mat-card-header>
            <mat-card-title>
                {{ idxObj + 1 }}. {{ objetivo.nome }}
                <ng-container *ngIf="objetivo.status == 'Adquirido'">
                    (Objetivo em manutenção)
                </ng-container>
            </mat-card-title>
        </mat-card-header>
        <mat-card-content>
            <div *ngIf="objetivo.habilidades" style=" display: flex; flex-wrap: wrap; flex-direction: row;  justify-content: space-between; width: 90%;">
                <ng-container *ngIf="objetivo.descricao">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Meta:</b><br> {{ objetivo.descricao }} <br>
                    </div>
                </ng-container>
                <ng-container *ngIf="objetivo.sd">
                    <div style="width: fit-content; padding: 5px;">
                        <b>SD:</b><br> {{ objetivo.sd }} <br>
                    </div>
                </ng-container>
                <div style="width: fit-content; padding: 5px;">
                    <b>Estímulos:</b> 
                    <ng-container *ngFor="let estimulo of objetivo.estimulos" style="width: 100%">
                        <li>{{ estimulo.nome }} </li>
                    </ng-container>
                </div>
                <ng-container *ngIf="objetivo.materiais">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Materiais:</b><br> {{ objetivo.materiais }} <br>
                    </div>
                </ng-container>
                <ng-container *ngIf="objetivo.oportunidadesEstimulo">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Nº de Oportunidades/Estímulo:</b><br> {{ objetivo.oportunidadesEstimulo }} <br>
                    </div>
                </ng-container>
                <ng-container *ngIf="objetivo.estimulosFuturos">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Estímulos Futuros:</b><br> {{ objetivo.estimulosFuturos }} <br>
                    </div>
                </ng-container>
                <ng-container *ngIf="objetivo.procedimento">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Procedimento:</b><br> {{ objetivo.procedimento }} <br>
                    </div>
                </ng-container>
                <ng-container *ngIf="objetivo.correcaoErros">
                    <div style="width: fit-content; padding: 5px;">
                        <b>Correção de Erros:</b><br> {{ objetivo.correcaoErros }} <br>
                    </div>
                </ng-container>
                <div style="width: fit-content; padding: 5px;">
                    <b>Tipos de Suporte:</b> 
                    <ng-container *ngFor="let tipoSuporte of objetivo.tiposSuporte" style="width: 100%">
                        <li>{{ tipoSuporte.nome }} </li>
                    </ng-container>
                </div>
            </div>
            <div *ngIf="!objetivo.habilidades">
                <p class="desc_objetivo">{{ objetivo.descricao_plano }}</p>
                <div *ngFor="let etapa of objetivo.etapa; let i=index">
                    <p [ngClass]="[etapa.status == 'Adquirida' ? 'etapa' : '', 
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] == undefined ? 'etapa_atual' : '',
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] != undefined && objetivo.etapa[i-1].status == 'Adquirida' ? 'etapa_atual' : '',
                                    etapa.status != 'Adquirida' && objetivo.etapa[i-1] != undefined && objetivo.etapa[i-1].status != 'Adquirida' ? 'etapa' : ''
                                    ]">
                        Etapa {{ i+1 }} - {{ etapa.id }} - {{ etapa.nome }}
                    </p>
                </div>
            </div>
        </mat-card-content>
    </mat-card>


    <!-- <table mat-table [dataSource]="planointervencao.objetivos" class="mat-elevation-z8" >
        <ng-container matColumnDef="conteudo"  >
            <td mat-cell *matCellDef="let row;  let i = index"  >
                <div style=" display: flex; flex-direction: row;  justify-content: space-between;">
                    <h2>{{ i + 1 }}. {{ row.nome }}</h2>
                    <hr>
                    <b>Habilidades:</b> 
                    <ng-container *ngFor="let habilidade of row.habilidades" style="width: 100%">

                        <li>{{ getTipoAvaliacao(habilidade.idTipoAvaliacao) }} - {{ habilidade.sigla == undefined || habilidade.sigla == "" ? dominio.nome + " - " + habilidade.ordem : habilidade.sigla }} </li>
                    </ng-container> <br>
                    <b>Meta:</b> {{ row.descricao }} <br>
                    <b>SD:</b> {{ row.sd }} <br>
                    <b>Estímulos:</b> 
                    <ng-container *ngFor="let estimulo of row.estimulos" style="width: 100%">
                        <li>{{ estimulo.nome }} </li>
                    </ng-container>
                    <b>Materiais:</b> {{ row.materiais }} <br>
                    <b>Nº de Oportunidades/Estímulo:</b> {{ row.oportunidadesEstimulo }} <br>
                    <b>Estímulos Futuros:</b> {{ row.estimulosFuturos }} <br>
                    <b>Procedimento:</b> {{ row.procedimento }} <br>
                    <b>Correção de Erros:</b> {{ row.correcaoErros }} <br>
                    <b>Tipos de Suporte:</b> 
                    <ng-container *ngFor="let tipoSuporte of row.tiposSuporte" style="width: 100%">
                        <li>{{ tipoSuporte.nome }} </li>
                    </ng-container>
                </div>
            </td>
        </ng-container>

        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

    </table> -->
    <!-- <mat-grid-list cols="1">
        <ng-container *ngFor="let objetivo of planointervencao.objetivos; let idxObj = index" style="width: 100%">
            <mat-grid-tile style="height: calc(100vh - 24px)">
                <div class="text-inside-grid">

                    <h2>{{ idxObj + 1 }}. {{ objetivo.nome }}</h2>
                    <hr>
                    <b>Habilidades:</b> 
                    <ng-container *ngFor="let habilidade of objetivo.habilidades" style="width: 100%">
                        <li>{{ habilidade.nome }} </li>
                    </ng-container> <br>
                    <b>Meta:</b> {{ objetivo.descricao }} <br>
                    <b>SD:</b> {{ objetivo.sd }} <br>
                    <b>Estímulos:</b> 
                    <ng-container *ngFor="let estimulo of objetivo.estimulos" style="width: 100%">
                        <li>{{ estimulo.nome }} </li>
                    </ng-container>
                    <b>Materiais:</b> {{ objetivo.materiais }} <br>
                    <b>Nº de Oportunidades/Estímulo:</b> {{ objetivo.oportunidadesEstimulo }} <br>
                    <b>Estímulos Futuros:</b> {{ objetivo.estimulosFuturos }} <br>
                    <b>Procedimento:</b> {{ objetivo.procedimento }} <br>
                    <b>Correção de Erros:</b> {{ objetivo.correcaoErros }} <br>
                    <b>Tipos de Suporte:</b> 
                    <ng-container *ngFor="let tipoSuporte of objetivo.tiposSuporte" style="width: 100%">
                        <li>{{ tipoSuporte.nome }} </li>
                    </ng-container>
                </div>
            </mat-grid-tile>
    
        </ng-container>
    </mat-grid-list> -->
    <br>
    <br>
    