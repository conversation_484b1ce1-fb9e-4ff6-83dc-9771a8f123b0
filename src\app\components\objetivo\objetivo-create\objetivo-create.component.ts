import { Etapa } from './../../etapa/etapa-model';
import { AuthService } from './../../template/auth/auth.service';
import { CompetenciaService } from './../../competencia/competencia.service';
import { NivelService } from './../../nivel/nivel.service';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { ActivatedRoute, Router } from '@angular/router';
import { ObjetivoService } from './../objetivo.service';
import { NgForm, FormControl, Validators, FormArray } from '@angular/forms';
import { Objetivo } from './../objetivo-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Optional } from '@angular/core';

export interface DialogData {
  obj: Objetivo;
}

@Component({
  selector: 'app-objetivo-create',
  templateUrl: './objetivo-create.component.html',
  styleUrls: ['./objetivo-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})


export class ObjetivoCreateComponent implements OnInit {
//TODO: Ao alterar o objetivo, alterar a competência também. Interferndo no Checklist.

  objetivo: Objetivo = new Objetivo();
  objetivos: Objetivo[] = [];

  public niveis: Nivel[] = [];
  public dominios: Dominio[] =[];
  //public dominiosTab: Dominio[] = [];

  //public objetivo: Objetivo = new Objetivo();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();
  public dominioMap: Map<string, Dominio[]> = new Map<string, Dominio[]>(); 
  public objetivosView: Objetivo[] = [];

  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;

  displayedColumns = ['id', 'nome', 'action']

  @ViewChild(NgForm) form;

  //Form Controls
  nomeFC = new FormControl('', [Validators.required]);
  descricaoFC = new FormControl('', [Validators.required]);
  descricao_planoFC = new FormControl('', [Validators.required]);
  //formArray = new FormArray([], [Validators.required]);

  idEtapaFC = new FormControl('', [Validators.required]);
  nomeEtapaFC = new FormControl('', [Validators.required]);

  isModal: boolean = false;
 
  constructor(private objetivoService: ObjetivoService,
    private nivelService: NivelService,
    private competenciaService: CompetenciaService,
    public authService: AuthService,
    private route: ActivatedRoute,
    private router: Router,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: DialogData
    ) { }

  async ngOnInit(): Promise<void> {
    this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','update');
    this.hasAccessDelete = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (ESDM)','delete');
    if(this.data == null){
      let idObjetivo = this.route.snapshot.paramMap.get('id');  

      this.objetivos = (JSON.parse(localStorage.getItem("objetivos")) as Objetivo[]);

      //Carregando Níveis
      this.nivelService.find().subscribe(niveis => {
        this.niveis = niveis;
        
        /*if(idObjetivo == undefined) { //Create
          this.nivel = this.niveis.find(n => n.id == 'N1');
        } else {
          this.nivel = this.niveis.find(n => n.id == 'N1');
        }*/

        //Carregando Domínios dos Níveis (TAB)
        niveis.forEach(nivel => {
          this.competenciaService.findDominiosByNivel(nivel.id).subscribe(async dominios => {
            this.dominioMap.set(nivel.id,dominios);
            
            if(idObjetivo == undefined) { //Create
              this.objetivo = new Objetivo();
              this.objetivo.ativo = true;
              //Carregando Domínios para Combo (Default: N1-CRE)
              this.nivel = this.niveis.find(n => n.id == 'N1');
              this.dominios = this.dominioMap.get('N1');
              if(this.dominios){
                this.dominio = this.dominios.find(dom => dom.id == 'CRE');
              }
            } else {  //Edit
                await this.objetivoService.findById(idObjetivo).subscribe(async objetivo => {
                  this.objetivo = objetivo; 
                  //Carregando Domínios para Combo (Default: N1-CRE)
                  this.nivel = this.niveis.find(n => n.id == this.objetivo.id_nivel);
                  this.dominios = this.dominioMap.get(this.objetivo.id_nivel);
                  if(this.dominios){
                    this.dominio = this.dominios.find(dom => dom.id == this.objetivo.id_dominio);
                  }
                  this.objetivo.etapa.forEach(etapa => {
                    //this.formArray.push(new FormControl());
                    //console.log(this.formArray.length)
                  })
                })
            }


            //Carregando Domínios para Combo (Default: N1-CRE)
            /*this.dominios = this.dominioMap.get('N1');
            if(this.dominios){
              this.dominio = this.dominios.find(dom => dom.id == 'CRE');
            }*/
          })
        })

      })

      if(idObjetivo == undefined) { //Create
        this.objetivo = new Objetivo();
        this.objetivo.ativo = true;
      } else {  //Edit
          await this.objetivoService.findById(idObjetivo).subscribe(async objetivo => {
            this.objetivo = objetivo; 
            this.objetivo.etapa.forEach(etapa => {
              //this.formArray.push(new FormControl());
              //console.log(this.formArray.length)
            })
          })
        }
    }else{
      //verifica se foi chamado para personalizar o objetivo do paciente
      this.objetivo = this.data.obj; 
      this.isModal = true;
      this.niveis.push(this.objetivo.nivel);
      this.dominios.push(this.objetivo.dominio);
      //console.log(this.niveis)
    }
  }

  addObjetivoPersonalizado(){
    localStorage.setItem("objetivoPersonalizado",JSON.stringify(this.objetivo));
  }

  setDominios(){
    this.dominios = this.dominioMap.get(this.nivel.id);
    this.dominio = this.dominios[0];//this.dominios.find(dom => dom.id == 'CRE');  
  }

  addEtapa(){
    let etapa: Etapa = new Etapa();
    let ordem: number;
    if(this.objetivo.etapa == undefined || this.objetivo.etapa.length == 0){
      this.objetivo.etapa = [];
      etapa.id = this.objetivo.id + "E01"
      this.objetivo.etapa.push(etapa)
    } else {
      ordem = parseInt(this.objetivo.etapa[this.objetivo.etapa.length -1].id.substr(7,2)) + 1;
      etapa.id = this.objetivo.etapa[this.objetivo.etapa.length -1].id.substr(0,7) + ("0" + ordem).slice(-2);
      etapa.nome = "";
      this.objetivo.etapa.push(etapa);
      //this.formArray.push(new FormControl());
    }
    this.objetivo.etapa = [...this.objetivo.etapa];
  }

  delete(id: string){
    let ordem: number;

    //Renumerar etapas posteriores
    for(let i = this.objetivo.etapa.indexOf(this.objetivo.etapa.find(e => e.id == id)) + 1; i < this.objetivo.etapa.length; i++){
      ordem = parseInt(this.objetivo.etapa[i].id.substr(7,2)) - 1;
      this.objetivo.etapa[i].id = this.objetivo.etapa[i].id.substr(0,7) + ("0" + ordem).slice(-2);
    }
    this.objetivo.etapa.splice(this.objetivo.etapa.indexOf(this.objetivo.etapa.find(e => e.id == id)), 1);
    this.objetivo.etapa = [...this.objetivo.etapa]
  }

  up(id: string){
    let nome: string;
    let i = this.objetivo.etapa.indexOf(this.objetivo.etapa.find(e => e.id == id));
    nome = this.objetivo.etapa.find(e => e.id == id).nome;

    this.objetivo.etapa[i].nome = this.objetivo.etapa[i-1].nome;
    this.objetivo.etapa[i-1].nome = nome;

    this.objetivo.etapa = [...this.objetivo.etapa]
  }

  down(id: string){
    let nome: string;
    let i = this.objetivo.etapa.indexOf(this.objetivo.etapa.find(e => e.id == id));
    nome = this.objetivo.etapa.find(e => e.id == id).nome;

    this.objetivo.etapa[i].nome = this.objetivo.etapa[i+1].nome;
    this.objetivo.etapa[i+1].nome = nome;

    this.objetivo.etapa = [...this.objetivo.etapa]
  }

  next(){
    var id;
    //this.dominioMap.get(this.objetivo.id_nivel);

    // console.log(this.nivel.id)
    // console.log(this.dominio.id)
    // console.log(this.objetivo.id)

    this.objetivosView = this.objetivos.filter(obj => 
      (obj.id_nivel==this.nivel.id 
        && obj.id_dominio == this.dominio.id))

    // console.log(this.objetivosView)
    // console.log(this.objetivosView[this.objetivosView.indexOf(this.objetivosView.filter(o => o.id == this.objetivo.id)[0])]);
    //console.log(this.objetivosView[this.objetivosView.indexOf(this.objetivosView.filter(o => o.id == this.objetivo.id)[0]) + 1]);

    if(this.objetivosView[this.objetivosView.indexOf(this.objetivosView.filter(o => o.id == this.objetivo.id)[0]) + 1] == undefined) {
      //Chegou ao último objetivo do domínio. Passar para o próximo domínio.
      if(this.dominios[this.dominios.indexOf(this.dominios.filter(d => d.id == this.dominio.id)[0]) + 1] == undefined){
        if(this.niveis[this.niveis.indexOf(this.niveis.filter(d => d.id == this.nivel.id)[0]) + 1] == undefined) {
          //Passou por todos os objetivos de todos os domínios de todos os níveis
          this.router.navigate(['/objetivo']);
        } else {
          //Chegou ao último objetivo do último domínio do nível
          let nivelNext = this.niveis[this.niveis.indexOf(this.niveis.filter(d => d.id == this.nivel.id)[0]) + 1];
          let dominioNext = this.dominioMap.get(nivelNext.id)[0]

          // console.log("Próximo nível");
          // console.log(nivelNext.id);
          // console.log(dominioNext.id);

          //Pego a lista de objetivos do próximo domínio
          this.objetivosView = this.objetivos.filter(obj => 
            (obj.id_nivel==nivelNext.id 
              && obj.id_dominio == dominioNext.id))
          
          //Pego o primeiro objetivo da lista
          id = this.objetivosView[0].id;
        }

      } else {
        let dominioNext = this.dominios[this.dominios.indexOf(this.dominios.filter(d => d.id == this.dominio.id)[0]) + 1];

        //Pego a lista de objetivos do próximo domínio
        this.objetivosView = this.objetivos.filter(obj => 
          (obj.id_nivel==this.nivel.id 
            && obj.id_dominio == dominioNext.id))
        
        //Pego o primeiro objetivo da lista
        id = this.objetivosView[0].id;
      }
    } else {
      //Ainda existem objetivos no domínio corrente.
      id = this.objetivosView[this.objetivosView.indexOf(this.objetivosView.filter(o => o.id == this.objetivo.id)[0]) + 1].id;
    }
    
    if(id != undefined){
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.onSameUrlNavigation = 'reload';  
      this.router.navigate(['/objetivo/update/' + id]);
    }
  }

  save(next: boolean){
    let valid: boolean = true;
    if(this.form.valid){
      //Verifico se existe alguma etapa com nome em branco (find não estava funcionando)
      this.objetivo.etapa.forEach(e =>{
        if(e.nome == ""){
          valid = false;
        }
      })
      //console.log((this.objetivo.etapa as Etapa[]).find(e => { e.nome == "Olha, SP, 3/5 opts"}))
      if(valid){
        
        if(this.objetivo.id == undefined){
          this.objetivoService.create(this.objetivo).subscribe((id) => {
            this.objetivoService.showMessage('Objetivo criado com sucesso!');
            this.router.navigate(['/objetivo']);
          });
        } else {
          this.objetivoService.update(this.objetivo).subscribe((funcao) => {
            this.objetivoService.showMessage('Objetivo alterado com sucesso!');
            if(next){
              this.next();
            } else {
              this.router.navigate(['/objetivo']);
            }
          });
        }
        
      } else {
        this.objetivoService.showMessage('Existem campos inválidos no formulário!',true);  
      }
    } else {
      this.objetivoService.showMessage('Existem campos inválidos no formulário!',true);
    }
  }

  cancel() {
    this.router.navigate(['/objetivo']);
  }

}
