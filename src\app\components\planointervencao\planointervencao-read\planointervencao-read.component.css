.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin: .5em;
  }
  
  .label-atrasado {
    background-color: #ff902b;
  }
  
  .label-ok {
    background-color: #27c24c;
  }
  
  .label-aviso {
    background-color: #e2b902;
  }
  
  table{
    width: 100%;
  }

.mat-column-index {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-expand {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-etapas {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
  }
  
  .mat-column-idNome {
    flex: 0 0 80% !important;
    width: 80% !important;
    text-align: left;
  }
  
  .mat-column-action {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: right;
  }
  
  .student-element-detail {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
  }
  
  .student-element-detail-old {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e1e1e1;
    box-shadow: inset 2px 3px 8px #c1c1c1;
    border-radius: 8px;
  }
  
  tr.student-detail-row {
    height: 0;
  }