import { HeaderService } from './../../components/template/header/header.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-eventoatendimento-crud',
  templateUrl: './eventoatendimento-crud.component.html',
  styleUrls: ['./eventoatendimento-crud.component.css']
})
export class EventoatendimentoCrudComponent implements OnInit {

  constructor(private router: Router,
    private headerService: HeaderService) { 
      headerService.headerData = {
        title: 'Atendimento',
        icon: 'schedule',
        routeUrl: '/agenda'
      }
    }

  ngOnInit(): void {
  }

}
