import { AuthService } from './../../template/auth/auth.service';
import { FirebaseUserModel } from './../../template/auth/user-model';
import { Etapa } from './../../etapa/etapa-model';
import { DominioService } from './../../dominio/dominio.service';
import { Dominio } from './../../dominio/dominio-model';
import { Objetivo } from './../../objetivo/objetivo-model';
import { MatSelectChange } from '@angular/material/select';
import { PlanoIntervencao } from './../../planointervencao/planointervencao-model';
import { PacienteService } from './../../paciente/paciente.service';
import { Paciente } from './../../paciente/paciente-model';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ColetadiariaService } from './../coletadiaria.service';
import { ProfissionalService } from './../../profissional/profissional.service';
import { PlanoIntervencaoService } from './../../planointervencao/planointervencao.service';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { ColetaDiaria } from './../coleta-diaria-model';
import { Profissional } from './../../profissional/profissional-model';
import { AppDateAdapter, APP_DATE_FORMATS } from 'src/app/shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { Component, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-coletadiaria-create',
  templateUrl: './coletadiaria-create.component.html',
  styleUrls: ['./coletadiaria-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class ColetadiariaCreateComponent implements OnInit {  
  
  public coletasdiarias: ColetaDiaria[] = [];
  public profissionais: Profissional[] = [];
  public idProfissional: string;
  public profissionaisDoPaciente: Profissional[];
  public idPaciente: string;
  public paciente: Paciente = new Paciente();
  public data: Date;
  public today: Date = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
  //public profissional: Profissional;
  public idPlanoIntervencao: string;
  public planoIntervencao: PlanoIntervencao = new PlanoIntervencao();
  public cantChangeData: boolean = false;

  public dominioMap: Map<string, Objetivo[]>[] = [];

  public domAtual: string = "";

  tabs = [];
  selected = new FormControl(0);

  hours = [
    "07:00", "07:15", "07:30", "07:45",
    "08:00", "08:15", "08:30", "08:45",
    "09:00", "09:15", "09:30", "09:45",
    "10:00", "10:15", "10:30", "10:45",
    "11:00", "11:15", "11:30", "11:45",
    "12:00", "12:15", "12:30", "12:45",
    "13:00", "13:15", "13:30", "13:45",
    "14:00", "14:15", "14:30", "14:45",
    "15:00", "15:15", "15:30", "15:45",
    "16:00", "16:15", "16:30", "16:45",
    "17:00", "17:15", "17:30", "17:45",
    "18:00", "18:15", "18:30", "18:45",
    "19:00", "19:15", "19:30", "19:45",
    "20:00", "20:15", "20:30", "20:45"
  ]

  //Form Controls
  dataFC = new FormControl('', [Validators.required]);
  profissionalFC = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  //moment(new Date()).format('YYYY-MM-DD')
  
  constructor(private planoIntervencaoService: PlanoIntervencaoService,
    private profissionalService: ProfissionalService,
    private pacienteService: PacienteService,
    private coletaDiariaService: ColetadiariaService,
    public authService: AuthService,
    private dominioService: DominioService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute) { }

  ngOnInit(): void {
    //this.idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    this.idPlanoIntervencao = this.route.snapshot.paramMap.get('id');

    

    //Recupero o Plano de Intervenção
    this.planoIntervencaoService.findById(this.idPlanoIntervencao).subscribe(plano => {
      this.planoIntervencao = plano;
      //console.log(this.planoIntervencao)
    
      //Recuperando os dados do paciente atual
      this.pacienteService.findById(this.planoIntervencao.idPaciente).subscribe(paciente => {
        this.paciente = paciente;
        this.profissionaisDoPaciente = paciente.equipe;
      })

      this.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
      //Carregando Profissionais
      this.profissionalService.find().subscribe(profissionais => {
        this.profissionais = profissionais;
        this.addColetaDiaria();
      })

      //ATENÇÃO: Código abaixo permite a edição das coletas caso seja no mesmo dia
      /*
      //Recupero as coletas diárias do plano de intervenção
      this.coletaDiariaService.findByPlanoIntervencaoData(this.idPlanoIntervencao, 
          //moment(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())).format('YYYY-MM-DD[T]HH:mm:ss.SSS[Z]')).subscribe(coletas => {
            new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()).toISOString()).subscribe(coletas => {
        this.coletasdiarias = coletas;

        if(this.coletasdiarias.length == 0) { //Create (Nenhuma coleta encontrada para o plano)
         console.log(this.coletasdiarias.length)
          //Seto a data atual
          this.data = new Date();
          this.addColetaDiaria();
        } else { //Edit
          //this.cantChangeData = true;
          this.data = this.coletasdiarias[0].data;
          this.idProfissional = this.coletasdiarias[0].idProfissional;
          this.coletasdiarias.forEach(coleta => {
            //this.addTab("Sessão " + (this.coletasdiarias.indexOf(coleta) + 1),false)
            this.addTab(coleta.sessao,false)
            this.setObjetivosPorDominioEdit(this.coletasdiarias.indexOf(coleta));
          })
        }

      })
      */

    })
    
  }

  setRespostaPeriodo(sessaoId: number, periodoId: number, objetivo: Objetivo, etapa: Etapa){
    let obj = this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)];
    let eta = obj.etapa[obj.etapa.indexOf(etapa)];

    if(eta.periodo[periodoId] == "") {
      // console.log("+")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = "+"
    } else if(eta.periodo[periodoId] == "+") {
      // console.log("-")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = "-"
    } else if(eta.periodo[periodoId] == "-") {
      // console.log("")
      this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
        .etapa[this.coletasdiarias[sessaoId].objetivos[this.coletasdiarias[sessaoId].objetivos.indexOf(objetivo)]
          .etapa.indexOf(etapa)].periodo[periodoId] = ""
    }
    this.save(sessaoId);
    
  }

  async setObjetivosPorDominioCreate(sessaoId: number){
    let dMap = new Map<string, Objetivo[]>();
    let objetivos: Objetivo[] = [];

    /*
    await this.planoIntervencao.objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    */

    await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    this.dominioMap.push(dMap);
    //console.log(this.coletasdiarias[sessaoId])
    //console.log(this.dominioMap[this.dominioMap.length - 1])
  }

  async setObjetivosPorDominioEdit(sessaoId: number){
    let dMap = new Map<string, Objetivo[]>();
    let objetivos: Objetivo[] = [];

    await this.coletasdiarias[sessaoId].objetivos.forEach(objetivo => {
      //objetivos = [];
      if(dMap.get(objetivo.dominio.id) != undefined){
        objetivos = dMap.get(objetivo.dominio.id);
        objetivos.push(objetivo);
        dMap.set(objetivo.id_dominio, objetivos)
      } else {
        dMap.set(objetivo.id_dominio, [objetivo])
      }
    })
    this.dominioMap.push(dMap);
  }

  checkDominioAtual(dominio: string): boolean {
    if(this.domAtual ==  dominio){
      return false;
    } else {
      this.domAtual = dominio;
      return true;
    }
  }

  getPeriodoIcon(periodo: string): string{
    if(periodo == "+") {
      return "thumb_up"
    }
    if(periodo == "-") {
      return "thumb_down"
    }
    if(periodo == "") {
      return "thumbs_up_down"
    }
  }

  setObjetivos(sessaoId: number){

    let objetivoNovo: Objetivo
    let etapaDefinida: boolean;

    //Recupero os objetivos do plano de intervenção atual e incluo na sessão apenas a etapa que está em andamento
    //Etapa em andamento: primeira etapa do objetivo que não esteja com status adquirida
    this.planoIntervencao.objetivos.forEach(objetivo => {
      objetivoNovo = {...objetivo};
      etapaDefinida = false;
      //console.log(objetivo.nome);

      if(objetivoNovo.status == "Adquirido") {
        //Se o objetivo foi adquirido, incluo o mesmo sem etapas
        objetivoNovo.etapa = []
        if(this.coletasdiarias[sessaoId].objetivos == undefined){
          this.coletasdiarias[sessaoId].objetivos = [];
        }
        this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
      } else {
        objetivoNovo.etapa.forEach(etapa => {
          if(!etapaDefinida){
            //console.log(etapa.status)
            if(etapa.status == undefined || etapa.status != "Adquirida"){
              //console.log(etapa.id);
              //console.log(objetivoNovo.etapa.indexOf(etapa))
              objetivoNovo.etapa = []
              objetivoNovo.etapa.push({...etapa})
              objetivoNovo.etapa[0].periodo = []
              objetivoNovo.etapa[0].periodo.push("", "", "", "")

    
              /*if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }
              this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});*/
              etapaDefinida = true;
            } else {
              objetivoNovo.etapa = [];
              
              /*if(this.coletasdiarias[sessaoId].objetivos == undefined){
                this.coletasdiarias[sessaoId].objetivos = [];
              }

              this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});*/
              //etapaDefinida = true;
            }
          }
        })
        if(this.coletasdiarias[sessaoId].objetivos == undefined){
          this.coletasdiarias[sessaoId].objetivos = [];
        }
        this.coletasdiarias[sessaoId].objetivos.push({...objetivoNovo});
      }
    })
    //Incluo os objetivos em um domínio de Map para facilitar a listagem no HTML
    this.setObjetivosPorDominioCreate(sessaoId);
  }

  addTab(name: string, selectAfterAdding: boolean) {
    this.tabs.push(name);
    //console.log(this.tabs)
    //console.log(name)

    if (selectAfterAdding) {
      this.selected.setValue(this.tabs.length - 1);
    }
  }

  addColetaDiaria(){
    let cd = new ColetaDiaria();
    let dataInicio: Date;
    //console.log(this.coletasdiarias.length)

    //Incluindo os dados do paciente
    cd.idPaciente = this.planoIntervencao.idPaciente;
    cd.paciente = this.paciente;

    //Incluindo os dados do plano de intervenção
    cd.idPlanoIntervencao = this.idPlanoIntervencao;

    //Incluindo os dados do profissional
    //cd.idProfissional = this.idProfissional;
    //Caso seja um profisisonal, seto como o criador
    // console.log(this.profissionais)
    // console.log(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid))
    if ( (this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
      if(this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid) != undefined){
        cd.profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
        cd.idProfissional = cd.profissional.id;
        this.idProfissional = cd.idProfissional;
      }
    }

    //Incluindo o nome da sessão
    cd.sessao = "Sessão " + (this.coletasdiarias.length + 1);



    //Aplicando a data e hora atual a coleta diária
    cd.data = new Date(new Date(this.data).getFullYear(), new Date(this.data).getMonth(), new Date(this.data).getDate(), 0,0,0,0);
    //console.log(cd.data)
    if(this.coletasdiarias.length > 0){
      dataInicio = new Date(this.data);
      dataInicio.setHours(parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(0,2)) + 1,
        parseInt(this.coletasdiarias[this.coletasdiarias.length - 1].horaInicio.substr(3,2)))
      // console.log(dataInicio)
      cd.horaInicio = ("0" + dataInicio.getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
      cd.horaTermino = ("0" + (dataInicio.getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
    } else {
      cd.horaInicio = ("0" + new Date().getHours().toLocaleString('pt-BR')).slice(-2) + ":00";
      cd.horaTermino = ("0" + (new Date().getHours() +1).toLocaleString('pt-BR')).slice(-2) + ":00";
    }

    this.coletasdiarias.push(cd);

    //this.addTab("Sessão " + (this.coletasdiarias.length), true)
    this.addTab(cd.sessao, true)
    this.setObjetivos(this.coletasdiarias.length - 1);
  }

  startSession(sessaoId: number){
    // console.log(this.coletasdiarias)
    this.save(sessaoId);
    //this.coletaDiariaService.showMessage("Sessão iniciada...");
  }

  setColetasPorData(){
    this.coletasdiarias.forEach(coleta => {
      coleta.data = new Date(this.data.getFullYear(), this.data.getMonth(), this.data.getDate(), 0,0,0,0);
      if(coleta.id != undefined){
        this.save(this.coletasdiarias.indexOf(coleta))
      }
    })
  }

  exitEdit(){
    this.router.navigate(['/paciente/' + this.paciente.id, {  tab: "coleta"}])
  }

  /*
  cleanEmptyPeriods(){
    this.coletasdiarias.forEach(coleta => {
      coleta.objetivos.forEach(objetivo => {
        objetivo.etapa.forEach(etapa => {
          etapa.periodo = etapa.periodo.filter(function(p) {
            return p != "";
          })
        })
      })
    })
  }
  */

  save(sessaoId: number){
    //this.cleanEmptyPeriods();
    /*if(sessaoId == -1) { //Salvar todas
      if(this.form.valid){
        this.coletasdiarias.forEach(coleta => {
          if(coleta.id == undefined){
            this.coletaDiariaService.create(coleta).subscribe((coleta) => {
              this.coletasdiarias[this.coletasdiarias.indexOf(coleta)] = coleta;
            });
          } else {
            this.coletaDiariaService.update(this.coletasdiarias[sessaoId]).subscribe((coleta) => {
            });
          }
        })
      } else {
        this.coletaDiariaService.showMessage('Existem campos inválidos no formulário!',true);
      }
    } else { //Salvar apenas uma sessão específica
      */
      // console.log(this.coletasdiarias[sessaoId])
      // console.log(this.form.valid)
      if(this.form.valid){
        // console.log(this.coletasdiarias[sessaoId].id)
        if(this.coletasdiarias[sessaoId].id == undefined){
          this.coletaDiariaService.create(this.coletasdiarias[sessaoId]).subscribe((id) => {
            //this.cantChangeData = true;
            this.coletasdiarias[sessaoId].id = id;
            this.coletasdiarias = [...this.coletasdiarias];
            // console.log(id)
            //this.planointervencaoService.showMessage('Plano de intervenção criado com sucesso!');
            //this.router.navigate(['/paciente/' + id]);
          });
        } else {
          this.coletaDiariaService.update(this.coletasdiarias[sessaoId]).subscribe((coleta) => {
            //this.planointervencaoService.showMessage('Plano de intervenção alterado com sucesso!');
            //this.router.navigate(['/paciente/' + paciente.id]);
          });
        }
      } else {
        this.coletaDiariaService.showMessage('Existem campos inválidos no formulário!',true);
      } 
    //}
  }

  cancel(){

  }

  setProfissional(event:MatSelectChange){
    this.coletasdiarias.forEach(coleta => {
      coleta.profissional = this.profissionais.find(p => p.id == event.value);
      coleta.idProfissional = coleta.profissional.id;
    })
  }

  test(){
    //console.log(this.coletasdiarias)
    this.planoIntervencaoService.setStatusEtapaPlano(this.planoIntervencao);
  }

}
