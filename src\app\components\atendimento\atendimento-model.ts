
import { LocalAtendimento } from '../local/local-atendimento-model';
import { Paciente } from '../paciente/paciente-model';
import { Profissional } from '../profissional/profissional-model';
import { StatusAgenda } from '../statusagenda/statusagenda-model';
import { TipoProcedimento } from '../tipoprocedimento/tipoprocedimento-model';

export class Atendimento {
    procedimento: TipoProcedimento; 
    idProcedimento: string;
    profissionais: Profissional[];
    idProfissionais: string[];
    paciente: Paciente;
    idPaciente: string;
    status: StatusAgenda;
    idStatus: string;
    recorrente?: boolean;
    local: LocalAtendimento;
    idLocal: string;
    idAtendRecorrencia?: string;

    constructor(){
        this.status = new StatusAgenda();
        this.idStatus = '';
        this.procedimento = new TipoProcedimento();
        this.idProcedimento = '';
        this.paciente = new Paciente();
        this.idPaciente = '';
        this.idProfissionais = [];
        this.profissionais = [];
        this.recorrente = false;
    }
}
