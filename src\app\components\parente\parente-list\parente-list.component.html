<div class="mat-elevation-z4">
    <table mat-table [dataSource]="parentes"> 
        <!--
            telefone: string;
            parentesco: string;
        -->
        <!-- Nome Column -->
        <ng-container matColumnDef="nome" fxFlex="70">
            <th mat-header-cell *matHeaderCellDef fxFlex="70">Nome</th>
            <td mat-cell *matCellDef="let row"  fxFlex="70">{{row.nome}}</td>
        </ng-container>

        <!-- email Column -->
        <ng-container matColumnDef="email" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">E-mail</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{row.email }}</td>
        </ng-container>

        <!-- telefone Column -->
        <ng-container matColumnDef="telefone" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Telefone</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{ row.telefone }}</td>
        </ng-container>

        <!-- parentesco Column -->
        <ng-container matColumnDef="parentesco" fxFlex="30">
            <th mat-header-cell *matHeaderCellDef fxFlex="30">Parentesco</th>
            <td mat-cell *matCellDef="let row" fxFlex="30">{{ row.parentesco }}</td>
        </ng-container>
  
      <!-- Action Column -->
      <ng-container matColumnDef="action">
        <th mat-header-cell *matHeaderCellDef>Ações</th>
        <td mat-cell *matCellDef="let row">
            <a (click)="edit(row)" class="edit">
                <i class="material-icons">
                    edit
                </i>
            </a>
            <a (click)="delete(row)" class="delete">
                <i class="material-icons">
                    delete
                </i>
            </a>
        </td>
      </ng-container>
  
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>
  