<!-- <mat-toolbar class="header mat-elevation-z4"> -->
    <mat-expansion-panel style="width: 100%;">
        <mat-expansion-panel-header>
          <mat-panel-title>
            Filtros
          </mat-panel-title>
          <!--mat-panel-description>
            Type your name and age
            <mat-icon>account_circle</mat-icon>
          </mat-panel-description-->
        </mat-expansion-panel-header>
    
    
    
        <!--div fxLayout="row wrap" fxLayoutAlign="space-around stretch" fxLayoutGap="10" style="width: 80%;" -->
            <mat-form-field style="width: 30%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="nome" name="nome"
                    (input) = "setFilter()"
                    >
            </mat-form-field>
            <mat-form-field style="width: 30%;">
                <mat-label>Profissional</mat-label>
                <mat-select placeholder="profissional" 
                    [(ngModel)]="profissional"
                    name="profissional.nome" (selectionChange) = "setFilter()">
                    <mat-option></mat-option>
                    <mat-option *ngFor="let prof of profissionais" [value]="prof.id" >
                    {{prof.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
    
            <mat-form-field  style="width: 15%;">
                <mat-label>Tipo de Intervenção</mat-label>
                <mat-select class="select" placeholder="Tipo de Intervenção" 
                    [(ngModel)]="tiposIntervencao"
                    (selectionChange) = "setFilter()"
                    name="tiposIntervencao" >
                    <mat-option value="ESDM" >
                        ESDM
                    </mat-option>
                    <mat-option value="ABA" >
                        ABA
                    </mat-option>
                </mat-select>
            </mat-form-field> 

            <mat-form-field  style="width: 10%; text-align: center;" floatLabel="always" appearance="none">
                <!--mat-label>Ativo</mat-label-->
                <mat-slide-toggle 
                    [(ngModel)]="ativo"
                    (change) = "setFilter()">
                    Ativo
                </mat-slide-toggle>
                <textarea matInput hidden></textarea>
            </mat-form-field>
        <!--/div-->
        <div class="v-middle" style="font-size: small; cursor: pointer; width: 10%; text-align: right;">
            <a (click) = "cleanFilter()">Limpar filtros</a>
        </div>
    </mat-expansion-panel>    
<!-- </mat-toolbar> -->