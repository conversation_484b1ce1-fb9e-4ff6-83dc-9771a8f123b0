<mat-card class="mat-elevation-z0" style="padding: 0px;">
    <mat-card-header>
        <!--div mat-card-avatar>
            <img class="logo" src="assets/img/CAPACITEAUTISMO.png">
        </div-->
        <mat-card-title>{{ tipoAvaliacao?.nome }}</mat-card-title>
    </mat-card-header>

    <div class="mat-elevation-z0" style="padding: 0;">   
        <mat-form-field  style="width: 15%; padding: 20px;">
            <mat-label>Paciente</mat-label>
            <mat-select placeholder="Paciente" 
                [(ngModel)]="avaliacao.idPaciente"
                name="paciente" disabled required>
                <!--mat-option *ngFor="let paciente of pacientes" [value]="paciente.id" >
                    {{paciente.nome}}
                </mat-option-->
                <mat-option [value]="avaliacao.idPaciente" >
                    {{paciente.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field  style="width: 15%;  padding: 0px;">
            <mat-label>Avaliação</mat-label>
            <mat-select placeholder="Avaliação" 
                [(ngModel)]="avaliacao"
                name="avaliacao" disabled>
                <mat-option [value]="avaliacao" >
                    {{avaliacao.data | date: 'dd/MM/yyyy'}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-form-field  style="width: 20%; padding: 20px;">
            <mat-label>Profissional</mat-label>
            <mat-select placeholder="Profissional" 
                [(ngModel)]="profissional.id"
                name="profissional"
                disabled>
                <mat-option [value]="profissional?.id" >
                    {{profissional?.nome}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <button mat-mini-fab alt="Sair da edição" color="primary" style="margin: 15px;" (click)="exitEdit()">
            <mat-icon>arrow_back</mat-icon>
        </button>

        <button mat-mini-fab color="primary" style="margin: 7px;" (click)="generatePDF()">
            <mat-icon>printer</mat-icon>
        </button>
    </div>
    
    <div class="mat-elevation-z0" style="padding: 0;"
        *ngIf="hasAccessReadVBMAPP">
        <span style="width: 90%; padding: 10px;">
            
            <mat-form-field  style="width: 10%; padding: 20px;">
                <mat-label>Nível</mat-label>
                <mat-select placeholder="Nivel" 
                    [(ngModel)]="nivel"
                    name="nivel" (selectionChange) = "setDominios()">
                    <mat-option *ngFor="let nivel of niveis" [value]="nivel" >
                        {{nivel.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field  style="width: 20%; padding: 20px;">
                <mat-label>Domínio</mat-label>
                <mat-select placeholder="Dominio" 
                    [(ngModel)]="dominio"
                    name="dominio" (selectionChange) = "filterHabilidades()">
                    <mat-option *ngFor="let dominio of dominios" [value]="dominio" >
                        {{dominio.nome}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

        </span>
        
    </div>  
    
    <div class="mat-elevation-z0"
        *ngIf="hasAccessRead">
        <strong>{{ tipoAvaliacao?.nome }} - {{ avaliacao?.data | date: 'dd/MM/yyyy' }}</strong><br>

        <table mat-table [dataSource]="habilidadesView"> 
            <!-- Id Column --> 
            <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef >Id</th>
                <td mat-cell *matCellDef="let row">{{row.sigla}}</td>
            </ng-container>
    
            <!-- Nome Column -->
            <ng-container matColumnDef="nome" fxFlex="30">
                <th mat-header-cell *matHeaderCellDef fxFlex="30">Nome</th>
                <td mat-cell *matCellDef="let row" fxFlex="30">{{row.nome}}</td>
            </ng-container> 
            


            <!-- Status Column -->
            <ng-container matColumnDef="status" fxFlex="30">
                <th mat-header-cell *matHeaderCellDef fxFlex="30">Status</th>
                <td mat-cell *matCellDef="let row" fxFlex="30">
                    <div [ngClass] = "[getClassRespostaHabilidade(row.id)]"><small>{{ getDominioResposta(getRespostaHabilidade(row.id)?.valor)?.nome }}</small></div>
                    <!-- <div class="label label-N" *ngIf="getRespostaHabilidade(row.id)] == 0"><small>{{ getDominioResposta(avaliacao.respostasHabilidades[findIndexHabilidade(row.id)].valor).nome }}</small></div>
                    <div class="label label-P" *ngIf="getRespostaHabilidade(row.id)] == 0.5"><small>{{ getDominioResposta(avaliacao.respostasHabilidades[findIndexHabilidade(row.id)].valor).nome }}</small></div>
                    <div class="label label-A" *ngIf="getRespostaHabilidade(row.id)] == 1"><small>{{ getDominioResposta(avaliacao.respostasHabilidades[findIndexHabilidade(row.id)].valor).nome }}</small></div>
                    <div class="label label-X" *ngIf="getRespostaHabilidade(row.id)] == undefined" disabled><small>{{ getDominioResposta(avaliacao.respostasHabilidades[findIndexHabilidade(row.id)].valor).nome }}</small></div> -->
                </td>
            </ng-container> 
        
            <tr mat-header-row *matHeaderRowDef="displayedColumnsMsAssmt"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumnsMsAssmt;"></tr>
        </table>
    </div>
    <app-avaliacoespdf *ngIf="viewAvaliacaoPDF" [idAvaliacao] = "idAvaliacao" [avaliacao]="avaliacao" (pdfGenerated)="onPDFGenerated()"></app-avaliacoespdf>
    <app-avaliacaovbmapppdf *ngIf="viewVbmappPDF" [idAvaliacao] = "idAvaliacao" [avaliacao]="avaliacao" (pdfGenerated)="onPDFGenerated()"></app-avaliacaovbmapppdf>
</mat-card>