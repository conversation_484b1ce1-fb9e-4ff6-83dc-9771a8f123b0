/* You can add global styles to this file, and also import other style files */
@import "../node_modules/angular-calendar/css/angular-calendar.css";

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

.red{
    color: #e35e6b;
}

.blue{
    color: cornflowerblue;
}

.v-middle{
    vertical-align: middle;
}

.msg-success{
    background-color: #28a745;
    color: #fff;
}

.msg-success .mat-simple-snackbar-action  {
    color: #fff;
}

.msg-error{
    background-color: #e35e6b;
    color: #fff;
}

.msg-error .mat-simple-snackbar-action  {
    color: #fff;
}

.mouseover-effect {
    transition: background-color 0.3s ease; 
}

.mouseover-effect:hover {
    background-color: #ececece3;
}

.mat-row {
    transition: background-color 0.5s ease;
  }

.mat-row:hover {
    background-color: #ececece3;
}

.createColeta {
    margin-right: 10px;
}

.createColeta > i {
    color: #5d4eff;
    cursor: pointer;
    transition: color 0.5s ease;
}

.createColeta:hover > i {
    color: #1500ff;
}

.edit {
    margin-right: 10px;
}

.edit > i {
    color: #d9cd26;
    cursor: pointer;
    transition: color 0.5s ease;
}

.edit:hover > i {
    color: #716603;
}

.delete > i {
    color: #e35e6b;
    cursor: pointer;
    transition: color 0.5s ease;
}

.delete:hover > i {
    color: #a91c2a;
}

.overlay{
    height:100vh;
    width:100%;
    background-color:rgba(0, 0, 0, 0.286);
    z-index:    10;
    top:        0; 
    left:       0; 
    position:   fixed;
}

.center {
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
} 

.custom-dialog-container .mat-dialog-container {
    max-height: 50vh !important;
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    padding: 24px;
}
  
.mat-dialog-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
    margin-bottom: 16px;
}
  
mat-dialog-actions {
    bottom: 0;
    background: white;
    padding-top: 12px;
}
