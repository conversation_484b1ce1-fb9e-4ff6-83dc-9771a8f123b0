import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Nota } from '../../nota-model';
import { Paciente } from '../../../paciente/paciente-model';
import { AuthService } from '../../../template/auth/auth.service';

@Component({
  selector: 'app-nota-edit-dialog',
  templateUrl: './nota-edit-dialog.component.html',
  styleUrls: ['./nota-edit-dialog.component.css']
})
export class NotaEditDialogComponent {
  notaEditada: Nota;
  paciente: any;

  constructor(
    public dialogRef: MatDialogRef<NotaEditDialogComponent>,
    public authService: AuthService,
    @Inject(MAT_DIALOG_DATA) public data: { nota: Nota, paciente: Paciente },
  ) {
    
    this.notaEditada = { ...data.nota };
  
    if (!this.notaEditada.nivelAcesso) {
      this.notaEditada.nivelAcesso = 0;
    }
  
    this.paciente = data.paciente;
  }

  onSave(): void {
    if (this.notaEditada.titulo && this.notaEditada.data && this.notaEditada.descricao && this.notaEditada.nivelAcesso != null) {
      this.dialogRef.close(this.notaEditada);
    }
  }  

  onCancel(): void {
    this.dialogRef.close(null);
  }

  isTeamMember(): boolean {
    const user = this.authService.getUser();
  
    if (!user || !this.paciente || !this.paciente.equipe) return user?.admin || user?.gsupervisor;
  
    return this.paciente.equipe.some(prof => prof.uid === user.uid) || user.admin || user.gsupervisor;
  }  
  
}
