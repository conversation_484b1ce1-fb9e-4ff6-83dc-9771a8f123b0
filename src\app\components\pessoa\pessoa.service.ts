import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Pessoa } from './pessoa-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class PessoaOldService {

  pessoaUrl = `${environment.API_URL}/pessoa`;
  profissionalUrl = `${environment.API_URL}/profissionais`;
  parenteUrl = `${environment.API_URL}/parentes`;
  
  public pessoas: BehaviorSubject<Pessoa[]> = 
    new BehaviorSubject<Pessoa[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(pessoa: Pessoa): Observable<Pessoa>{
    //console.log(pessoa);
    return this.http.post<Pessoa>(this.pessoaUrl, pessoa).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(pessoa: Pessoa): Observable<Pessoa>{
    //console.log(pessoa);
    return this.http.put<Pessoa>(this.pessoaUrl + '/' + pessoa.id, pessoa).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Pessoa>{
    return this.http.get<Pessoa>(this.pessoaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByEmail(email: string): Observable<Pessoa>{
    return this.http.get<Pessoa>(this.pessoaUrl + '/email/' + email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByUserId(uid: string): Observable<Pessoa>{
    return this.http.get<Pessoa>(this.pessoaUrl + '/uid/' + uid).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Pessoa[]>{
    return this.http.get<Pessoa[]>(this.pessoaUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findProfissionais(): Observable<Pessoa[]>{
    return this.http.get<Pessoa[]>(this.profissionalUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findParentes(): Observable<Pessoa[]>{
    return this.http.get<Pessoa[]>(this.parenteUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Pessoa>{
    return this.http.delete<Pessoa>(this.pessoaUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
