import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { ProfissionalService } from './../../profissional/profissional.service';
import { MatSelectChange } from '@angular/material/select';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { forkJoin } from 'rxjs';
import { Dominio } from './../../dominio/dominio-model';
import { ESDMChkLstCompetencia } from '../esdmchklst-competencia-model';
import { ESDMChecklist } from '../esdmchecklist-model';
import { PacienteService } from '../../paciente/paciente.service';
import { NivelService } from '../../nivel/nivel.service';
import { Nivel } from '../../nivel/nivel-model';
import { Paciente } from '../../paciente/paciente-model';
import { CompetenciaService } from '../../competencia/competencia.service';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { EsdmchecklistService } from '../esdmchecklist.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { LoadingService } from 'src/app/shared/service/loading.service';
import { FirebaseUserModel } from '../../template/auth/user-model';

@Component({
  selector: 'app-esdmchecklist-create',
  templateUrl: './esdmchecklist-create.component.html',
  styleUrls: ['./esdmchecklist-create.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
}) 
export class EsdmchecklistCreateComponent implements OnInit {

  public esdmchecklists: ESDMChecklist[];
  //public pacientes: Paciente[];
  public pac: Paciente = new Paciente();
  public profissionais: Profissional[];
  public profissionaisDoPaciente: Profissional[];
  public niveis: Nivel[];
  public dominios: Dominio[];
  public chklstcompView: ESDMChkLstCompetencia[] = [];
  public prevchklstcompView: ESDMChkLstCompetencia[] = [];

  public esdmchecklist: ESDMChecklist = new ESDMChecklist();
  public prevesdmchecklist: ESDMChecklist = new ESDMChecklist();
  public nivel: Nivel = new Nivel();
  public dominio: Dominio = new Dominio();

  public hasAccessUpdate: boolean;
  public hasAccessRead: boolean;

  saveDisabled: boolean = false;
  disabledButtomNext: boolean = false;
  textoBotao: string = 'Próximo Domínio';

  displayedColumns = ['id', 'nome', 'prevchklst', 'N', 'P', 'A', 'X']

  //Form Controls
  paciente = new FormControl('', [Validators.required]);
  profissional = new FormControl('', [Validators.required]);
  data = new FormControl('', [Validators.required]);

  allButton: any;

  @ViewChild(NgForm) form;

  constructor(private esdmchecklistService: EsdmchecklistService,
    private competenciaService: CompetenciaService,
    private nivelService: NivelService,
    private pacienteService: PacienteService,
    public authService: AuthService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    public dialog: MatDialog,
    private router: Router,
    private route: ActivatedRoute,
    private loadingService: LoadingService) { }

  ngOnInit(): void {
    this.loadingService.reset();
    this.loadingService.show();
  
    let idPaciente = this.route.snapshot.paramMap.get('idPaciente');
    let idChecklist = this.route.snapshot.paramMap.get('idChecklist');
  
    let requests = []; // Array para armazenar as promessas de requisições
  
    // Carregando profissionais vinculados ao paciente
    requests.push(
      this.pacienteService.findById(idPaciente).toPromise().then(paciente => {
        this.profissionaisDoPaciente = paciente.equipe;
        this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','update')
        this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de ESDM Checklist','read')
      })
    );
  
    if (idChecklist == undefined) { // Create
      // Carregando Competências
      requests.push(
        this.competenciaService.find().toPromise().then(competencias => {
          this.esdmchecklist.checklist = [];
  
          competencias.forEach(competencia => {
            let chklst = new ESDMChkLstCompetencia();
            chklst.competencia = competencia;
            this.esdmchecklist.checklist.push(chklst);
          });
  
          this.chklstcompView = this.esdmchecklist.checklist.filter(chklst =>
            (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE')
          );
  
          // Carregando Paciente
          return this.pacienteService.findById(idPaciente).toPromise().then(paciente => {
            this.pac = paciente;
            this.esdmchecklist.paciente = paciente;
            this.esdmchecklist.idPaciente = idPaciente;
          });
        })
      );
  
      // Pegando o checklist anterior
      requests.push(
        this.esdmchecklistService.findLastByPacienteData(
          idPaciente,
          moment(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())).format('YYYY-MM-DD')
        ).toPromise().then(chklsts => {
          if (chklsts.length > 0) {
            if (chklsts.filter(msassmt => msassmt.status != false).length > 0) {
              this.prevesdmchecklist = chklsts.filter(msassmt => msassmt.status != false)[0];
            }
            if (this.prevesdmchecklist) {
              this.prevchklstcompView = this.prevesdmchecklist.checklist.filter(chklst =>
                (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE')
              );
            }
          }
        })
      );
    } else { // Edit
      // Carregando o checklist a ser editado
      requests.push(
        this.esdmchecklistService.findById(idChecklist).toPromise().then(chklst => {
          this.esdmchecklist = chklst;
  
          // Carregando Paciente
          return this.pacienteService.findById(idPaciente).toPromise().then(paciente => {
            this.pac = paciente;
            this.esdmchecklist.paciente = paciente;
            this.esdmchecklist.idPaciente = idPaciente;
          });
        }).then(() => {
          this.chklstcompView = this.esdmchecklist.checklist.filter(chklst =>
            (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE')
          );
  
          // Pegando o checklist anterior
          return this.esdmchecklistService.findLastByPacienteData(
            idPaciente,
            moment(this.esdmchecklist.data).subtract(1, "days").format('YYYY-MM-DD')
          ).toPromise().then(chklsts => {
            if (chklsts.length > 0) {
              if (chklsts.filter(chklst => chklst.status != false).length > 0) {
                this.prevesdmchecklist = chklsts.filter(chklst => chklst.status != false)[0];
              }
              if (this.prevesdmchecklist) {
                this.prevchklstcompView = this.prevesdmchecklist.checklist.filter(chklst =>
                  (chklst.competencia.id_nivel == 'N1' && chklst.competencia.id_dominio == 'CRE')
                );
              }
            }
          });
        })
      );
    }
  
    // Carregando Níveis
    requests.push(
      this.nivelService.find().toPromise().then(niveis => {
        this.niveis = niveis;
        this.nivel = this.niveis.find(n => n.id == 'N1');
      })
    );
  
    // Carregando Domínios
    requests.push(
      this.competenciaService.findDominiosByNivel("N1").toPromise().then(dominios => {
        this.dominios = dominios;
        this.dominio = this.dominios.find(dom => dom.id == 'CRE');
      })
    );
  
    // Carregando Profissionais
    requests.push(
      this.profissionalService.find().toPromise().then(profissionais => {
        this.profissionais = profissionais;
        if (idChecklist == undefined) { // Create
          if ((this.authService.getUser() as FirebaseUserModel).profissional || !(this.authService.getUser() as FirebaseUserModel).familia) {
            let profissional = this.profissionais.find(p => p.uid == (this.authService.getUser() as FirebaseUserModel).uid);
            if (profissional) {
              this.esdmchecklist.profissional = profissional;
              this.esdmchecklist.idProfissional = profissional.id;
            }
          }
        }
      })
    );
  
    // Quando todas as requisições terminarem, escondo o loading
    Promise.all(requests).finally(() => this.loadingService.hide());
  }

  getEsdmChkLstValue(id: string) : string{
    //console.log(this.prevesdmchecklist);
    if(this.prevesdmchecklist != undefined && this.prevesdmchecklist.checklist.length > 0){
      return this.prevesdmchecklist.checklist[this.prevesdmchecklist.checklist.findIndex(e => e.competencia.id == id)].valor
    } else {
      return "";
    }
  }

  do2(){
    // console.log(this.esdmchecklist.data);
    // console.log(moment(this.esdmchecklist.data).format('YYYY-MM-DD'));
    // console.log(moment(this.esdmchecklist.data).utc().format('YYYY-MM-DD HH:mm:ss'));
    this.esdmchecklistService.findLastByPacienteData(this.esdmchecklist.idPaciente, moment(this.esdmchecklist.data).subtract(1, "days").format('YYYY-MM-DD')).subscribe(chklsts => {
      this.prevesdmchecklist = undefined;
      if(chklsts.length > 0){
        // console.log(msassmts);
        
        //Atribuindo o primeiro MsAssmt para visualização
        if (chklsts.filter(chklst => chklst.status != false).length > 0){
          this.prevesdmchecklist = chklsts.filter(chklst => chklst.status != false)[0];
        }
        // console.log(this.vbmappmsassmt)
        
        //console.log(this.prevvbmappmsassmt)
        if(this.prevesdmchecklist != undefined){
          this.prevchklstcompView = chklsts.filter(msassmt => msassmt.status != false)[0].checklist.filter(chklst => 
            (chklst.competencia.id_nivel=='N1' && chklst.competencia.id_dominio == 'CRE'))
        }
      }
      // this.prevesdmchecklist = chklsts[0];
      // if(this.prevesdmchecklist != undefined){
      //   this.prevchklstcompView = chklsts[0].checklist.filter(chklst => 
      //     (chklst.competencia.id_nivel=='N1' && chklst.competencia.id_dominio == 'CRE'))
      // }
    })
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

   // console.log(funcoes)


    return funcoes;
  }

  setDominios(){
    this.competenciaService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
      this.dominios = dominios;
      this.dominio = this.dominios[0];
      this.filterChecklist();
    })
  }

  filterChecklist(){
    const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
    const ultimoIndice = this.dominios.length - 1;
    const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
    const ultimoNivel = this.niveis.length - 1;
    this.disabledButtomNext = false;
    this.textoBotao = 'Próximo Domínio';

    //Altero o botão de acordo com o indice
    if (indiceAtual === ultimoIndice) {
      this.textoBotao = 'Próximo Nível e Domínio';
      if (nivelAtual === ultimoNivel) {
        this.disabledButtomNext = true;
        this.textoBotao = 'Último nível alcançado!'
      }
    }

    if(this.prevesdmchecklist != undefined && this.prevesdmchecklist.checklist.length > 0){
      this.prevchklstcompView = this.prevesdmchecklist.checklist.filter(chklst => (
        chklst.competencia.id_nivel==this.nivel.id && chklst.competencia.id_dominio == this.dominio.id))
    }

    this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => (
      chklst.competencia.id_nivel==this.nivel.id && chklst.competencia.id_dominio == this.dominio.id))

  }

  setProfissional(event:MatSelectChange){
    let profissional = this.profissionais.find(p => p.id == event.value);
    this.esdmchecklist.profissional = profissional ? profissional : this.profissionaisDoPaciente.find(p => p.id == event.value);
    //if(this.esdmchecklist.id != undefined){
    //  this.save(false);
    //}
  }

  /*
  delete(id: string): void{
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        this.esdmchecklistService.delete(id).subscribe(
          () => {
            this.esdmchecklistService.showMessage('Paciente excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/paciente']);
          }
        );
      }
    });
  }
  */

  do() {

  }

  save(exit: boolean){
    //var submitButton = document.getElementById('btnSave');
    //console.log(submitButton.innerText);
    this.loadingService.show();
    this.saveDisabled=true;
    //submitButton.setAttribute('disabled', 'true' );
    if(this.form.valid){
      if(this.esdmchecklist.id == undefined){
        this.esdmchecklistService.create(this.esdmchecklist).subscribe((checklistId) => {
          //console.log(checklistId)
          this.esdmchecklist.id = checklistId
          //console.log(this.esdmchecklist.id)
          this.esdmchecklistService.showMessage('Checklist criado com sucesso!');
          this.saveDisabled=false;
          this.loadingService.hide();
          if(exit){
            if(this.esdmchecklist.id == undefined){
              this.router.navigate(['/paciente/'+this.esdmchecklist.idPaciente, {
                tab:"checklist"
              }]);
            } else {
              this.router.navigate(['/paciente/'+this.esdmchecklist.idPaciente, {
                tab:"checklist",
                idESDMChklst: this.esdmchecklist.id
              }]);
            }
          }
        });
      } else {
        this.esdmchecklistService.update(this.esdmchecklist).subscribe(() => {
          this.esdmchecklistService.showMessage('Checklist alterado com sucesso!');
          this.saveDisabled=false;
          this.loadingService.hide();
          if(exit){
            if(this.esdmchecklist.id == undefined){
              this.router.navigate(['/paciente/'+this.esdmchecklist.idPaciente, {
                tab:"checklist"
              }]);
            } else {
              this.router.navigate(['/paciente/'+this.esdmchecklist.idPaciente, {
                tab:"checklist",
                idESDMChklst: this.esdmchecklist.id
              }]);
            }
          }
        });
      }
      //console.log(this.esdmchecklist);
    } else {
      this.esdmchecklistService.showMessage('Existem campos inválidos no formulário!',true);
      this.saveDisabled=false;
      this.loadingService.hide();
    }

  }

  cancel(){
    this.router.navigate(['/paciente/'+this.esdmchecklist.idPaciente, {
      tab:"avaliacao"
    }]);
  }

  nextDominio() {
    const indiceAtual = this.dominios.findIndex(dom => dom.id === this.dominio.id);
    const ultimoIndice = this.dominios.length - 1;

    if (indiceAtual < ultimoIndice) {
      // Ainda há domínios neste nível, então avança para o próximo domínio
      this.dominio = this.dominios[indiceAtual + 1];
    } else {
      // É o último domínio do nível atual, então avança para o próximo nível
      const nivelAtual = this.niveis.findIndex(nivel => nivel.id === this.nivel.id);
  
      // Ainda há níveis, então avança para o próximo nível e define o domínio para o primeiro do novo nível
      this.nivel = this.niveis[nivelAtual + 1];
      this.competenciaService.findDominiosByNivel(this.nivel.id).subscribe(dominios => {
        this.dominios = dominios;
        this.dominio = this.dominios[0];
      });
      this.dominio = this.dominios[0];
    }
    this.filterChecklist();

    //Altero o botão de acordo com o indice
    if (indiceAtual === ultimoIndice) {
      this.textoBotao = 'Próximo Domínio';
    }
    window.scrollTo(0, 0);
  }
  

  checkAllAcquired(value: string){
    /*this.chklstcompView = this.esdmchecklist.checklist.filter(chklst => 
      (chklst.competencia.id_nivel=='N1' && chklst.competencia.id_dominio == 'CRE'))
    */
    
    this.allButton = value;

    this.chklstcompView.forEach(chkLst => {
      this.esdmchecklist.checklist[this.esdmchecklist.checklist.indexOf(chkLst)].valor = value
    })

    // Reset da variável allButton após o hide do loading
    setTimeout(() => {
      this.allButton = "";
    }, 0);
  }

}
