import { Profissional } from './../../../profissional/profissional-model';
import { ProfissionalService } from './../../../profissional/profissional.service';
import { MatTableDataSource } from '@angular/material/table';
import { FirebaseUserModel } from '../user-model';
import { AuthService } from '../auth.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-admin',
  templateUrl: './adminmanager.component.html',
  styleUrls: ['./adminmanager.component.css']
})
export class AdminManagerComponent implements OnInit {

  adminUsers: Profissional[] = [];
  users: Profissional[] = [];
  user: Profissional;

  datasourceAdmins = new MatTableDataSource();


  displayedColumns = ['nome', 'action']

  constructor(private authService: AuthService,
    //private pessoaService: PessoaService
    private profissionalService: ProfissionalService
    ) { }

  async ngOnInit(): Promise<void> { 

    //Recupero os usuários adminisradores
   await this.authService.listAdmins().subscribe(async users => {
      //console.log(users)
      for(let user of users) {
      //users.forEach(async user => {
        //console.log(user)
        await this.profissionalService.findByEmail(user.email).subscribe(pessoa => {
          //Se o administrador não for um profissional, preencho com os valores do usuário
          if(pessoa[0] == undefined){
            pessoa[0] = new Profissional();
            pessoa[0].nome = user.displayName;
            pessoa[0].email = user.email
          }
          //console.log(pessoa)
          this.adminUsers.push(pessoa[0]);

          this.datasourceAdmins.data = this.adminUsers.sort(function(a, b) {
            if( a.nome > b.nome) {
              return 1;
            }
            if (a.nome < b.nome) {
              return -1;
            }
            return 0;
          });
        })
        //console.log(this.adminUsers); 
      }
    })

    //Recupero as pessoas que podem virar administradores
    this.profissionalService.find().subscribe(users => {
      this.users = users;
      //console.log(users)
    })
  }

  add(){
    this.authService.grantAdminAccess(this.user).subscribe(() => {
      this.adminUsers.push(this.user);
      this.datasourceAdmins.data = this.adminUsers.sort(function(a, b) {
        if( a.nome > b.nome) {
          return 1;
        }
        if (a.nome < b.nome) {
          return -1;
        }
        return 0;
      });
      this.authService.showMessage("Acesso de administrador concedido!")
    });
  }

  delete(user: Profissional){
    //console.log(user)
    this.authService.revokeAdminAccess(user).subscribe(() => {
      this.adminUsers.splice(this.adminUsers.indexOf(user), 1);
      this.datasourceAdmins.data = this.adminUsers;
      this.authService.showMessage("Acesso de administrador revogado!")
    });
  }

}
