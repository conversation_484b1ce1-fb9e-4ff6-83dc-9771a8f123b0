<mat-card> 
    <mat-card-title class="title">{{ profissional.id == undefined ? "Novo Profissional" : profissional.nome }}</mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <!--
                email: string;
                funcao: Funcao[];
                ativo: boolean;
            -->
            <mat-form-field style="width: 60%;">
                <input class="input" matInput placeholder="Nome" 
                    [(ngModel)]="profissional.nome" name="nome" 
                    onfocusout="this.value = this.value.trim()" required>
                <mat-error *ngIf="nome.invalid">Nome é obrigatório.</mat-error>  
            </mat-form-field>
            
            <mat-form-field style="width: 20%;">
                <input class="input" matInput placeholder="Data de Nascimento" 
                    [(ngModel)]="profissional.dataNascimento" name="dataNascimento"
                    [matDatepicker]="picker">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    <mat-error *ngIf="dataNascimento.invalid">Data de nascimento é obrigatória.</mat-error>  
            </mat-form-field>

            <mat-form-field  style="width: 10%;">
                <mat-label>Sexo</mat-label>
                <mat-select class="select" placeholder="Sexo" 
                    [(ngModel)]="profissional.sexo"
                    name="sexo" required>
                    <mat-option value="" >    
                    </mat-option>
                    <mat-option value="M" >
                    Masculino
                    </mat-option>
                    <mat-option value="F" >
                        Feminino
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="sexo.invalid">Sexo é obrigatório</mat-error>  
            </mat-form-field>

            <mat-form-field style="width: 20%;">
                <mat-label>Telefone</mat-label>
                <input class="input" matInput placeholder="Telefone" 
                    [(ngModel)]="profissional.telefone" name="telefone">
            </mat-form-field>
            <mat-form-field style="width: 40%;">
                <mat-label>E-mail</mat-label>
                <input class="input" matInput placeholder="E-mail" type="email" email
                    [(ngModel)]="profissional.email" name="email" required
                    oninput="this.value = this.value.toLowerCase()">
                <mat-error *ngIf="email.invalid">E-mail inválido.</mat-error>  
            </mat-form-field>
            
            <mat-form-field style="width: 100%;">
                <mat-label>
                    Assinatura
                </mat-label>
                <mat-hint align="start">Assinatura a ser utilizada em alguns relatórios.</mat-hint>
                <textarea matInput
                    name="assinatura"
                    rows="6"
                    placeholder="Assinatura" 
                    [(ngModel)]="profissional.assinatura"></textarea>
            </mat-form-field>
        </div>
        <mat-checkbox name="loginUser" [(ngModel)]="profissional.loginUser">Usuário de login</mat-checkbox>
    </form>



    <mat-card class="mat-elevation-z0"  style="margin-top: 10px;">
        <mat-card-title class="subtitle">Funções</mat-card-title>
        <!-- Início da lista de funções -->
        <div>
            <mat-card fxFlex="31" *ngFor="let funcao of profissional.funcao; index as id"
            class="mat-elevation-z0" style="margin: 10px;"
            fxFlexAlign="stretch" fxLayoutGap="20px">
            <mat-card-header> 
                <mat-card-title class="mat-card-title v-middle">
                    <mat-icon style="font-size: 36px; padding: 10px;">
                        psychology
                    </mat-icon>
                    <small style="text-align: left;" _ngcontent-c9>
                        {{funcao.nome}}
                        <a style="padding-left: 10px; color: gray; cursor: pointer;"
                            (click)="deleteFuncao(profissional.funcao.indexOf(funcao))">
                            <mat-icon>delete</mat-icon>
                        </a>
                    </small>
                </mat-card-title>
                </mat-card-header>
            </mat-card>
        </div>
        <!-- Fim da lista de funções-->
        <!-- Início da adição de funções-->
        <mat-form-field  style="width: 15%;">
                <mat-label>Função</mat-label>
                <mat-select placeholder="Função" 
                    [(ngModel)]="funcao"
                    name="funcao">
                    <mat-option *ngFor="let funcao of funcoes" [value]="funcao" (click)="addFuncao()">
                        {{funcao.nome}}
                    </mat-option>
                </mat-select>
        </mat-form-field>
        <!-- Fim da adição de funções-->
    </mat-card> 






    <button mat-raised-button (click)="save()" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>