.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin: .5em;
  }
  
  .label-atrasado {
    background-color: #ff902b;
  }
  
  .label-ok {
    background-color: #27c24c;
  }
  
  .label-aviso {
    background-color: #e2b902;
  }
  
  .objetivo-element-detail {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    width: 100%;
  }
  
  tr.objetivo-detail-row {
    height: 0;
  }



@media (max-width: 700px) {
  table{
    width: 100%;
  }

  .field-plano-intervencao{
    width: 80%; 
  }

  .div-plano-intervencao{
    display: flex; 
    width: 50%;
  }

  .div-nova-coleta{
    display: flex; 
    width: 50%;
  }

  .div-etapas{
    width: 20%;
  }

  .mat-column-index {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-expand {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-etapas {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
  }

  .mat-column-nomeObj {
    flex: 0 0 80% !important;
    width: 80% !important;
    padding-left: 5px;
    text-align: justify;
    overflow-wrap: normal !important;
    word-wrap: normal !important;
  }

  .mat-column-expandedDetail{
    width: 100%;
    overflow-wrap: normal !important;
    text-align: justify;
  }

  .mat-list-item{
    width: 100%;
    overflow-wrap: normal !important;
    word-wrap: normal !important;
    height:max-content!important;
  }

  .mat-list{
    height: max-content!important;
  }

  .div-mat-line{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 100%;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
    padding: 0px !important;
  }

  .div-status-etapa{
    width: 5%;
  }

  .div-dados-etapa{
    width: 100%;
    text-align: left;
    font-size: small;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .div-dados-etapa-detail{
    width: 100%;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    hyphens: auto;
    display: inline-block;
    white-space: pre-wrap !important;
  }

  .div-coleta{
    background-color: lightgray; 
    width: 100% !important;
    text-align: left;
  }

  .div-coleta-table{
    display: table;
    width: 100% !important;
    table-layout: fixed !important;
    max-height: 100px !important;
  }

  .div-coleta-table-row{
    text-align: center; 
    width: 100% !important; 
    display: table-row;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .div-coleta-data-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-profissional-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-data-PIC{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-score-cell{
    display: table-cell;
    width: 10% !important;
    padding: 0px !important;
  }

  .div-coleta-percentual-cell{
    display: table-cell;
    width: 10% !important;
    padding: 0px !important;
  }

  .div-coleta-estimulo-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-independentes-cell {
    display: table-cell;
    width: 40% !important;
    padding: 0px !important;
    text-align: center;
    vertical-align: middle;
  }

  .div-coleta-status-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-status-PIC{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM{
    background-color: lightgray; 
    width: 100% !important;
  }

  .div-coleta-ESDM-table{
    width: inherit !important; 
    display: table;
  }

  .div-coleta-ESDM-table-row{
    text-align: center; 
    width: 100% !important; 
    display: table-row;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .div-coleta-ESDM-data-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM-score-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM-percentual-cell{
    display: table-cell;
    width: 5% !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM-status-cell{
    display: table-cell;
    width: 20% !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM-profissional-cell{
    display: table-cell;
    width: 40% !important;
    padding: 0px !important;
  }
}

@media (min-width: 701px) {
  table{
    width: 100%;
  }

  .field-plano-intervencao{
    width: 30%; 
    padding: 10px;
  }

  .div-plano-intervencao{
    display: flex; 
    width: 70%;
  }

  .div-nova-coleta{
    display: flex; 
    width: 30%;
  }

  .div-etapas{
    width: 20%;
  }

  .mat-column-index {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }
  
  .mat-column-expand {
    flex: 0 0 5% !important;
    width: 5% !important;
    text-align: left;
  }

  .mat-column-tipoSuporte {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .mat-column-nomeObj {
    flex: 0 0 80% !important;
    width: 80% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }
  .mat-column-dominio {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .mat-column-tipoSuporte {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  .mat-column-tipoColeta {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
  }

  
  
  .mat-column-action {
    flex: 0 0 10% !important;
    width: 10% !important;
    text-align: right;
  }

  .div-mat-line{
    width: 100%; 
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    flex: 0 0 100%; 
    padding: 0px; 
  }

  .div-status-etapa{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 5%; 
    background-color: aqua;
  }

  .div-dados-etapa{
    display: flex; 
    flex-direction: row; 
    flex-wrap: wrap; 
    width: 80%; 
    text-align: left;
  }

  .div-dados-etapa-detail{
    width: 100%;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    hyphens: auto;
    display: inline-block;
    white-space: pre-wrap !important;
  }

  .div-coleta{
    background-color: lightgray; 
    width: 100% !important;
    margin-right:auto;margin-left:20px;
    max-width: 600px;
  }

  .div-coleta-table{
    display: table;
    width: 100% !important;
    table-layout: fixed !important;
    max-height: 100px !important;
  }

  .div-coleta-table-row{
    text-align: center; 
    width: 100% !important; 
    display: table-row;
    word-wrap: break-word !important;
    white-space: pre-wrap !important;
    height:initial!important;
  }

  .hidden{
    display: none;
  }

  .center{
    text-align: center;
  }

  .div-coleta-data-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0px !important;
  }

  .div-coleta-data-PIC{
    display: table-cell;
    width: 150px !important;
    padding: 0px !important;
  }

  .div-coleta-score-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
  }

  .div-coleta-percentual-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
  }

  .div-coleta-estimulo-cell{
    display: table-cell;
    width: 40% !important;
    padding: 0px !important;
  }

  .div-coleta-independentes-cell {
    display: table-cell;
    width: 40% !important;
    padding: 0px !important;
    text-align: center;
    vertical-align: middle;
  }

  .div-coleta-status-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0px !important;
  }

  .div-coleta-status-PIC{
    display: table-cell;
    width: 200px !important;
    padding: 0px !important;
  }

  .div-coleta-ESDM{
    background-color: lightgray; 
    width: 50%;
  }

  .div-coleta-ESDM-table{
    display: table;
    width: 100% !important;
    table-layout: fixed !important;
  }

  .div-coleta-ESDM-table-row{
    display: table-row;
    /*text-align: center; */
    width: 100% !important; 
    /*padding: 0px !important;
    table-layout: fixed !important;*/
  }

  .center{
    text-align: center;
  }

  .div-coleta-ESDM-data-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #27c24c;*/
  }

  .div-coleta-ESDM-score-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
    /*min-width: 10%;
    max-width: 10%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #e2b902;*/
  }

  .div-coleta-ESDM-percentual-cell{
    display: table-cell;
    width: 50px !important;
    padding: 0%;
    /*min-width: 10%;
    max-width: 10%;
    text-align: center;
    table-layout: fixed !important;
    background-color: #ff902b;*/
  }

  .div-coleta-ESDM-status-cell{
    display: table-cell;
    width: 100px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: aqua;*/
  }
  .div-coleta-ESDM-profissional-cell{
    display: table-cell;
    width: 150px !important;
    padding: 0%;
    /*min-width: 40%;
    max-width: 40%;
    text-align: center;
    table-layout: fixed !important;
    background-color: aqua;*/
  }

}

.btn-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  border: none;
  background: transparent;
  font-weight: 600;
  font-size: 12px;
  padding: 4px 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.btn-toggle:hover {
  background-color: rgba(0,0,0,0.05);
}

.btn-toggle.closed {
  color: #007bff !important; /* Azul para "Ver mais" */
}

.btn-toggle.opened {
  color: #dc3545 !important; /* Vermelho para "Ver menos" */
}

.btn-toggle.closed {
  color: #007bff;
}

.btn-toggle.opened {
  color: #dc3545;
}

.row{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

  /* DivTable.com */
.divTable{ display: table; width: 100%}
.divTableRow { display: table-row; }
.divTableRow:nth-child(even) {
  background: lightgray;
} 
.divTableHeading { display: table-header-group;}
.divTableCell, .divTableHead { display: table-cell;}
.divTableFoot { display: table-footer-group;}
.divTableBody { display: table-row-group;}