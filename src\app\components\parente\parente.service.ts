import { Parente } from './parente-model';
import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ParenteService {

  parenteUrl = `${environment.API_URL}/parente`;

  public parentes: BehaviorSubject<Parente[]> = 
    new BehaviorSubject<Parente[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  create(parente: Parente): Observable<Parente>{
    //console.log(parente);
    return this.http.post<Parente>(this.parenteUrl, parente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(parente: Parente): Observable<Parente>{
    //console.log(parente);
    return this.http.put<Parente>(this.parenteUrl + '/' + parente.id, parente).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<Parente>{
    return this.http.get<Parente>(this.parenteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByEmail(email: string): Observable<Parente>{
    return this.http.get<Parente>(this.parenteUrl + '/email/' + email).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByUserId(uid: string): Observable<Parente>{
    return this.http.get<Parente>(this.parenteUrl + '/uid/' + uid).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<Parente[]>{
    return this.http.get<Parente[]>(this.parenteUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  delete(id: string): Observable<Parente>{
    return this.http.delete<Parente>(this.parenteUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
