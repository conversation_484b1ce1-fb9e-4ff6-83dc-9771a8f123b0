import { Component, OnInit, ViewChild } from "@angular/core";
import { Estimulo } from "./../estimulo-model";
import { EstimuloService } from "./../estimulo.service";
import { GrupoEstimulo } from "./../grupoestimulo-model";
import { GrupoEstimuloService } from "./../grupoestimulo.service";
import { Observable } from "rxjs";
import { Router, ActivatedRoute } from "@angular/router";
import { Validators, FormControl, NgForm } from "@angular/forms";
import { startWith, map } from "rxjs/operators";

@Component({
  selector: "app-estimulo-create",
  templateUrl: "./estimulo-create.component.html",
  styleUrls: ["./estimulo-create.component.css"],
})
export class EstimuloCreateComponent implements OnInit {
  estimulo: Estimulo = new Estimulo();
  grupos: GrupoEstimulo[] = [];

  categoriaFC = new FormControl("", [Validators.required]);

  //datasource: MatTableDataSource<string>;
  datasource: string[];

  filteredCategorias: Observable<GrupoEstimulo[]>;

  @ViewChild(NgForm) form;

  //Form Controls
  nome = new FormControl("", [Validators.required]);

  constructor(
    private estimuloService: EstimuloService,
    private grupoEstimuloService: GrupoEstimuloService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  async ngOnInit(): Promise<void> {
    let idEstimulo = this.route.snapshot.paramMap.get("id");
    //console.log(idEstimulo)

    this.grupoEstimuloService.find().subscribe((grps) => {
      this.grupos = grps;
      this.filteredCategorias = this.categoriaFC.valueChanges.pipe(
        startWith(""),
        map((value) => (typeof value === "string" ? value : value.nome)),
        map((nome) => (nome ? this._filter(nome) : this.grupos.slice())),
        map((categorias) => categorias.filter(categoria => categoria.ativo))
      );
    });

    if (idEstimulo == undefined) {
      //Create
      this.estimulo = new Estimulo();
      this.estimulo.ativo = true;
    } else {
      //Edit
      await this.estimuloService.findById(idEstimulo).subscribe((estimulo) => {
        // console.log(this.estimulo);
        this.estimulo = estimulo;
      });
    }
  }

  displayFn(grupo: GrupoEstimulo): string {
    return grupo && grupo.nome ? grupo.nome : "";
  }

  private _filter(value: string): GrupoEstimulo[] {
    const filterValue = value.toLowerCase();

    return this.grupos.filter(
      (option) => option.nome.toLowerCase().indexOf(filterValue) === 0
    );
    //return this.parentes.filter(option => option.nome.toLowerCase().includes(filterValue));
  }

  async save(exit: boolean) {
    var categoria: GrupoEstimulo = new GrupoEstimulo();
    // console.log(this.estimulo.grupo.nome);
    //console.log("1")
    if (this.form.valid) {
      //Verifico se a categoria (grupo) de estímulo existe. Caso não exista, crio.
      if (this.estimulo.grupo.nome == undefined) {
        if (this.grupos.find((grp) => grp.nome == (this.estimulo.grupo as unknown as string)
          ) == undefined
        ) {
          categoria.nome = this.estimulo.grupo as unknown as string;
          await this.grupoEstimuloService
            .create(categoria)
            .toPromise()
            .then((id) => {
              //this.estimuloService.showMessage('Categoria de estímulo criada com sucesso!');
              categoria.id = id;
              this.estimulo.grupo = categoria;
            });
        } else {
          categoria = this.grupos.find(
            (grp) => grp.nome == (this.estimulo.grupo as unknown as string)
          );
          this.estimulo.grupo = categoria;
        }
      }
      //console.log("3")
      // console.log(this.estimulo.grupo);
      if (this.estimulo.id == undefined) {
        this.estimuloService.create(this.estimulo).subscribe((id) => {
          // console.log("4")
          this.estimuloService.showMessage("Estímulo criado com sucesso!");
          if (exit) {
            this.router.navigate(["/estimulo"]);
          } else {
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = "reload";
            this.router.navigate(["/estimulo/create"]);
          }
        });
      } else {
        this.estimuloService.update(this.estimulo).subscribe((estimulo) => {
          this.estimuloService.showMessage("Estímulo alterado com sucesso!");
          if (exit) {
            this.router.navigate(["/estimulo"]);
          } else {
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = "reload";
            this.router.navigate(["/estimulo/create"]);
          }
        });
      }
    } else {
      this.estimuloService.showMessage(
        "Existem campos inválidos no formulário!",
        true
      );
    }
  }

  cancel() {
    this.router.navigate(["/estimulo"]);
  }
}
