import { PlanointervencaovbmappGraphComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-graph/planointervencaovbmapp-graph.component';
import { PlanointervencaoGraphComponent } from './components/planointervencao/planointervencao-graph/planointervencao-graph.component';
import { PacientesPorFuncaoComponent } from './components/relatorios/pacientes-por-at/pacientes-por-funcao.component';
import { PlanoIntervencaoResumidoPdfComponent } from './components/planointervencao/relatorio-resumido-pdf/relatorio-resumido-pdf.component';
import { ColetadiariavbmappCreateComponent } from './components/coletadiariavbmapp/coletadiariavbmapp-create/coletadiariavbmapp-create.component';
import { EstimuloCrudComponent } from './views/estimulo-crud/estimulo-crud.component';
import { EstimuloCreateComponent } from './components/estimulo/estimulo-create/estimulo-create.component';
import { PlanointervencaovbmappReadComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-read/planointervencaovbmapp-read.component';
import { PlanointervencaovbmappPdfComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-pdf/planointervencaovbmapp-pdf.component';
import { PlanointervencaovbmappCreateComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-create/planointervencaovbmapp-create.component';
import { ObjetivovbmappCrudComponent } from './views/objetivovbmapp-crud/objetivovbmapp-crud.component';
import { ObjetivovbmappCreateComponent } from './components/objetivovbmapp/objetivovbmapp-create/objetivovbmapp-create.component';
import { VbmappmsassmtCreateComponent } from './components/vbmappmsassmt/vbmappmsassmt-create/vbmappmsassmt-create.component';
import { EventoatendimentoCreateComponent } from './components/eventoatendimento/eventoatendimento-create/eventoatendimento-create.component';
import { EventoatendimentoCrudComponent } from './views/eventoatendimento-crud/eventoatendimento-crud.component';
import { TipoprocedimentoCreateComponent } from './components/tipoprocedimento/tipoprocedimento-create/tipoprocedimento-create.component';
import { TipoprocedimentoCrudComponent } from './views/tipoprocedimento-crud/tipoprocedimento-crud.component';
import { AgendaViewComponent } from './components/agenda/agenda-view/agenda-view.component';
import { ParenteCreateComponent } from './components/parente/parente-create/parente-create.component';
import { ParenteCrudComponent } from './views/parente-crud/parente-crud.component';
import { ParentescoCreateComponent } from './components/parente/parentesco-create/parentesco-create.component';
import { ParentescoCrudComponent } from './views/parentesco-crud/parentesco-crud.component';
import { UserEditComponent } from './components/template/auth/user-edit/user-edit.component';
import { AdministradorGeralComponent } from './components/template/auth/administrador-geral/administrador-geral.component';
import { OrganizacaoCreateComponent } from './components/organizacao/organizacao-create/organizacao-create.component';
import { ObjetivoCreateComponent } from './components/objetivo/objetivo-create/objetivo-create.component';
import { ObjetivoCrudComponent } from './views/objetivo-crud/objetivo-crud.component';
import { ColetadiariaCreateComponent } from './components/coletadiaria/coletadiaria-create/coletadiaria-create.component';
import { FuncaoCreateComponent } from './components/funcao/funcao-create/funcao-create.component';
import { FuncaoCrudComponent } from './views/funcao-crud/funcao-crud.component';
import { ProfissionalCreateComponent } from './components/profissional/profissional-create/profissional-create.component';
import { ProfissionalCrudComponent } from './views/profissional-crud/profissional-crud.component';
import { ProfissionalListComponent } from './components/profissional/profissional-list/profissional-list.component';
import { AdminManagerComponent } from './components/template/auth/adminmanager/adminmanager.component';
import { UserResolver } from './components/template/auth/user/user.resolver';
import { UserComponent } from './components/template/auth/user/user.component';
import { RegisterComponent } from './components/template/auth/register/register.component';
import { AuthGuard } from './components/template/auth/auth.guard';
import { LoginComponent } from './components/template/auth/login/login.component';
import { PlanointervencaoPdfComponent } from './components/planointervencao/planointervencao-pdf/planointervencao-pdf.component';
import { PlanointervencaoReadComponent } from './components/planointervencao/planointervencao-read/planointervencao-read.component';
import { PlanointervencaoCreateComponent } from './components/planointervencao/planointervencao-create/planointervencao-create.component';
import { PacienteEditComponent } from './components/paciente/paciente-edit/paciente-edit.component';
import { PacienteCreateComponent } from './components/paciente/paciente-create/paciente-create.component';
import { PacienteViewComponent } from './components/paciente/paciente-view/paciente-view.component';
import { PacienteReadComponent } from './components/paciente/paciente-read/paciente-read.component';
import { EsdmchecklistCreateComponent } from './components/esdmchecklist/esdmchecklist-create/esdmchecklist-create.component';
import { PacienteCrudComponent } from './views/paciente-crud/paciente-crud.component';
import { ListaEsperaCreateComponent } from './components/listaespera/lista-espera-create/lista-espera-create.component';
import { ListaEsperaEditComponent } from './components/listaespera/lista-espera-edit/lista-espera-edit.component';
import { ListaEsperaCrudComponent } from './views/lista-espera-crud/lista-espera-crud.component';
import { AnamneseCrudComponent } from './views/anamnese-crud/anamnese-crud/anamnese-crud.component';
import { AnamneseCreateComponent } from './components/anamnese/anamnese-create/anamnese-create.component';
import { HomeComponent } from './views/home/<USER>';
import { PacienteViewAnamneseComponent } from './components/paciente/paciente-view-anamnese/paciente-view-anamnese.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AgendaListComponent } from './components/agenda/agenda-list/agenda-list.component';
import { AtendimentoComponent } from './components/atendimento/atendimento.component';
import { StatusagendaCrudComponent } from './views/statusagenda-crud/statusagenda-crud.component';
import { StatusagendaCreateComponent } from './components/statusagenda/statusagenda-create/statusagenda-create.component';
import { LocalCreateComponent } from './components/local/local-create/local-create.component';
import { LocalCrudComponent } from './views/local-crud/local-crud.component';
import { AvaliacaoCreateComponent } from './components/avaliacao/avaliacao-create/avaliacao-create.component';
import { AvaliacaoReadComponent } from './components/avaliacao/avaliacao-read/avaliacao-read.component';
import { VbmappmsassmtReadComponent } from './components/vbmappmsassmt/vbmappmsassmt-read/vbmappmsassmt-read.component';
import { ColetasDiariasReportComponent } from './components/relatorios/coletas-diarias-report/coletas-diarias-report.component';
import { EstimuloCategoryCrudComponent } from './views/estimulo-category-crud/estimulo-category-crud.component';
import { EstimuloCategoryCreateComponent } from './components/estimulo/estimulo-category-create/estimulo-category-create.component';
import { PlanoPICCreateComponent } from './components/planopic/planopic-create/planopic-create.component';
import { PlanoPICPdfComponent } from './components/planopic/planopic-pdf/planopic-pdf.component';
import { PlanoPICResumidoPdfComponent } from './components/planopic/planopic-resumido-pdf/planopic-resumido-pdf.component';
import { PlanoPICGraphComponent } from './components/planopic/planopic-graph/planopic-graph.component';
import { ObjetivoPICCreateComponent } from './components/objetivopic/objetivopic-create/objetivopic-create.component';
import { EsdmchecklistReadComponent } from './components/esdmchecklist/esdmchecklist-read/esdmchecklist-read.component';
import { PlanointervencaovbmappPdfresumidoComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-pdfresumido/planointervencaovbmapp-pdfresumido.component';
import { ColetaDiariaPdfComponent } from './components/relatorios/coleta-diaria-pdf/coleta-diaria-pdf.component';
import { ObjetivocomportamentalCrudComponent } from './views/objetivocomportamental-crud/objetivocomportamental-crud.component';

const routes: Routes = [
{
  path: "",
  component: HomeComponent,
  resolve: { data: UserResolver}
},
{
  path: "home",
  component: HomeComponent,
  resolve: { data: UserResolver}
},
/*{
  path: '', 
  redirectTo: 'login', 
  pathMatch: 'full' 
},*/
{ path: 'login', 
  component: LoginComponent, 
  canActivate: [AuthGuard] 
},
{ 
  path: 'register', 
  component: RegisterComponent, 
  canActivate: [AuthGuard] 
},
{ 
  path: 'user', 
  component: UserComponent,  
  resolve: { data: UserResolver}
},
{
  path: 'listaespera',
  component: ListaEsperaCrudComponent
},
{
  path: 'listaespera/update/:id',
  component: ListaEsperaEditComponent
},
{
  path: 'listaespera/create',
  component: ListaEsperaCreateComponent
},
{
  path: 'useradmins',
  component: AdminManagerComponent , 
  resolve: { data: UserResolver},
},
{
  path: 'admin/users',
  component: AdministradorGeralComponent , 
  resolve: { data: UserResolver},
},
{
  path: 'admin/users/:uid',
  component: UserEditComponent , 
  resolve: { data: UserResolver},
},
{
  path: 'paciente',
  component: PacienteCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'paciente/create',
  component: PacienteCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'paciente/update/:id',
  component: PacienteEditComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'paciente/:id',
  component: PacienteViewComponent, 
  resolve: { data: UserResolver},
  //pathMatch: 'full',
  /*children:[
    {
      path: '',
      component: PlanointervencaoReadComponent,
      outlet: 'esdm_plan'
    },
    {
      path: '',
      component: PlanointervencaovbmappReadComponent,
      outlet: 'vbmapp_plan'
    },
    {
      path: 'planointervencao', 
      component: PlanointervencaoCreateComponent,
      outlet: 'esdm_plan'
    },
    {
      path: 'vbmapp_planointervencao',
      component: PlanointervencaovbmappCreateComponent,
      outlet: 'vbmapp_plan'
    }
  ]*/
},
{
  path: 'paciente/read/:id',
  component: PacienteReadComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'funcao',
  component: FuncaoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'funcao/create',
  component: FuncaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'funcao/:id',
  component: FuncaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo',
  component: EstimuloCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo/create',
  component: EstimuloCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo/category',
  component: EstimuloCategoryCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo/category/create',
  component: EstimuloCategoryCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo/category/:id',
  component: EstimuloCategoryCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'estimulo/:id',
  component: EstimuloCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parentesco',
  component: ParentescoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parentesco/create',
  component: ParentescoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parentesco/:id',
  component: ParentescoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parente',
  component: ParenteCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parente/create',
  component: ParenteCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'parente/:id',
  component: ParenteCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'profissional',
  component: ProfissionalCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'profissional/create',
  component: ProfissionalCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'profissional/:id',
  component: ProfissionalCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'planointervencao/create',
  component: PlanointervencaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic/create',
  component: PlanoPICCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'relatorio/pacientesporfuncao',
  component: PacientesPorFuncaoComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'relatorio/coleta',
  component: ColetasDiariasReportComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'planointervencao/pdf/:id',
  component: PlanointervencaoPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'planointervencao/pdf/resumo/:id',
  component: PlanoIntervencaoResumidoPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic/pdf/:id',
  component: PlanoPICPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic/pdf/resumo/:id',
  component: PlanoPICResumidoPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_planointervencao/evolucao/:id',
  component: PlanointervencaovbmappGraphComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'planointervencao/evolucao/:id',
  component: PlanointervencaoGraphComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic/evolucao/:id',
  component: PlanoPICGraphComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_planointervencao/create',
  component: PlanointervencaovbmappCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_planointervencao/pdf/:id',
  component: PlanointervencaovbmappPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_planointervencaoresumido/pdf/:id',
  component: PlanointervencaovbmappPdfresumidoComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'esdmchecklist/create',
  component: EsdmchecklistCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'esdmchecklist',
  component: EsdmchecklistReadComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'organizacao/create',
  component: OrganizacaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'organizacao/update',
  component: OrganizacaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'objetivo',
  component: ObjetivoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'objetivo/create',
  component: ObjetivoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'objetivo/update/:id',
  component: ObjetivoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_objetivo',
  component: ObjetivovbmappCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_objetivo/create',
  component: ObjetivovbmappCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmapp_objetivo/update/:id',
  component: ObjetivovbmappCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'objetivo_comportamental',
  component: ObjetivocomportamentalCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic_objetivo/create',
  component: ObjetivoPICCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'pic_objetivo/update/:id',
  component: ObjetivoPICCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'tipoprocedimento',
  component: TipoprocedimentoCrudComponent,
  resolve: { data: UserResolver},
},
{
  path: 'tipoprocedimento/create',
  component: TipoprocedimentoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'tipoprocedimento/:id',
  component: TipoprocedimentoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'eventoatendimento',
  component: EventoatendimentoCrudComponent,
  resolve: { data: UserResolver},
},
{
  path: 'eventoatendimento/create',
  component: EventoatendimentoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'eventoatendimento/:id',
  component: EventoatendimentoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'coletadiaria/create',
  component: ColetadiariaCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'coletadiaria/:id',
  component: ColetadiariaCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'coletadiaria_vbmapp/create',
  component: ColetadiariavbmappCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'coletadiaria_vbmapp/:id',
  component: ColetadiariavbmappCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'coletadiaria/pdf/:id',
  component: ColetaDiariaPdfComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmappmsassmt/create',
  component: VbmappmsassmtCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'vbmappmsassmt',
  component: VbmappmsassmtReadComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'agenda',
  component: AgendaViewComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'agenda/lista',
  component: AgendaListComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'status-agenda',
  component: StatusagendaCrudComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'status-agenda/create',
  component: StatusagendaCreateComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'status-agenda/:id',
  component: StatusagendaCreateComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'atendimento',
  component: AtendimentoComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'local-atendimento',
  component: LocalCrudComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'local-atendimento/:id',
  component: LocalCreateComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'local/create',
  component: LocalCreateComponent, 
  //component: EventoatendimentoCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'anamnese',
  component: AnamneseCrudComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'anamnese/create',
  component: AnamneseCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'anamnese/:id',
  component: AnamneseCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'paciente/anamnese/:idPaciente/:idAnamnese',
  component: PacienteViewAnamneseComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'avaliacao/create/:idTipoAvaliacao/paciente/:idPaciente',
  component: AvaliacaoCreateComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'avaliacao/:idAvaliacao',
  component: AvaliacaoReadComponent, 
  resolve: { data: UserResolver},
},
{
  path: 'avaliacao/edit/:idAvaliacao',
  component: AvaliacaoCreateComponent, 
  resolve: { data: UserResolver},
}
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
