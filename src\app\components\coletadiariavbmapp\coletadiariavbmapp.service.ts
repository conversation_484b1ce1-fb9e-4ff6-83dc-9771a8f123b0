import { ColetaDiariaVBMAPP } from './coletadiariavbmapp-model';
import { map, catchError } from 'rxjs/operators';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ColetadiariavbmappService {

  coletadiariaUrl = `${environment.API_URL}/coletadiaria_vbmapp`;
    
    constructor(private snackbar: MatSnackBar,
      private http: HttpClient) { }
  
    showMessage(msg: string, isError: boolean = false) : void{
      this.snackbar.open(msg,'X',{
        duration: 3000,
        horizontalPosition: "right",
        verticalPosition: "top",
        panelClass: isError ? ['msg-error'] : ['msg-success']
      })
    }
  
    errorHandler(e: any): Observable<any>{
      this.showMessage('Ocorreu um erro!', true);
      return EMPTY;
    }    
  
    findById(id: string): Observable<ColetaDiariaVBMAPP>{
      return this.http.get<ColetaDiariaVBMAPP>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPaciente(idPaciente: string): Observable<ColetaDiariaVBMAPP>{
      return this.http.get<ColetaDiariaVBMAPP>(this.coletadiariaUrl + '/paciente/' + idPaciente).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPlanoIntervencao(idPlanoIntervencao: string): Observable<ColetaDiariaVBMAPP[]>{
      return this.http.get<ColetaDiariaVBMAPP[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findLastByPlanoIntervencao(idPlanoIntervencao: string): Observable<ColetaDiariaVBMAPP[]>{
      return this.http.get<ColetaDiariaVBMAPP[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao + '/last' ).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findResumoByPlanoIntervencao(idPlano: string): Observable<any[]> {
      return this.http.get<any[]>(`${this.coletadiariaUrl}/planointervencao/${idPlano}/resumo`).pipe(
        map(res => res),
        catchError(() => EMPTY)
      );
    }

    findResumoByPlanoIntervencaoPeriodo(
      idPlano: string,
      inicio: string,
      fim: string
    ): Observable<any[]> {
      const today = new Date();
      const defaultFim = fim+'T24:00:00.000Z' || today.toISOString();
      const defaultInicio = inicio+'T00:00:00.000Z' || new Date(today.getTime() - 30 * 86400000).toISOString();

      let params = new HttpParams()
        .set('inicio', defaultInicio)
        .set('fim', defaultFim);

      return this.http.get<any[]>(
        `${this.coletadiariaUrl}/planointervencao/${idPlano}/periodo`,
        {
          params,
        }
      ).pipe(
        map(res => res),
        catchError(() => EMPTY)
      );
    }

    findResumoByPlanoIntervencaoPaginado(
      idPlano: string,
      limit: number = 50,
      startAfter?: string
    ): Observable<any[]> {
      let params = new HttpParams().set('limit', limit.toString());

      if (startAfter) {
        params = params.set('startAfter', startAfter);
      }

      return this.http
        .get<any[]>(`${this.coletadiariaUrl}/planointervencao/${idPlano}/resumo/paginado`, { params })
        .pipe(
          map(res => res),
          catchError(() => EMPTY)
        );
    }

    findAllByPlanoIntervencao(idPlanoIntervencao: string): Observable<ColetaDiariaVBMAPP[]>{
      return this.http.get<ColetaDiariaVBMAPP[]>(this.coletadiariaUrl + '/planointervencao/all/' + idPlanoIntervencao).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    findByPlanoIntervencaoData(idPlanoIntervencao: string, data: string): Observable<ColetaDiariaVBMAPP[]>{
      return this.http.get<ColetaDiariaVBMAPP[]>(this.coletadiariaUrl + '/planointervencao/' + idPlanoIntervencao + "/data/" + data).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    find(): Observable<ColetaDiariaVBMAPP[]>{
      return this.http.get<ColetaDiariaVBMAPP[]>(this.coletadiariaUrl).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    // Por podermos ter coletas tradicionais e naturalistas, a Etapa nesse caso
    // é vista como uma generalização de Tipo de Suporte, "etapas" para coletas naturalistas,
    // e Estímulos, "etapas" para coletas traidcionais.
    setStatusEtapaIndividual(coletadiaria: any): void {
      let positivos: number;
      let negativos: number;
      let independentes: number;
      let total: number;

      coletadiaria.objetivos.forEach(objetivo => {
        if (objetivo.habilidades) {
          if(objetivo.tipoColeta == "Naturalista"){ //Coleta Naturalista
            objetivo.tiposSuporte.forEach(etapa => {
              // Verifico se todos os estímulos de um Tipo de Suporte foram
              // Adquiridos. Caso tenham sido, coloco o Tipo de Suporte como Adquirido.
              if (etapa.estimulos.length > 0 && etapa.estimulos.filter(e => e.status == "Adquirido").length == etapa.estimulos.length) {
                etapa.status = "Adquirido"
              } else {
                etapa.status = "Não adquirido"
  
              }
  
              etapa.estimulos.forEach(estimulo => {
                positivos = estimulo.positivos || 0;
                negativos = estimulo.negativos || 0;
                independentes = estimulo.independentes || 0;
                estimulo.percentual = (positivos + independentes) / (negativos + positivos + independentes);
                if((negativos + positivos + independentes) > 0){
                  if(estimulo.percentual > 0.8){
                    estimulo.status = "Adquirido"
                  } else {
                    estimulo.status = "Recusado"
                  }
                } else {
                  estimulo.status = ""
                } 
              })
  
            })
          } 
        } else {
          objetivo.etapa.forEach(etapa => {
            positivos = etapa.periodo.filter(function(p) {
                          return p == "+"
                        }).length;
            negativos = etapa.periodo.filter(function(p) {
                          return p == "-"
                        }).length;
            etapa.positivos = positivos;
            etapa.negativos = negativos;
            etapa.percentual = positivos/(negativos + positivos);
            if((negativos + positivos) > 0){
              if(etapa.percentual > 0.8){
                etapa.status = "Adquirida"
              } else {
                etapa.status = "Recusada"
              }
            } else {
              etapa.status = ""
            }
          })
        }

        /*if(objetivo.etapa.length==0){
          //Se o objetivo não tem etapas trabalhadas, excluo o objetivo
          coletadiaria.objetivos.splice(coletadiaria.objetivos.indexOf(objetivo), 1);
        }*/
      })
    }

    create(coletadiaria: ColetaDiariaVBMAPP): Observable<string>{
      this.setStatusEtapaIndividual(coletadiaria);
      // console.log(coletadiaria);
      return this.http.post<string>(this.coletadiariaUrl, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
  
    update(coletadiaria: ColetaDiariaVBMAPP): Observable<ColetaDiariaVBMAPP>{
      this.setStatusEtapaIndividual(coletadiaria);
      // console.log(coletadiaria);
      return this.http.put<ColetaDiariaVBMAPP>(this.coletadiariaUrl + "/" + coletadiaria.id, coletadiaria).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }

    delete(id: string): Observable<ColetaDiariaVBMAPP>{
      return this.http.delete<ColetaDiariaVBMAPP>(this.coletadiariaUrl + '/' + id).pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      );
    }
}
