import { PlanoIntervencaoResumidoPdfComponent } from './components/planointervencao/relatorio-resumido-pdf/relatorio-resumido-pdf.component';
import { PersonalPipesModule } from './shared/personal-pipes.module';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';
import { CalendarModule, DateAdapter } from 'angular-calendar';
import { DemoUtilsModule } from './components/agenda/demo-utils/module';
import { TextMaskModule } from 'angular2-text-mask';
import { ngxLoadingAnimationTypes, NgxLoadingModule } from 'ngx-loading';
//import { DemoUtilsModule } from '../demo-utils/module';
//import { DemoComponent } from './component';

import { ObjetivoCrudComponent } from './views/objetivo-crud/objetivo-crud.component';
import { BrowserModule } from '@angular/platform-browser';
import { NgModule, LOCALE_ID, DEFAULT_CURRENCY_CODE } from '@angular/core';
import localePt from '@angular/common/locales/pt';

import { FooterComponent } from './components/template/footer/footer.component';
import { NavComponent } from './components/template/nav/nav.component';
import { HomeComponent } from './views/home/<USER>';
import { HeaderComponent } from './components/template/header/header.component';
import { DeleteConfirmDialogComponent } from './components/template/delete-confirm-dialog/delete-confirm-dialog.component';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { AngularFireModule } from '@angular/fire';
import { AngularFirestoreModule } from '@angular/fire/firestore';
import { AngularFireAuthModule } from '@angular/fire/auth';
import { environment } from '../environments/environment';


import {MatToolbarModule} from '@angular/material/toolbar';
import {MatSidenavModule} from '@angular/material/sidenav';
import {MatListModule} from '@angular/material/list';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatSnackBarModule} from '@angular/material/snack-bar';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MatFormFieldModule} from  '@angular/material/form-field';
import {MatInputModule} from  '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule} from '@angular/material/expansion';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule, } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core'
import { MatRadioModule } from '@angular/material/radio';
import { MatTabsModule } from '@angular/material/tabs';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ChartsModule } from 'ng2-charts';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {MatGridListModule} from '@angular/material/grid-list';
import {MatChipsModule} from '@angular/material/chips';
import {MatBadgeModule} from '@angular/material/badge';




import { AuthInterceptor } from './components/template/auth/auth.interceptor';

import { ListaEsperaCrudComponent } from './views/lista-espera-crud/lista-espera-crud.component';
import { ListaEsperaEditComponent } from './components/listaespera/lista-espera-edit/lista-espera-edit.component';
import { ListaEsperaListComponent } from './components/listaespera/lista-espera-list/lista-espera-list.component';
import { ListaEsperaCreateComponent } from './components/listaespera/lista-espera-create/lista-espera-create.component';
import { ListaEsperaFilterComponent } from './components/listaespera/lista-espera-filter/lista-espera-filter.component';
import { PacienteCrudComponent } from './views/paciente-crud/paciente-crud.component';
import { PacienteListComponent } from './components/paciente/paciente-list/paciente-list.component';
import { EsdmchecklistCreateComponent } from './components/esdmchecklist/esdmchecklist-create/esdmchecklist-create.component';
import { PacienteViewComponent } from './components/paciente/paciente-view/paciente-view.component';
import { PacienteReadComponent } from './components/paciente/paciente-read/paciente-read.component';
import { EsdmchecklistReadComponent } from './components/esdmchecklist/esdmchecklist-read/esdmchecklist-read.component';
import { FilterDominioPipe } from './shared/filterdominio-pipe';
import { CNPJPipe } from './shared/cnpj-pipe';
import { BooleanPipe } from './shared/boolean.pipe';
import { EsdmchecklistGraphsComponent } from './components/esdmchecklist/esdmchecklist-graphs/esdmchecklist-graphs.component';
import { PacienteCreateComponent } from './components/paciente/paciente-create/paciente-create.component';
import { PacienteEditComponent } from './components/paciente/paciente-edit/paciente-edit.component';
import { PlanointervencaoCreateComponent } from './components/planointervencao/planointervencao-create/planointervencao-create.component';
import { PlanointervencaoReadComponent } from './components/planointervencao/planointervencao-read/planointervencao-read.component';
import { PlanointervencaoPdfComponent } from './components/planointervencao/planointervencao-pdf/planointervencao-pdf.component';
import { LoginComponent } from './components/template/auth/login/login.component';
import { RegisterComponent } from './components/template/auth/register/register.component';
import { UserComponent } from './components/template/auth/user/user.component';
import { UserResolver } from './components/template/auth/user/user.resolver';
import { AuthGuard } from './components/template/auth/auth.guard';
import { AuthService } from './components/template/auth/auth.service';
import { UserService } from './components/template/auth/user.service';
import { NotaComponent } from './components/nota/nota.component';
import { AdminManagerComponent } from './components/template/auth/adminmanager/adminmanager.component';
import { ProfissionalListComponent } from './components/profissional/profissional-list/profissional-list.component';
import { ProfissionalCrudComponent } from './views/profissional-crud/profissional-crud.component';
import { ProfissionalCreateComponent } from './components/profissional/profissional-create/profissional-create.component';
import { FuncaoListComponent } from './components/funcao/funcao-list/funcao-list.component';
import { FuncaoCreateComponent } from './components/funcao/funcao-create/funcao-create.component';
import { FuncaoCrudComponent } from './views/funcao-crud/funcao-crud.component';
import { ColetadiariaCreateComponent } from './components/coletadiaria/coletadiaria-create/coletadiaria-create.component';
import { ColetadiariaReadComponent } from './components/coletadiaria/coletadiaria-read/coletadiaria-read.component';
import { ObjetivoListComponent } from './components/objetivo/objetivo-list/objetivo-list.component';
import { ObjetivoCreateComponent } from './components/objetivo/objetivo-create/objetivo-create.component';
import { OrganizacaoCreateComponent } from './components/organizacao/organizacao-create/organizacao-create.component';
import { AdministradorGeralComponent } from './components/template/auth/administrador-geral/administrador-geral.component';
import { UserEditComponent } from './components/template/auth/user-edit/user-edit.component';
import { ParentescoListComponent } from './components/parente/parentesco-list/parentesco-list.component';
import { ParentescoCrudComponent } from './views/parentesco-crud/parentesco-crud.component';
import { ParentescoCreateComponent } from './components/parente/parentesco-create/parentesco-create.component';
import { ParenteCrudComponent } from './views/parente-crud/parente-crud.component';
import { ParenteListComponent } from './components/parente/parente-list/parente-list.component';
import { ParenteCreateComponent } from './components/parente/parente-create/parente-create.component';
import { PacienteFilterComponent } from './components/paciente/paciente-filter/paciente-filter.component';
import { AgendaViewComponent } from './components/agenda/agenda-view/agenda-view.component';
import { CommonModule, DatePipe, registerLocaleData } from '@angular/common';
import { TipoprocedimentoListComponent } from './components/tipoprocedimento/tipoprocedimento-list/tipoprocedimento-list.component';
import { TipoprocedimentoCreateComponent } from './components/tipoprocedimento/tipoprocedimento-create/tipoprocedimento-create.component';
import { TipoprocedimentoCrudComponent } from './views/tipoprocedimento-crud/tipoprocedimento-crud.component';
import { EventoatendimentoListComponent } from './components/eventoatendimento/eventoatendimento-list/eventoatendimento-list.component';
import { EventoatendimentoCreateComponent } from './components/eventoatendimento/eventoatendimento-create/eventoatendimento-create.component';
import { EventoatendimentoCrudComponent } from './views/eventoatendimento-crud/eventoatendimento-crud.component';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { VbmappmsassmtReadComponent } from './components/vbmappmsassmt/vbmappmsassmt-read/vbmappmsassmt-read.component';
import { VbmappmsassmtCreateComponent } from './components/vbmappmsassmt/vbmappmsassmt-create/vbmappmsassmt-create.component';
import { VbmappmsassmtGraphsComponent } from './components/vbmappmsassmt/vbmappmsassmt-graphs/vbmappmsassmt-graphs.component';
import { ObjetivovbmappListComponent } from './components/objetivovbmapp/objetivovbmapp-list/objetivovbmapp-list.component';
import { ObjetivovbmappCreateComponent } from './components/objetivovbmapp/objetivovbmapp-create/objetivovbmapp-create.component';
import { PlanointervencaovbmappReadComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-read/planointervencaovbmapp-read.component';
import { PlanointervencaovbmappCreateComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-create/planointervencaovbmapp-create.component';
import { PlanointervencaovbmappPdfComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-pdf/planointervencaovbmapp-pdf.component';
import { ObjetivovbmappCrudComponent } from './views/objetivovbmapp-crud/objetivovbmapp-crud.component';
import { EstimuloListComponent } from './components/estimulo/estimulo-list/estimulo-list.component';
import { EstimuloCreateComponent } from './components/estimulo/estimulo-create/estimulo-create.component';
import { EstimuloCrudComponent } from './views/estimulo-crud/estimulo-crud.component';
import { ColetadiariavbmappReadComponent } from './components/coletadiariavbmapp/coletadiariavbmapp-read/coletadiariavbmapp-read.component';
import { ColetadiariavbmappCreateComponent } from './components/coletadiariavbmapp/coletadiariavbmapp-create/coletadiariavbmapp-create.component';
import { PacientesPorFuncaoComponent } from './components/relatorios/pacientes-por-at/pacientes-por-funcao.component';
import { AnamneseCrudComponent } from './views/anamnese-crud/anamnese-crud/anamnese-crud.component';
import { AnamneseListComponent } from './components/anamnese/anamnese-list/anamnese-list.component';
import { AnamneseCreateComponent } from './components/anamnese/anamnese-create/anamnese-create.component';
import { PacienteViewAnamneseComponent } from './components/paciente/paciente-view-anamnese/paciente-view-anamnese.component';
import { AnamneseRelatorioComponent } from './components/relatorios/anamnese/anamnese-relatorio/anamnese-relatorio.component';
import { AgendaListComponent } from './components/agenda/agenda-list/agenda-list.component';
import { AtendimentoComponent } from './components/atendimento/atendimento.component';
import { StatusagendaCrudComponent } from './views/statusagenda-crud/statusagenda-crud.component';
import { StatusagendaListComponent } from './components/statusagenda/statusagenda-list/statusagenda-list.component';
import { StatusagendaCreateComponent } from './components/statusagenda/statusagenda-create/statusagenda-create.component';
import { LocalCreateComponent } from './components/local/local-create/local-create.component';
import { LocalListComponent } from './components/local/local-list/local-list.component';
import { LocalCrudComponent } from './views/local-crud/local-crud.component';
import { ConfirmDialogComponent } from './components/template/confirm-dialog/confirm-dialog.component';
import { TimeLineAtendimentosComponent } from './components/atendimento/time-line/time-line-atendimentos/time-line-atendimentos.component';
import { UserOrganizacoesService } from './components/template/auth/user-organizacoes-service';
import { PlanointervencaoGraphComponent } from './components/planointervencao/planointervencao-graph/planointervencao-graph.component';
import { PlanointervencaovbmappGraphComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-graph/planointervencaovbmapp-graph.component';
import { AvaliacaoListComponent } from './components/avaliacao/avaliacao-list/avaliacao-list.component';
import { AvaliacaoCreateComponent } from './components/avaliacao/avaliacao-create/avaliacao-create.component';
import { AvaliacaoReadComponent } from './components/avaliacao/avaliacao-read/avaliacao-read.component';
import { AvaliacaoGraphsComponent } from './components/avaliacao/avaliacao-graphs/avaliacao-graphs.component';
import { ColetasDiariasReportComponent } from './components/relatorios/coletas-diarias-report/coletas-diarias-report.component';
import { EstimuloCategoryListComponent } from './components/estimulo/estimulo-category-list/estimulo-category-list.component';
import { EstimuloCategoryCreateComponent } from './components/estimulo/estimulo-category-create/estimulo-category-create.component';
import { EstimuloCategoryCrudComponent } from './views/estimulo-category-crud/estimulo-category-crud.component';
import { SanitizePipe } from './shared/sanitize.pipe';
import { SecondsFormatPipe } from './shared/seconds-format.pipe';
import { PlanoPICReadComponent } from './components/planopic/planopic-read/planopic-read.component';
import { PlanoPICCreateComponent } from './components/planopic/planopic-create/planopic-create.component';
import { PlanoPICGraphComponent } from './components/planopic/planopic-graph/planopic-graph.component';
import { PlanoPICResumidoPdfComponent } from './components/planopic/planopic-resumido-pdf/planopic-resumido-pdf.component';
import { PlanoPICPdfComponent } from './components/planopic/planopic-pdf/planopic-pdf.component';
import { ObjetivoPICCreateComponent } from './components/objetivopic/objetivopic-create/objetivopic-create.component';
import { ObjetivovbmappFilterComponent } from './components/objetivovbmapp/objetivovbmapp-filter/objetivovbmapp-filter.component';
import { AvaliacoespdfComponent } from './components/relatorios/avaliacoespdf/avaliacoespdf.component';
import { LoadingComponent } from './components/loading/loading.component';
import { AvaliacaovbmapppdfComponent } from './components/relatorios/avaliacaovbmapppdf/avaliacaovbmapppdf.component';
import { EsdmchecklistpdfComponent } from './components/relatorios/esdmchecklistpdf/esdmchecklistpdf.component';
import { TimerComponent } from './shared/timer/timer.component';
import { CountComponent } from './shared/count/count.component';
import { AlertDialogComponent } from './components/template/alert-dialog/alert-dialog.component';
import { PlanointervencaovbmappPdfresumidoComponent } from './components/planointervencaovbmapp/planointervencaovbmapp-pdfresumido/planointervencaovbmapp-pdfresumido.component';
import { ColetaDiariaPdfComponent } from './components/relatorios/coleta-diaria-pdf/coleta-diaria-pdf.component';
import { NotaEditDialogComponent } from './components/nota/nota-edit/nota-edit-dialog/nota-edit-dialog.component';
import { LoadingService } from './shared/service/loading.service';
import { LoadingInterceptor } from './shared/interceptors/loading.interceptor';
import { ConfirmDialogCustomComponent } from './components/template/confirm-dialog-custom/confirm-dialog-custom.component';
import { ObjetivopicListComponent } from './components/objetivopic/objetivopic-list/objetivopic-list.component';
import { ObjetivocomportamentalCrudComponent } from './views/objetivocomportamental-crud/objetivocomportamental-crud.component';
import { ObjetivopicFilterComponent } from './components/objetivopic/objetivopic-filter/objetivopic-filter.component';
import { DialogSelectComponent } from './components/template/dialog-select/dialog-select.component';
//import { StatusagendaCrudComponent } from './views/statusagenda-crud/statusagenda-crud.component';
registerLocaleData(localePt);

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    NavComponent,
    HomeComponent,
    DeleteConfirmDialogComponent,
    ListaEsperaCrudComponent,
    ListaEsperaEditComponent,
    ListaEsperaListComponent,
    ListaEsperaCreateComponent,
    ListaEsperaFilterComponent,
    PacienteCrudComponent,
    PacienteListComponent,
    EsdmchecklistCreateComponent,
    PacienteViewComponent,
    PacienteReadComponent,
    EsdmchecklistReadComponent,
    FilterDominioPipe,
    CNPJPipe,
    BooleanPipe,
    EsdmchecklistGraphsComponent,
    PacienteCreateComponent,
    PacienteEditComponent,
    PlanointervencaoCreateComponent,
    PlanointervencaoReadComponent,
    PlanointervencaoPdfComponent,
    LoginComponent,
    RegisterComponent,
    UserComponent,
    NotaComponent,
    AdminManagerComponent,
    ProfissionalListComponent,
    ProfissionalCrudComponent,
    ProfissionalCreateComponent,
    FuncaoListComponent,
    FuncaoCreateComponent,
    FuncaoCrudComponent,
    ColetadiariaCreateComponent,
    ColetadiariaReadComponent,
    ObjetivoListComponent,
    ObjetivoCrudComponent,
    ObjetivoCreateComponent,
    OrganizacaoCreateComponent,
    AdministradorGeralComponent,
    UserEditComponent,
    ParentescoListComponent,
    ParentescoCrudComponent,
    ParentescoCreateComponent,
    ParenteCrudComponent,
    ParenteListComponent,
    ParenteCreateComponent,
    PacienteFilterComponent,
    AgendaViewComponent,
    AgendaViewComponent,
    TipoprocedimentoListComponent,
    TipoprocedimentoCreateComponent,
    TipoprocedimentoCrudComponent,
    EventoatendimentoListComponent,
    EventoatendimentoCreateComponent,
    EventoatendimentoCrudComponent,
    VbmappmsassmtReadComponent,
    VbmappmsassmtCreateComponent,
    VbmappmsassmtGraphsComponent,
    ObjetivovbmappListComponent,
    ObjetivovbmappCreateComponent,
    PlanointervencaovbmappReadComponent,
    PlanointervencaovbmappCreateComponent,
    PlanointervencaovbmappPdfComponent,
    ObjetivovbmappCrudComponent,
    EstimuloListComponent,
    EstimuloCreateComponent,
    EstimuloCrudComponent,
    ColetadiariavbmappReadComponent,
    ColetadiariavbmappCreateComponent,
    PlanoIntervencaoResumidoPdfComponent,
    PacientesPorFuncaoComponent,
    AnamneseCrudComponent,
    AnamneseListComponent,
    AnamneseCreateComponent,
    PacienteViewAnamneseComponent,
    AnamneseRelatorioComponent,
    AgendaListComponent,
    AtendimentoComponent,
    StatusagendaCrudComponent,
    StatusagendaListComponent,
    StatusagendaCreateComponent,
    LocalCreateComponent,
    LocalListComponent,
    LocalCrudComponent,
    ConfirmDialogComponent,
    TimeLineAtendimentosComponent,
    PlanointervencaoGraphComponent,
    PlanointervencaovbmappGraphComponent,
    AvaliacaoListComponent,
    AvaliacaoCreateComponent,
    AvaliacaoReadComponent,
    AvaliacaoGraphsComponent,
    ColetasDiariasReportComponent,
    EstimuloCategoryListComponent,
    EstimuloCategoryCreateComponent,
    EstimuloCategoryCrudComponent,
    SanitizePipe,
    SecondsFormatPipe,
    PlanoPICReadComponent,
    PlanoPICCreateComponent,
    PlanoPICGraphComponent,
    PlanoPICResumidoPdfComponent,
    PlanoPICPdfComponent,
    ObjetivoPICCreateComponent,
    ObjetivovbmappFilterComponent,
    AvaliacoespdfComponent,
    LoadingComponent,
    AvaliacaovbmapppdfComponent,
    EsdmchecklistpdfComponent,
    CountComponent,
    TimerComponent,
    AlertDialogComponent,
    PlanointervencaovbmappPdfresumidoComponent,
    ColetaDiariaPdfComponent,
    NotaEditDialogComponent,
    ConfirmDialogCustomComponent,
    ObjetivopicListComponent,
    ObjetivocomportamentalCrudComponent,
    ObjetivopicFilterComponent,
    DialogSelectComponent,
  ],
  exports: [AgendaViewComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatCardModule,
    MatButtonModule,
    MatSnackBarModule,
    HttpClientModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatDialogModule,
    MatExpansionModule,
    MatSelectModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatTabsModule,
    MatGridListModule,
    MatChipsModule,
    MatBadgeModule,
    NgbModule,
    ChartsModule,
    MatCheckboxModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    AngularFireModule.initializeApp(environment.firebase),
    AngularFirestoreModule, 
    AngularFireAuthModule,
    MatTooltipModule,
    MatAutocompleteModule,
    MatSlideToggleModule,
    CommonModule,
    TextMaskModule,
    PersonalPipesModule,
    MatProgressSpinnerModule,
    CalendarModule.forRoot({
      provide: DateAdapter,
      useFactory: adapterFactory,
    }),
    NgxLoadingModule.forRoot({
        animationType : ngxLoadingAnimationTypes.wanderingCubes,
        primaryColour : '#019fc7 ', 
        SecondColour : '#8dd8f8', 
        fullScreenBackdrop: true 
    }),
    DemoUtilsModule,
    NgxMaterialTimepickerModule.setLocale("pt-BR")
  ],
  providers: [
    {
      provide : HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi   : true,
    },
    { provide: LOCALE_ID, useValue: 'pt' },
    {
      provide:  DEFAULT_CURRENCY_CODE,
      useValue: 'BRL'
    },
    LoadingService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LoadingInterceptor,
      multi: true
    },
    ListaEsperaListComponent, AuthService, UserService, UserResolver, AuthGuard, UserOrganizacoesService, DatePipe
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
