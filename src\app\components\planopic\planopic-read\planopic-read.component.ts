import { LoadingService } from './../../../shared/service/loading.service';
import { Profissional } from './../../profissional/profissional-model';
import { PICService } from './../pic.service';
import { PacienteService } from './../../paciente/paciente.service';
import { AuthService } from './../../template/auth/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { Observable } from 'rxjs';
import { Paciente } from './../../paciente/paciente-model';
import { PIC } from './../pic-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { Component, OnInit, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { TipoAvaliacao } from '../../avaliacao/tipo-avaliacao-model';
import { TipoAvaliacaoService } from '../../avaliacao/tipo-avaliacao.service';
import { ConfirmDialogCustomComponent, ConfirmDialogCustomData } from '../../template/confirm-dialog-custom/confirm-dialog-custom.component';

@Component({
  selector: 'app-planopic-read',
  templateUrl: './planopic-read.component.html',
  styleUrls: ['./planopic-read.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})

export class PlanoPICReadComponent implements OnInit {
  public pic: PIC = new PIC();
  
  public idPaciente: string;

  public loading: boolean = true;

  public PICs: PIC[] = [];

  @Input()
  $pacienteSearch: Observable<Paciente>;
  
  @Input()
  $idPICPlanSearch: string;

  public paciente: Paciente = new Paciente();
  public tiposAvaliacoes: TipoAvaliacao[] = [];

  public hasAccessCreate: boolean;
  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessRead: boolean;

  datasourceObjetivos = new MatTableDataSource();
  datasourceMarcos = new MatTableDataSource();

  displayedColumnsObjs = ['index', 'nomeCompAlvo', 'defOp', 'tipoColeta']

  constructor(private picService: PICService,
    private pacienteService: PacienteService,
    private tipoAvaliacaoService: TipoAvaliacaoService,
    public authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private loadingService: LoadingService) { }

  public isTableExpanded = false;

  async ngOnInit(): Promise<void> {
    this.loadingService.show();
    try {
      this.idPaciente = this.route.snapshot.paramMap.get('id');
      this.tipoAvaliacaoService.find().subscribe(tipos => {
        this.tiposAvaliacoes = tipos.filter(ta => ta.ativo == true);
      });
  
      this.pacienteService.findById(this.idPaciente).subscribe(paciente => {
        this.paciente = paciente;

        this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','create');
        this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','update');
        this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','delete');
        this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de PIC','read');
        
        //Carrego os planos de intervenção do paciente
        this.picService.findResumoByPaciente(this.idPaciente).subscribe(async planos => {
          this.PICs = planos.filter(p => p.status !== false);
          
          //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
          if(this.PICs.length > 0){
            if(this.$idPICPlanSearch == undefined){
              await this.findPICSelecionado(this.PICs[0].id);
            } else {
              await this.findPICSelecionado(this.PICs.find(plans => plans.id == this.$idPICPlanSearch).id);
            }
          }
        })
      })
    } catch (error) {
      console.error("Erro ao carregar os dados:", error);
    } finally {
      this.loadingService.hide();
    }
  }

  async findPICSelecionado(idPIC: string){
    this.pic = await this.picService.findById(idPIC).toPromise();

    if (this.pic?.ativo == undefined) {
      this.pic.ativo = true;
      this.save();
    }

    let idx = this.PICs?.findIndex(p => p.id === idPIC);
    this.PICs[idx] = this.pic;

    this.datasourceObjetivos.data = this.pic.objetivos;
  }
 
  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid)?.funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }
    return funcoes;
  }

  save(): void{
    if(this.pic.id == undefined){
      this.picService.create(this.pic).subscribe((id) => {
        this.pic.id = id;
      });
    } else {
      this.picService.update(this.pic).subscribe((paciente) => {
      });
    } 
  }

  saveToPDF(){
    this.loadingService.reset();
    this.router.navigate([]).then(result => {
      window.open('pic/pdf/' + this.pic.id + "?hasFullView=true",'_blank');
    })
  }

  edit(){
    this.loadingService.reset();
    this.router.navigate(['/pic/create', {   
      idPaciente: this.idPaciente, 
      idPICPlan: this.pic.id
    }])
  }

  graph(){
    this.loadingService.reset();
    window.open('pic/evolucao/' + this.pic.id + "?hasFullView=true",'_blank');
  }

  add(){  
    this.loadingService.reset();
    const data: ConfirmDialogCustomData = {
      message: 'Você deseja criar um novo PIC ou adicionar/editar um comportamento do PIC atual?',
      options: [
        'Criar um novo PIC',
        'Adicionar comportamentos ao PIC atual',
        'Cancelar'
      ]
    };

    const dialogRef = this.dialog.open(ConfirmDialogCustomComponent, {
      width: 'auto',
      data
    });

    dialogRef.afterClosed().subscribe((selected: string) => {
      switch (selected) {
        case 'Criar um novo PIC':
          this.router.navigate(['/pic/create', {
            idPaciente: this.idPaciente
          }]);
          break;

        case 'Adicionar comportamentos ao PIC atual':
          this.edit();
          break;

        default:
          break;
      }
    });
  }

  async delete(id:string){
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '250px',
      data: {
        msg: 'Tem certeza que deseja excluir o PIC?'
      }
    });

    await dialogRef.afterClosed().subscribe(async result => {
      if(result){

        this.loading = true;
        this.pic = this.PICs.find(p => p.id == id);
        this.pic.status = false;
        this.picService.update(this.pic).subscribe(
          () => {
            this.picService.showMessage('Plano excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/paciente/'+this.paciente.id, {   
              tab: 'pic'
            }])
            this.loading = false;
          }
        );
      }
    }) 
  }
}
