import { environment } from '../../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserOrganizacoesModel } from './user-organizacoes-model';
import { BehaviorSubject, Observable, EMPTY } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})

//TODO: Não permitir userOrganizacoes com o mesmo nome

export class UserOrganizacoesService {

  userOrganizacoesUrl = `${environment.API_URL}/user-organizacoes`;
  
  public userOrganizacoes: BehaviorSubject<UserOrganizacoesModel[]> = 
    new BehaviorSubject<UserOrganizacoesModel[]>([]);

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    console.log(e)
    return EMPTY;
  }

  create(userOrganizacoes: UserOrganizacoesModel): Observable<UserOrganizacoesModel>{
    //console.log(userOrganizacoes);
    return this.http.post<UserOrganizacoesModel>(this.userOrganizacoesUrl, userOrganizacoes).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  update(userOrganizacoes: UserOrganizacoesModel): Observable<UserOrganizacoesModel>{
    // console.log(userOrganizacoes);
    return this.http.put<UserOrganizacoesModel>(this.userOrganizacoesUrl + "/" + userOrganizacoes.id, userOrganizacoes).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findById(id: string): Observable<UserOrganizacoesModel>{
    return this.http.get<UserOrganizacoesModel>(this.userOrganizacoesUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  find(): Observable<UserOrganizacoesModel[]>{
    return this.http.get<UserOrganizacoesModel[]>(this.userOrganizacoesUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );

  }

  delete(id: string): Observable<UserOrganizacoesModel>{
    return this.http.delete<UserOrganizacoesModel>(this.userOrganizacoesUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }

  findByUser(idUser: string): Observable<UserOrganizacoesModel[]>{
    return this.http.get<UserOrganizacoesModel>(this.userOrganizacoesUrl + '/user/' + idUser).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }


}