
import { CalendarEvent } from 'angular-calendar';
import {  EventColor, EventAction } from 'calendar-utils';
import { Atendimento } from './atendimento-model'

export class CalendarEventImp implements CalendarEvent<Atendimento>{
    id?: string;

    start: Date;
    end: Date;
    title: string;
    color: EventColor;
    actions: EventAction[];
    allDay: boolean;
    cssClass: string;
    resizable: Resizable;
    draggable: boolean;
    meta: Atendimento;
    idAtendRecorrencia?: string;

    constructor(){
        this.start = new Date();
        this.end = new Date();
        this.meta = new Atendimento();
        this.resizable = new Resizable();
        this.color = new EventColorImp();
    }


}

export class EventColorImp {
    primary: string;
    secondary: string;

    constructor(){
        this.primary = '#ad2121';
        this.secondary = '#FAE3E3';
    }
}

export class Resizable{
    beforeStart: boolean;
    afterEnd: boolean

    constructor(){
        this.afterEnd = true;
        this.beforeStart = true;
    }
}
