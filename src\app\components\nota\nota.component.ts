import { AuthService } from './../template/auth/auth.service';
import { UserService } from './../template/auth/user.service';
import { HeaderService } from './../template/header/header.service';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { FormControl, Validators, NgForm } from '@angular/forms';
import { Nota } from './nota-model';
import { NotaService } from './nota.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { Paciente } from './../paciente/paciente-model';
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { Profissional } from '../profissional/profissional-model';
import { FirebaseUserModel } from '../template/auth/user-model';
import { DeleteConfirmDialogComponent } from '../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { NotaEditDialogComponent } from './nota-edit/nota-edit-dialog/nota-edit-dialog.component';

@Component({
  selector: 'app-nota',
  templateUrl: './nota.component.html',
  styleUrls: ['./nota.component.css'],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class NotaComponent implements OnInit {

  @Input()
  $pacienteSearch: Observable<Paciente>;

  paciente: Paciente = new Paciente();

  public notas: Nota[] = [];

  public nota: Nota = new Nota();

  public hasAccessCreate: boolean = false;
  public hasAccessDelete: boolean = false;
  public hasAccessRead: boolean = false;

  //Form Controls
  titulo = new FormControl('', [Validators.required]);
  data = new FormControl('', [Validators.required]);
  nivelAcesso = new FormControl('', [Validators.required]);
  descricao = new FormControl('', [Validators.required]);

  @ViewChild(NgForm) form;

  constructor(private route: ActivatedRoute,
    //private pacienteService: PacienteService,
    private notaService: NotaService,
    private userService: UserService,
    public authService: AuthService,
    public dialog: MatDialog,
    //private headerService: HeaderService//,
    //private router: Router
    ) { }

  ngOnInit(): void {
    //Carregando o paciente
    this.$pacienteSearch.subscribe(paciente => {
      //Setando o paciente
      this.paciente = paciente;
      this.nota.idPaciente = this.paciente.id;

      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Registro de notas','create')
      this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Registro de notas','delete')
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Registro de notas','read')

      //Setando os dados do autor
      this.nota.profissional = new Profissional();
      this.userService.getCurrentUser().then(user => {
        this.nota.profissional.nome = user.displayName;
        this.nota.profissional.uid = user.uid;
      }); 

      //Carregando as notas do paciente
      //console.log(this.paciente.id)
      this.notaService.find(this.paciente.id).subscribe(async ns => {
        this.notas = ns;
        await this.organizeNotas();
      })

      //Setando a data atual para novos comentários
      this.nota.data = new Date(new Date().getFullYear(),new Date().getMonth() , new Date().getDate());
    });

  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

    // console.log(funcoes)


    return funcoes;
  }

  setNota(){
    //Setando o paciente
    this.nota.idPaciente = this.paciente.id;

    //Setando os dados do autor
      this.userService.getCurrentUser().then(user => {
        this.nota.profissional.nome = user.displayName;
        this.nota.profissional.uid = user.uid;
      }); 
  }

  isTeamMember() {
    const user = this.authService.getUser();
    
    if (!user) {
      console.error('User not found');
      return false;
    }

    if (!this.paciente || !this.paciente.equipe) {
      // console.warn('Paciente ou equipe não definidos');
      return user.admin || user.gsupervisor;
    }

    if (this.paciente.equipe.length === 0) {
      // console.warn('Equipe do paciente está vazia');
      return user.admin || user.gsupervisor;
    }

    const isInTeam = this.paciente.equipe.some(prof => prof.uid === user.uid);
    const isAdmin = user.admin;
    const isGSupervisor = user.gsupervisor;

    return isInTeam || isAdmin || isGSupervisor;
  }

  changeNivelAcesso(nota: Nota): void {
    if (nota.nivelAcesso == 0 || nota.nivelAcesso == undefined) {
      nota.nivelAcesso = 1;
      this.notaService.update(nota).subscribe(() => {
        this.notaService.showMessage('Nível de acesso atualizado para Apenas Equipe!');
      });
    } else {
      nota.nivelAcesso = 0;
      this.notaService.update(nota).subscribe(() => {
        this.notaService.showMessage('Nível de acesso atualizado para Público!');
      });
    }
  }

  save() {
    if(this.form.valid){
      if(this.nota.id == undefined){
        this.notaService.create(this.nota).subscribe(async id => {
          this.notaService.showMessage('Nota criada com sucesso!');
          this.nota.id = id;
          this.notas.unshift(this.nota);
          await this.organizeNotas();
          this.nota = new Nota();
          this.setNota();
          this.form.resetForm();
        })
      } else {
        this.notaService.update(this.nota).subscribe(async id => {
          this.notaService.showMessage('Nota atualizada com sucesso!');
          this.nota.id = id;
          this.notas.unshift(this.nota);
          await this.organizeNotas();
          this.nota = new Nota();
          this.setNota();
          this.form.resetForm();
        })
      }
    } else{
      if(this.nota.titulo != undefined && this.nota.data != undefined && this.nota.descricao != undefined && this.nota.nivelAcesso == undefined){
        this.notaService.showMessage('Nível de acesso é obrigatório!',true)
      } else {
        this.notaService.showMessage('Existem campos inválidos ou não preenchidos no formulário!',true)
      }
    }
  }

  edit(nota: Nota) {
    const dialogRef = this.dialog.open(NotaEditDialogComponent, {
      data: { nota, paciente: this.paciente },
      width: '70vw', 
      height: 'auto',
      minHeight: '460px',
      panelClass: 'custom-dialog-container'
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.notaService.update(result).subscribe(async () => {
          this.notaService.showMessage('Nota atualizada com sucesso!');
          const index = this.notas.findIndex(n => n.id === result.id);
          if (index !== -1) {
            this.notas[index] = result;
            await this.organizeNotas();
          }
        });
      }
    });
  }

  delete(nota: Nota){
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        let index = this.notas.indexOf(nota)
        //console.log(nota.id + " - " + this.paciente.id)
        this.notaService.delete(nota.id, this.paciente.id).subscribe((id) => {
          this.notas.splice(index, 1)
          this.notaService.showMessage('Nota excluída com sucesso!');
          //this.router.navigate(['/paciente/' + id]);
        });
      }
    })
  }

  async organizeNotas() {
    this.notas.sort(function(a, b) {
      return new Date(b.data).getTime() - new Date(a.data).getTime();
    })
  }

  canEdit(nota: Nota): boolean {
    const user = this.authService.getUser();
    return (
      user &&
      (user.admin || user.superadmin || nota.profissional?.uid === user.uid)
    );
  }

}
