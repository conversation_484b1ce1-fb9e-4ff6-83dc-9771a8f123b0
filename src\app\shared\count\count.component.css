.counter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px;
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  position: relative;
}

.counter {
  border: 3px solid #004A7F;
  border-radius: 50%;
  width: 150px;
  height: 150px; 
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: Arial, sans-serif;
  text-align: center;
  position: relative;
  margin-bottom: 10px;
}

.counter-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 95%;
}

.counter-controls button {
  background-color: transparent;
  border: none;
  font-size: 16px; 
  color: #004A7F;
  cursor: pointer;
  padding: 5px; 
  transition: color 0.3s ease;
}

.counter-controls button:hover {
  color: #006bbf; 
}

.counter-controls span {
  font-size: 20px; 
  font-weight: bold;
  color: #004A7F;
}

.counter-label {
  font-size: 12px; 
  font-weight: bold;
  color: #004A7F;
  margin-top: 5px;
  border-top: 2px solid #004A7F;
  padding-top: 5px;
  display: inline-block;
  width: 90%;
  text-align: center;
}

.counter-instructions {
  margin-top: 7px;
  font-size: 14px;
  color: #333;
}

/* Estilização do checkbox */
.confirm-checkbox-container {
  margin-top: 15px;
  display: flex;
  align-items: center;
}

.confirm-checkbox-container label {
  font-size: 14px;
  margin-right: 10px;
  color: #333;
}

.confirm-checkbox-container input {
  transform: scale(1.2); 
}

.truncatable {         /* Evita que o texto quebre em várias linhas */
  overflow: hidden;            /* Oculta o texto que ultrapassar a largura da div */
  text-overflow: ellipsis;     /* Adiciona os "..." quando o texto for muito longo */
  max-width: 100%;             /* Limita a largura ao tamanho da div */
}

.tooltip-container {
  position: absolute;
  top: 0;
  right: 0;
  margin: 10px; /* Ajuste de margem opcional */
}

.tooltip-descricao-text {
  visibility: hidden;
  background-color: rgb(29, 29, 29);
  color: #fff;
  text-align: left;
  border-radius: 6px;
  padding: 3px;
  position: absolute;
  z-index: 1;
  top: 50%; 
  left: -10px; 
  transform: translateX(-100%) translateY(-50%); 
  width: 150px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 9px;
  white-space: pre-line;
  letter-spacing: 0.8px;
}

.tooltip-text {
  visibility: hidden;
  background-color: rgb(29, 29, 29);
  color: #fff;
  text-align: left;
  border-radius: 6px;
  padding: 3px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -80px;
  width: 160px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 9px;
  white-space: pre-line;
  letter-spacing: 0.8px;
}

.tooltip-container:hover .tooltip-descricao-text{
  visibility: visible;
  opacity: 0.65;
}

.tooltip-container:hover .tooltip-text{
  visibility: visible;
  opacity: 0.65;
}

.custom-cursor {
  pointer-events: none;
}
