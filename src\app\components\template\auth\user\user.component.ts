import { Parente } from './../../../parente/parente-model';
import { ParenteService } from './../../../parente/parente.service';
import { Profissional } from './../../../profissional/profissional-model';
import { ProfissionalService } from './../../../profissional/profissional.service';
import { HeaderService } from './../../header/header.service';
import { AuthService } from './../auth.service';
import { UserService } from './../user.service';
import { FirebaseUserModel } from './../user-model';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';


@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.css']
})
export class UserComponent implements OnInit {

  user: FirebaseUserModel = new FirebaseUserModel();
  profissional: Profissional = new Profissional();
  parente: Parente = new Parente();
  profileForm: FormGroup;

  constructor(
    private userService: UserService,
    private authService: AuthService,
    //private pessoaService: PessoaService,
    private profissionalService: ProfissionalService,
    private parenteService: ParenteService,
    private route: ActivatedRoute,
    private location : Location,
    //private headerService: HeaderService,
    private fb: FormBuilder
  ) {

  }

  ngOnInit(): void {
    //Recupero o usuário logado
    this.route.data.subscribe(routeData => {
      let data = routeData['data'];
      if (data) {
        //this.user = data;
        this.user = this.authService.getUser()
        //console.log(this.user)
        //console.log(this.user.name)
        //console.log(this.user.uid)
        //Preencho o formulário com as informações do usuário logado
        if(!this.user.familia){
          this.profissionalService.findByUserId(this.user.uid).subscribe(p => {
            this.profissional = p[0];
            //console.log(this.profissional.nome)
            //console.log(this.pessoa.telefone)
            this.createForm(this.profissional.nome, this.user.email, this.profissional.telefone);
            
          })
        } else {
            this.parenteService.findByUserId(this.user.uid).subscribe(p => {
              this.parente = p[0];
              //console.log(this.parente.nome)
              //console.log(this.pessoa.telefone)
              this.createForm(this.parente.nome, this.user.email, this.parente.telefone);
              
            })
        }
      }
      this.createForm('', '', '');
    })
  }

  createForm(nome, email, telefone) {
    this.profileForm = this.fb.group({
      nome: [nome, Validators.required ],
      email: new FormControl({value: email, disabled: true }),
      //email: [email, Validators.required, disabled: true],
      telefone: [telefone, Validators.required ]
    });
  }

  save(value){
    //Atualizo o profile do usuário no Auth do Firebase
    this.userService.updateCurrentUser(value)
    .then(res => {
      //console.log(res);
      //this.pessoaService.update(this.pessoa).subscribe(p => {
      if(!this.user.familia){
        this.profissional.nome = value.nome;
        this.profissional.telefone = value.telefone;
        value.displayName = value.nome;
        this.profissionalService.update(this.profissional).subscribe(p => {
          //console.log(p)
          this.authService.showMessage("Perfil salvo com sucesso.")
        })
      } else {
        this.parente.nome = value.nome;
        this.parente.telefone = value.telefone;
        value.displayName = value.nome;
        this.parenteService.update(this.parente).subscribe(p => {
          //console.log(p)
          this.authService.showMessage("Perfil salvo com sucesso.")
        })
      }
    }, err => {
      //console.log(err)
      this.authService.showMessage("Houve um erro ao salvar o seu perfil.", true)
    })
  }

  redefinePassword(){
    //console.log(this.authService.getUser().email);
    this.authService.resetPassword(this.authService.getUser().email)
      .then(res => {
        //console.log(res);
        this.authService.showMessage("Um e-mail foi enviado para você com as instruções para a redefinição de senha.")
      }, err => console.log(err))
  }

  logout(){
    this.authService.doLogout()
    .then((res) => {
      this.location.back();
    }, (error) => {
      console.log("Logout error", error);
    });
  }

}
