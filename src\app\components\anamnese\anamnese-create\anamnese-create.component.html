<mat-card> 
    <mat-card-title class="title">Nova Anamnese</mat-card-title>
    <mat-card-title>
        <button mat-fab  class="md-fab-bottom-right2"color="primary" matTooltip="Add novo grupo" (click)="addGroup()">
            <mat-icon>playlist_add</mat-icon>
        </button>
       <!-- <button mat-raised-button color="primary" (click)="imprime()">Console</button> -->
    </mat-card-title>
    <form #ngForm>
        <div style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
            <!--
                parentesco: string;
            -->
            <mat-form-field style="width: 60%;">
                <input class="input" matInput placeholder="Descrição" 
                    [(ngModel)]="anamnese.descricao" name="descricao" required> 
            </mat-form-field>
            <div style="width: 100%;" *ngFor="let grupo of grupoList; index as indexGrupo;">
                <div style="margin-top: 20px;" >
                    <mat-accordion class="example-headers-align" multi >
                        <mat-expansion-panel>
                            <mat-expansion-panel-header>
                                <mat-panel-title>
                                    {{grupo.descricao == null ? 'Novo Grupo' : grupo.descricao}}
                                </mat-panel-title>
                            </mat-expansion-panel-header>
                            <mat-form-field style="width: 60%; margin-right: 20px;">
                                <input class="input" matInput placeholder="Grupo" 
                                    [(ngModel)]="grupo.descricao" name="{{'descricao'+indexGrupo}}"> 
                            </mat-form-field>
                            <button mat-mini-fab color="primary" matTooltip="add nova pergunta" (click)="addPergunta(indexGrupo)">
                                <mat-icon>add</mat-icon>
                            </button>
                            <div *ngFor="let pergunta of grupo.perguntas; index as indexPergunta;">
                                <mat-card style="margin-top: 20px;">
                                    <mat-form-field style="width: 10%; margin-right: 10px;">
                                        <input class="input" type="number" matInput placeholder="Numeração"  [ngModelOptions]="{standalone: true}"
                                            [(ngModel)]="grupo.perguntas[indexPergunta].numeracao">
                                    </mat-form-field>
                                    <mat-form-field style="width: 40%; margin-right: 10px;">
                                        <input class="input" type="text" matInput placeholder="Descrição"  [ngModelOptions]="{standalone: true}"
                                            [(ngModel)]="grupo.perguntas[indexPergunta].descricao">
                                    </mat-form-field>
                                    <mat-form-field style="width: 30%; margin-right: 10px;" >                                    
                                        <mat-select placeholder="Tipo Campo" 
                                                    [(ngModel)]="grupo.perguntas[indexPergunta].campo.tipo" 
                                                    (ngModelChange)="onChangeTipoCampo(grupo.perguntas[indexPergunta].campo.tipo,indexGrupo, indexPergunta)"
                                                     [ngModelOptions]="{standalone: true}">
                                        <mat-option *ngFor="let tipoCampo of tiposCampo" [value]="tipoCampo.value">
                                            {{tipoCampo.descricao}}
                                        </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                    <mat-form-field style="width: 10%; margin-right: 10px;">
                                        <input class="input" type="number" matInput placeholder="Tamanho (% width)" 
                                            [(ngModel)]="grupo.perguntas[indexPergunta].campo.tamanho"  [ngModelOptions]="{standalone: true}">
                                    </mat-form-field>
                                    <button mat-mini-fab matTooltip="Remover pergunta" (click)="removerPergunta(indexGrupo,indexPergunta)">
                                        <mat-icon>remove</mat-icon>
                                    </button>
                                    <div *ngIf="grupo.perguntas[indexPergunta].campo.tipo == 'SELECAO'">
                                        <div style="width: 100%; flex-direction: row; display: inline-block;" *ngFor="let valor of grupo.perguntas[indexPergunta].campo.valores; index as indexValor">
                                           
                                            <mat-form-field style="width: 10%; margin-right: 10px;">
                                                <input class="input"  
                                                        matInput placeholder="Valor" [(ngModel)]="grupo.perguntas[indexPergunta].campo.valores[indexValor].value"  
                                                        [ngModelOptions]="{standalone: true}">
                                            
                                            </mat-form-field>
                                            <mat-form-field style="width: 10%;">
                                                <input class="input"  
                                                matInput placeholder="Descrição" [(ngModel)]="grupo.perguntas[indexPergunta].campo.valores[indexValor].descricao"  
                                                [ngModelOptions]="{standalone: true}">
                                            </mat-form-field>
                                        </div>
                                        <button mat-mini-fab color="primary" matTooltip="add nova opção" (click)="addOpcao(indexGrupo,indexPergunta)" >
                                            <mat-icon>add</mat-icon>
                                        </button>
                                    </div>
                                    
                                    <div>
                                        
                                    </div>
                                    
                                </mat-card> 
                            </div>                                               
                        </mat-expansion-panel>
                </mat-accordion>
                </div>
            </div>
        </div>
    </form>
    <br>

    <button mat-raised-button (click)="save()" color="primary">
        Salvar
    </button>

    <button mat-raised-button (click)="cancel()">
        Cancelar
    </button>
</mat-card>