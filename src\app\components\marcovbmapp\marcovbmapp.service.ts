import { map, catchError } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DominioVBMAPP } from './../dominiovbmapp/dominiovbmapp-model';
import { Observable, EMPTY } from 'rxjs';
import { MarcoVBMAPP } from './marcovbmapp-model';
import { environment } from './../../../environments/environment';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class MarcovbmappService {

  marcoVBMAPPlUrl = `${environment.API_URL}/marco_vbmapp`;
  
  //public competencias: BehaviorSubject<Competencia[]> = 
  //  new BehaviorSubject<Competencia[]>([]);
  
  public $competencias: Observable<MarcoVBMAPP[]>;
  public $domsNivel: Map<string, Observable<DominioVBMAPP[]>> = new Map<string, Observable<DominioVBMAPP[]>>();

  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  /*
  create(competencia: Competencia): Observable<Competencia>{
    console.log(competencia);
    return this.http.post<Competencia>(this.marcoVBMAPPlUrl, competencia).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  /*
  findById(id: string): Observable<Competencia>{
    return this.http.get<Competencia>(this.marcoVBMAPPlUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  findDominiosByNivel(id_nivel: string): Observable<DominioVBMAPP[]>{
    /*
    if(this.$domsNivel.get(id_nivel) == null){
      console.log('Carreguei os domínios do nível ' + id_nivel);
      this.$domsNivel.set(id_nivel, this.http.get<Dominio[]>(this.marcoVBMAPPlUrl + '/' + id_nivel + '/dominio').pipe(
        map(obj => obj),
        catchError(e => this.errorHandler(e) )
      ));
    }

    return this.$domsNivel.get(id_nivel);
    */
    
    return this.http.get<DominioVBMAPP[]>(this.marcoVBMAPPlUrl + '/' + id_nivel + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
    
  }

  /*
  findByNivelDominio(id_nivel: string, id_dominio: string): Observable<Competencia>{
    return this.http.get<Competencia>(this.marcoVBMAPPlUrl + '/' + id_nivel + 'nivel/'
        + id_dominio + '/dominio').pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */

  find(): Observable<MarcoVBMAPP[]>{
    /*
    if(this.$competencias == null){
      //Carrego as competências pela primeira vez
      console.log('Carreguei as competências...');
      this.$competencias = this.http.get<Competencia[]>(this.marcoVBMAPPlUrl).pipe(
          map(obj => obj),
          catchError(e => this.errorHandler(e) )
        );
    }
    return this.$competencias;
    */

    return this.http.get<MarcoVBMAPP[]>(this.marcoVBMAPPlUrl);
  }

  /*
  delete(id: string): Observable<MarcoVBMAPP>{
    return this.http.delete<MarcoVBMAPP>(this.marcoVBMAPPlUrl + '/' + id).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
  */
}
