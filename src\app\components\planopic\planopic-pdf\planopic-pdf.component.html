
<!-- TÍTULO -->
<div style="display: flex; flex-direction: row; width: 90%;">
    <div style="display: flex; width: 30%; text-align: left">
        <img src="assets/img/{{ user.organizationId }}.png" style="max-width: 150px; max-height: 78px" alt="" />
    </div>
    <div style="width: 70%; text-align: center; margin: auto">
        <p class="title">Plano de Intervenção Comportamental</p>
    </div>
</div>

<!-- SUB-TÍTULO -->
<div class="subtitulo">
    <div style="text-align: left; width: 34%">
        <p class="subtitle">Paciente: {{ paciente.nome }}</p>
    </div>
    <div style="text-align: center; width: 33%">
        <p class="subtitle">
            Profissional: {{ profissional.nome }}
        </p>
    </div>
    <div style="text-align: right; width: 33%">
        <p class="subtitle">
            Data: {{ pic.data | date: "dd/MM/yyyy" }}
        </p>
    </div>
</div>

<mat-card *ngFor="let objetivo of pic.objetivos; let idxObj = index" style="margin-bottom: 3px;">
    <mat-card-header>
        <mat-card-title>
            {{ idxObj + 1 }}. {{ objetivo.comportamentoAlvo }}
        </mat-card-title>
    </mat-card-header>
    <mat-card-content >
        <div style=" display: flex; flex-wrap: wrap; flex-direction: row;  justify-content: space-between; width: 90%;">
            <!-- <ng-container *ngIf="objetivo.comportamentoAlvo">
                <div style="width: fit-content; padding: 5px;">
                    <b>Meta:</b><br> {{ objetivo.comportamentoAlvo }} <br>
                </div>
            </ng-container> -->
            <ng-container *ngIf="objetivo.definicaoOperacional">
                <div style="width: fit-content; padding: 5px;">
                    <b>Tipo de Coleta:</b><br> {{ objetivo.definicaoOperacional }} <br>
                </div>
            </ng-container>
            <ng-container *ngIf="objetivo.meta">
                <div style="width: fit-content; padding: 5px;">
                    <b>Meta:</b><br> {{ objetivo.meta }} <br>
                </div>
            </ng-container>
            <ng-container *ngIf="objetivo.estrategiasAntecedentes">
                <div style="width: fit-content; padding: 5px;">
                    <b>Estratégias Antecedentes:</b><br> {{ objetivo.estrategiasAntecedentes }} <br>
                </div>
            </ng-container>
            <ng-container *ngIf="objetivo.estrategiasConsequencia">
                <div style="width: fit-content; padding: 5px;">
                    <b>Estratégias de Consequência:</b><br> {{ objetivo.estrategiasConsequencia }} <br>
                </div>
            </ng-container>
            <ng-container *ngIf="objetivo.tipoColeta">
                <div style="width: fit-content; padding: 5px;">
                    <b>Tipo de Coleta:</b><br> {{ objetivo.tipoColeta }} <br>
                    <ng-container *ngIf="objetivo.tipoColeta == 'Amostragem de Tempo'">
                        <b>Intervalo:</b><br> {{ objetivo.intervalo }} {{ objetivo.medidaIntervalo }} <br>
                        <b>Tipo de Amostragem:</b><br> {{ objetivo.tipoAmostragem }} <br>
                    </ng-container>
                </div>
            </ng-container>
        </div>
    </mat-card-content>
</mat-card>
<br>
<br>

    