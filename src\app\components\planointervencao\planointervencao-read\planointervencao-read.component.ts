import { AuthService } from './../../template/auth/auth.service';
import { Profissional } from './../../profissional/profissional-model';
import { PacienteService } from './../../paciente/paciente.service';
import { Etapa } from './../../etapa/etapa-model';
import { Objetivo } from './../../objetivo/objetivo-model';
import { NivelService } from './../../nivel/nivel.service';
import { PlanoIntervencaoService } from './../planointervencao.service';
import { MatTableDataSource } from '@angular/material/table';
import { Dominio } from './../../dominio/dominio-model';
import { Nivel } from './../../nivel/nivel-model';
import { PlanoIntervencao } from './../planointervencao-model';
import { AppDateAdapter, APP_DATE_FORMATS } from './../../../shared/format-datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { trigger, state, transition, style, animate } from '@angular/animations';
import { Router, ActivatedRoute } from '@angular/router';
import { Paciente } from './../../paciente/paciente-model';
import { Observable } from 'rxjs';
import { Component, OnInit, Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../template/confirm-dialog/confirm-dialog.component';
import { LoadingService } from 'src/app/shared/service/loading.service';

@Component({
  selector: 'app-planointervencao-read',
  templateUrl: './planointervencao-read.component.html',
  styleUrls: ['./planointervencao-read.component.css'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  providers: [
    {provide: DateAdapter, useClass: AppDateAdapter},
    {provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS}
  ]
})
export class PlanointervencaoReadComponent implements OnInit {

  public planointervencao: PlanoIntervencao = new PlanoIntervencao();
  //public nivel: Nivel = new Nivel();
  //public dominio: Dominio = new Dominio();
  //public paciente: Paciente;
  public idPaciente: string;

  //public pacientes: Paciente[];
  //public profissionais: Pessoa[];
  public planosintervencao: PlanoIntervencao[] = [];
  //public niveis: Nivel[];
  //public dominios: Dominio[];
  //public objetivosMap: Map<string,Objetivo> = new Map<string,Objetivo>();
  //public chklstcompView: ESDMChkLstCompetencia[] = [];
  //public esdmchecklists: ESDMChecklist[];
  //public esdmchecklist: ESDMChecklist = new ESDMChecklist();

  @Input()
  $pacienteSearch: Observable<Paciente>;
  
  @Input()
  $idPlanoIntervencaoSearch: string;

  paciente: Paciente = new Paciente();

  datasourceObjetivos = new MatTableDataSource();

  displayedColumnsObjs = ['index', 'expand', 'etapas', 'idNome']
  hasAccessCreate: boolean;
  hasAccessUpdate: boolean;
  hasAccessDelete: boolean;
  hasAccessRead: boolean;


  constructor(private planointervencaoService: PlanoIntervencaoService,
    //private objetivoService: ObjetivoService,
    //private esdmchecklistService: EsdmchecklistService,
    //private nivelService: NivelService,
    private pacienteService: PacienteService,
    //private pessoaService: PessoaService,
    //public dialog: MatDialog,
    public authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private loadingService: LoadingService
    ) { }

    public isTableExpanded = false;

  ngOnInit(): void {
    this.loadingService.show();
    //console.log(this.$idPlanoIntervencaoSearch)
    this.idPaciente = this.route.snapshot.paramMap.get('id');
    
    //Recupero o paciente selecionado

    this.pacienteService.findById(this.idPaciente).subscribe(paciente => {
      this.paciente = paciente;

      this.hasAccessCreate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','create')
      this.hasAccessUpdate = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','update')
      this.hasAccessDelete = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','delete')
      this.hasAccessRead = this.authService.verifySimpleAccess(this.getFuncoesUsuario(paciente), 'Paciente.Cadastro de Plano de Intervenção','read')
      
      //Carrego os planos de intervenção do paciente
      this.planointervencaoService.findByPaciente(this.idPaciente).subscribe(planos => {
        this.planosintervencao = planos.filter(p => p.status !== false);
        
        //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
        if(this.planosintervencao.length >0){
          if(this.$idPlanoIntervencaoSearch == undefined){
            this.planointervencao = this.planosintervencao[0];
          } else {
            this.planointervencao = this.planosintervencao.find(plano => plano.id == this.$idPlanoIntervencaoSearch);

          }
          
          //Seto os objetivos do plano atual para serem visualizados
          this.datasourceObjetivos.data = this.planointervencao.objetivos?.filter(obj => obj != null);

          //ColapseAll nos objetivos
          //this.planointervencao.objetivos.forEach(o => {
          //  o.isExpanded = false;
          //})
        }
        this.loadingService.hide();
      })    
    })
  }

  setObjetivosPlano(){
    this.datasourceObjetivos.data = this.planointervencao.objetivos.filter(obj => obj != null);
  }

  getFuncoesUsuario(paciente: Paciente): string[]{
    let funcoes: string[] = [];

    let profissional: Profissional;

    //Verifico se existe uma equipe estabelecida
    if(paciente.equipe != undefined){
      //Procuro o usuário logado dentro da equipe
      profissional = paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid);
  
      //Verifico se achou o profissional, uma vez que pode ser um Administrador, Funcionário ou Familiar.
      if (profissional != undefined){  
        paciente.equipe.find(prof => prof.uid == this.authService.getUser().uid).funcao.forEach(funcao => {
          funcoes.push(funcao.nome);
        })
      }
    }

   // console.log(funcoes)


    return funcoes;
  }

  toggleTableRow(row: any){
    row.isExpanded = !row.isExpanded;
  }

  percentualEtapas(row: Objetivo){
    if(row){
      return this.countEtapasAdquiridas(row) / this.countEtapas(row)
    } else {
      return 0;
    }
  }

  countEtapas(row: Objetivo){
    //return row.etapa.filter(e => (e.status=='Não adquirida' || e.status == undefined)).length
    if(row){
      return row.etapa.length
    } else {
      return 0;
    }

  }

  countEtapasAdquiridas(row: Objetivo){
    if(row){
      return row.etapa.filter(e => (e.status=='Adquirida')).length
    } else {
      return 0;
    }
  }

  changeEtapaStatus(etapa: Etapa){
    //Verifico se o usuário possui autorização de alterar o status da etapa
    if(this.hasAccessCreate){
      let obj: Objetivo;
      if(etapa.status == undefined || etapa.status == 'Não adquirida'){
        etapa.status = "Adquirida"
      } else {
        etapa.status = "Não adquirida"
      }
      //Verifico se todas as etapas foram adquiridas e, caso postivo, coloco o objetivo como adquirido
      obj = this.planointervencao.objetivos.find(objetivo => objetivo.id == etapa.id.substr(0, 6));
      //console.log(obj.id);
      if (obj.etapa.find(e => e.status != "Adquirida") == undefined){
        obj.status = "Adquirido";
      }  else {
        obj.status = "Não adquirido";
      }
      this.save(false);
    }
  }

  save(atualizar: boolean = false){
    this.loadingService.show();
    if(this.planointervencao.id == undefined){
      this.planointervencaoService.create(this.planointervencao).subscribe((id) => {
        this.planointervencao.id = id;
        this.loadingService.hide();
      });
    } else {
      this.planointervencaoService.update(this.planointervencao).subscribe((paciente) => {
        if(atualizar){
          this.atualizar();
        }
        this.loadingService.hide();
      });
    } 
  }

  saveToPDF(){
    this.router.navigate([]).then(result => {
      window.open('planointervencao/pdf/' + this.planointervencao.id + "?hasFullView=true",'_blank');
    })
  }

  resumoSaveToPDF(){
    this.router.navigate([]).then(result => {
      window.open('planointervencao/pdf/resumo/' + this.planointervencao.id + "?hasFullView=true",'_blank');
    })
  }

  graficoEvolucao(){
    this.router.navigate([]).then(result => {
      window.open('planointervencao/evolucao/' + this.planointervencao.id + "?hasFullView=true",'_blank');
    })
  }

  edit(){
    //console.log(this.esdmchecklist.id);
      //this.router.navigate(['/planointervencao/create', {   
      /*this.router.navigate(['/paciente/' + this.idPaciente + '/planointervencao', {   
        idPaciente: this.idPaciente, 
        idPlanoIntervencao: this.planointervencao.id
      }]
      //,{relativeTo: this.route.parent, skipLocationChange: true}
      )*/
      this.router.navigate(['/planointervencao/create', {   
        idPaciente: this.idPaciente, 
        idPlanoIntervencao: this.planointervencao.id
      }]
      //,{relativeTo: this.route.parent, skipLocationChange: true}
      )
  }

  atualizar(){

    //Carrego os planos de intervenção do paciente
    this.planointervencaoService.findByPaciente(this.idPaciente).subscribe(planos => {
      this.planosintervencao = planos.filter(p => p.status !== false);
      
      //Seto o plano de intervenção mais novo para visualiação (se tiver algum plano)
      if(this.planosintervencao.length >0){

          this.planointervencao = this.planosintervencao[0];
        
        //Seto os objetivos do plano atual para serem visualizados
        this.datasourceObjetivos.data = this.planointervencao.objetivos.filter(obj => obj != null);

      }
      this.loadingService.hide();

      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/paciente/'+this.idPaciente, {   
              tab: 'esdm_plano'
            }])
      this.loadingService.hide();  
    }) 

      
      
  }

  async delete(id:string){
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '250px',
      data: {
        msg: 'Tem certeza que deseja excluir o plano de intervenção ?'
      }
    });

    await dialogRef.afterClosed().subscribe(async result => {
        if(result){  

          this.planointervencao = this.planosintervencao.find(p => p.id == id);
          this.planointervencao.status = false;
          this.save(true);
          
      }
    
  
    })  
    
  }

  add(){
    //this.router.navigate(['/planointervencao/create', {   
    this.router.navigate(['/planointervencao/create', {   
      idPaciente: this.idPaciente
    }])
  }

}
