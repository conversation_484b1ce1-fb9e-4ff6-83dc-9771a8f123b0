<div fxLayout="row" fxLayoutWrap="wrap">
	<!-- Card column -->
	<div fxFlex.gt-sm="100%" fxFlex="100">
		<mat-card class="mat-elevation-z0">
			<mat-card-title>
				<div style="display: flex;">
					<div  mat-card-avatar *ngIf="paciente.sexo=='M'" class="header-image" style=" background-image: url('/assets/img/menino_perfil.svg');"></div>
					<div  mat-card-avatar *ngIf="paciente.sexo=='F'" class="header-image" style="background-image: url('/assets/img/menina_perfil.svg');"></div>
					<div style="margin-top: 10px; margin-left: 5px;">
						{{ paciente.nome }}
					</div>
				</div>
			</mat-card-title>
			<mat-card-subtitle>
				<span style="color: black;">({{idade+' meses'}} - {{paciente.dataNascimento | date: 'dd/MM/yyyy'}})</span>
			</mat-card-subtitle>
			<mat-card-content> 
					
					<div>
						<div fxFlex="15"></div>
						<div [ngClass]="[paciente.kpiChkLst == 'green' ? 'label label-ok' : 'label', 
										paciente.kpiChkLst == 'orange' ? 'label label-aviso' : 'label', 
										paciente.kpiChkLst == 'red' ? 'label label-atrasado' : 'label']">
								<small>Avaliação</small>
						</div>
						<div [ngClass]="[paciente.kpiPlInterv == 'green' ? 'label label-ok' : 'label', 
										paciente.kpiPlInterv == 'orange' ? 'label label-aviso' : 'label', 
										paciente.kpiPlInterv == 'red' ? 'label label-atrasado' : 'label']">
							<small>Plano</small>
						</div>
						<div [ngClass]="[paciente.kpiColeta == 'green' ? 'label label-ok' : 'label', 
										paciente.kpiColeta == 'orange' ? 'label label-aviso' : 'label', 
										paciente.kpiColeta == 'red' ? 'label label-atrasado' : 'label']">
							<small>Coleta</small>
						</div>
					</div>
			</mat-card-content>
			<mat-tab-group [selectedIndex]="selected.value"
			(selectedIndexChange)="selected.setValue($event)">
				<mat-tab label="Dados Básicos">
					<br>
					<div fxLayout="row wrap" fxLayoutAlign="space-around stretch" fxFlex="100" fxLayoutGap="10" style="width: 100%;">
						<mat-form-field style="width: 37%;">
							<input class="input" matInput placeholder="Nome" 
								[(ngModel)]="paciente.nome" name="nome" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 20%; padding-left: 10px;">
							<input class="input" matInput placeholder="CPF" 
								[(ngModel)]="paciente.cpf" name="cpf" disabled>
						</mat-form-field>
						
						<mat-form-field style="width: 16%; padding-left: 10px;">
							<input class="input" matInput placeholder="Data de Nascimento" 
								[(ngModel)]="paciente.dataNascimento" name="dataNascimento"
								[matDatepicker]="picker" disabled>
								<mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
								<mat-datepicker #picker></mat-datepicker>
						</mat-form-field>
			
						<mat-form-field  style="width: 10%; padding-left: 10px;">
							<mat-label>Sexo</mat-label>
							<mat-select class="select" placeholder="Sexo" 
								[(ngModel)]="paciente.sexo"
								name="sexo" disabled>
								<mat-option value="" >    
								</mat-option>
								<mat-option value="M" >
								Masculino
								</mat-option>
								<mat-option value="F" >
									Feminino
								</mat-option>
							</mat-select>
						</mat-form-field>
			
						<mat-form-field style="width: 20%;" >
							<input class="input" matInput placeholder="Telefone" [(ngModel)]="paciente.telefone" name="telefone" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 30%; padding-left: 10px;">
							<input class="input" matInput placeholder="E-mail" 
								[(ngModel)]="paciente.email" name="email" disabled>
						</mat-form-field>
											
						<br>

						<mat-form-field style="width: 20%;">
							<input class="input"  matInput placeholder="CEP" 
							[(ngModel)]="paciente.endereco.cep" name="cep" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 40%; padding-left: 10px;">
							<input class="input" matInput placeholder="Logradouro" 
							[(ngModel)]="paciente.endereco.logradouro" name="logradouro" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 10%; padding-left: 10px;">
							<input class="input" type="number" matInput placeholder="Nº" 
							[(ngModel)]="paciente.endereco.numero" name="numero" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 30%;">
							<input class="input" matInput placeholder="Complemento" 
							[(ngModel)]="paciente.endereco.complemento" name="complemento" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 20%; padding-left: 10px;">
							<input class="input" matInput placeholder="Bairro" 
							[(ngModel)]="paciente.endereco.bairro" name="bairro" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 15%; padding-left: 10px;">
							<input class="input" matInput placeholder="Cidade" 
							[(ngModel)]="paciente.endereco.cidade" name="cidade" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 5%; padding-left: 10px;">
							<input class="input" matInput placeholder="UF" 
							[(ngModel)]="paciente.endereco.uf" name="uf" disabled>
						</mat-form-field>
			
						<mat-form-field style="width: 20%; padding-left: 10px;">
							<input class="input" matInput placeholder="País" 
							[(ngModel)]="paciente.endereco.pais" name="pais" disabled>
						</mat-form-field>
					</div>
					<app-paciente-read [$pacienteSearch]="$pacienteSearch"></app-paciente-read>
					
                </mat-tab>
				<mat-tab label="Anamnese">
					<table mat-table [dataSource]="anamneses" class="mat-elevation-z8" > 

						<ng-container matColumnDef="checked">
							<th mat-header-cell style="text-align: center;" *matHeaderCellDef>#</th>
							<td mat-cell style="text-align: center;" *matCellDef="let row">
								<i class="material-icons" *ngIf="checkRespostaAnamnese(row.id)" style="color: #27c24c;">
									check
								</i>
								<i class="material-icons" *ngIf="!checkRespostaAnamnese(row.id)"style="color: #e40000;">
									cancel
								</i>
							</td>
						  </ng-container>

						<!-- Nome Column -->
						<ng-container matColumnDef="descricao"  >
							<th mat-header-cell *matHeaderCellDef >Descrição</th>
							<td mat-cell *matCellDef="let row"  >{{row.descricao}}</td>
						</ng-container>
				  
					  <!-- Action Column -->
					  <ng-container matColumnDef="action">
						<th mat-header-cell style="text-align: center;" *matHeaderCellDef>Visualiar/Responder Anamnese</th>
						<td mat-cell style="text-align: center;" *matCellDef="let row">
							<a (click)="anamneseViewPaciente(row.id)" class="edit" style="cursor: pointer;">
								<i class="material-icons">
									list_alt
								</i>
							</a>
						</td>
					  </ng-container>
				  
					  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
					  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
					</table>
					
				</mat-tab>
				<mat-tab label="Notas">
					<app-nota [$pacienteSearch]="$pacienteSearch"></app-nota>
				</mat-tab>

				<mat-tab>
					<ng-template mat-tab-label>
						<span>Ciclo Terapêutico</span>
					</ng-template>
					<mat-tab-group [selectedIndex]="selectedVBMAPP.value"
						(selectedIndexChange)="selectedVBMAPP.setValue($event)">
						<mat-tab label="Avaliações">
							<app-avaliacao-list [$pacienteSearch]="$pacienteSearch"></app-avaliacao-list>
						</mat-tab> 
						<mat-tab label="Evolução por Gráficos">
							<app-avaliacao-graphs [$pacienteSearch]="$pacienteSearch"></app-avaliacao-graphs>
						</mat-tab> 
						<mat-tab>
							<ng-template mat-tab-label>
								<span matTooltip="Plano de Ensino Individualizado">PEI</span>
							</ng-template>
							<app-planointervencaovbmapp-read [$pacienteSearch]="$pacienteSearch" [$idVbmappPlanSearch]="$idVbmappPlanSearch"></app-planointervencaovbmapp-read>
						</mat-tab> 
						<mat-tab>
							<ng-template mat-tab-label>
								<span matTooltip="Plano de Intervenção Comportamental">PIC</span>
							</ng-template>
							<app-planopic-read [$pacienteSearch]="$pacienteSearch" [$idPICPlanSearch]="$idPICPlanSearch"></app-planopic-read>
						</mat-tab> 
						<mat-tab label="Coleta Diária">
							<app-coletadiariavbmapp-read [$pacienteSearch]="$pacienteSearch"></app-coletadiariavbmapp-read>
						</mat-tab>
					</mat-tab-group>
				</mat-tab>
			</mat-tab-group>
		</mat-card>    
	</div>
</div>