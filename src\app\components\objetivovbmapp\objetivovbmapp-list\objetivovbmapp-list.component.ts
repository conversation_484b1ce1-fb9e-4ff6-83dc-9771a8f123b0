import { LoadingService } from '../../../shared/service/loading.service';
import { DominioVBMAPPService } from './../../dominiovbmapp/dominiovbmapp.service';
import { TipoSuporteService } from './../../tiposuporte/tiposuporte.service';
import { DeleteConfirmDialogComponent } from './../../template/delete-confirm-dialog/delete-confirm-dialog.component';
import { MarcovbmappService } from './../../marcovbmapp/marcovbmapp.service';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { AuthService } from './../../template/auth/auth.service';
import { NivelService } from './../../nivel/nivel.service';
import { ObjetivoVBMAPPService } from './../objetivovbmapp.service';
import { DominioVBMAPP } from './../../dominiovbmapp/dominiovbmapp-model';
import { Nivel } from './../../nivel/nivel-model';
import { ObjetivoVBMAPP } from './../objetivovbmapp-model';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-objetivovbmapp-list',
  templateUrl: './objetivovbmapp-list.component.html',
  styleUrls: ['./objetivovbmapp-list.component.css']
})
export class ObjetivovbmappListComponent implements OnInit {

  public objetivos: ObjetivoVBMAPP[];
  public objetivosView: ObjetivoVBMAPP[] = [];

  public niveis: Nivel[];
  public dominios: DominioVBMAPP[];
  //public dominiosTab: Dominio[] = [];

  //public objetivo: Objetivo = new Objetivo();
  public nivel: Nivel = new Nivel();
  public dominio: DominioVBMAPP = new DominioVBMAPP();
  public dominioMap: Map<string, DominioVBMAPP[]> = new Map<string, DominioVBMAPP[]>(); 

  public hasAccessUpdate: boolean;
  public hasAccessDelete: boolean;
  public hasAccessRead: boolean;


  // displayedColumns = ['nome', 'estimulos', 'dominio', 'marco', 'tiposuporte', 'action']
  displayedColumns = ['nome', 'habilidades', 'estimulos', 'tiposuporte', 'action']

  constructor(private objetivoService: ObjetivoVBMAPPService,
    public authService: AuthService,
    public dialog: MatDialog,
    private router: Router,
    private loadingService: LoadingService) { }

  async ngOnInit(): Promise<void> {
    this.loadingService.show()

    try {
      this.hasAccessUpdate = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','update')
      this.hasAccessDelete = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','delete')
      this.hasAccessRead = this.authService.verifySimpleAccess(['*'], 'Objetivo.Cadastro de objetivos (VBMAPP)','read')

      this.objetivoService.objetivos.subscribe((data) => {
        this.objetivos = data;
      })
  
      //Carregando Objetivos
      const objs = await this.objetivoService.find().toPromise();
      this.objetivoService.objetivos.next(objs.filter(o => o.ativo == true));
      
    } catch (error) {
      console.log("Erro: ", error)
      this.loadingService.hide()
    } finally {
      this.loadingService.hide()
    }
  }

  delete(id: string): void{
    let objetivo: ObjetivoVBMAPP;
    
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      width: '250px',
      data: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if(result){
        //Pego o objetivo e coloco ele como inativo
        objetivo = this.objetivos.find(obj => obj.id == id);
        objetivo.ativo = false;
        this.objetivoService.update(objetivo).subscribe(
          () => {
            this.objetivoService.showMessage('Objetivo excluído!', false);
            this.router.routeReuseStrategy.shouldReuseRoute = () => false;
            this.router.onSameUrlNavigation = 'reload';  
            this.router.navigate(['/vbmapp_objetivo']);
          }
        );
      }
    });
  }

  do() {

  }

  edit(id: string){
    //Colocar na sessão a lista de domínios por nível
    localStorage.removeItem("vbmapp_objetivo")
    localStorage.setItem("vbmapp_objetivo", JSON.stringify( this.objetivos ))
    this.router.navigate(['/vbmapp_objetivo/update/' + id])
  }

  new(){
    //console.log(this.esdmchecklist.paciente.id);
    this.router.navigate(['/vbmapp_objetivo/create'])
  }

  getNameHabilidade(hab: any) {
    if (!hab.idTipoAvaliacao || hab.idTipoAvaliacao == "5"){  
      return hab.id.substr(1)
    } else {
      return hab.sigla == undefined || hab.sigla == "" ? this.dominio?.nome + " - " + hab.ordem : hab.sigla
    }
  }

}
