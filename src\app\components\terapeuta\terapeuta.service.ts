import { environment } from './../../../environments/environment';
import { map, catchError } from 'rxjs/operators';
import { Terapeuta } from './terapeuta-model';
import { Observable, EMPTY } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TerapeutaService {

  terapeutaUrl = `${environment.API_URL}/terapeutas`;
  
  constructor(private snackbar: MatSnackBar,
    private http: HttpClient) { }

  showMessage(msg: string, isError: boolean = false) : void{
    this.snackbar.open(msg,'X',{
      duration: 3000,
      horizontalPosition: "right",
      verticalPosition: "top",
      panelClass: isError ? ['msg-error'] : ['msg-success']
    })
  }

  errorHandler(e: any): Observable<any>{
    this.showMessage('Ocorreu um erro!', true);
    return EMPTY;
  }

  find(): Observable<Terapeuta[]>{
    return this.http.get<Terapeuta[]>(this.terapeutaUrl).pipe(
      map(obj => obj),
      catchError(e => this.errorHandler(e) )
    );
  }
}
