form{
    display: flex;
    flex-flow: row wrap;
}

button{
    margin-right: 15px;
    margin-top: 20px;
}

.subtitle{
    font-weight: 500;
    font-size: large;
}

.mat-column-action {
    flex: 0 0 15% !important;
    width: 15% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-nome {
    flex: 0 0 65% !important;
    width: 65% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

.mat-column-id {
    flex: 0 0 20% !important;
    width: 20% !important;
    text-align: left;
    overflow-wrap: break-word;
    word-wrap: break-word;
}

table{
    width: 100%;
    border-collapse: collapse;
}

td {
    border: 0px solid lightgray;
}

tr:nth-child(even){
    background-color: #f2f2f2;
}

.header{
    background-color: #3f51b5;
    font-weight: bold;
    color: white;
}

.rowNome{
    text-align: left;
}
.rowCRUD{
    text-align: center;
}

a{
    cursor: pointer;
    text-decoration: none;
}

textarea{
    height: auto;
}

.edit {
    margin-right: 10px;
}
.edit > i {
    color: #d9cd26;
    cursor: pointer;
}

.delete > i {
    color: #e35e6b;
    cursor: pointer;
}